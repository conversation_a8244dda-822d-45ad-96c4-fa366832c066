import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{M as S,a as $,I as b}from"./index-46de5032.js";import{r as n,i as R}from"./vendor-4cdf2bd1.js";import{X as _}from"./XMarkIcon-cfb26fe7.js";import{t as c,S as l}from"./@headlessui/react-cdd9213e.js";new S;function H({refetch:j,reject:s,row:t}){var x,f,h,y;const[g,a]=n.useState(!1),{authDispatch:B,globalDispatch:E,custom:v,showToast:d,tokenExpireError:w}=$(),[r,m]=n.useState(!1);R();const i=`${(f=(x=t==null?void 0:t.update_request)==null?void 0:x.user)==null?void 0:f.first_name} ${(y=(h=t==null?void 0:t.update_request)==null?void 0:h.user)==null?void 0:y.last_name}`,p=()=>{a(!0)};async function u(I,q){m(!0);try{(await v({method:"POST",endpoint:"/v3/api/custom/goodbadugly/update-requests/acceptance",payload:{request:I,update_id:q}})).error||(a(!1),d("Accepted"),j())}catch(o){w(o.message),d(o.message,5e3,"error")}finally{m(!1)}}const N=`Are you sure you want to accept this update from ${i}?`,T=`Are you sure you want to reject this update from ${i}?`,k=s?T:N,C=`If accepted, ${i} will be able to send and you will be able to request updates moving forward.`,A=`If rejected, ${i} will be prohibited from sending you updates and you will be prohibited from requesting updates moving forward.`,F=s?A:C;return e.jsxs(e.Fragment,{children:[s?e.jsx("div",{className:" flex h-[24px] w-[60px] cursor-pointer flex-row items-center justify-center rounded-[4px] bg-red-500 font-Inter text-[12px]  text-white ",onClick:()=>p(),children:"Reject"}):e.jsx("div",{className:" flex h-[24px] w-[60px] cursor-pointer flex-row items-center justify-center rounded-[4px] bg-black font-Inter text-[12px]  text-white ",onClick:()=>p(),children:"Accept"}),e.jsx(c,{appear:!0,show:g,as:n.Fragment,children:e.jsxs(l,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>a(!1),children:[e.jsx(c.Child,{as:n.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(c.Child,{as:n.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(l.Panel,{className:"flex h-auto w-full max-w-md transform flex-col justify-between overflow-hidden rounded-md bg-[#f2dfce] p-6 text-left align-middle  shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between gap-5",children:[e.jsx(l.Title,{as:"h3",className:"text-[16px] font-medium  leading-7 text-gray-900 sm:text-[20px]",children:s?"Reject Update":"Accept Update"}),e.jsx("button",{onClick:()=>a(!1),type:"button",children:e.jsx(_,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-4 w-full flex-row items-center justify-between gap-3  space-y-4 text-sm",children:[e.jsx("p",{className:"flex items-center justify-between gap-5 font-[700]",children:k}),e.jsx("p",{children:F})]}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-[.125rem] border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>a(!1),children:"Cancel"}),s?e.jsx(b,{loading:r,disabled:r,onClick:()=>u(2,t==null?void 0:t.id),className:" h-[2.5rem] w-full max-w-[12.5rem]  bg-red-500 py-2 text-center text-[1.25rem] font-bold text-white transition-colors duration-100",children:"Yes, reject"}):e.jsx(b,{loading:r,disabled:r,onClick:()=>u(1,t==null?void 0:t.id),className:" h-[2.5rem] w-full max-w-[12.5rem]  bg-black py-2 text-center text-[1.25rem] font-bold text-white transition-colors duration-100 ",children:"Yes, accept"})]})]})})})})]})})]})}export{H as A};
