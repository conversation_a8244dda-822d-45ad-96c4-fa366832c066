import{A as R,G as F,M as T,t as D,s as k,I as _}from"./index-46de5032.js";import{r as a,h as B,i as M}from"./vendor-4cdf2bd1.js";import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{X as O}from"./XMarkIcon-cfb26fe7.js";import{t as N,S}from"./@headlessui/react-cdd9213e.js";function J(f,r){const[o,g]=a.useState(!1),[c,P]=a.useState([]),[u,l]=a.useState(0),[t,d]=B(),{dispatch:h,state:A}=a.useContext(R),{dispatch:C}=a.useContext(F),i=parseInt(t.get("limit")||"30"),s=parseInt(t.get("page")||"1"),y=t.get("company_name")||"",b=t.get("status")||"";async function w(){var j,I;g(!0);try{const x=new T,$=U=>Object.entries(U).reduce((E,[z,v])=>(v!=null&&v!==""&&(E[z]=v),E),{}),q=new URLSearchParams($({page:s,limit:i,request:f,update_id:r,company_name:y,status:b})),n=await x.callRawAPI(`/v3/api/goodbadugly/customer/fund-update-requests?${q.toString()}`);P(n==null?void 0:n.list),console.log(n==null?void 0:n.total),l((I=(j=n==null?void 0:n.total)==null?void 0:j[0])==null?void 0:I.total)}catch(x){D(h,x.message),x.message!=="TOKEN_EXPIRED"&&k(C,x.message,5e3,"error")}g(!1)}a.useEffect(()=>{w()},[i,s,y,b,f,r]);const p=Math.ceil(u/i);return console.log(u,i),console.log(p),{loading:o,updates:c,refetch:w,currentPage:s,pageCount:p,pageSize:i,updatePageSize:j=>{t.set("limit",j.toString()),t.set("page","1"),d(t)},previousPage:()=>{s>1&&(t.set("page",(s-1).toString()),d(t))},nextPage:()=>{s<p&&(t.set("page",(s+1).toString()),d(t))},canPreviousPage:s>1,canNextPage:s<p}}new T;function V({refetch:f,reject:r,row:o}){const[g,c]=a.useState(!1),{dispatch:P}=a.useContext(R),{dispatch:u}=a.useContext(F),[l,t]=a.useState(!1);M();const d=()=>{c(!0)};async function h(w,p){t(!0);try{await new T().callRawAPI(`/v3/api/goodbadugly/customer/fund-update-requests?page=1&limit=10&request=${w}&update_id=${p}`),c(!1),k(u,"Accepted"),f()}catch(m){D(P,m.message),m.message!=="TOKEN_EXPIRED"&&k(u,m.message,5e3,"error")}setUpdating(!1)}const A=`Are you sure you want to accept this update from ${o.company_name}?`,C=`Are you sure you want to reject this update from ${o.company_name}?`,i=r?C:A,s=`If accepted, ${o.company_name} will be able to send and you will be able to request updates moving forward.`,y=`If rejected, ${o.company_name} will be prohibited from sending you updates and you will be prohibited from requesting updates moving forward.`,b=r?y:s;return e.jsxs(e.Fragment,{children:[r?e.jsx("div",{className:" flex h-[24px] w-[60px] cursor-pointer flex-row items-center justify-center rounded-[4px] bg-red-500 font-Inter text-[12px]  text-white ",onClick:()=>d(),children:"Reject"}):e.jsx("div",{className:" flex h-[24px] w-[60px] cursor-pointer flex-row items-center justify-center rounded-[4px] bg-black font-Inter text-[12px]  text-white ",onClick:()=>d(),children:"Accept"}),e.jsx(N,{appear:!0,show:g,as:a.Fragment,children:e.jsxs(S,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>c(!1),children:[e.jsx(N.Child,{as:a.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(N.Child,{as:a.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(S.Panel,{className:"flex h-auto w-full max-w-md transform flex-col justify-between overflow-hidden rounded-md bg-[#f2dfce] p-6 text-left align-middle  shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between gap-5",children:[e.jsx(S.Title,{as:"h3",className:"text-[16px] font-medium  leading-7 text-gray-900 sm:text-[20px]",children:i}),e.jsx("button",{onClick:()=>c(!1),type:"button",children:e.jsx(O,{className:"h-6 w-6"})})]}),e.jsx("div",{className:"mt-4 flex w-full flex-row items-center justify-between  gap-3 text-sm",children:e.jsx("p",{children:b})}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-md border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>c(!1),children:"Cancel"}),r?e.jsx(_,{loading:l,disabled:l,onClick:()=>h(2,o.update_id),className:"disabled:bg-disabledblack h-[40px] w-full max-w-[200px] rounded-md bg-red-500 py-2 text-center text-[20px] font-bold text-white transition-colors duration-100",children:"Yes, reject"}):e.jsx(_,{loading:l,disabled:l,onClick:()=>h(1,o.update_id),className:"disabled:bg-disabledblack h-[40px] w-full max-w-[200px] rounded-md bg-black py-2 text-center text-[20px] font-bold text-white transition-colors duration-100 ",children:"Yes, accept"})]})]})})})})]})})]})}export{V as A,J as u};
