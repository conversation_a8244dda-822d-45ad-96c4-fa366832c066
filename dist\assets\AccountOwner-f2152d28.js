import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{u as a,L as t,E as i,d as n,a9 as l}from"./index-46de5032.js";import"./index-ae32885b.js";import{E as c}from"./lucide-react-0b94883e.js";import"./vendor-4cdf2bd1.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const D=()=>{var r,s;const{profile:o}=a();return e.jsxs("div",{className:"relative flex h-[9.1875rem] max-h-[9.1875rem] min-h-[9.1875rem] flex-col justify-between gap-[.75rem] rounded-[.25rem] border border-[#1F1D1A] bg-[#FFF0E5] p-4",children:[e.jsx("button",{className:"hidden absolute top-2 right-3 p-1 rounded-full transition-colors peer hover:bg-black/5",children:e.jsx(c,{className:"w-5 h-5 text-gray-600"})}),e.jsxs("div",{className:"absolute right-3 top-[-5000%] flex gap-2 rounded-[.25rem] border border-black bg-brown-main-bg p-2 opacity-0 shadow-lg transition-all hover:top-12 hover:opacity-100 focus:top-12 focus:opacity-100 peer-focus:top-12 peer-focus:opacity-100 peer-focus-visible:top-12 peer-focus-visible:opacity-100",children:[e.jsx("button",{className:"p-2 rounded-lg transition-colors hover:bg-gray-100",title:"Edit",children:e.jsx(t,{children:e.jsx(i,{})})}),e.jsx("button",{className:"p-2 rounded-lg transition-colors hover:bg-gray-100",title:"Delete",children:e.jsx(t,{children:e.jsx(n,{})})})]}),e.jsx("label",{className:"block font-iowan text-[1.25rem] font-[700] leading-[1.5537rem] text-[#1F1D1A]",children:"Account Owner"}),e.jsx("div",{className:"space-y-[.75rem]  ",children:e.jsx("div",{className:"flex h-10 w-10 rounded-full items-center gap-2 font-iowan  text-[32px] font-[700] leading-[39.78px]",children:o!=null&&o.photo?e.jsx("img",{src:o==null?void 0:o.photo,alt:"",className:"object-contain w-10 h-10 rounded-full"}):e.jsx(t,{children:e.jsx(l,{className:"w-10 h-10"})})})}),e.jsx("span",{className:" font-Inter text-[1rem] font-normal leading-[1.5rem] text-gray-600",children:(s=(r=o==null?void 0:o.companies)==null?void 0:r[0])==null?void 0:s.email})]})};export{D as default};
