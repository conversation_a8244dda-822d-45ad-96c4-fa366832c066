import{j as o}from"./@nextui-org/listbox-0f38ca19.js";import{R as w}from"./vendor-4cdf2bd1.js";import{u as re}from"./react-hook-form-9f4fcfa9.js";import{o as te}from"./yup-c41d85d2.js";import{c as ie,a as E}from"./yup-342a5df4.js";import{M as se,G as ne,A as oe,I as me,s as P,w as j,x as q,y as $,n as v,bE as R}from"./index-46de5032.js";import{M as le}from"./MkdInput-a0090fba.js";import{A as ae}from"./AddButton-51d1b2cd.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";const Ke=({data:i={id:null},options:a={endpoint:null,method:"GET"},onSuccess:t,onClose:O,multiple:T=!1,action:h="",mode:u="create",customMessage:b="",table:m="",input:s="input",initialValue:D="",inputType:I="text",disableCancel:N=!1,inputConfirmation:g=!0,customLoading:d=null})=>{var A,k,C;new se;const G=ie({...["input","input_create","input_update"].includes(u)?{[s]:E().required()}:{confirm:E().required().oneOf([h],`Confirmation must be "${h}"`)}}).required(),{state:{createModel:f,updateModel:c,deleteModel:p},dispatch:n}=w.useContext(ne),{state:fe,dispatch:l}=w.useContext(oe),{register:z,handleSubmit:B,setValue:_,watch:F,formState:{errors:K}}=re({resolver:te(G),defaultValues:{[s]:D}}),y=F(),U=async()=>{if(!["create","update","delete","custom"].includes(u))return P(n,"Mode must be create, update, delete or custom",5e3,"error");if(!Array.isArray(i))return P(n,"Data must be an list",5e3,"error");const r=i==null?void 0:i.map(x=>V(u,x));(await Promise.all(r)).some(x=>!x.error)&&t&&t()},V=(r,e)=>{var x;if(["create"].includes(r))return j(n,l,m,e,!1);if(["update"].includes(r))return q(n,l,m,e==null?void 0:e.id,e,!1);if(["delete"].includes(r))return $(n,l,m,e.id,null,!1);if(["custom"].includes(r))return v(n,l,{endpoint:a==null?void 0:a.endpoint,method:"POST",payload:e},(x=R)==null?void 0:x.createModel,!1)},H=async r=>{const e=await q(n,l,m,r==null?void 0:r.id,r);!(e!=null&&e.error)&&t&&t()},J=async r=>{const e=await q(n,l,m,r==null?void 0:r.id,{[s]:y[s]});!(e!=null&&e.error)&&t&&t({[s]:y[s],id:e==null?void 0:e.data})},Q=async r=>{const e=await j(n,l,m,{...r,[s]:y[s]});!(e!=null&&e.error)&&t&&t({[s]:y[s],id:e==null?void 0:e.data})},W=async r=>{if(h==="move")return M(r);const e=await j(n,l,m,r);!(e!=null&&e.error)&&t&&t()},X=async r=>{const e=await $(n,l,m,r.id);!(e!=null&&e.error)&&t&&t()},Y=async r=>{const e=await v(n,l,{endpoint:a==null?void 0:a.endpoint,method:a==null?void 0:a.method,payload:r},R.createModel);e&&(e!=null&&e.hasOwnProperty("error"))&&!(e!=null&&e.error)&&t&&t(e)},Z=()=>{const r=y[s];t&&t({[s]:r})},L=r=>{t&&t(r)},M=async r=>{const e=await v(n,l,{endpoint:"/v3/api/custom/qualitysign/inventory/move",method:"POST",payload:r},R.createModel);!(e!=null&&e.error)&&t&&t(e)},S={create:W,input_create:Q,input_update:J,update:H,delete:X,custom:Y,manual:L,input:Z},ee=async()=>{if(T)U();else{const r=S[u];return r(i)}};return w.useEffect(()=>{g||_("confirm",h)},[g]),w.useEffect(()=>{["input_update"].includes(u)&&_(s,D)},[u]),o.jsx("div",{className:"mx-auto flex h-fit flex-col items-center justify-start rounded pb-5 !font-inter leading-snug tracking-wide md:pb-0",children:o.jsx("form",{className:"flex h-fit w-full flex-col text-start",onSubmit:B(ee,r=>{console.log("ERROR >>",r)}),children:o.jsxs("div",{className:"space-y-5",children:[o.jsx("div",{className:"my-2",children:b?o.jsx("div",{children:b}):o.jsxs("div",{children:["Are you sure you want to ",h," ",i!=null&&i.id&&((A=i==null?void 0:i.id)!=null&&A.length)&&((k=i==null?void 0:i.id)==null?void 0:k.length)>1||(i==null?void 0:i.length)>1?"these":"this"," ",(C=m==null?void 0:m.split("_"))==null?void 0:C.join(" "),"?"]})}),o.jsx("div",{className:`!mb-10 ${g?"":"hidden"}`,children:o.jsx(le,{type:I,page:"items",rows:"5",name:["input","input_create","input_update"].includes(u)?s:"confirm",errors:K,register:z,label:o.jsxs("div",{className:"font-bold text-black",children:["Type"," ",["input","input_create","input_update"].includes(u)?"":`'${h}'`," ","below"]}),className:"grow resize-none"})}),o.jsxs("div",{className:"mt-5 flex w-full grow gap-5",children:[N?null:o.jsx(ae,{type:"button",onClick:()=>O(),disabled:d!==null?d:(f==null?void 0:f.loading)||(c==null?void 0:c.loading)||(p==null?void 0:p.loading),showPlus:!1,className:"grow self-end !border-gray-200 !bg-transparent font-iowan text-[14px] font-bold !text-primary-black",children:"Cancel"}),o.jsx(me,{type:"submit",loading:d!==null?d:(f==null?void 0:f.loading)||(c==null?void 0:c.loading)||(p==null?void 0:p.loading),disabled:d!==null?d:(f==null?void 0:f.loading)||(c==null?void 0:c.loading)||(p==null?void 0:p.loading),className:`self-end rounded px-4 py-2 font-iowan font-bold capitalize text-white ${N?"!grow":"!w-1/2"}`,children:h})]})]})})})};export{Ke as ActionConfirmation,Ke as default};
