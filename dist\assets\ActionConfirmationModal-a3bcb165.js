import{j as o}from"./@nextui-org/listbox-0f38ca19.js";import"./vendor-4cdf2bd1.js";import{p as A,L}from"./index-46de5032.js";import{A as y}from"./index-2d79ce9c.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const N=({data:i={id:null},options:l={endpoint:null,method:"GET"},onSuccess:a,onClose:t,multiple:p=!1,action:e="",mode:n="create",table:s="",title:d="",input:f="input",isOpen:r=!1,inputConfirmation:u=!0,disableCancel:m=!1,modalClasses:x={modalDialog:"max-h-[90%] min-h-[12rem] overflow-y-auto !w-full md:!w-[29.0625rem]",modal:"h-full"},customMessage:c="",inputType:h="text",initialValue:j="",customLoading:w=null})=>o.jsx(A,{isOpen:r,modalCloseClick:t,title:d,modalHeader:!0,classes:x,disableCancel:m,children:r&&o.jsx(L,{children:o.jsx(y,{data:i,mode:n,input:f,table:s,action:e,onClose:t,options:l,multiple:p,onSuccess:a,inputType:h,initialValue:j,disableCancel:m,customMessage:c,inputConfirmation:u,customLoading:w})})});export{N as ActionConfirmationModal,N as default};
