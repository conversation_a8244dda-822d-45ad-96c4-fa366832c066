import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as L,R as a,b as _}from"./vendor-4cdf2bd1.js";import{u as I}from"./react-hook-form-9f4fcfa9.js";import{o as P}from"./yup-c41d85d2.js";import{c as F,a as o}from"./yup-342a5df4.js";import{G as j,M,s as q,t as D}from"./index-46de5032.js";import{_ as R}from"./qr-scanner-cf010ec4.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const V=L.lazy(()=>R(()=>import("./DynamicContentType-400d997f.js"),["assets/DynamicContentType-400d997f.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-46de5032.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-d88d3663.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css"])),ne=({setSidebar:r})=>{var h,y,f;const w=F({page:o().required(),key:o().required(),type:o().required(),value:o()}).required(),i=[{key:"text",value:"Text"},{key:"image",value:"Image"},{key:"number",value:"Number"},{key:"kvp",value:"Key-Value Pair"},{key:"image-list",value:"Image List"},{key:"captioned-image-list",value:"Captioned Image List"},{key:"team-list",value:"Team List"}],{dispatch:C}=a.useContext(j),{dispatch:m}=a.useContext(j),[N,E]=a.useState((h=i[0])==null?void 0:h.key),[T,A]=a.useState(""),[d,c]=a.useState(!1),S=_(),{register:l,handleSubmit:p,setError:u,formState:{errors:x}}=I({resolver:P(w)}),g=async t=>{let b=new M;c(!0),console.log(t);try{b.setTable("cms");const s=await b.cmsAdd(t.page,t.key,t.type,T);if(!s.error)S("/admin/cms"),q(m,"Added");else if(s.validation){const v=Object.keys(s.validation);for(let n=0;n<v.length;n++){const k=v[n];u(k,{type:"manual",message:s.validation[k]})}}}catch(s){console.log("Error",s),u("page",{type:"manual",message:s.message}),D(C,s.message)}c(!1)};return a.useEffect(()=>{m({type:"SETPATH",payload:{path:"cms"}})},[]),e.jsxs("div",{className:"mx-auto rounded",children:[e.jsxs("div",{className:"flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("svg",{onClick:()=>r(!1),xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M14.3322 5.83203L19.8751 11.3749C20.2656 11.7654 20.2656 12.3986 19.8751 12.7891L14.3322 18.332M19.3322 12.082H3.83218",stroke:"#A8A8A8","stroke-width":"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{className:"text-lg font-semibold",children:"Add CMS Content"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{className:"flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 shadow-sm hover:bg-[#F4F4F4]",onClick:()=>r(!1),children:"Cancel"}),e.jsx("button",{className:"flex items-center rounded-md bg-[#1f1d1a] px-3 py-2 text-white shadow-sm",onClick:async()=>{await p(g)(),r(!1)},disabled:d,children:d?"Saving...":"Save"})]})]}),e.jsxs("form",{className:" w-full max-w-lg p-4 text-left",onSubmit:p(g),children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"page",children:"Page"}),e.jsx("input",{type:"text",placeholder:"Page",...l("page"),className:`focus:shadow-outline } mb-3 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow
focus:outline-none`})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"key",children:"Content Identifier"}),e.jsx("input",{type:"text",placeholder:"Content Identifier",...l("key"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-[#1f1d1a] shadow focus:outline-none ${(y=x.key)!=null&&y.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(f=x.key)==null?void 0:f.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold capitalize capitalize text-gray-700",children:"Content Type"}),e.jsx("select",{name:"type",id:"type",className:"focus:shadow-outline mb-3  w-full rounded border px-3 py-2 leading-tight text-[#1f1d1a] shadow focus:outline-none",...l("type",{onChange:t=>E(t.target.value)}),children:i.map(t=>e.jsx("option",{name:t.name,value:t.key,children:t.value},t.key))})]}),e.jsx(V,{contentType:N,setContentValue:A})]})]})};export{ne as default};
