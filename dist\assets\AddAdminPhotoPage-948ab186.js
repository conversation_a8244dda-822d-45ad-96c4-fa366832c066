import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{R as r,b as h}from"./vendor-4cdf2bd1.js";import{X as u}from"./@uppy/xhr-upload-5e511714.js";import{u as f,D as x}from"./@uppy/react-eecc9c21.js";import{M as b,G as p,s as g}from"./index-46de5032.js";import{a as j}from"./@uppy/core-a4ba4b97.js";import"./@nextui-org/theme-345a09ed.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/compressor-4bcbc734.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@uppy/drag-drop-20104cbe.js";import"./@uppy/progress-bar-f89fcdd6.js";import"./@uppy/file-input-b22c76c2.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";let d=new b;const V=({setSidebar:i})=>{const{dispatch:l}=r.useContext(p),m=h(),{dispatch:a}=r.useContext(p),n=f(()=>{let e=new j;return e.use(u,{id:"XHRUpload",method:"post",formData:!0,limit:0,fieldName:"file",allowedMetaFields:["caption","size"],headers:d.getHeader(),endpoint:d.uploadUrl()}),e.on("file-added",o=>{e.setFileMeta(o.id,{size:o.size,caption:""})}),e.on("upload-success",async(o,s)=>{s.status,s.body,console.log("response",s),g(a,"Uploaded"),m("/admin/photos")}),e.on("upload-error",(o,s,c)=>{c.status==401&&tokenExpireError(l,"TOKEN_EXPIRED")}),e});return r.useEffect(()=>{a({type:"SETPATH",payload:{path:"photos"}})},[]),t.jsxs("div",{className:"relative flex-auto p-4",children:[t.jsxs("div",{className:"flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] pb-3",children:[t.jsxs("div",{className:"flex items-center gap-3",children:[t.jsx("svg",{onClick:()=>i(!1),xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:t.jsx("path",{d:"M14.3322 5.83203L19.8751 11.3749C20.2656 11.7654 20.2656 12.3986 19.8751 12.7891L14.3322 18.332M19.3322 12.082H3.83218",stroke:"#A8A8A8","stroke-width":"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),t.jsx("span",{className:"text-lg font-semibold",children:"Add Photo"})]}),t.jsx("div",{className:"flex items-center gap-4",children:t.jsx("button",{className:"flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 shadow-sm hover:bg-[#F4F4F4]",onClick:()=>i(!1),children:"Cancel"})})]}),t.jsx(x,{uppy:n})]})};export{V as default};
