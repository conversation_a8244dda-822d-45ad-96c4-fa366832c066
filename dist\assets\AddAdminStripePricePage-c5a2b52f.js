import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as m,R as p,b as W}from"./vendor-4cdf2bd1.js";import{u as Y}from"./react-hook-form-9f4fcfa9.js";import{o as z}from"./yup-c41d85d2.js";import{c as J,a as l}from"./yup-342a5df4.js";import{A as Q,G as X,M as L,s as u,t as Z}from"./index-46de5032.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const Ne=({setSidebar:i})=>{var v,w,j,N,k,_,S,C,P,A,T,E,F,$;const[M,R]=m.useState("one_time"),[U,I]=m.useState([]),[x,h]=m.useState(!1),D=J({product_id:l().required(),name:l().required(),amount:l().required(),type:l().required(),interval:l().when("type",{is:"recurring",then:t=>t.required(),otherwise:t=>t.notRequired()}),interval_count:l(),usage_type:l().when("type",{is:"recurring",then:t=>t.required(),otherwise:t=>t.notRequired()}),usage_limit:l(),trial_days:l()}).required(),{dispatch:g}=p.useContext(Q),{dispatch:n}=p.useContext(X),G=W(),{register:r,handleSubmit:y,setError:H,setValue:b,trigger:O,resetField:ee,getValues:te,formState:{errors:s}}=Y({resolver:z(D)}),V=[{key:0,value:"",display:"Nothing Selected"},{key:1,value:"one_time",display:"One Time"},{key:2,value:"recurring",display:"Recurring"}],B=[{key:0,value:"",display:"Nothing Selected"},{key:1,value:"licenced",display:"Upfront"},{key:2,value:"metered",display:"Metered"}],K=[{key:0,value:"",display:"Nothing Selected"},{key:1,value:"day",display:"Day"},{key:2,value:"week",display:"Week"},{key:3,value:"month",display:"Month"},{key:4,value:"year",display:"Year"},{key:5,value:"lifetime",display:"Lifetime"}],f=async t=>{let o=new L;console.log(t),h(!0);try{const a=await o.addStripePrice(t);if(!a.error)u(n,"Price Added"),G("/admin/prices");else if(a.validation){const q=Object.keys(a.validation);for(let d=0;d<q.length;d++){const c=q[d];console.log(c),H(c,{type:"manual",message:a.validation[c]})}}}catch(a){console.log("Error",a),u(n,a.message),Z(g,a.message)}h(!1)};return p.useEffect(()=>{n({type:"SETPATH",payload:{path:"prices"}}),(async()=>{let t=new L;const{list:o,error:a}=await t.getStripeProducts({limit:"all"});if(a){u(g,"Something went wrong while fetching products list");return}I(o)})()},[]),e.jsxs("div",{className:"mx-auto rounded ",children:[e.jsxs("div",{className:"flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("svg",{onClick:()=>i(!1),xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M14.3322 5.83203L19.8751 11.3749C20.2656 11.7654 20.2656 12.3986 19.8751 12.7891L14.3322 18.332M19.3322 12.082H3.83218",stroke:"#A8A8A8","strok-width":"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{className:"text-lg font-semibold",children:"Add Price"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{className:"flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 shadow-sm hover:bg-[#f4f4f4]",onClick:()=>i(!1),children:"Cancel"}),e.jsx("button",{className:"flex items-center rounded-md bg-[#1f1d1a] px-3 py-2 text-white shadow-sm",onClick:async()=>{await y(f)(),i(!1)},disabled:x,children:x?"Saving...":"Save"})]})]}),e.jsxs("form",{className:"w-full max-w-lg p-4 text-left ",onSubmit:y(f),children:[e.jsxs("div",{className:"mb-4 ",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"product_id",children:"Product"}),e.jsxs("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border border-[#1f1d1a] px-3 py-2 leading-tight text-gray-700   shadow focus:outline-none",...r("product_id"),children:[e.jsx("option",{value:"",children:"Nothing selected"},"prod_default"),U.map((t,o)=>e.jsx("option",{value:t.id,children:t.name},`prod_${t.id}`))]}),e.jsx("p",{className:"text-xs italic text-red-500",children:(v=s.product_id)==null?void 0:v.message})]}),e.jsxs("div",{className:"mb-4 ",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"name",children:"Name"}),e.jsx("input",{type:"text",placeholder:"Name",...r("name"),className:`"shadow focus:shadow-outline w-[100px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 leading-tight text-[#1d1f1a] focus:outline-none   sm:w-[180px] ${(w=s.name)!=null&&w.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(j=s.name)==null?void 0:j.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"amount",children:"Amount"}),e.jsx("input",{type:"number",min:.1,step:"any",placeholder:"Amount",...r("amount"),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border border-[#1f1d1a] px-3 py-2 leading-tight text-gray-700   shadow focus:outline-none ${(N=s.amount)!=null&&N.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(k=s.amount)==null?void 0:k.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"type",children:"Type"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border border-[#1f1d1a] px-3 py-2 leading-tight text-gray-700   shadow focus:outline-none",...r("type"),onChange:t=>{const o=t.target.value;R(o),b("type",o),O("type")},children:V.map(t=>e.jsx("option",{value:t.value.toLowerCase(),children:t.display},`interval_${t.key}`))}),e.jsx("p",{className:"text-xs italic text-red-500",children:(_=s.type)==null?void 0:_.message})]}),M==="recurring"?e.jsxs("div",{className:"ml-6",children:[e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"interval",children:"Interval"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border border-[#1f1d1a] px-3 py-2 leading-tight text-gray-700   shadow focus:outline-none",...r("interval"),placeholder:"Select",children:K.map(t=>e.jsx("option",{value:t.value.toLowerCase(),children:t.display},`interval_${t.key}`))}),e.jsx("p",{className:"text-xs italic text-red-500",children:(S=s.interval)==null?void 0:S.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"interval_count",children:"Interval Count"}),e.jsx("input",{type:"number",step:"1",placeholder:"Interval Count",...r("interval_count"),...b("interval_count",1),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border border-[#1f1d1a] px-3 py-2 leading-tight text-gray-700   shadow focus:outline-none ${(C=s.interval_count)!=null&&C.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(P=s.interval_count)==null?void 0:P.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"usage_type",children:"Usage Type"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border border-[#1f1d1a] px-3 py-2 leading-tight text-gray-700   shadow focus:outline-none",...r("usage_type"),placeholder:"Select",children:B.map(t=>e.jsx("option",{value:t.value.toLowerCase(),children:t.display},`interval_${t.key}`))}),e.jsx("p",{className:"text-xs italic text-red-500",children:(A=s.usage_type)==null?void 0:A.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"trial_days",children:"Trial Days"}),e.jsx("input",{type:"number",step:"1",placeholder:"0",...r("trial_days"),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border border-[#1f1d1a] px-3 py-2 leading-tight text-gray-700   shadow focus:outline-none ${(T=s.trial_days)!=null&&T.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(E=s.trial_days)==null?void 0:E.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"trial_days",children:"Usage Limit"}),e.jsx("input",{type:"number",step:"1",placeholder:"1000",...r("usage_limit"),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border border-[#1f1d1a] px-3 py-2 leading-tight text-gray-700   shadow focus:outline-none ${(F=s.usage_limit)!=null&&F.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:($=s.usage_limit)==null?void 0:$.message})]})]}):""]})]})};export{Ne as default};
