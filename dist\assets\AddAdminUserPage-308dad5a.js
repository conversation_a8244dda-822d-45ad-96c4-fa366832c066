import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{R as r,b as A}from"./vendor-4cdf2bd1.js";import{u as S}from"./react-hook-form-9f4fcfa9.js";import{o as R}from"./yup-c41d85d2.js";import{c as L,a as n}from"./yup-342a5df4.js";import{G as v,M,s as U,t as $}from"./index-46de5032.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const se=({setSidebar:o})=>{var f,h,b,g;const y=L({email:n().email().required(),password:n().required(),role:n()}).required(),{dispatch:d}=r.useContext(v),{dispatch:N}=r.useContext(v),[m,c]=r.useState(!1),k=A(),{register:l,handleSubmit:p,setError:u,formState:{errors:t}}=S({resolver:R(y)}),E=[{name:"role",value:"admin"},{name:"role",value:"member"}],x=async s=>{let C=new M;c(!0);try{const a=await C.register(s.email,s.password,s.role);if(!a.error)U(d,"Added"),k("/admin/users");else if(a.validation){const w=Object.keys(a.validation);for(let i=0;i<w.length;i++){const j=w[i];u(j,{type:"manual",message:a.validation[j]})}}}catch(a){console.log("Error",a),u("email",{type:"manual",message:a.message}),$(d,a.message)}c(!1)};return r.useEffect(()=>{N({type:"SETPATH",payload:{path:"users"}})},[]),e.jsxs("div",{className:"mx-auto rounded",children:[e.jsxs("div",{className:"flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("svg",{onClick:()=>o(!1),xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M14.3322 5.83203L19.8751 11.3749C20.2656 11.7654 20.2656 12.3986 19.8751 12.7891L14.3322 18.332M19.3322 12.082H3.83218",stroke:"#A8A8A8",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{className:"text-lg font-semibold",children:"Add User"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{className:"flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 shadow-sm hover:bg-[#f4f4f4]",onClick:()=>o(!1),children:"Cancel"}),e.jsx("button",{className:"flex items-center rounded-md bg-[#1f1d1a] px-3 py-2 text-white shadow-sm",onClick:async()=>{await p(x)(),o(!1)},disabled:m,children:m?"Saving...":"Save"})]})]}),e.jsxs("form",{className:"w-full max-w-lg p-4 text-left",onSubmit:p(x),children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"email",children:"Email"}),e.jsx("input",{type:"email",placeholder:"Email",...l("email"),className:`shadow-outline w-full appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 leading-tight text-[#1d1f1a] shadow focus:w-full focus:outline-none ${(f=t.email)!=null&&f.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(h=t.email)==null?void 0:h.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Role"}),e.jsx("select",{name:"role",id:"role",className:"shadow-outline w-full appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 leading-tight text-[#1d1f1a] shadow focus:w-full focus:outline-none",...l("role"),children:E.map(s=>e.jsx("option",{value:s.value,defaultValue:s.value==="client",children:s.value},s.value))})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"password",children:"Password"}),e.jsx("input",{type:"password",placeholder:"******************",...l("password"),className:`shadow-outline w-full appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 leading-tight text-[#1d1f1a] shadow focus:w-full focus:outline-none ${(b=t.password)!=null&&b.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(g=t.password)==null?void 0:g.message})]})]})]})};export{se as default};
