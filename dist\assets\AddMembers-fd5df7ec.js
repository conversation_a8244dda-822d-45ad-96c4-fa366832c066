import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as s,b as r,j as m}from"./vendor-4cdf2bd1.js";import"./index-f373d1dd.js";import{A as o,G as i,bE as l}from"./index-46de5032.js";import"./moment-a9aaa855.js";import{D as d}from"./DocumentTextIcon-54b5e200.js";import"./@nextui-org/theme-345a09ed.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const p=({onClose:n,title:c="Add Members",classes:x={modal:"h-full",modalDialog:"h-[90%]",modalContent:""},page:b="",isOpen:f,disableCancel:h=!1})=>{var a;s.useContext(o);const{dispatch:u,state:t}=s.useContext(i);return r(),(a=t==null?void 0:t[l.createModel])!=null&&a.loading,e.jsxs("div",{className:"divide-y divide-[#1f1d1a]/10",children:[e.jsx("div",{className:"flex w-full items-start gap-4 px-6 py-4 hover:bg-brown-main-bg",children:e.jsxs("div",{children:[e.jsxs("div",{className:"flex gap-2",children:["→",e.jsx("p",{className:"text-left text-xl font-semibold",children:"Start Here"})]}),e.jsx("p",{className:"mt-1 font-medium"})]})}),e.jsxs(m,{className:"flex w-full items-start gap-4 px-6 py-4 hover:bg-brown-main-bg",to:"/member/company/teams?trigger=add",children:[e.jsx("div",{className:"flex items-center justify-center gap-2 rounded-[.625rem] border-2 border-[#1f1d1a] p-2",children:e.jsx(d,{className:"h-8 w-8 text-primary-black",strokeWidth:2})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-left text-xl font-semibold",children:"New Team Member"}),e.jsx("p",{className:"mt-1 font-medium",children:"Add Team Members To be able to create updates"})]})]})]})},O=s.memo(p);export{O as default};
