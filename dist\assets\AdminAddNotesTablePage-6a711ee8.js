import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{R as s,b as w}from"./vendor-4cdf2bd1.js";import{u as v}from"./react-hook-form-9f4fcfa9.js";import{o as I}from"./yup-c41d85d2.js";import{c as k,a as l}from"./yup-342a5df4.js";import{G as g,I as E,M as T,s as A,t as C}from"./index-46de5032.js";import"./react-quill-a78e6fc7.js";/* empty css                   */import{M as p}from"./MkdInput-a0090fba.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@craftjs/core-a2cdaeb4.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@uppy/compressor-4bcbc734.js";const pt=()=>{const{dispatch:u}=s.useContext(g),h=k({content:l(),type:l(),status:l(),update_id:l()}).required(),{dispatch:y}=s.useContext(g),[f,R]=s.useState({}),[x,d]=s.useState(!1),S=w(),{register:i,handleSubmit:j,setError:b,setValue:D,formState:{errors:m}}=v({resolver:I(h)});s.useState([]);const N=async a=>{let c=new T;d(!0);try{for(let r in f){let o=new FormData;o.append("file",f[r].file);let n=await c.uploadImage(o);a[r]=n.url}c.setTable("notes");const t=await c.callRestAPI({content:a.content,type:a.type,status:a.status,update_id:a.update_id},"POST");if(!t.error)A(u,"Added"),S("/admin/notes");else if(t.validation){const r=Object.keys(t.validation);for(let o=0;o<r.length;o++){const n=r[o];b(n,{type:"manual",message:t.validation[n]})}}d(!1)}catch(t){d(!1),console.log("Error",t),b("content",{type:"manual",message:t.message}),C(y,t.message)}};return s.useEffect(()=>{u({type:"SETPATH",payload:{path:"notes"}})},[]),e.jsxs("div",{className:" mx-auto rounded  p-5 shadow-md",children:[e.jsx("h4",{className:"text-[16px] font-medium md:text-xl",children:"Add Notes"}),e.jsxs("form",{className:" w-full max-w-lg",onSubmit:j(N),children:[e.jsx(p,{type:"text",page:"notes",name:"content",errors:m,label:"Content",placeholder:"Content",register:i,className:""}),e.jsx(p,{type:"text",page:"notes",name:"type",errors:m,label:"Type",placeholder:"Type",register:i,className:""}),e.jsx(p,{type:"text",page:"notes",name:"status",errors:m,label:"Status",placeholder:"Status",register:i,className:""}),e.jsx(p,{type:"text",page:"notes",name:"update_id",errors:m,label:"Update Id",placeholder:"Update Id",register:i,className:""}),e.jsx(E,{type:"submit",loading:x,disabled:x,className:"focus:shadow-outline rounded bg-primary-black px-4 py-2 font-bold text-white focus:outline-none",children:"Submit"})]})]})};export{pt as default};
