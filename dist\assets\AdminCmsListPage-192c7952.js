import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{R as i,b as ie}from"./vendor-4cdf2bd1.js";import{u as re}from"./react-hook-form-9f4fcfa9.js";import{o as ne}from"./yup-c41d85d2.js";import{c as oe,a as S}from"./yup-342a5df4.js";import{M as le,A as ce,G as de,t as pe,g as w}from"./index-46de5032.js";import{M as D}from"./index-d526f96e.js";import{B as me,a as ue}from"./index.esm-54e24cf9.js";import{A as xe,a as he}from"./index.esm-25e0e799.js";import{R as fe}from"./index.esm-3e7472af.js";import ge from"./EditAdminCmsPage-e66918eb.js";import je from"./AddAdminCmsPage-425a8274.js";import{A as be}from"./AddButton-51d1b2cd.js";import ye from"./Skeleton-7d73a945.js";import{P as Se}from"./index-3283c9b7.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./react-icons-36ae72b7.js";import"./react-loading-skeleton-f57eafae.js";let F=new le;const n=[{header:"Page",accessor:"page",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Identifier",accessor:"content_key",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Content Type",accessor:"content_type",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],nt=()=>{const{dispatch:R}=i.useContext(ce),{dispatch:M}=i.useContext(de);i.useState("");const[p,T]=i.useState([]),[c,v]=i.useState(10),[N,O]=i.useState(0),[we,$]=i.useState(0),[d,q]=i.useState(0),[B,z]=i.useState(!1),[L,_]=i.useState(!1),[x,h]=i.useState(!1),[I,f]=i.useState(!1),[G,ve]=i.useState(!1),[C,H]=i.useState(!1),[A,P]=i.useState(!1),[o,g]=i.useState([]),[K,j]=i.useState([]),[Ne,U]=i.useState(""),[V,J]=i.useState("eq"),[Q,W]=i.useState();ie();const X=oe({page:S(),key:S(),type:S()}),{register:Ce,handleSubmit:Y,setError:Ae,formState:{errors:Pe}}=re({resolver:ne(X)});function Z(t){console.log(n[t]),n[t].isSorted?n[t].isSortedDesc=!n[t].isSortedDesc:(n.map(s=>s.isSorted=!1),n.map(s=>s.isSortedDesc=!1),n[t].isSorted=!0),async function(){await l(0,c)}()}function ee(t){(async function(){v(t),await l(0,t)})()}function te(){(async function(){await l(d-1>0?d-1:0,c)})()}function se(){(async function(){await l(d+1<=N?d+1:0,c)})()}const k=(t,s,a)=>{const r=s==="eq"&&isNaN(a)?`"${a}"`:a,m=`${t},${s},${r}`;j(b=>[...b.filter(u=>!u.includes(t)),m]),U(a)};async function l(t,s,a){try{F.setTable("cms");const r=await F.callRestAPI({payload:{...a},page:t,limit:s},"PAGINATE"),{list:m,total:b,limit:E,num_pages:u,page:y}=r;T(m),v(E),O(u),q(y),$(b),z(y>1),_(y+1<=u)}catch(r){console.log("ERROR",r),pe(R,r.message)}}const ae=t=>{let s=w(t.page),a=w(t.key),r=w(t.type);l(0,10,{page:s,content_key:a,content_type:r})};return i.useEffect(()=>{M({type:"SETPATH",payload:{path:"cms"}}),async function(){await l(1,c)}()},[]),i.useEffect(()=>{x||l(1,c,K)},[x]),e.jsxs("div",{className:"px-8",children:[e.jsxs("div",{className:"flex items-center justify-between py-3",children:[e.jsxs("form",{className:"relative rounded bg-brown-main-bg",onSubmit:Y(ae),children:[e.jsxs("div",{className:"flex items-center gap-4 text-gray-700",children:[e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-[#0003] px-3 py-1",onClick:()=>H(!C),children:[e.jsx(me,{}),e.jsx("span",{children:"Filters"}),o.length>0&&e.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start  text-white",children:o.length>0?o.length:null})]}),e.jsxs("div",{className:" flex cursor-pointer items-center justify-between gap-3 rounded-md border border-[#0003] px-2 py-1 focus-within:border-gray-400",children:[e.jsx(ue,{className:"text-xl text-gray-200"}),e.jsx("input",{type:"text",placeholder:"search",className:"border-none p-0 placeholder:text-left  focus:outline-none",style:{boxShadow:"0 0 transparent"},onInput:t=>{var s;return k("name","cs",(s=t.target)==null?void 0:s.value)}}),e.jsx(xe,{className:"text-lg text-gray-200"})]})]}),C&&e.jsxs("div",{className:"top-fill filter-form-holder absolute left-0  z-20 mt-4 min-w-[70%] rounded-md border border-[#0003] bg-brown-main-bg p-5 shadow-xl",children:[o==null?void 0:o.map((t,s)=>e.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[e.jsx("div",{className:" mb-3  w-1/3 rounded-md border border-black/60 px-3 py-2 leading-tight text-gray-700 outline-none",children:t}),e.jsxs("select",{className:"w-[30%] appearance-none border-none outline-0",onChange:a=>{J(a.target.value)},children:[e.jsx("option",{value:"eq",selected:!0,children:"equals"}),e.jsx("option",{value:"cs",children:"contains"}),e.jsx("option",{value:"sw",children:"start with"}),e.jsx("option",{value:"ew",children:"ends with"}),e.jsx("option",{value:"lt",children:"lower than"}),e.jsx("option",{value:"le",children:"lower or equal"}),e.jsx("option",{value:"ge",children:"greater or equal"}),e.jsx("option",{value:"gt",children:"greater than"}),e.jsx("option",{value:"bt",children:"between"}),e.jsx("option",{value:"in",children:"in"}),e.jsx("option",{value:"is",children:"is null"})]}),e.jsx("input",{type:"text",placeholder:"Enter value",className:" mb-3 w-1/3 rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:a=>k(t,V,a.target.value)}),e.jsx(fe,{className:" cursor-pointer text-xl",onClick:()=>{g(a=>a.filter(r=>r!==t)),j(a=>a.filter(r=>!r.includes(t)))}})]},s)),e.jsxs("div",{className:"search-buttons relative flex items-center justify-between font-semibold",children:[e.jsxs("div",{className:"mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-brown-main-bg px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out ",onClick:()=>{P(!A)},children:[e.jsx(he,{}),"Add filter"]}),A&&e.jsx("div",{className:"absolute top-11 z-10 bg-brown-main-bg px-5 py-3 text-gray-600 shadow-md",children:e.jsx("ul",{className:"flex flex-col gap-2 text-gray-500",children:n.slice(0,-1).map(t=>e.jsx("li",{className:`${o.includes(t.header)?"cursor-not-allowed text-gray-400":"cursor-pointer"}`,onClick:()=>{o.includes(t.header)||g(s=>[...s,t.header]),P(!1)},children:t.header},t.header))})}),o.length>0&&e.jsx("div",{onClick:()=>{g([]),j([])},className:"inline-block cursor-pointer  rounded px-6  py-2.5 font-medium leading-tight text-gray-600  transition duration-150 ease-in-out",children:"Clear all filter"})]})]})]}),e.jsx(be,{onClick:()=>h(!0)})]}),G?e.jsx(ye,{}):e.jsx("div",{children:e.jsx("div",{className:"overflow-x-auto  shadow ",children:e.jsxs("table",{className:"min-w-full divide-y divide-[#1f1d1a]/10",children:[e.jsx("thead",{children:e.jsx("tr",{children:n.map((t,s)=>e.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",onClick:()=>Z(s),children:[t.header,e.jsx("span",{children:t.isSorted?t.isSortedDesc?" ▼":" ▲":""})]},s))})}),e.jsxs("tbody",{className:"font-iowan-regular divide-y divide-[#1f1d1a]/10",children:[(p==null?void 0:p.length)===0&&e.jsx("div",{className:"w-full py-3 text-center",children:"No data"}),p.map((t,s)=>e.jsx("tr",{className:"  md:h-[60px]",children:n.map((a,r)=>a.accessor==""?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsxs("button",{className:"cursor-pointer text-xs text-[#4F46E5]",onClick:()=>{W(t==null?void 0:t.id),f(!0)},children:[" ","Edit"]})},r):a.mapping?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:a.mapping[t[a.accessor]]},r):e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t[a.accessor]},r))},s))]})]})})}),e.jsx(Se,{currentPage:d,pageCount:N,pageSize:c,canPreviousPage:B,canNextPage:L,updatePageSize:ee,previousPage:te,nextPage:se}),e.jsx(D,{isModalActive:x,closeModalFn:()=>h(!1),children:e.jsx(je,{setSidebar:h})}),e.jsx(D,{isModalActive:I,closeModalFn:()=>f(!1),children:e.jsx(ge,{activeId:Q,setSidebar:f})})]})};export{nt as default};
