import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{r as F,R as S,L}from"./vendor-4cdf2bd1.js";import{u as C}from"./react-hook-form-9f4fcfa9.js";import{o as R}from"./yup-c41d85d2.js";import{c as $,a as q}from"./yup-342a5df4.js";import{G as A,I as D,M as G,s as I,t as M}from"./index-46de5032.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const oe=()=>{var n,r;const[o,a]=F.useState(!1),j=$({email:q().email().required()}).required(),{register:y,handleSubmit:k,setError:m,formState:{errors:s}}=C({resolver:R(j)}),{dispatch:l}=S.useContext(A),N=async v=>{var d,c,p,x,u,g,f,b;let E=new G;try{a(!0);const e=await E.forgot(v.email,admin);if(!e.error)I(l,"Reset Code Sent");else if(e.validation){const h=Object.keys(e.validation);for(let i=0;i<h.length;i++){const w=h[i];m(w,{type:"manual",message:e.validation[w]})}}a(!1)}catch(e){a(!1),console.log("Error",e),m("email",{type:"manual",message:(c=(d=e==null?void 0:e.response)==null?void 0:d.data)!=null&&c.message?(x=(p=e==null?void 0:e.response)==null?void 0:p.data)==null?void 0:x.message:e==null?void 0:e.message}),M(l,(g=(u=e==null?void 0:e.response)==null?void 0:u.data)!=null&&g.message?(b=(f=e==null?void 0:e.response)==null?void 0:f.data)==null?void 0:b.message:e==null?void 0:e.message)}};return t.jsx("div",{className:"flex min-h-screen w-full items-center justify-center",children:t.jsxs("div",{className:"mx-auto w-full max-w-xs",children:[t.jsxs("form",{onSubmit:k(N),className:"mb-4 mt-8 rounded bg-[#FFF4EC] px-8 pb-8 pt-6 shadow-md ",children:[t.jsxs("div",{className:"mb-4",children:[t.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"email",children:"Email"}),t.jsx("input",{type:"email",placeholder:"Email",...y("email"),className:`"shadow focus:shadow-outline w-[100px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 leading-tight text-[#1d1f1a] focus:outline-none   sm:w-[180px] ${s&&((n=s.email)!=null&&n.message)?"border-red-500":""}`}),t.jsx("p",{className:"text-xs italic text-red-500",children:s&&((r=s.email)==null?void 0:r.message)})]}),t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsx(D,{className:"focus:shadow-outline rounded bg-primary-black px-4 py-2 font-bold text-white focus:outline-none disabled:cursor-not-allowed",type:"submit",loading:o,disabled:o,children:"Forgot Password"}),t.jsx(L,{className:"inline-block align-baseline text-sm font-bold text-primary-black",to:"/admin/login",children:"Login?"})]})]}),t.jsxs("p",{className:"text-center text-xs text-gray-500",children:["© ",new Date().getFullYear()," manaknightdigital inc. All rights reserved."]})]})})};export{oe as default};
