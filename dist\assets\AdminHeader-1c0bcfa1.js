import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{b as f,r as c,j as h}from"./vendor-4cdf2bd1.js";import{a as u,bb as j,bm as r,L as w,bh as g,bl as b}from"./index-46de5032.js";import{H as v}from"./index-59df21c1.js";import{c as N}from"./index.esm-54e24cf9.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./react-icons-36ae72b7.js";const V=()=>{var a;const{globalState:l,gobalDispatch:p,authState:s,authDispatch:x}=u(),o=f(),[n,t]=c.useState(!1),m=[{to:`/${s==null?void 0:s.role}/dashboard`,text:"Dashboard",icon:e.jsx(j,{fillOpacity:`${l.path==="dashboard"?"1":"0.6"}`}),value:"dashboard"},{to:`/${s==null?void 0:s.role}/plans`,text:"Plans",icon:e.jsx(r,{fillOpacity:l.path==="plans"?"1":"0.6"}),value:"plans"},{to:`/${s==null?void 0:s.role}/pricing`,text:"Plan Prices",icon:e.jsx(r,{fillOpacity:l.path==="pricing"?"1":"0.6"}),value:"pricing"},{to:`/${s==null?void 0:s.role}/coupons`,text:"Coupons",icon:e.jsx(r,{fillOpacity:l.path==="coupons"?"1":"0.6"}),value:"coupons"},{to:`/${s==null?void 0:s.role}/email`,text:"Emails",icon:e.jsx(r,{fillOpacity:l.path==="emails"?"1":"0.6"}),value:"email"},{to:`/${s==null?void 0:s.role}/users`,text:"Users",icon:e.jsx(r,{fillOpacity:l.path==="users"?"1":"0.6"}),value:"users"}].filter(i=>i);let d=i=>{p({type:"OPEN_SIDEBAR",payload:{isOpen:i}})};return e.jsxs("div",{className:`relative z-[10] flex flex-col bg-[#1F1D1A] px-5 text-[#A8A8A8] transition-all ${l.isOpen?"absolute left-0 top-0 h-full w-[16.25rem] sm:min-w-[16.25rem] sm:max-w-[260px] lg:relative":"relative h-full w-[5.25rem] bg-[#1f1d1a] text-white sm:min-w-[5rem] sm:max-w-[5.25rem]"} `,children:[e.jsxs("div",{className:"relative flex h-screen max-h-full min-h-full min-w-full max-w-full flex-col overflow-y-clip   pt-[1.5rem] ",children:[e.jsx("div",{className:`text-[#393939] ${l.isOpen?"flex w-full":"flex items-center justify-center"} `,children:e.jsx(w,{children:e.jsx(v,{})})}),e.jsx("div",{className:`mt-6 flex h-fit w-auto flex-1   ${l.isOpen?"justify-start":"justify-center"}`,children:e.jsx("div",{className:"w-full",children:e.jsx("ul",{className:"flex flex-col gap-[.75rem]   text-sm ",children:m.map(i=>e.jsx("li",{className:`flex h-[2.5rem] w-full  list-none items-center rounded-[.25rem]  font-inter text-[.875rem] font-[600] leading-[1.0588rem] hover:bg-[#F7F5D714] ${l.path==i.value?"bg-[#F7F5D714]":""} `,children:e.jsxs(h,{to:i.to,className:`flex w-full items-center  gap-3 whitespace-nowrap px-[.8456rem] ${l.isOpen?" w-full justify-start ":"w-[2.5rem]  justify-center"}`,children:[e.jsx("span",{children:i.icon}),l.isOpen&&e.jsx("span",{children:i.text})]})},i.value))})})}),e.jsx("div",{className:"flex justify-end pb-5",children:e.jsxs("div",{className:"relative z-[9999999] w-full space-y-3 px-2 text-[#262626]",children:[e.jsxs("button",{className:"flex w-full items-center justify-center gap-[13px] border-t border-t-white/10 pb-1 pt-4 sm:gap-6 md:gap-[13px]",onClick:()=>c.startTransition(()=>t(!n)),children:[e.jsx("img",{className:`${l.isOpen?"h-10 w-10":"h-5 w-5 xl:h-6 xl:w-6"} rounded-[50%] object-cover`,src:((a=s.profile)==null?void 0:a.photo)??"/default.png",alt:""}),l.isOpen?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"text-left text-white",children:[e.jsxs("p",{className:"w-32 truncate text-sm font-medium",children:[s.profile.first_name," ",s.profile.last_name]}),e.jsx("p",{className:"mt-1 w-[120px] truncate text-xs md:w-[150px]",children:s.profile.email})]}),e.jsx(N,{className:"ml-[-15px] min-h-5 min-w-5 text-white"})]}):null]}),n&&e.jsx("div",{className:"fixed bottom-[50px] z-[9999999] w-[160px] divide-y divide-gray-100 rounded-md border border-[#1f1d1a] bg-brown-main-bg font-bold text-[#1f1d1a] shadow-lg ring-1 ring-[#1f1d1a] ring-opacity-5 focus:outline-none",children:e.jsxs("div",{className:"px-1 py-1",children:[e.jsxs("button",{className:"group flex w-full items-center border-b border-b-transparent px-3 py-3 text-sm hover:border-b-black/20",onClick:()=>{o(`/${s==null?void 0:s.role}/profile`),t(!1)},children:[e.jsx(g,{className:"mr-2 h-5 w-5"}),"Account"]}),e.jsxs("button",{className:"group flex w-full items-center px-3 py-3 text-sm text-[#CE0000]",onClick:()=>{x({type:"LOGOUT"}),o(`/${s==null?void 0:s.role}/login`),t(!1)},children:[e.jsx(b,{className:"mr-2 h-5 w-5"}),"Logout"]})]})})]})})]}),e.jsx("div",{className:" absolute -right-3 top-[1.5rem] z-[5] cursor-pointer",children:e.jsx("div",{onClick:()=>d(!l.isOpen),children:e.jsx("span",{children:e.jsxs("svg",{width:"25",height:"25",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:`${l.isOpen?"hidden":""}`,children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"19",height:"19",rx:"9.5",fill:"#1F1D1A"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"19",height:"19",rx:"9.5",stroke:"#FFF0E5"}),e.jsx("path",{d:"M8.66602 6.66667L11.9993 10L8.66602 13.3333",stroke:"#FFF0E5","stroke-width":"1.33333","stroke-linecap":"round","stroke-linejoin":"round"})]})})})})]})};export{V as AdminHeader,V as default};
