import{j as a}from"./@nextui-org/listbox-0f38ca19.js";import{R as t,r as S,b as Q}from"./vendor-4cdf2bd1.js";import{M as W,A as X,G as Y,f as Z,T as ee,t as y}from"./index-46de5032.js";import{u as te}from"./react-hook-form-9f4fcfa9.js";import{o as se}from"./yup-c41d85d2.js";import{c as ae}from"./yup-342a5df4.js";import{P as oe}from"./index-3283c9b7.js";import{A as re}from"./AddButton-51d1b2cd.js";import{M as ie}from"./index-d0de8b06.js";import{E as ne}from"./ExportButton-eb4cf1f9.js";import"./MkdInput-a0090fba.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";let m=new W;const r=[{header:"Id",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Create At",accessor:"create_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Update At",accessor:"update_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Content",accessor:"content",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Type",accessor:"type",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Update Id",accessor:"update_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],He=()=>{const{dispatch:g}=t.useContext(X),{dispatch:P}=t.useContext(Y);t.useState("");const[x,p]=t.useState([]),[n,h]=t.useState(10),[D,C]=t.useState(0),[ce,T]=t.useState(0),[c,j]=t.useState(0),[N,v]=t.useState(!1),[A,k]=t.useState(!1),[R,d]=t.useState(!1),[L,f]=t.useState(!1),[E,le]=t.useState(!1);t.useState(!1),t.useState(!1),t.useState([]);const[w,pe]=S.useState([]),[M,me]=S.useState(""),[V,de]=S.useState("eq");Q();const F=ae({});te({resolver:se(F)});function z(s){console.log(r[s]),r[s].isSorted?r[s].isSortedDesc=!r[s].isSortedDesc:(r.map(e=>e.isSorted=!1),r.map(e=>e.isSortedDesc=!1),r[s].isSorted=!0),async function(){await l(0,n)}()}function _(s){(async function(){h(s),await l(0,s)})()}function B(){(async function(){await l(c-1>0?c-1:0,n)})()}function G(){(async function(){await l(c+1<=D?c+1:0,n)})()}async function l(s,e,O){let i=new ee;try{let o=r.filter(J=>J.isSorted);const U=await i.getPaginate("notes",{page:s,size:e,order:o.length?o[0].accessor:"",direction:o.length?o[0].isSortedDesc?"DESC":"ASC":"",filter:[...w]}),{list:q,total:H,limit:$,num_pages:b,page:u}=U;p(q),h($),C(b),j(u),T(H),v(u>1),k(u+1<=b)}catch(o){console.log("ERROR",o),y(g,o.message)}}const I=async s=>{try{m.setTable("notes"),f(!0);const e=await m.callRestAPI({id:s},"DELETE");if(!(e!=null&&e.error)){const i=x.filter(o=>Number(o.id)!==Number(s));i!=null&&i.length?p(()=>i):p(()=>[]),f(!1),d(!1)}}catch(e){throw f(!1),d(!1),y(g,e==null?void 0:e.message),new Error(e)}},K=async s=>{try{m.setTable("notes");const e=await m.exportCSV()}catch(e){throw new Error(e)}};return t.useEffect(()=>{P({type:"SETPATH",payload:{path:"wireframe"}}),function(){const e=setTimeout(async()=>{await l(1,n)},700);return()=>{clearTimeout(e)}}()},[M,w,V]),a.jsx(a.Fragment,{children:E?a.jsx(Z,{}):a.jsxs(a.Fragment,{children:[a.jsxs("div",{className:"overflow-x-auto  rounded bg-brown-main-bg p-5 ",children:[a.jsxs("div",{className:"mb-6 flex w-full justify-between text-center  ",children:[a.jsx("h4",{className:"text-[16px] font-medium md:text-xl",children:"Notes"}),a.jsxs("div",{className:"flex",children:[a.jsx(re,{link:"/admin/add-notes"}),a.jsx(ne,{onClick:K,className:"px-2 py-2  font-medium"})]})]}),a.jsx(ie,{onSort:z,columns:r,tableRole:"admin",table:"notes",actionId:"id",deleteItem:I,loading:E,deleteLoading:L,showDeleteModal:R,currentTableData:x,setShowDeleteModal:d,setCurrentTableData:p,actions:{view:!0,edit:!0,delete:!0}})]}),a.jsx(oe,{currentPage:c,pageCount:D,pageSize:n,canPreviousPage:N,canNextPage:A,updatePageSize:_,previousPage:B,nextPage:G})," "]})})};export{He as default};
