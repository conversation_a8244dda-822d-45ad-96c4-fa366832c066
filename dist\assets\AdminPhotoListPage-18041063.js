import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{R as a,b as ee}from"./vendor-4cdf2bd1.js";import{M as te,A as se,G as E,t as ae,g as b,s as re}from"./index-46de5032.js";import{o as ie}from"./yup-c41d85d2.js";import{c as oe,a as w}from"./yup-342a5df4.js";import{u as ne}from"./react-hook-form-9f4fcfa9.js";import{P as le}from"./index-3283c9b7.js";import{S as ce}from"./react-loading-skeleton-f57eafae.js";import{M as de}from"./index-d526f96e.js";import{B as pe,a as ue}from"./index.esm-54e24cf9.js";import{A as me,a as xe}from"./index.esm-25e0e799.js";import{R as he}from"./index.esm-3e7472af.js";import ge from"./AddAdminPhotoPage-948ab186.js";import{A as fe}from"./AddButton-51d1b2cd.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-icons-36ae72b7.js";import"./@uppy/xhr-upload-5e511714.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/react-eecc9c21.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/compressor-4bcbc734.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/dashboard-51133bb7.js";import"./@uppy/drag-drop-20104cbe.js";import"./@uppy/progress-bar-f89fcdd6.js";import"./@uppy/file-input-b22c76c2.js";let p=new te;const F=[{header:"Photos",accessor:"url"},{header:"Create at",accessor:"create_at"},{header:"Action",accessor:""}],je=[{header:"Date",accessor:"create_at"},{header:"ID",accessor:"id"},{header:"User ID",accessor:"user_id"},{header:"Action",accessor:""}],ut=()=>{const{dispatch:R}=a.useContext(se),{dispatch:y}=a.useContext(E);a.useState("");const[u,T]=a.useState([]),[m,v]=a.useState(3),[S,_]=a.useState(0),[be,I]=a.useState(0),[l,O]=a.useState(0),[$,q]=a.useState(!1),[B,M]=a.useState(!1),[z,N]=a.useState(!1),[L,x]=a.useState(!1),[C,G]=a.useState(!1),[P,A]=a.useState(!1),[o,h]=a.useState([]),[we,g]=a.useState([]),[ye,H]=a.useState(""),[K,U]=a.useState("eq");ee(),a.useContext(E);const V=oe({date:w(),id:w(),user_id:w()}),{register:ve,handleSubmit:J,setError:Se,formState:{errors:Ne}}=ne({resolver:ie(V)});function Q(t){(async function(){v(t),await n(0,t)})()}function W(){(async function(){await n(l-1>0?l-1:0,m)})()}function X(){(async function(){await n(l+1<=S?l+1:0,m)})()}async function n(t,r,s){try{p.setTable("photo");const i=await p.callRestAPI({payload:{...s},page:t,limit:r},"PAGINATE"),{list:c,total:f,limit:k,num_pages:d,page:j}=i;T(c),v(k),_(d),O(j),I(f),q(j>1),M(j+1<=d)}catch(i){console.log("ERROR",i),ae(R,i.message)}}const D=(t,r,s)=>{const i=r==="eq"&&isNaN(s)?`"${s}"`:s,c=`${t},${r},${i}`;g(f=>[...f.filter(d=>!d.includes(t)),c]),H(s)},Y=t=>{let r=b(t.date),s=b(t.id),i=b(t.user_id);n(0,50,{create_at:r,id:s,user_id:i})};a.useEffect(()=>{y({type:"SETPATH",payload:{path:"photos"}}),async function(){N(!0),await n(0,50),N(!1)}()},[]);async function Z(t){p.setTable("photo"),await p.callRestAPI({id:t},"DELETE"),re(y,"Deleted"),await n(0,50)}return console.log("data",u),e.jsxs("div",{className:"px-8",children:[e.jsxs("div",{className:"flex items-center justify-between py-3",children:[e.jsxs("form",{className:"relative rounded bg-brown-main-bg",onSubmit:J(Y),children:[e.jsxs("div",{className:"flex items-center gap-4 text-gray-700",children:[e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-[#0003] px-3 py-1",onClick:()=>G(!C),children:[e.jsx(pe,{}),e.jsx("span",{children:"Filters"}),o.length>0&&e.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start  text-white",children:o.length>0?o.length:null})]}),e.jsxs("div",{className:" flex cursor-pointer items-center justify-between gap-3 rounded-md border border-[#0003] px-2 py-1 focus-within:border-gray-400",children:[e.jsx(ue,{className:"text-xl text-gray-200"}),e.jsx("input",{type:"text",placeholder:"search",className:"border-none p-0 placeholder:text-left  focus:outline-none",style:{boxShadow:"0 0 transparent"},onInput:t=>{var r;return D("name","cs",(r=t.target)==null?void 0:r.value)}}),e.jsx(me,{className:"text-lg text-gray-200"})]})]}),C&&e.jsxs("div",{className:"top-fill filter-form-holder absolute left-0  z-20 mt-4 min-w-[70%] rounded-md border border-[#0003] bg-brown-main-bg p-5 shadow-xl",children:[o==null?void 0:o.map((t,r)=>e.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[e.jsx("div",{className:" mb-3  w-1/3 rounded-md border border-black/60 px-3 py-2 leading-tight text-gray-700 outline-none",children:t}),e.jsxs("select",{className:"w-[30%] appearance-none border-none outline-0",onChange:s=>{U(s.target.value)},children:[e.jsx("option",{value:"eq",selected:!0,children:"equals"}),e.jsx("option",{value:"cs",children:"contains"}),e.jsx("option",{value:"sw",children:"start with"}),e.jsx("option",{value:"ew",children:"ends with"}),e.jsx("option",{value:"lt",children:"lower than"}),e.jsx("option",{value:"le",children:"lower or equal"}),e.jsx("option",{value:"ge",children:"greater or equal"}),e.jsx("option",{value:"gt",children:"greater than"}),e.jsx("option",{value:"bt",children:"between"}),e.jsx("option",{value:"in",children:"in"}),e.jsx("option",{value:"is",children:"is null"})]}),e.jsx("input",{type:"text",placeholder:"Enter value",className:" mb-3 w-1/3 rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:s=>D(t,K,s.target.value)}),e.jsx(he,{className:" cursor-pointer text-xl",onClick:()=>{h(s=>s.filter(i=>i!==t)),g(s=>s.filter(i=>!i.includes(t)))}})]},r)),e.jsxs("div",{className:"search-buttons relative flex items-center justify-between font-semibold",children:[e.jsxs("div",{className:"mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-brown-main-bg px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out ",onClick:()=>{A(!P)},children:[e.jsx(xe,{}),"Add filter"]}),P&&e.jsx("div",{className:"absolute top-11 z-10 bg-brown-main-bg px-5 py-3 text-gray-600 shadow-md",children:e.jsx("ul",{className:"flex flex-col gap-2 text-gray-500",children:je.slice(0,-1).map(t=>e.jsx("li",{className:`${o.includes(t.header)?"cursor-not-allowed text-gray-400":"cursor-pointer"}`,onClick:()=>{o.includes(t.header)||h(r=>[...r,t.header]),A(!1)},children:t.header},t.header))})}),o.length>0&&e.jsx("div",{onClick:()=>{h([]),g([])},className:"inline-block cursor-pointer  rounded px-6  py-2.5 font-medium leading-tight text-gray-600  transition duration-150 ease-in-out",children:"Clear all filter"})]})]})]}),e.jsx(fe,{onClick:()=>x(!0)})]}),e.jsx("div",{children:e.jsx("div",{className:"overflow-x-auto  shadow ",children:e.jsxs("table",{className:"min-w-full divide-y divide-[#1f1d1a]/10",children:[e.jsx("thead",{children:e.jsx("tr",{children:F.map((t,r)=>e.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:[t.header,e.jsx("span",{children:t.isSorted?t.isSortedDesc?" ▼":" ▲":""})]},r))})}),e.jsxs("tbody",{className:"font-iowan-regular divide-y divide-[#1f1d1a]/10",children:[u.length==0?e.jsx("tr",{children:e.jsx("td",{colSpan:3,children:z&&e.jsx(ce,{count:4})})}):null,u.map((t,r)=>e.jsx("tr",{className:"  md:h-[60px]",children:F.map((s,i)=>s.accessor==""?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsxs("button",{className:"text-xs text-red-400",onClick:()=>{Z(t.id)},children:[" ","Delete"]})},i):s.mapping?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s.mapping[t[s.accessor]]},i):e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s.accessor=="url"?e.jsx("img",{width:200,height:200,src:t==null?void 0:t.url}):t[s.accessor]},i))},r))]})]})})}),e.jsx(le,{currentPage:l,pageCount:S,pageSize:m,canPreviousPage:$,canNextPage:B,updatePageSize:Q,previousPage:W,nextPage:X}),e.jsx(de,{isModalActive:L,closeModalFn:()=>x(!1),children:e.jsx(ge,{setSidebar:x})})]})};export{ut as default};
