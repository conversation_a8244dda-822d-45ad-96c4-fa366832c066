import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{u as J,R as w,r as g,j as E}from"./vendor-4cdf2bd1.js";import{u as S}from"./react-hook-form-9f4fcfa9.js";import{o as U}from"./yup-c41d85d2.js";import{c as R,a as n}from"./yup-342a5df4.js";import{M as Q,G as W,a9 as X,E as Y,I as c,t as j,s as p}from"./index-46de5032.js";import{M as y}from"./MkdInput-a0090fba.js";import{M as Z}from"./index-36531d2b.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";let i=new Q;const _e=()=>{var k,v;const F=J().pathname.includes("password"),_=R({email:n().email().required("This field is required"),first_name:n().required("This field is required"),last_name:n().required("This field is required")}).required(),D=R({password:n().required("This field is required")}).required(),{dispatch:t}=w.useContext(W),[l,d]=g.useState(!1),[q,I]=g.useState(""),[f,C]=g.useState(""),[a,L]=w.useState({}),{register:b,handleSubmit:T,setError:A,setValue:u,reset:M,formState:{errors:x}}=S({resolver:U(_)}),{register:O,handleSubmit:$,setError:G,reset:N,formState:{errors:V}}=S({resolver:U(D)}),B=(r,s)=>{let o=a;o[r]={file:s.files[0],tempURL:URL.createObjectURL(s.files[0])},L({...o})};async function P(){try{const r=await i.getProfile();u("email",r==null?void 0:r.email),u("first_name",r==null?void 0:r.first_name),u("last_name",r==null?void 0:r.last_name),I(r==null?void 0:r.email),C(r==null?void 0:r.photo)}catch(r){console.log("Error",r),j(t,r.message)}}const H=async r=>{var s,o;d(!0);try{if(a&&a.photo&&((s=a.photo)!=null&&s.file)){let h=new FormData;h.append("file",(o=a.photo)==null?void 0:o.file);let z=await i.uploadImage(h);r.photo=z.url,p(t,"Profile Photo Updated")}(await i.updateProfile({first_name:r.first_name,last_name:r.last_name,photo:r.photo||f})).error||p(t,"Profile Updated"),q!==r.email&&((await i.updateEmail(r.email)).error||p(t,"Email Updated")),await P()}catch(m){console.log("Error",m),A("email",{type:"manual",message:m.message}),j(t,m.message)}d(!1)},K=async r=>{d(!0);try{(await i.updatePassword(r.password)).error||(p(t,"Password Updated"),N())}catch(s){console.log("Error",s),G("password",{type:"manual",message:s.message}),j(t,s.message)}d(!1)};return w.useEffect(()=>{t({type:"SETPATH",payload:{path:"profile"}}),P()},[]),e.jsxs("div",{className:"mx-auto",children:[e.jsxs("ul",{className:"scrollbar-hide custom-overflow mb-6 flex w-full overflow-x-auto border-y border-b-[1px] border-[#0003] border-b-[#1f1d1a]/20 px-6 py-1 md:overflow-hidden",children:[e.jsx("li",{children:e.jsx(E,{to:"/admin/profile",end:!0,className:({isActive:r})=>`block whitespace-nowrap border-b-[.375rem] px-6 py-2 pb-3 font-iowan text-[1.25rem] font-[700] leading-[1.5537rem] text-[#1f1d1a] transition-all duration-200 ease-out ${r?"border-b-[.375rem] border-b-[#1F1D1A]":"border-b-transparent"}`,children:"Profile"})}),e.jsx("li",{children:e.jsx(E,{to:"/admin/profile/password",className:({isActive:r})=>`block whitespace-nowrap border-b-[.375rem] px-6 py-2 pb-3 font-iowan text-[1.25rem] font-[700] leading-[1.5537rem] text-[#1f1d1a] transition-all duration-200 ease-out ${r?"border-b-[.375rem] border-b-[#1F1D1A]":"border-b-transparent"}`,children:"Password"})})]}),e.jsx("div",{className:"px-8 py-6",children:F?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"mb-4 font-iowan text-[20px] font-[700] md:text-[1.5rem] md:leading-[1.865rem] ",children:"Password"}),e.jsx("p",{className:"mb-6 font-inter text-[1rem] font-[400] leading-[1.21rem]",children:"Update your password"}),e.jsxs("form",{onSubmit:$(K),children:[e.jsx("div",{className:"flex w-full flex-col items-start gap-0 md:w-[45%]",children:e.jsx("div",{className:"mb-6 w-full",children:e.jsx(Z,{name:"password",label:"New Password",errors:V,register:O,className:"mt-1 block w-full rounded-md !border !border-black shadow-sm focus:border-gray-500 focus:ring-gray-500"})})}),e.jsxs("div",{className:"flex justify-end gap-4",children:[e.jsx(c,{type:"button",className:"flex h-[2.75rem] w-fit items-center justify-center whitespace-nowrap rounded-[.0625rem] !border !border-black bg-transparent px-2 py-2 !text-[1rem] tracking-wide text-black md:px-5",color:"black",onClick:()=>N(),children:"Discard Changes"}),e.jsx(c,{className:"flex h-[2.75rem] w-fit items-center justify-center whitespace-nowrap rounded-[.0625rem] bg-[#1f1d1a] px-2 py-2 !text-[1rem] tracking-wide text-white md:px-5",loading:l,disabled:l,type:"submit",children:"Update Password"})]})]})]}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"mb-4 font-iowan text-[20px] font-[700] md:text-[1.5rem] md:leading-[1.865rem] ",children:"Personal Info"}),e.jsx("p",{className:"mb-6 font-inter text-[1rem] font-[400] leading-[1.21rem]",children:"Update your personal info"}),e.jsx("div",{className:"mb-6 flex items-center",children:e.jsxs("div",{className:"relative flex h-[7.5rem] w-[7.5rem] items-center justify-center rounded-full border border-[#1F1D1A] bg-transparent",children:[(k=a.photo)!=null&&k.tempURL||f?e.jsx("img",{src:((v=a.photo)==null?void 0:v.tempURL)||f,alt:"profile",className:"h-full min-h-full w-full min-w-full rounded-[50%] object-cover"}):e.jsx(X,{}),e.jsxs("label",{htmlFor:"photo",className:"absolute bottom-0 right-0 flex h-8 w-8 cursor-pointer items-center justify-center rounded-full border border-[#1F1D1A] bg-brown-main-bg",children:[e.jsx("input",{type:"file",id:"photo",onChange:r=>B("photo",r.target),className:"hidden"}),e.jsx(Y,{})]})]})}),e.jsxs("form",{onSubmit:T(H),children:[e.jsxs("div",{className:"flex w-full flex-col items-start gap-0 md:w-[45%]",children:[e.jsxs("div",{className:"mb-6 grid w-full grid-cols-1 gap-4 md:grid-cols-2",children:[e.jsx("div",{children:e.jsx(y,{type:"text",id:"first-name",label:"First Name",name:"first_name",errors:x,register:b,className:"mt-1 block w-full rounded-md !border !border-black shadow-sm focus:border-gray-500 focus:ring-gray-500"})}),e.jsx("div",{children:e.jsx(y,{type:"text",id:"last-name",name:"last_name",label:"Last Name",errors:x,register:b,className:"mt-1 block w-full rounded-md !border !border-black shadow-sm focus:border-gray-500 focus:ring-gray-500"})})]}),e.jsx("div",{className:"mb-6 w-full",children:e.jsx(y,{type:"email",id:"email",label:"Email",name:"email",errors:x,register:b,className:"mt-1 block w-full rounded-md !border !border-black shadow-sm focus:border-gray-500 focus:ring-gray-500"})})]}),e.jsxs("div",{className:"flex justify-end gap-4",children:[e.jsx(c,{type:"button",className:"flex h-[2.75rem] w-fit items-center justify-center whitespace-nowrap rounded-[.0625rem] !border !border-black bg-transparent px-2 py-2 !text-[1rem] tracking-wide text-black md:px-5",color:"black",onClick:()=>M(),children:"Discard Changes"}),e.jsx(c,{className:"flex h-[2.75rem] w-fit items-center justify-center whitespace-nowrap rounded-[.0625rem] bg-[#1f1d1a] px-2 py-2 !text-[1rem] tracking-wide text-white md:px-5",loading:l,disabled:l,type:"submit",children:"Save Changes"})]})]})]})})]})};export{_e as default};
