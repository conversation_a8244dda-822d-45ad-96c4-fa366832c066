import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{R as d,r as i}from"./vendor-4cdf2bd1.js";import{A as f,G as h,u as x,L as c,J as g}from"./index-46de5032.js";import{M as S}from"./index-d0de8b06.js";import"./index-8a8a991b.js";import{M as w}from"./index-d526f96e.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const j=[{header:"Row",accessor:"row"},{header:"ID",accessor:"id",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Name",accessor:"name",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Stripe ID",accessor:"stripe_id",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Object",accessor:"object",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!1},{header:"Status",accessor:"status",isSorted:!0,isSortedDesc:!1,mappingExist:!0,mappings:{0:{text:"Inactive",bg:"#F6A13C",color:"black"},1:{text:"Active",bg:"#9DD321",color:"black"}},selected_column:!0},{header:"Created At",accessor:"create_at",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Updated At",accessor:"update_at",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Action",accessor:""}],b={add:"Add a Plan"},R=()=>{const s=d.useRef(null);i.useContext(f);const{state:A,dispatch:n}=i.useContext(h),[a,m]=i.useState({modal:null,showModal:!1,selectedItems:[]}),{profile:l}=x(),o=(t,r,p=[])=>{m({modal:r?t:null,showModal:r,selectedItems:r?p:[]})},u=()=>{var t;s!=null&&s.current&&((t=s==null?void 0:s.current)==null||t.click())};return d.useEffect(()=>{n({type:"SETPATH",payload:{path:"plans"}})},[]),e.jsxs(e.Fragment,{children:[e.jsx("div",{className:" mx-auto rounded  p-5 shadow-md",children:e.jsx("div",{className:"min-h-screen ",children:l!=null&&l.id?e.jsx("div",{className:"flex h-full max-h-full min-h-full w-full items-start justify-center gap-[1.5rem] rounded-[.625rem] md:h-full md:max-h-full md:min-h-full md:flex-row",children:e.jsx(S,{showSearch:!1,useDefaultColumns:!0,defaultColumns:j,useImage:!1,hasFilter:!1,tableRole:"admin",table:"stripe_product",actionId:"id",tableTitle:"Plans",join:["user","division","campaign","warehouse"],actions:{view:{show:!1,action:null,multiple:!1},select:{show:!1,action:null,multiple:!1},delete:{show:!1,action:null,multiple:!0},orders:{show:!1,type:"static",action:()=>{},children:e.jsx(e.Fragment,{children:"View All"}),className:"!gap-2 !bg-transparent !text-black !underline !shadow-none !border-0"},add:{show:!0,action:()=>o("add",!0),multiple:!0,children:"Add"},export:{show:!1,action:null,multiple:!0}},defaultPageSize:10,showPagination:!1,refreshRef:s,maxHeight:"md:grid-rows-[auto_1fr_auto] grid-rows-[auto_25rem_auto]"})}):null})}),e.jsx(c,{children:e.jsx(w,{isModalActive:a.showModal,showHeader:!0,title:b[a.modal],closeModalFn:()=>o(null,!1),customMinWidthInTw:"md:w-[25%] w-full",children:["add"].includes(a.modal)?e.jsx(c,{children:e.jsx(g,{onSuccess:()=>{o(null,!1),u()}})}):null})})]})};export{R as default};
