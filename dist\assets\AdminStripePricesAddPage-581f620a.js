import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{R as c,r as C,b as B}from"./vendor-4cdf2bd1.js";import{u as H}from"./react-hook-form-9f4fcfa9.js";import{o as K}from"./yup-c41d85d2.js";import{c as W,a as r}from"./yup-342a5df4.js";import{M as Y,A as J,G as Q,s as m,L as X,I as Z,h as q,t as ee}from"./index-46de5032.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const _e=({onSuccess:u})=>{var h,y,g,b,f,v,j,w,N,_,k,S,T,F;let p=new Y;const{dispatch:L}=c.useContext(J),{dispatch:o,state:{addStripePrice:i}}=c.useContext(Q),[P,A]=C.useState("one_time"),[E,I]=C.useState([]);B();const R=W({product_id:r().required(),name:r().required(),amount:r().required(),type:r().required(),interval:r().when("type",{is:"recurring",then:t=>t.required(),otherwise:t=>t.notRequired()}),interval_count:r(),usage_type:r().when("type",{is:"recurring",then:t=>t.required(),otherwise:t=>t.notRequired()}),usage_limit:r(),trial_days:r()}).required(),{register:l,handleSubmit:D,setError:M,setValue:x,trigger:U,resetField:te,getValues:ae,formState:{errors:a}}=H({resolver:K(R)}),G=[{key:0,value:"",display:"Nothing Selected"},{key:1,value:"one_time",display:"One Time"},{key:2,value:"recurring",display:"Recurring"}],O=[{key:0,value:"",display:"Nothing Selected"},{key:1,value:"licenced",display:"Upfront"},{key:2,value:"metered",display:"Metered"}],V=[{key:0,value:"",display:"Nothing Selected"},{key:1,value:"day",display:"Day"},{key:2,value:"week",display:"Week"},{key:3,value:"month",display:"Month"},{key:4,value:"year",display:"Year"},{key:5,value:"lifetime",display:"Lifetime"}],z=async t=>{console.log(t);try{q(o,!0,"addStripePrice");const s=await p.addStripePrice(t);if(!s.error)m(o,"Price Added",5e3,"success"),u&&u();else if(s.validation){const $=Object.keys(s.validation);for(let n=0;n<$.length;n++){const d=$[n];console.log(d),M(d,{type:"manual",message:s.validation[d]})}}}catch(s){console.log("Error",s),m(o,s.message,5e3,"error"),ee(L,s.message)}finally{q(o,!1,"addStripePrice")}};return c.useEffect(()=>{o({type:"SETPATH",payload:{path:"pricing"}}),(async()=>{const{list:t,error:s}=await p.getStripeProducts({limit:"all"});if(s){m(o,"Something went wrong while fetching products list",5e3,"error");return}I(t)})()},[]),e.jsx("div",{className:" mx-auto h-full max-h-full  min-h-full rounded bg-brown-main-bg  p-5 shadow-md",children:e.jsxs("form",{className:"grid h-full max-h-full min-h-full w-full max-w-lg grid-cols-1 grid-rows-[1fr_auto]",onSubmit:D(z),children:[e.jsxs("div",{className:"flex h-full max-h-full min-h-full flex-col gap-4 overflow-y-auto",children:[e.jsxs("div",{className:"mb-4 ",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"product_id",children:"Product"}),e.jsxs("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...l("product_id"),children:[e.jsx("option",{value:"",children:"Nothing selected"},"prod_default"),E.map((t,s)=>e.jsx("option",{value:t.id,children:t.name},`prod_${t.id}`))]}),e.jsx("p",{className:"text-xs italic text-red-500",children:(h=a.product_id)==null?void 0:h.message})]}),e.jsxs("div",{className:"mb-4 ",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"name",children:"Name"}),e.jsx("input",{type:"text",placeholder:"Name",...l("name"),className:`"shadow focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 focus:outline-none ${(y=a.name)!=null&&y.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(g=a.name)==null?void 0:g.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"amount",children:"Amount"}),e.jsx("input",{type:"number",min:.1,step:"any",placeholder:"Amount",...l("amount"),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(b=a.amount)!=null&&b.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(f=a.amount)==null?void 0:f.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"type",children:"Type"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...l("type"),onChange:t=>{const s=t.target.value;A(s),x("type",s),U("type")},children:G.map(t=>e.jsx("option",{value:t.value.toLowerCase(),children:t.display},`interval_${t.key}`))}),e.jsx("p",{className:"text-xs italic text-red-500",children:(v=a.type)==null?void 0:v.message})]}),P==="recurring"?e.jsxs("div",{className:"ml-6",children:[e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"interval",children:"Interval"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...l("interval"),placeholder:"Select",children:V.map(t=>e.jsx("option",{value:t.value.toLowerCase(),children:t.display},`interval_${t.key}`))}),e.jsx("p",{className:"text-xs italic text-red-500",children:(j=a.interval)==null?void 0:j.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"interval_count",children:"Interval Count"}),e.jsx("input",{type:"number",step:"1",placeholder:"Interval Count",...l("interval_count"),...x("interval_count",1),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(w=a.interval_count)!=null&&w.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(N=a.interval_count)==null?void 0:N.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"usage_type",children:"Usage Type"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...l("usage_type"),placeholder:"Select",children:O.map(t=>e.jsx("option",{value:t.value.toLowerCase(),children:t.display},`interval_${t.key}`))}),e.jsx("p",{className:"text-xs italic text-red-500",children:(_=a.usage_type)==null?void 0:_.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"trial_days",children:"Trial Days"}),e.jsx("input",{type:"number",step:"1",placeholder:"0",...l("trial_days"),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(k=a.trial_days)!=null&&k.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(S=a.trial_days)==null?void 0:S.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"trial_days",children:"Usage Limit"}),e.jsx("input",{type:"number",step:"1",placeholder:"1000",...l("usage_limit"),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(T=a.usage_limit)!=null&&T.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(F=a.usage_limit)==null?void 0:F.message})]})]}):""]}),e.jsx(X,{children:e.jsx(Z,{type:"submit",color:"white",loading:i==null?void 0:i.loading,disabled:i==null?void 0:i.loading,className:" !text-white",children:"Submit"})})]})})};export{_e as default};
