import{j as s}from"./@nextui-org/listbox-0f38ca19.js";import{R as m,r as u,b as S}from"./vendor-4cdf2bd1.js";import{a as _,u as b,L as a,d as j,e as M}from"./index-46de5032.js";import{M as P}from"./index-d0de8b06.js";import{M as v}from"./index-d526f96e.js";import{A}from"./index-a807e4ab.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const y=[{header:"Row",accessor:"row"},{header:"Stripe Id",accessor:"stripe_id",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Product",accessor:"name",isSorted:!0,join:"stripe_product",isSortedDesc:!1,mappingExist:!1,mappings:{},filter_field:"product_id",selected_column:!0},{header:"Nickname",accessor:"name",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Type",accessor:"type",isSorted:!0,isSortedDesc:!1,mappingExist:!0,selected_column:!0,mappings:{one_time:{text:"One Time",bg:"#9DD321",color:"black"},recurring:{text:"Recurring",bg:"#9DD321",color:"black"},lifetime:{text:"Lifetime",bg:"#9DD321",color:"black"}}},{header:"Price",accessor:"amount",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Trial",accessor:"trial_days",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Status",accessor:"status",isSorted:!0,isSortedDesc:!1,mappingExist:!0,selected_column:!0,mappings:{0:{text:"Inactive",bg:"#F6A13C",color:"black"},1:{text:"Active",bg:"#9DD321",color:"black"}}},{header:"Action",accessor:""}],D={add:"Add a Price"},Y=({onSuccess:T=null})=>{const o=m.useRef(null),{globalDispatch:p,deleteOne:f,showToast:h,setLoading:n,RequestItems:i}=_(),[e,x]=u.useState({modal:null,showModal:!1,selectedItems:[]});S();const{profile:c}=b(),l=(t,r,w=[])=>{x({showModal:r,[t]:r??null,modal:r?t:null,selectedItems:r?w:[]})},g=async(t=null)=>{try{n(i==null?void 0:i.deleteModel,!0);const r=await f("stripe_price",t);r!=null&&r.error||(h("Price Deleted",5e3,"success"),d())}catch{}finally{l("remove",!1),n(i==null?void 0:i.deleteModel,!1)}},d=()=>{var t;o!=null&&o.current&&((t=o==null?void 0:o.current)==null||t.click())};return m.useEffect(()=>{p({type:"SETPATH",payload:{path:"pricing"}})},[]),s.jsxs(u.Fragment,{children:[s.jsx("div",{className:" mx-auto rounded  p-5 shadow-md",children:s.jsx("div",{className:"min-h-screen ",children:c!=null&&c.id?s.jsx("div",{className:"flex h-full max-h-full min-h-full w-full items-start justify-center gap-[1.5rem] rounded-[.625rem] md:h-full md:max-h-full md:min-h-full md:flex-row",children:s.jsx(P,{showSearch:!1,useDefaultColumns:!0,defaultColumns:y,useImage:!1,hasFilter:!1,tableRole:"admin",table:"stripe_price",actionId:"id",tableTitle:"Prices",join:["stripe_product|product_id"],actions:{view:{show:!1,action:null,multiple:!1},select:{show:!1,action:null,multiple:!1},remove:{show:!0,action:t=>l("remove",!0,t),multiple:!0,icon:s.jsx(a,{children:s.jsx(j,{})}),locations:["buttons"]},orders:{show:!1,type:"static",action:()=>{},children:s.jsx(s.Fragment,{children:"View All"}),className:"!gap-2 !bg-transparent !text-black !underline !shadow-none !border-0"},add:{show:!0,action:()=>l("add",!0),multiple:!0,children:"Add"},export:{show:!1,action:null,multiple:!0}},defaultPageSize:10,actionPostion:["buttons"],showPagination:!1,refreshRef:o,maxHeight:"md:grid-rows-[auto_1fr_auto] grid-rows-[auto_25rem_auto]"})}):null})}),s.jsx(a,{children:s.jsx(v,{isModalActive:(e==null?void 0:e.showModal)&&["add"].includes(e==null?void 0:e.modal),showHeader:!0,title:D[e.modal],closeModalFn:()=>l(null,!1),customMinWidthInTw:"md:w-[25%] w-full",children:["add"].includes(e.modal)?s.jsx(a,{children:s.jsx(M,{onSuccess:()=>{l(null,!1),d()}})}):null})}),s.jsx(a,{children:s.jsx(A,{title:"Delete Price",mode:"manual",action:"Delete",multiple:!1,onSuccess:()=>{var t;return g((t=e==null?void 0:e.selectedItems)==null?void 0:t[0])},inputConfirmation:!1,onClose:()=>l("remove",!1),customMessage:s.jsx(s.Fragment,{children:"Are you sure you want to delete this price?"}),isOpen:(e==null?void 0:e.showModal)&&["remove"].includes(e==null?void 0:e.modal)})})]})};export{Y as default};
