import{j as s}from"./@nextui-org/listbox-0f38ca19.js";import{R as a,i as n}from"./vendor-4cdf2bd1.js";import"./yup-342a5df4.js";import{M as o,G as r,t as p,f as h}from"./index-46de5032.js";import"./MkdInput-a0090fba.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";let l=new o;const z=()=>{a.useContext(r);const{dispatch:c}=a.useContext(r),[e,d]=a.useState({}),[x,i]=a.useState(!0),m=n();return a.useEffect(function(){(async function(){try{i(!0),l.setTable("notes");const t=await l.callRestAPI({id:Number(m==null?void 0:m.id),join:""},"GET");t.error||(d(t.model),i(!1))}catch(t){i(!1),console.log("error",t),p(c,t.message)}})()},[]),s.jsx("div",{className:" mx-auto rounded  p-5 shadow-md",children:x?s.jsx(h,{}):s.jsxs(s.Fragment,{children:[s.jsx("h4",{className:"text-[16px] font-medium md:text-xl",children:"View Notes"}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Create At"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.create_at})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Update At"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.update_at})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Content"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.content})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Type"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.type})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Status"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.status})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Update Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.update_id})]})})]})})};export{z as default};
