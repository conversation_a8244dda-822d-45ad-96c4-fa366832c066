import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{G as f,A as x,M as h,x as g}from"./index-46de5032.js";import{u as b}from"./useLocalStorage-53cfe2d8.js";import{h as y,r as a,b as j,L as k}from"./vendor-4cdf2bd1.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";function K(){const[l]=y(),[r,i]=a.useState(!0),[o,n]=a.useState(""),{dispatch:p}=a.useContext(f),{dispatch:c}=a.useContext(x),d=j(),{setLocalStorage:s}=b([]);async function u(){i(!0);try{const t=await new h().callRawAPI(`/v2/api/lambda/verify-email?token=${l.get("token")}`);n(""),c({type:"LOGIN",payload:t}),s("token",t==null?void 0:t.token),s("user",t==null?void 0:t.user_id),s("role",t==null?void 0:t.role),s("step",2),await g(p,c,"user",t==null?void 0:t.user_id,{step:2},!1),d("/collaborator/get-started",{state:{first_login:!0}})}catch(m){n(m.message)}i(!1)}return a.useEffect(()=>{u()},[]),e.jsxs("div",{className:"flex min-h-screen w-full justify-center",children:[e.jsxs("div",{className:"mx-auto mt-32 max-w-lg text-center",children:[" ",e.jsx("h1",{className:"mb-3 text-3xl font-semibold",children:r?"Verifying...":o?e.jsx("p",{className:"text-red-500 empty:hidden",children:o}):"Account verification complete!"})," ",e.jsxs("p",{children:[r||o?"":"Your account has been verified, you can now "," ",e.jsxs(k,{to:"/collaborator/login",className:`font-semibold text-primary-black underline ${r||o?"hidden":""}`,children:[" ","Login"]})]})," "]})," "]})}export{K as default};
