import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{A as J,G as Y,u as Z,L as B,$ as ee,I as se,p as ae,a0 as te,M as S,s as u,t as _}from"./index-46de5032.js";import{u as oe,r as o,i as le,b as re}from"./vendor-4cdf2bd1.js";import{u as ne}from"./useUpdateCollaborators-238d9e50.js";import"./yup-342a5df4.js";import"./InteractiveButton-060359e0.js";import{M as ie}from"./index-af54e68d.js";import{u as de}from"./useCompanyMember-f698e8a0.js";import{M as ce}from"./index-dc002f62.js";import{P as me}from"./PlusIcon-26cedb5d.js";import{T as ue}from"./TrashIcon-e6ce5aef.js";import{X as fe}from"./XMarkIcon-cfb26fe7.js";import{C as xe}from"./ChevronUpDownIcon-e0f342e0.js";import{L as A,t as h,S as F,W as p}from"./@headlessui/react-cdd9213e.js";function Te({note_id:g,isOwner:b=!1}){var $,U,X;const H=oe(),[K,i]=o.useState(!1),{dispatch:C}=o.useContext(J),{dispatch:c}=o.useContext(Y),[k,v]=o.useState(!1),[I,y]=o.useState(!1),[r,M]=o.useState(""),[d,f]=o.useState({}),[W,j]=o.useState(!1),[P,R]=o.useState(!1),[L,z]=o.useState(null),{id:w}=le(),{profile:l}=Z({isPublic:!1}),{updateCollaborators:N,refetch:D}=ne(w,g),{companyMember:{myMembers:E},getMyCompanyMembers:O}=de({filter:[`goodbadugly_company_member.company_id,eq,${(U=($=l==null?void 0:l.companies)==null?void 0:$[0])==null?void 0:U.id}`]});re();async function q(s){var a;v(!0);try{await new S().callRawAPI("/v3/api/custom/goodbadugly/add-collaborator",{update_id:w,collaborator_id:(a=d==null?void 0:d.user)==null?void 0:a.id,note_id:g},"POST"),f({}),M(""),D(),i(!1),u(c,"Successful")}catch(t){_(C,t.message),t.message!=="TOKEN_EXPIRED"&&u(c,t.message,5e3,"error")}v(!1)}async function G(s){v(!0);try{await new S().callRawAPI("/v3/api/custom/goodbadugly/remove-collaborator",{update_id:w,collaborator_id:s.user.id,note_id:g},"DELETE"),f({}),M(""),D(),i(!1),u(c,"Collaborator removed successfully")}catch(a){_(C,a.message),a.message!=="TOKEN_EXPIRED"&&u(c,a.message,5e3,"error")}v(!1)}async function V(s){R(!0),z(s.user.id);try{await new S().callRawAPI("/v3/api/custom/goodbadugly/add-collaborator",{update_id:w,collaborator_id:s.user.id,note_id:g},"POST"),u(c,"Invite resent successfully")}catch(a){_(C,a.message),a.message!=="TOKEN_EXPIRED"&&u(c,a.message,5e3,"error")}R(!1),z(null)}const Q=()=>{H.pathname==="/member/get-started"?(console.log("hi"),y(!0)):(i(!1),y(!0))},m=r===""?E.filter(s=>!N.some(a=>{var t;return(a==null?void 0:a.collaborator_id)==((t=s==null?void 0:s.user)==null?void 0:t.id)})):E.filter(s=>!N.some(a=>{var t;return(a==null?void 0:a.collaborator_id)==((t=s==null?void 0:s.user)==null?void 0:t.id)})).filter(s=>{var a,t,n,x;return(x=(t=(a=s==null?void 0:s.user)==null?void 0:a.email)==null?void 0:t.toLowerCase().replace(/\s+/g,""))==null?void 0:x.includes((n=r==null?void 0:r.toLowerCase())==null?void 0:n.replace(/\s+/g,""))});return o.useEffect(()=>{if(r==""){f({});return}const s=E.find(a=>{var t;return((t=a==null?void 0:a.user)==null?void 0:t.email)==r});s&&f(s)},[r,I]),o.useEffect(()=>{var s,a;l!=null&&l.id&&O({filter:[`goodbadugly_company_member.company_id,eq,${(a=(s=l==null?void 0:l.companies)==null?void 0:s[0])==null?void 0:a.id}`]})},[l==null?void 0:l.id]),e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"mt-6 text-end",children:e.jsxs("div",{className:"relative flex flex-col items-center gap-2 font-inter",children:[e.jsx("span",{className:"absolute top-0",children:e.jsx(B,{children:e.jsx(ie,{display:e.jsx("button",{className:"section-collaborators h-0 w-full p-0"}),openOnClick:!0,backgroundColor:"#1f1d1a",place:"top",children:e.jsxs("span",{className:"flex items-center gap-2 text-white",children:["Add collaborator"," ",e.jsx(ee,{className:"h-4 w-4 rotate-180",stroke:"white"})]})})})}),e.jsx("button",{disabled:!b,onClick:()=>i(!0),children:N.length==0&&b?e.jsx("span",{className:"font-inter text-base font-semibold underline underline-offset-2",children:"+ Add collaborator"}):e.jsxs("div",{className:"flex items-center gap-4",children:[b?e.jsx(me,{className:"h-4",strokeWidth:2}):null,e.jsx("div",{className:"flex -space-x-1",children:N.map(s=>e.jsx(A,{className:"relative",children:({open:a})=>{var t,n,x;return e.jsxs(e.Fragment,{children:[e.jsx(A.Button,{as:"div",className:"cursor-pointer",onMouseEnter:()=>j(s.user.id),onMouseLeave:()=>j(!1),children:e.jsx("img",{className:"h-7 min-h-7 w-7 min-w-7 rounded-[50%] object-cover",src:((t=s.user)==null?void 0:t.photo)||"/default.png"})}),e.jsx(h,{as:o.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",show:s.user.id===W,onMouseEnter:()=>j(s.user.id),onMouseLeave:()=>j(!1),children:e.jsx(A.Panel,{className:"absolute left-0 z-10 mt-3 w-fit -translate-x-[20%] transform whitespace-nowrap bg-brown-main-bg px-4 sm:-translate-x-full",children:e.jsx("div",{className:"overflow-hidden rounded-lg bg-[#1f1d1a] p-4 px-4 text-white shadow-lg ring-1 ring-[#1f1d1a]/5",children:e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsxs("div",{className:"flex flex-row items-center gap-2 text-sm font-medium",children:[(n=s.user)==null?void 0:n.first_name," ",(x=s.user)==null?void 0:x.last_name]}),b&&e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:T=>{T.stopPropagation(),V(s)},disabled:P&&L===s.user.id,className:"flex items-center gap-2 rounded border border-white px-2 py-1 text-xs hover:bg-white/10",children:P&&L===s.user.id?e.jsx(ce,{loading:!0,color:"#ffffff",size:12}):e.jsxs(e.Fragment,{children:[e.jsx("svg",{className:"h-3 w-3",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z",clipRule:"evenodd"})}),"Resend Invite"]})}),e.jsxs("button",{onClick:T=>{T.stopPropagation(),G(s)},disabled:k,className:"flex items-center gap-2 rounded border border-red-400 px-2 py-1 text-xs text-red-400 hover:bg-red-400/10",children:[e.jsx(ue,{className:"h-3 w-3"}),"Remove"]})]})]})})})})]})}},s.id))})]})})]})}),e.jsx(h,{appear:!0,show:K,as:o.Fragment,children:e.jsxs(F,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>i(!1),children:[e.jsx(h.Child,{as:o.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(h.Child,{as:o.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(F.Panel,{className:"h-auto w-full max-w-md transform rounded-md bg-brown-main-bg p-5 text-left text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(F.Title,{as:"h3",className:"text-xl font-semibold leading-6 text-gray-900",children:"Invite a collaborator"}),e.jsx("button",{onClick:()=>i(!1),type:"button",children:e.jsx(fe,{className:"h-6 w-6"})})]}),e.jsx("p",{className:"font-iowan-regular my-4",children:"Type their email to add them to this update only"}),e.jsx(p,{value:d,onChange:f,children:e.jsxs("div",{className:"relative mt-6",children:[e.jsxs(p.Button,{className:"relative w-full cursor-default rounded-md text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 sm:text-sm",children:[e.jsx(p.Input,{className:"focus:shadow-outline w-full appearance-none rounded border  bg-brown-main-bg px-3 py-2 leading-tight text-[#1f1d1a] shadow focus:outline-none ",placeholder:"Type to search",displayValue:s=>{var a;return((a=s==null?void 0:s.user)==null?void 0:a.email)??""},onChange:s=>M(s.target.value)}),e.jsx("div",{className:"absolute inset-y-0 right-0 flex items-center pr-2",children:e.jsx(xe,{className:"h-5 w-5 text-gray-400","aria-hidden":"true"})})]}),e.jsx(h,{as:o.Fragment,leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx(p.Options,{className:"absolute z-[99999] mt-1 max-h-60 w-full overflow-auto rounded-md bg-brown-main-bg py-1 text-base shadow-lg  focus:outline-[#1f1d1a] sm:text-sm",children:(m==null?void 0:m.length)===0&&r!==""?e.jsx("div",{className:"relative cursor-default select-none px-4 py-2 text-gray-700",children:"Nothing found."}):m==null?void 0:m.map(s=>e.jsx(p.Option,{className:({active:a})=>`relative cursor-default select-none py-2 pl-10 pr-4 ${a?"bg-[#1f1d1a] text-white":"text-gray-900"}`,value:s,children:({selected:a,active:t})=>{var n;return e.jsxs(e.Fragment,{children:[e.jsx("span",{className:`block truncate ${a?"font-medium":"font-normal"}`,children:(n=s==null?void 0:s.user)==null?void 0:n.email}),a?e.jsx("span",{className:`absolute inset-y-0 left-0 flex items-center pl-3 ${t?"text-white":"text-teal-600"}`}):null]})}},s.id))})})]})}),e.jsxs("div",{className:"mt-6 flex items-center justify-between",children:[e.jsx("button",{className:"text-base font-semibold text-[#1f1d1a] underline",onClick:Q,children:"New collaborator?"}),e.jsx(se,{loading:k,disabled:k||!((X=d==null?void 0:d.user)!=null&&X.id),onClick:q,className:"rounded-sm bg-[#1f1d1a] px-4 py-2 text-center font-semibold !text-white transition-colors duration-100  disabled:bg-disabled-black",children:"Add"})]})]})})})})]})}),e.jsx(ae,{modalHeader:!0,title:"Add New Collaborator",isOpen:I,modalCloseClick:()=>y(!1),classes:{modalDialog:"h-fit max-h-fit min-h-fit z-[51] md:!w-fit !w-full",modalContent:"!bg-brown-main-bg !z-[51] !mt-0 overflow-hidden !py-0",modal:"h-full z-[51]"},children:I?e.jsx(B,{children:e.jsx(te,{onSuccess:()=>{var s,a;O({filter:[`goodbadugly_company_member.company_id,eq,${(a=(s=l==null?void 0:l.companies)==null?void 0:s[0])==null?void 0:a.id}`]}),y(!1),i(!0)}})}):null})]})}export{Te as C};
