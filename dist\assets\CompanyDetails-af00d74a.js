import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{b as L,a,u,L as o,bx as g,by as b,bz as v,bA as p,as as F,bB as H,am as N,an as V,bC as k,bD as M,ap as Z}from"./index-46de5032.js";import{r as j,R as w}from"./vendor-4cdf2bd1.js";import{S as _}from"./index-a613c3fd.js";import{S as E}from"./ShareButton-62ae2097.js";import{c as I,d as S}from"./lucide-react-0b94883e.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./InteractiveButton-060359e0.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";import"./yup-c41d85d2.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-hook-form-9f4fcfa9.js";import"./index.esm-3e7472af.js";import"./react-icons-36ae72b7.js";import"./yup-342a5df4.js";const B=({children:r,text:s})=>{const[l,C]=j.useState(!1);return e.jsxs("div",{className:"relative inline-block",children:[e.jsx("div",{onMouseEnter:()=>C(!0),onMouseLeave:()=>C(!1),children:r}),l&&e.jsx("div",{className:"absolute left-1/2 z-10 -translate-x-1/2 transform",children:e.jsx("div",{className:"mt-2 whitespace-nowrap rounded-sm bg-[#1f1d1a] px-3 py-2 text-sm text-white shadow-lg",children:s})})]})},q={linkedin:e.jsx("div",{className:"flex min-h-[36px] min-w-[36px] items-center justify-center rounded-[50%] bg-black",children:e.jsxs("svg",{width:"22",height:"20",viewBox:"0 0 22 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M1.5858 3.96546C1.11157 3.52529 0.875732 2.98043 0.875732 2.33214C0.875732 1.68386 1.11283 1.11503 1.5858 0.673592C2.06004 0.233414 2.67049 0.0126953 3.41841 0.0126953C4.16633 0.0126953 4.75283 0.233414 5.2258 0.673592C5.70003 1.11377 5.93587 1.66746 5.93587 2.33214C5.93587 2.99683 5.69877 3.52529 5.2258 3.96546C4.75157 4.40564 4.14994 4.62636 3.41841 4.62636C2.68688 4.62636 2.06004 4.40564 1.5858 3.96546ZM5.53732 6.4905V19.9859H1.27302V6.4905H5.53732Z",fill:"#FFF0E5"}),e.jsx("path",{d:"M19.7332 7.82405C20.6627 8.83306 21.1269 10.2179 21.1269 11.9812V19.748H17.077V12.5285C17.077 11.6394 16.8462 10.9482 16.3858 10.4563C15.9255 9.9644 15.3049 9.7172 14.528 9.7172C13.7511 9.7172 13.1305 9.96314 12.6701 10.4563C12.2098 10.9482 11.979 11.6394 11.979 12.5285V19.748H7.90515V6.45307H11.979V8.2163C12.3914 7.62856 12.9476 7.16442 13.6464 6.82262C14.3451 6.48082 15.1309 6.31055 16.0049 6.31055C17.5613 6.31055 18.8049 6.81505 19.7332 7.82279V7.82405Z",fill:"#FFF0E5"})]})}),github:e.jsx(g,{}),x:e.jsx(b,{}),medium:e.jsx(v,{}),facebook:e.jsx("div",{className:"flex min-h-[36px] min-w-[36px] items-center justify-center rounded-[50%] bg-black",children:e.jsxs("svg",{width:"36",height:"36",viewBox:"0 0 36 36",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M35.9975 18.0006C35.9975 27.0917 29.2588 34.6076 20.5048 35.8272C19.6862 35.9407 18.8488 36 17.9987 36C17.0175 36 16.054 35.9218 15.1156 35.7705C6.54568 34.3906 0 26.9593 0 18.0006C0 8.05941 8.05914 0 18 0C27.9409 0 36 8.05941 36 18.0006H35.9975Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M20.5052 14.2672V18.1884H25.3559L24.5878 23.4706H20.5052V35.6404C19.6867 35.7539 18.8493 35.8132 17.9992 35.8132C17.018 35.8132 16.0544 35.735 15.1161 35.5836V23.4706H10.6426V18.1884H15.1161V13.3906C15.1161 10.414 17.5288 8 20.5065 8V8.00252C20.5153 8.00252 20.5229 8 20.5317 8H25.3571V12.5683H22.2041C21.267 12.5683 20.5065 13.3288 20.5065 14.2659L20.5052 14.2672Z",fill:"#FFF0E5"})]})}),youtube:e.jsx(p,{}),instagram:e.jsx("div",{className:"flex min-h-[36px] min-w-[36px] items-center justify-center rounded-[50%] bg-black",children:e.jsx(F,{className:"object-cover",pathFill:"#fff0e5"})}),reddit:e.jsx(H,{}),angel_list:e.jsx("div",{className:"flex min-h-[36px] min-w-[36px] items-center justify-center rounded-[50%] bg-black",children:e.jsxs("svg",{width:"17",height:"24",viewBox:"0 0 17 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("g",{"clip-path":"url(#clip0_7283_21123)",children:e.jsx("path",{d:"M12.824 9.9574C13.56 7.95806 14.134 6.3074 14.546 5.0054C14.808 4.3194 15.023 3.5114 15.155 2.6764L15.164 2.6064L15.166 2.5344C15.166 2.2844 15.088 2.0524 14.955 1.8614L14.958 1.8654C14.8944 1.7829 14.8128 1.71611 14.7193 1.67016C14.6259 1.62422 14.5231 1.60035 14.419 1.6004L14.388 1.6014H14.389C14.0816 1.6014 13.7686 1.85673 13.45 2.3674C13.048 3.0704 12.69 3.8864 12.42 4.7404L12.395 4.8304L10.748 9.5924L12.824 9.9574ZM10.633 14.3894C10.1615 14.3703 9.69202 14.3178 9.22798 14.2324L9.28998 14.2414C8.86011 14.1656 8.43828 14.0498 8.02998 13.8954L8.07898 13.9114C8.25432 14.2634 8.41065 14.6151 8.54798 14.9664C8.66098 15.2404 8.77598 15.5874 8.87098 15.9414L8.88598 16.0054C9.14898 15.6774 9.42098 15.3844 9.71298 15.1124L9.71798 15.1074C9.99265 14.8507 10.289 14.6161 10.607 14.4034L10.632 14.3874L10.633 14.3894ZM8.77198 9.3974L6.99298 4.2564C6.70607 3.34325 6.33411 2.45904 5.88198 1.6154L5.92198 1.6974C5.66932 1.29673 5.39465 1.0964 5.09798 1.0964H5.07998C4.9758 1.09655 4.87301 1.12029 4.77931 1.16584C4.68562 1.21139 4.60345 1.27756 4.53898 1.3594L4.53798 1.3604C4.38486 1.56466 4.30904 1.81655 4.32398 2.0714V2.0684C4.41198 3.0404 4.61598 3.9364 4.92398 4.7814L4.89998 4.7074C5.28465 5.9594 5.86132 7.60173 6.62998 9.6344C6.68938 9.52001 6.78706 9.43011 6.90598 9.3804L6.90998 9.3794C7.06926 9.32264 7.23808 9.29751 7.40698 9.3054H7.40398C7.46998 9.3054 7.60198 9.31106 7.79998 9.3224C7.99798 9.33373 8.32198 9.3604 8.77198 9.4024V9.3974ZM7.15698 17.3554C7.2553 17.3525 7.35182 17.3282 7.43987 17.2844C7.52792 17.2405 7.6054 17.1781 7.66698 17.1014L7.66798 17.1004C7.81288 16.9533 7.8953 16.7559 7.89798 16.5494C7.81135 16.0326 7.65387 15.5292 7.42998 15.0554L7.44498 15.0904C7.09498 14.2194 6.71698 13.4804 6.28098 12.7794L6.31698 12.8414C6.05735 12.3814 5.7304 11.9627 5.34698 11.5994L5.34498 11.5974C5.10614 11.3519 4.78558 11.2025 4.44398 11.1774H4.43898C4.13691 11.2152 3.86216 11.3713 3.67498 11.6114L3.67298 11.6144C3.43446 11.821 3.28445 12.1113 3.25398 12.4254V12.4304C3.30898 12.8754 3.45498 13.2774 3.67298 13.6294L3.66598 13.6164C4.01098 14.2554 4.37798 14.8054 4.78798 15.3184L4.77098 15.2964C5.16711 15.8481 5.6225 16.3548 6.12898 16.8074L6.13798 16.8154C6.40398 17.0914 6.75398 17.2854 7.14698 17.3544L7.15798 17.3564L7.15698 17.3554ZM2.21398 16.9604C2.36732 17.1477 2.58065 17.4224 2.85398 17.7844C3.57932 18.7837 4.24932 19.2834 4.86398 19.2834H4.87098C5.07351 19.2837 5.26978 19.2133 5.42598 19.0844L5.42498 19.0854C5.49439 19.0401 5.55265 18.9797 5.59542 18.9087C5.63819 18.8377 5.66437 18.7579 5.67198 18.6754V18.6734C5.62326 18.3752 5.5092 18.0914 5.33798 17.8424L5.34198 17.8494C5.04798 17.3544 4.74898 16.9294 4.42098 16.5284L4.43598 16.5474C4.10639 16.1106 3.7415 15.7015 3.34498 15.3244L3.34098 15.3204C3.15981 15.1208 2.91809 14.9862 2.65298 14.9374L2.64498 14.9364C2.19998 14.9634 1.81498 15.2014 1.58498 15.5494L1.58198 15.5544C1.25547 15.9703 1.08367 16.4868 1.09598 17.0154V17.0134C1.11198 17.5624 1.23498 18.0774 1.44398 18.5464L1.43398 18.5204C1.70498 19.1594 2.03398 19.7094 2.42598 20.2084L2.41398 20.1934C3.03774 21.033 3.85212 21.7123 4.78998 22.1754L4.82798 22.1924C5.79446 22.657 6.85363 22.8967 7.92598 22.8934L8.02098 22.8924H8.01598L8.13798 22.8934C9.13343 22.8945 10.1175 22.6815 11.0233 22.2688C11.9292 21.8561 12.7356 21.2533 13.388 20.5014L13.395 20.4934C14.8665 18.8412 15.6428 16.6844 15.562 14.4734V14.4874L15.564 14.3014C15.5626 13.7447 15.504 13.2054 15.388 12.6834L15.397 12.7334C15.3251 12.3671 15.1346 12.0347 14.855 11.7874L14.853 11.7854C14.0695 11.2751 13.188 10.9345 12.265 10.7854L12.226 10.7804C10.9102 10.4805 9.56453 10.3312 8.21498 10.3354H8.14398H8.14798C7.85175 10.3085 7.55479 10.3787 7.30198 10.5354L7.30798 10.5324C7.21769 10.6192 7.14848 10.7255 7.10567 10.8432C7.06285 10.9609 7.04756 11.0869 7.06098 11.2114V11.2074C7.06458 11.5752 7.18738 11.932 7.41097 12.2241C7.63455 12.5162 7.94685 12.7279 8.30098 12.8274L8.31298 12.8304C9.34698 13.1594 10.536 13.3484 11.77 13.3484C11.98 13.3484 12.1886 13.3431 12.396 13.3324L12.367 13.3334H13.056C13.2 13.3334 13.329 13.4004 13.412 13.5054L13.413 13.5064C13.517 13.6524 13.585 13.8294 13.602 14.0214V14.0254C13.272 14.2774 12.89 14.4814 12.476 14.6184L12.448 14.6264C11.938 14.8114 11.497 15.0364 11.091 15.3084L11.113 15.2944C10.4823 15.7517 9.94662 16.3274 9.53598 16.9894L9.52098 17.0144C9.15133 17.5738 8.94335 18.2243 8.91998 18.8944V18.9014C8.93698 19.3764 9.02898 19.8234 9.18498 20.2404L9.17498 20.2114C9.28398 20.4834 9.37298 20.8024 9.42698 21.1324L9.43098 21.1584V21.2734L9.39798 21.4214C9.1502 21.3958 8.91374 21.3045 8.71299 21.1571C8.51224 21.0096 8.35448 20.8112 8.25598 20.5824L8.25298 20.5734C7.93949 19.9043 7.79559 19.1683 7.83398 18.4304V18.4404V18.2924C7.76053 18.3598 7.67638 18.4146 7.58498 18.4544L7.57898 18.4564C7.49504 18.49 7.40539 18.5069 7.31498 18.5064H7.29998C7.20332 18.5064 7.10898 18.4977 7.01698 18.4804L7.02598 18.4814C6.91894 18.4605 6.81312 18.4338 6.70898 18.4014L6.72898 18.4074C6.75698 18.5034 6.78298 18.6234 6.80098 18.7454L6.80298 18.7614C6.81598 18.8414 6.82498 18.9354 6.82798 19.0304V19.0384C6.82798 19.4544 6.64298 19.8264 6.34998 20.0774L6.34798 20.0794C6.05798 20.3504 5.66698 20.5164 5.23798 20.5164H5.20998C4.40346 20.4641 3.64945 20.1018 3.10398 19.5054L3.10198 19.5034C2.79828 19.2647 2.54666 18.9663 2.36264 18.6266C2.17862 18.2869 2.06609 17.9132 2.03198 17.5284L2.03098 17.5174V17.5034C2.03098 17.3941 2.04565 17.2894 2.07498 17.1894L2.07298 17.1974C2.09834 17.1067 2.14654 17.0241 2.21298 16.9574L2.21398 16.9604ZM14.012 10.2374C14.4276 10.2859 14.8283 10.4218 15.1878 10.636C15.5473 10.8502 15.8575 11.1379 16.098 11.4804L16.104 11.4894C16.5832 12.443 16.7934 13.5092 16.712 14.5734L16.713 14.5544C16.7631 15.7973 16.5678 17.038 16.1384 18.2054C15.7089 19.3729 15.0536 20.4443 14.21 21.3584L14.216 21.3514C13.3994 22.2224 12.4062 22.909 11.3028 23.365C10.1993 23.8209 9.01123 24.0359 7.81798 23.9954H7.83098H7.82398C6.78954 23.9951 5.76426 23.8014 4.80098 23.4244L4.85698 23.4434C3.93486 23.099 3.09072 22.5741 2.37398 21.8994L2.37798 21.9024C1.65934 21.264 1.06324 20.4998 0.618982 19.6474L0.598982 19.6044C0.210074 18.8362 0.00695704 17.9874 0.00598213 17.1264C-0.0446696 16.3436 0.172959 15.5659 0.622982 14.9234L0.615982 14.9344C1.10985 14.3709 1.78579 13.9986 2.52598 13.8824L2.54398 13.8804C2.41062 13.5843 2.29541 13.2805 2.19898 12.9704L2.18198 12.9084C2.11645 12.7132 2.07743 12.51 2.06598 12.3044V12.2984C2.08846 12.0063 2.16903 11.7217 2.30297 11.4612C2.43691 11.2007 2.62153 10.9696 2.84598 10.7814L2.84898 10.7784C3.21098 10.3494 3.72898 10.0624 4.31498 10.0054L4.32398 10.0044C4.54998 10.0084 4.76332 10.0454 4.96398 10.1154L4.94998 10.1114C5.24098 10.2144 5.49198 10.3334 5.72598 10.4764L5.70798 10.4664C4.86198 8.07173 4.24665 6.22373 3.86198 4.9224C3.58298 4.1364 3.37898 3.2224 3.28898 2.2744L3.28498 2.2284C3.25192 1.63839 3.4414 1.05744 3.81598 0.600397L3.81298 0.604397C3.99232 0.402864 4.21466 0.244219 4.46356 0.140191C4.71247 0.0361628 4.98157 -0.0105875 5.25098 0.00339681H5.24698C6.27898 0.00339681 7.58498 2.31006 9.16498 6.9234C9.43965 7.71473 9.64832 8.3244 9.79098 8.7524C9.91232 8.4124 10.0826 7.92906 10.302 7.3024C11.884 2.7324 13.246 0.447397 14.388 0.447397L14.459 0.446397C14.964 0.446397 15.417 0.666397 15.729 1.0144L15.73 1.0164C16.087 1.44401 16.2686 1.99115 16.238 2.5474V2.5424C16.148 3.5214 15.953 4.4214 15.662 5.2774L15.686 5.1944C15.3173 6.47973 14.76 8.15906 14.014 10.2324L14.012 10.2374Z",fill:"#FFF0E5"})}),e.jsx("defs",{children:e.jsx("clipPath",{id:"clip0_7283_21123",children:e.jsx("rect",{width:"17",height:"24",fill:"white"})})})]})}),crunch_base:e.jsx("div",{className:"flex min-h-[36px] min-w-[36px] items-center justify-center rounded-[50%] bg-black",children:e.jsxs("svg",{width:"23",height:"16",viewBox:"0 0 23 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M6.65277 13.4764C5.88088 13.765 5.02596 13.7352 4.27608 13.3935C3.81944 13.1854 3.41999 12.8698 3.1119 12.4737C2.80381 12.0776 2.59624 11.6127 2.50694 11.1189C2.41764 10.6251 2.44927 10.117 2.59912 9.6381C2.74897 9.15918 3.01259 8.72368 3.36743 8.36884C3.72227 8.014 4.15777 7.75038 4.63669 7.60053C5.11561 7.45068 5.6237 7.41905 6.11751 7.50835C6.61132 7.59765 7.07615 7.80522 7.47225 8.11331C7.86836 8.4214 8.18395 8.82085 8.39204 9.27749H10.8366C10.5215 7.98659 9.74431 6.85588 8.65201 6.09919C7.5597 5.3425 6.22799 5.01227 4.9087 5.17096C3.58941 5.32964 2.37399 5.96623 1.49227 6.96036C0.610552 7.95448 0.123657 9.23723 0.123657 10.566C0.123657 11.8948 0.610552 13.1776 1.49227 14.1717C2.37399 15.1658 3.58941 15.8024 4.9087 15.9611C6.22799 16.1198 7.5597 15.7896 8.65201 15.0329C9.74431 14.2762 10.5215 13.1455 10.8366 11.8546H8.39204C8.0503 12.6044 7.42466 13.1878 6.65277 13.4764Z",fill:"#FFF0E5"}),e.jsx("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M19.4219 15.5656C19.9918 15.3484 20.5198 15.0345 20.9829 14.6378C21.8235 13.9195 22.4235 12.9606 22.702 11.8906C22.9805 10.8206 22.9242 9.69091 22.5405 8.65393C22.1569 7.61695 21.4644 6.72259 20.5565 6.09151C19.6486 5.46043 18.569 5.12298 17.4634 5.12471H17.0658C16.1169 5.19971 15.2036 5.51935 14.415 6.05246V0H12.2061V15.5656H14.4298V15.006C15.1549 15.4952 15.9878 15.8017 16.8571 15.8991C17.7264 15.9966 18.6065 15.8821 19.4219 15.5656ZM20.4384 11.7708C20.59 11.3871 20.6635 10.977 20.6545 10.5646C20.6369 9.7547 20.3028 8.98397 19.7238 8.41746C19.1448 7.85095 18.3669 7.53371 17.5569 7.53371C16.7468 7.53371 15.969 7.85095 15.39 8.41746C14.811 8.98397 14.4769 9.7547 14.4592 10.5646C14.4502 10.977 14.5237 11.3871 14.6753 11.7708C14.827 12.1545 15.0537 12.5041 15.3422 12.799C15.6308 13.0939 15.9753 13.3282 16.3556 13.4881C16.7359 13.6481 17.1443 13.7305 17.5569 13.7305C17.9694 13.7305 18.3779 13.6481 18.7582 13.4881C19.1385 13.3282 19.483 13.0939 19.7715 12.799C20.0601 12.5041 20.2868 12.1545 20.4384 11.7708Z",fill:"#FFF0E5"})]})}),g2:e.jsx("div",{className:"flex min-h-[36px] min-w-[36px] items-center justify-center rounded-[50%] bg-black",children:e.jsxs("svg",{width:"20",height:"21",viewBox:"0 0 20 21",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M14.0933 14.9619C14.8497 16.3024 15.5976 17.6277 16.345 18.9516C13.0355 21.5393 7.88656 21.8521 4.07489 18.872C-0.311504 15.44 -1.03245 9.58393 1.31596 5.36848C4.01697 0.519809 9.07145 -0.552504 12.3258 0.233782C12.2378 0.429062 10.2887 4.55893 10.2887 4.55893C10.2887 4.55893 10.1346 4.56927 10.0474 4.57099C9.08551 4.61264 8.36906 4.84123 7.60116 5.24672C6.75867 5.69571 6.03667 6.34863 5.49829 7.14841C4.9599 7.94818 4.62151 8.87046 4.51269 9.83463C4.39915 10.8124 4.53153 11.8037 4.89734 12.7147C5.20664 13.4849 5.64415 14.169 6.23069 14.7465C7.13047 15.6333 8.2012 16.1824 9.44232 16.3641C10.6177 16.5365 11.748 16.3659 12.8072 15.8222C13.2045 15.6186 13.5425 15.3938 13.9375 15.0854C13.9879 15.052 14.0326 15.0098 14.0933 14.9619Z",fill:"#FFF0E5"}),e.jsx("path",{d:"M14.1006 3.20542C13.9086 3.01243 13.7306 2.83438 13.5534 2.65519C13.4477 2.54836 13.3459 2.43722 13.2377 2.33298C13.1989 2.29536 13.1533 2.24395 13.1533 2.24395C13.1533 2.24395 13.1902 2.16412 13.2059 2.13138C13.4131 1.70665 13.7379 1.39621 14.1231 1.14924C14.5491 0.874064 15.0454 0.734053 15.5492 0.746905C16.194 0.759828 16.7935 0.923806 17.2993 1.36548C17.6727 1.69143 17.8642 2.10496 17.8979 2.59861C17.9542 3.43142 17.6168 4.06924 16.9467 4.51436C16.5531 4.77627 16.1285 4.97872 15.7028 5.21852C15.468 5.3509 15.2672 5.46721 15.0378 5.70671C14.8359 5.94708 14.8261 6.17366 14.8261 6.17366L17.876 6.16964V7.55699H13.1682C13.1682 7.55699 13.1682 7.46222 13.1682 7.42288C13.1502 6.74112 13.2281 6.09957 13.5338 5.48042C13.8149 4.91239 14.2519 4.49656 14.7769 4.17636C15.1812 3.92967 15.6069 3.71975 16.0121 3.47421C16.262 3.32287 16.4386 3.10088 16.4372 2.77896C16.4372 2.5027 16.2404 2.25716 15.9592 2.18049C15.2962 1.99784 14.6214 2.28933 14.2704 2.90905C14.2193 2.99951 14.167 3.0894 14.1006 3.20542Z",fill:"#FFF0E5"}),e.jsx("path",{d:"M20.0002 13.4875L17.4299 8.9541H12.3437L9.75684 13.5343H14.8805L17.4088 18.0461L20.0002 13.4875Z",fill:"#FFF0E5"})]})}),glassdoor:e.jsx("div",{className:"flex min-h-[36px] min-w-[36px] items-center justify-center rounded-[50%] bg-black",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M14.2869 17.1419H2.85817C2.85792 17.517 2.93158 17.8885 3.07493 18.2352C3.21828 18.5819 3.42852 18.8969 3.69364 19.1623C3.95876 19.4277 4.27357 19.6383 4.62008 19.782C4.96659 19.9258 5.33803 19.9998 5.71317 20H14.2844C14.6598 20.0002 15.0316 19.9265 15.3785 19.7829C15.7253 19.6394 16.0405 19.4289 16.306 19.1634C16.5714 18.898 16.7819 18.5828 16.9255 18.2359C17.069 17.889 17.1428 17.5173 17.1425 17.1419V5.41C17.1427 5.39625 17.1401 5.3826 17.1349 5.36986C17.1298 5.35712 17.1221 5.34555 17.1123 5.33582C17.1026 5.3261 17.091 5.31841 17.0783 5.31323C17.0656 5.30804 17.0519 5.30546 17.0382 5.30562H14.3882C14.3747 5.30579 14.3614 5.30862 14.349 5.31397C14.3366 5.31932 14.3254 5.32708 14.316 5.33679C14.3067 5.3465 14.2993 5.35797 14.2945 5.37053C14.2896 5.3831 14.2872 5.39652 14.2875 5.41V17.145L14.2869 17.1419ZM14.2869 0C14.6621 0.000164095 15.0335 0.0742264 15.38 0.217957C15.7265 0.361688 16.0413 0.572271 16.3064 0.837681C16.5716 1.10309 16.7818 1.41813 16.9252 1.7648C17.0685 2.11147 17.1422 2.48298 17.1419 2.85812H5.71629V14.59C5.71581 14.6175 5.70466 14.6438 5.68519 14.6633C5.66572 14.6827 5.63945 14.6939 5.61192 14.6944H2.96192C2.93439 14.6939 2.90812 14.6827 2.88865 14.6633C2.86918 14.6438 2.85803 14.6175 2.85754 14.59V2.85812C2.8573 2.48298 2.93095 2.11147 3.07431 1.7648C3.21766 1.41813 3.4279 1.10309 3.69302 0.837681C3.95814 0.572271 4.27294 0.361688 4.61946 0.217957C4.96597 0.0742264 5.3374 0.000164095 5.71254 0L14.2869 0Z",fill:"#FFF0E5"})})}),tiktok:e.jsx("div",{className:"flex min-h-[36px] min-w-[36px] items-center justify-center rounded-[50%] bg-black",children:e.jsx(N,{className:"object-cover",pathFill:"#fff0e5"})}),slack:e.jsx("div",{className:"flex min-h-[36px] min-w-[36px] items-center justify-center rounded-[50%] bg-black",children:e.jsx(V,{className:"object-cover",pathFill:"#fff0e5"})}),discord:e.jsx(k,{}),trust_pilot:e.jsx(M,{}),trust_radius:e.jsxs("div",{className:"flex min-h-[36px] min-w-[36px] items-center justify-center rounded-[50%] bg-black",children:[" ",e.jsxs("svg",{width:"27",height:"18",viewBox:"0 0 27 18",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsxs("g",{"clip-path":"url(#clip0_7283_21134)",children:[e.jsx("path",{d:"M8.36592 1.00245C8.2432 0.879723 8.10001 0.818359 7.93638 0.818359H0.286377C0.102286 0.818359 0.0204676 1.04336 0.143195 1.16609L0.613649 1.63654H9.00001L8.36592 1.00245Z",fill:"#FFF0E5"}),e.jsx("path",{d:"M9.40907 2.0459H1.02271L1.84089 2.86408H10.2273L9.40907 2.0459Z",fill:"#FFF0E5"}),e.jsx("path",{d:"M11.4545 4.3361C11.4545 4.17246 11.3932 4.00882 11.2705 3.90655L10.6364 3.27246H2.25L2.88409 3.90655C3.00682 4.02928 3.15 4.09064 3.31364 4.09064H8.18182V14.727C8.18182 14.8906 8.24318 15.0543 8.36591 15.1566L11.1068 17.8975C11.2295 18.0202 11.4545 17.9384 11.4545 17.7543V4.3361Z",fill:"#FFF0E5"}),e.jsx("path",{d:"M26.6523 16.8338L21.4773 11.6588L22.0909 11.0452H20.8637L20.4546 10.6361H22.5L23.3182 9.81792H19.6364L19.2273 9.40882H23.7273L24.3614 8.77473C24.4841 8.65201 24.5455 8.50882 24.5455 8.34519V4.3361C24.5455 4.17246 24.4841 4.00882 24.3614 3.90655L23.7273 3.27246H13.9091L13.275 3.90655C13.1523 4.02928 13.0909 4.17246 13.0909 4.3361V17.7543C13.0909 17.9384 13.3159 18.0202 13.4387 17.8975L16.1796 15.1566C16.3023 15.0338 16.3637 14.8906 16.3637 14.727V10.6361V4.09064H21.2728V8.59064H18.4909C18.4296 8.59064 18.3887 8.6111 18.3478 8.65201L16.3637 10.6361L22.725 16.9975C22.8478 17.1202 22.9909 17.1816 23.1546 17.1816H26.4887C26.6932 17.1816 26.775 16.9566 26.6523 16.8338Z",fill:"#FFF0E5"}),e.jsx("path",{d:"M22.5001 2.0459H15.1364L14.3182 2.86408H23.3182L22.5001 2.0459Z",fill:"#FFF0E5"}),e.jsx("path",{d:"M21.4569 1.00245C21.3342 0.879723 21.191 0.818359 21.0274 0.818359H16.6092C16.4455 0.818359 16.2819 0.879723 16.1796 1.00245L15.5455 1.63654H22.091L21.4569 1.00245Z",fill:"#FFF0E5"})]}),e.jsx("defs",{children:e.jsx("clipPath",{id:"clip0_7283_21134",children:e.jsx("rect",{width:"27",height:"18",fill:"white"})})})]})]}),bluesky:e.jsx("div",{className:"flex min-h-[36px] min-w-[36px] items-center justify-center rounded-[50%] bg-black",children:e.jsx("svg",{width:"22",height:"20",viewBox:"0 0 22 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M4.97637 1.66143C7.41456 3.54524 10.0371 7.36483 11 9.41464C11.9629 7.36498 14.5853 3.5452 17.0236 1.66143C18.7828 0.302148 21.6333 -0.749588 21.6333 2.59709C21.6333 3.26547 21.261 8.21181 21.0426 9.01483C20.2835 11.8067 17.5173 12.5188 15.0567 12.0878C19.3577 12.8411 20.4519 15.3365 18.089 17.8319C13.6013 22.5712 11.6389 16.6428 11.1359 15.1238C11.0437 14.8453 11.0005 14.715 10.9999 14.8258C10.9993 14.715 10.9561 14.8453 10.864 15.1238C10.3611 16.6428 8.39875 22.5713 3.91086 17.8319C1.54792 15.3365 2.64201 12.841 6.94309 12.0878C4.48246 12.5188 1.71625 11.8067 0.957253 9.01483C0.738859 8.21173 0.366516 3.26539 0.366516 2.59709C0.366516 -0.749588 3.21706 0.302148 4.97625 1.66143H4.97637Z",fill:"#FFF0E5"})})}),capterra:e.jsx("div",{className:"flex min-h-[36px] min-w-[36px] items-center justify-center rounded-[50%] bg-black",children:e.jsx(Z,{className:"object-cover",pathFill:"#fff0e5"})})},c1=({update:r=null,companyDetails:s})=>{var x,n,h,m;L(),a();const[l,C]=j.useState(!1);u();const d=i=>{var c;if(!(i!=null&&i.value))return null;const t=i.key.split("_").map(f=>f.charAt(0).toUpperCase()+f.slice(1)).join(" ");return e.jsx(B,{text:t,children:e.jsx("a",{href:(c=i.value)!=null&&c.startsWith("http")?i.value:`http://${i.value}`,target:"_blank",rel:"noopener noreferrer",children:q[i.key]})})};return e.jsxs(e.Fragment,{children:[e.jsx("div",{id:"overview",className:"mt-[1rem] flex w-full flex-row items-start justify-between gap-3  md:mt-0 ",children:e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex w-full flex-wrap items-start md:mt-0 lg:flex-nowrap",children:[e.jsxs("div",{className:"mt-[-20px] flex w-full flex-row items-center gap-4 pr-3 sm:pr-8 md:block md:w-auto xl:pr-12",children:[e.jsx("img",{src:(s==null?void 0:s.logo)||"/default.png",alt:"",className:"h-[80px] min-h-[80px] w-[80px] min-w-[80px] rounded-[50%] object-cover md:h-32 md:min-h-32 md:w-32 md:min-w-32"}),e.jsxs("div",{className:"flex flex-col gap-2 md:hidden",children:[e.jsx("h1",{className:"font-inter text-[28px] font-semibold capitalize leading-[33.89px]  ",children:(s==null?void 0:s.name)||"{{Company Name}}"}),e.jsx("a",{href:(x=s==null?void 0:s.website)!=null&&x.startsWith("http")?s==null?void 0:s.website:`http://${s==null?void 0:s.website}`,target:"_blank",rel:"noopener noreferrer",className:"font-inter text-base font-semibold text-[#1f1d1a] underline sm:text-base md:mt-0 md:block",children:s==null?void 0:s.website})]})]}),e.jsxs("div",{className:"flex flex-row items-center gap-4 md:hidden",children:[(r==null?void 0:r.private_link_access)==1?null:e.jsx(o,{children:e.jsx(E,{update_id:r==null?void 0:r.id})}),(n=r==null?void 0:r.companies)!=null&&n.contact_link?e.jsx("img",{onClick:()=>{var i;window.location.href=(i=r==null?void 0:r.companies)==null?void 0:i.contact_link},src:"/assets/calendar.svg",alt:"",className:"h-5 min-h-5 w-5 min-w-5 cursor-pointer"}):null]}),e.jsx("div",{className:"flex w-full items-start justify-between gap-4 md:gap-2",children:e.jsxs("div",{className:"hidden w-full space-y-5 md:block",children:[e.jsx("h1",{className:"hidden font-inter text-[28px] font-semibold capitalize leading-[33.89px] md:block  ",children:(s==null?void 0:s.name)||"{{Company Name}}"}),e.jsx("a",{href:(h=s==null?void 0:s.website)!=null&&h.startsWith("http")?s==null?void 0:s.website:`http://${s==null?void 0:s.website}`,target:"_blank",rel:"noopener noreferrer",className:"mt-5  hidden w-fit font-Inter text-base font-semibold text-[#1f1d1a] underline sm:text-base md:mt-0 md:block",children:s==null?void 0:s.website}),e.jsx("div",{className:"flex flex-wrap gap-2 2xl:flex-nowrap",children:s!=null&&s.socials?s==null?void 0:s.socials.map((i,t)=>e.jsx(w.Fragment,{children:d(i)},t)):null}),e.jsx("p",{className:"max-w-[100%] overflow-hidden  text-ellipsis font-iowan text-[16px] !font-[400] leading-6 sm:max-w-[400px] lg:max-w-[600px] xl:max-w-[900px]",children:(s==null?void 0:s.description)||e.jsx(e.Fragment,{children:"Lorem ipsum, dolor sit amet consectetur adipisicing elit. Quasi magnam eum ipsum obcaecati qui maxime quae nam atque commodi cum iste eos doloremque voluptas, dolorem laudantium est velit sunt eligendi."})})]})})]}),e.jsx(o,{children:e.jsx(_,{update:r})})]})}),l?e.jsxs("div",{className:"mt-0 block w-full md:hidden",children:[e.jsx("h1",{className:"hidden font-inter text-[28px] font-semibold capitalize leading-[33.89px] md:block  ",children:(s==null?void 0:s.name)||"{{Company Name}}"}),e.jsx("a",{href:(m=s==null?void 0:s.website)!=null&&m.startsWith("http")?s==null?void 0:s.website:`http://${s==null?void 0:s.website}`,target:"_blank",rel:"noopener noreferrer",className:"mt-5  hidden w-fit font-Inter text-base font-semibold text-[#1f1d1a] underline sm:text-base md:mt-0 md:block",children:s==null?void 0:s.website}),e.jsx("div",{className:"flex flex-wrap gap-2 2xl:flex-nowrap",children:s!=null&&s.socials?s==null?void 0:s.socials.map((i,t)=>e.jsx(w.Fragment,{children:d(i)},t)):null}),e.jsx("p",{className:"mt-3 max-w-[100%]  overflow-hidden text-ellipsis font-iowan text-[16px] !font-[400] leading-6 sm:max-w-[400px] lg:max-w-[600px] xl:max-w-[900px]",children:(s==null?void 0:s.description)||e.jsx(e.Fragment,{children:"Lorem ipsum, dolor sit amet consectetur adipisicing elit. Quasi magnam eum ipsum obcaecati qui maxime quae nam atque commodi cum iste eos doloremque voluptas, dolorem laudantium est velit sunt eligendi."})})]}):null,e.jsx("div",{className:"mt-3 flex flex-row items-center gap-4 pb-4 md:hidden md:pb-0",children:l?e.jsxs("div",{onClick:()=>C(!1),className:"flex cursor-pointer flex-row items-center gap-2",children:[e.jsx("button",{className:"text-primary-black",children:"Hide Details"}),e.jsx(I,{className:"h-5 w-5"})]}):e.jsxs("div",{className:"flex cursor-pointer flex-row items-center gap-2",onClick:()=>C(!0),children:[e.jsx("button",{className:"text-primary-black",children:"View Details"}),e.jsx(S,{className:"h-5 w-5"})]})})]})};export{c1 as default};
