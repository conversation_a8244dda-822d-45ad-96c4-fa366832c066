import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as d,b as v}from"./vendor-4cdf2bd1.js";import{G as S,A as U,u as N,I as P,ax as k,h as l,s as n,n as E}from"./index-46de5032.js";import{c as R,a as C}from"./yup-342a5df4.js";import{u as T}from"./react-hook-form-9f4fcfa9.js";import{M as A}from"./MkdInput-a0090fba.js";import{l as $}from"./logo5-2e16f0f2.js";import{o as I}from"./yup-c41d85d2.js";import{u as L}from"./useLocalStorage-53cfe2d8.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";import"./@hookform/resolvers-fad2f3d1.js";const he=({role:_=k.STARTUP,updateStep:q=null})=>{const{dispatch:o,state:{recipientCreation:a}}=d.useContext(S),{dispatch:f}=d.useContext(U);d.useState([1]),v();const{localStorageData:D,setLocalStorage:c}=L(["step"]),{profile:m,updateProfile:p}=N({isPublic:!0}),u=R({email:C().required("This field is required").test("is-valid-emails","Some emails are invalid",t=>t?t.split(",").map(r=>r.trim()).every(r=>/^\S+@\S+\.\S+$/.test(r)):!1)}),{register:g,handleSubmit:x,setError:h,watch:F,formState:{errors:w}}=T({resolver:I(u)}),b=async()=>{try{console.log("[Profile Update] Starting skip process"),l(o,!0,"skipStep"),console.log("[Profile Update] Calling updateProfile");const t=await p({step:5,is_onboarded:1});if(console.log("[Profile Update] Update result:",t),t!=null&&t.error)throw console.error("[Profile Update] Update failed:",t.error),new Error(t.error);console.log("[Profile Update] Update successful, updating local storage"),c("step",5),l(o,!1,"skipStep"),console.log("[Profile Update] Waiting for state updates"),await new Promise(i=>setTimeout(i,100)),console.log("[Profile Update] Navigation starting"),window.location.href="/member/dashboard"}catch(t){console.error("[Profile Update] Error in skip:",t),l(o,!1,"skipStep"),n(o,t.message||"Failed to update profile",5e3,"error")}},y=async t=>{const i=[];for(const r of t.email.split(",").map(s=>s.trim()))/^\S+@\S+\.\S+$/.test(r)?i.push(r):n(o,`${r} is not a valid email`,5e3,"error");try{l(o,!0,"recipientCreation"),console.log("[Profile Update] Starting recipient creation");const r=await E(o,f,{endpoint:"/v3/api/custom/goodbadugly/member/recipients",method:"POST",payload:{emails:i}},"recipientCreation");if(!(r!=null&&r.error)){n(o,"Recipient(s) added successfully",5e3,"success"),console.log("[Profile Update] Recipient creation successful, starting profile update");const s=await p({step:5,is_onboarded:1});if(console.log("[Profile Update] Update result:",s),s!=null&&s.error)throw console.error("[Profile Update] Update failed:",s.error),new Error(s.error);console.log("[Profile Update] Update successful, updating local storage"),c("step",5),console.log("[Profile Update] Waiting for state updates"),await new Promise(j=>setTimeout(j,50)),console.log("[Profile Update] Navigation starting"),window.location.href="/member/dashboard"}}catch(r){console.error("[Profile Update] Error in onSubmit:",r),n(o,r.message||"Failed to update profile",5e3,"error"),h("team_name",{type:"manual",message:r.message})}finally{l(o,!1,"recipientCreation")}};return e.jsxs("div",{className:"w-full md:mt-[50px] md:w-[60%] md:max-w-[500px] md:px-6 lg:w-[70%] xl:mt-[160px]",children:[e.jsx("style",{children:`
        .bd {
          border: 1px solid #1f1d1a;
        }
          .style {
            
          }
      `}),e.jsx("div",{className:"sticky right-0 top-0 z-[9] flex h-[4.5rem] w-full flex-row items-center justify-between bg-[#1f1d1a] px-8 md:hidden",children:e.jsx("img",{src:$,alt:"logo",className:"h-10 w-[180px]"})}),e.jsx("form",{onSubmit:x(y,t=>{console.log("ERROR >>",t)}),className:"flex h-full max-h-full min-h-full w-full flex-col items-center justify-center px-4 md:px-0",children:e.jsxs("div",{className:"mt-5 w-full md:mt-8",children:[e.jsxs("div",{className:"space-y-5",children:[e.jsxs("div",{className:"l font-iowan text-[2.25rem] font-[700] leading-[3rem] md:text-[2.5rem] ",children:["Welcome ",m==null?void 0:m.first_name]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-inter text-[1rem] font-[600] leading-[1.5rem]",children:"Next, add a recipient user to your account"}),e.jsx("h5",{className:"font-inter text-[1rem] font-[400] leading-[1.5rem]",children:"Invite team members, investors, or limited partners to receive your updates via UpdateStack"})]}),e.jsx("p",{className:"font-inte we rfadd user text-[1rem] font-[400] leading-[1.5rem]",children:"Add recipients using their business email if adding multiple recipients, separate each email by a comma"})]}),e.jsx("div",{className:"scrollbar-hide h-full max-h-full min-h-full w-full overflow-y-auto pb-5",children:e.jsx("div",{className:"my-5 mt-4 grid grid-cols-1 gap-4 md:mt-2",children:e.jsxs("div",{className:"w-full items-end justify-start gap-2 space-y-[1rem]",children:[e.jsx("div",{className:"mt-4 flex flex-col gap-[1rem] md:flex-row",children:e.jsx("div",{className:"grid grow grid-cols-1",children:e.jsx(A,{type:"text",name:"email",errors:w,label:"Email",register:g,className:"focus:shadow-outline !h-[2.75rem] w-full appearance-none !rounded-[.125rem] border border-[#1f1d1a] !bg-transparent text-sm leading-tight text-[#1d1f1a] shadow focus:outline-none",placeholder:"<EMAIL>,<EMAIL>"})})}),e.jsxs("div",{className:"flex w-full flex-col items-center gap-[1rem] ",children:[e.jsx(P,{type:"submit",className:"my-4 flex h-[2.75rem] !w-full items-center justify-center rounded-sm bg-[#1f1d1a] !px-3 py-2 tracking-wide text-white outline-none focus:outline-none",loading:a==null?void 0:a.loading,disabled:a==null?void 0:a.loading,children:e.jsx("span",{className:"capitalize",children:"Add User(s)"})}),e.jsx("button",{type:"button",onClick:()=>b(),className:"w-fit cursor-pointer whitespace-nowrap text-sm font-medium md:text-base",children:"Skip,  Add Later →"})]})]})})})]})})]})};export{he as default};
