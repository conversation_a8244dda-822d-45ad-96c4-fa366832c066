import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{A as L,G as $,I as le,w as A,t as I,s as z,M as oe,q as ne,p as ie}from"./index-46de5032.js";import{r as s,b as ce}from"./vendor-4cdf2bd1.js";import{M as de}from"./MkdCustomInput-af54c64d.js";import{o as V}from"./yup-c41d85d2.js";import{u as X,a as ue}from"./react-hook-form-9f4fcfa9.js";import{c as K,a as E,d as me}from"./yup-342a5df4.js";import{InteractiveButton2 as pe}from"./InteractiveButton-060359e0.js";import{X as re}from"./XMarkIcon-cfb26fe7.js";import{t as q,S as B,W as O}from"./@headlessui/react-cdd9213e.js";import"./index-6edcbb0d.js";function fe({onSuccess:n,onClose:i,buttonText:c="Next"}){const{dispatch:d,state:v}=s.useContext(L),{dispatch:l,state:{createModel:o}}=s.useContext($),r=(o==null?void 0:o.loading)||!1,u=K({group_name:E().required("This field is required")}),{register:p,handleSubmit:b,setError:j,setValue:m,watch:S,formState:{errors:y}}=X({resolver:V(u)});async function N(a){try{const f=await A(l,d,"group",{user_id:v.user,group_name:a==null?void 0:a.group_name,role:"member"});n&&n(f.data)}catch(f){I(d,f.message),f.message!=="TOKEN_EXPIRED"&&z(l,f.message,5e3,"error")}}return e.jsxs("div",{className:"space-y-[1.375rem]",children:[e.jsx("p",{className:"mt-2 font-inter text-lg",children:"Use groups to organize your update recipients"}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block  text-[1rem] font-semibold capitalize leading-[1.5rem] text-[#1f1d1a]",children:"Group name"}),e.jsx(de,{type:"text",errors:y,name:"group_name",placeholder:"New group name",onChange:a=>{m("group_name",a.target.value)}})]}),e.jsxs("div",{className:"mt-6 flex justify-end gap-4",children:[e.jsx("button",{className:"border border-[#0003] px-3 py-2 text-center font-iowan font-medium ",type:"button",onClick:()=>i(),children:"Cancel"}),e.jsx(le,{loading:r,disabled:r,onClick:b(N,a=>console.log("error >>",a)),className:"bg-primary-black px-3 py-2 text-center font-semibold text-white transition-colors duration-100 disabled:bg-disabled-black",children:c})]})]})}function xe({title:n,titleId:i,...c},d){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:d,"aria-labelledby":i},c),n?s.createElement("title",{id:i},n):null,s.createElement("path",{fillRule:"evenodd",d:"M10.53 3.47a.75.75 0 0 0-1.06 0L6.22 6.72a.75.75 0 0 0 1.06 1.06L10 5.06l2.72 2.72a.75.75 0 1 0 1.06-1.06l-3.25-3.25Zm-4.31 9.81 3.25 3.25a.75.75 0 0 0 1.06 0l3.25-3.25a.75.75 0 1 0-1.06-1.06L10 14.94l-2.72-2.72a.75.75 0 0 0-1.06 1.06Z",clipRule:"evenodd"}))}const he=s.forwardRef(xe),ge=he,be=[{label:"Manager/Founder",value:"manager"},{label:"Collaborator",value:"collaborator"},{label:"Fund Manager",value:"investor"},{label:"Stakeholder",value:"stakeholder"}];function je({isOpen:n,closeModal:i,collaborator:c=!1,onAdd:d}){const{dispatch:v,state:l}=s.useContext(L),{dispatch:o}=s.useContext($),[r,u]=s.useState(!1),p=K({first_name:E().required("This field is required"),last_name:E().required("This field is required"),email:E().email().required("This field is required"),role:E().required("This field is required")}),{register:b,handleSubmit:j,formState:{errors:m},reset:S,setValue:y}=X({resolver:V(p)});s.useEffect(()=>{c&&y("role","collaborator")},[c,y]);async function N(a){u(!0);try{const x=await new oe().callRawAPI("/v3/api/custom/goodbadugly/member/create-user",{email:a.email,first_name:a.first_name,last_name:a.last_name,role:a.role,company_id:l.company.id},"POST");i(),S(),z(o,"User added successfully"),d&&d()}catch(f){I(v,f.message),f.message!=="TOKEN_EXPIRED"&&z(o,f.message,5e3,"error")}u(!1)}return e.jsx(q,{appear:!0,show:n,as:s.Fragment,children:e.jsxs(B,{as:"div",className:"relative z-[50]",onClose:i,children:[e.jsx(q.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(q.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(B.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(B.Title,{as:"h3",className:"text-xl font-semibold leading-6 text-gray-900",children:"Add New User"}),e.jsx("button",{onClick:i,type:"button",children:e.jsx(re,{className:"h-6 w-6"})})]}),e.jsxs("form",{onSubmit:j(N),className:"mt-4",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-semibold",children:"First Name"}),e.jsx("input",{...b("first_name"),className:"w-full rounded border border-[#1f1d1a] bg-transparent p-2"}),m.first_name&&e.jsx("p",{className:"text-red-500",children:m.first_name.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-semibold",children:"Last Name"}),e.jsx("input",{...b("last_name"),className:"w-full rounded border border-[#1f1d1a] bg-transparent p-2"}),m.last_name&&e.jsx("p",{className:"text-red-500",children:m.last_name.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-semibold",children:"Email"}),e.jsx("input",{...b("email"),className:"w-full rounded border border-[#1f1d1a] bg-transparent p-2",type:"email"}),m.email&&e.jsx("p",{className:"text-red-500",children:m.email.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-semibold",children:"Role"}),e.jsxs("select",{...b("role"),disabled:c,defaultValue:c?"collaborator":"",className:"w-full rounded border border-[#1f1d1a] bg-transparent p-2",children:[e.jsx("option",{value:"",children:"Select a role"}),be.map(a=>e.jsx("option",{value:a.value,children:a.label},a.value))]}),m.role&&e.jsx("p",{className:"text-red-500",children:m.role.message})]}),e.jsxs("div",{className:"mt-6 flex justify-end gap-4",children:[e.jsx("button",{className:"rounded-md border border-[#0003] px-3 py-2 text-center font-iowan font-medium ",type:"button",onClick:i,children:"Cancel"}),e.jsx(pe,{loading:r,disabled:r,type:"submit",className:"disabled:bg-disabledblack rounded-lg bg-primary-black px-3 py-2 text-center font-semibold text-white transition-colors duration-100",children:"Add User"})]})]})]})})})})]})})}function ve({control:n,name:i,setValue:c,watch:d,defaultSelectedMembers:v=[]}){var P,W,Z,H,Q,Y;const[l,o]=s.useState([]),[r,u]=s.useState(""),[p,b]=s.useState([]),{dispatch:j}=s.useContext($),{dispatch:m,state:S}=s.useContext(L),[y,N]=s.useState(!1),a=v.length>0?v:p;async function f(){try{const t=await ne(j,m,"company_member",{filter:[`company_id,eq,${S.company.id}`],join:["user|member_id"]});t!=null&&t.error||o(()=>t==null?void 0:t.data)}catch(t){I(m,t.message),t.message!=="TOKEN_EXPIRED"&&z(j,t.message,5e3,"error")}}s.useEffect(()=>{S.company.id&&f()},[S.company.id]);const{field:x,formState:w,fieldState:h}=ue({control:n,name:i});s.useEffect(()=>{console.log("SelectGroupMembers - field.value changed:",x.value),x.value?b(x.value):b([])},[x.value]),console.log("recipients >>",l),console.log("membersSelected >>",a),(l==null?void 0:l.length)>0&&(console.log("First recipient structure:",l[0]),console.log("First recipient user ID:",(W=(P=l[0])==null?void 0:P.user)==null?void 0:W.id,"type:",typeof((H=(Z=l[0])==null?void 0:Z.user)==null?void 0:H.id))),(a==null?void 0:a.length)>0&&console.log("First selected member ID:",a[0],"type:",typeof a[0]);const C=r===""?l==null?void 0:l.filter(t=>!(p!=null&&p.includes(t.id.toString()))):l==null?void 0:l.filter(t=>!(p!=null&&p.includes(t.id))).filter(t=>{var _,g,G,k,F,T,M,D,U,R,J,ee,te,ae,se;return((F=(G=(g=(_=t==null?void 0:t.user)==null?void 0:_.email)==null?void 0:g.toLowerCase())==null?void 0:G.replace(/\s+/g,""))==null?void 0:F.includes((k=r==null?void 0:r.toLowerCase())==null?void 0:k.replace(/\s+/g,"")))||((R=(D=(M=(T=t==null?void 0:t.user)==null?void 0:T.first_name)==null?void 0:M.toLowerCase())==null?void 0:D.replace(/\s+/g,""))==null?void 0:R.includes((U=r==null?void 0:r.toLowerCase())==null?void 0:U.replace(/\s+/g,"")))||((se=(te=(ee=(J=t==null?void 0:t.user)==null?void 0:J.last_name)==null?void 0:ee.toLowerCase())==null?void 0:te.replace(/\s+/g,""))==null?void 0:se.includes((ae=r==null?void 0:r.toLowerCase())==null?void 0:ae.replace(/\s+/g,"")))});return e.jsxs(e.Fragment,{children:[e.jsxs(O,{value:x.value,onChange:t=>c([...x.value?x.value:[],t]),children:[e.jsxs("div",{className:"relative z-30 mt-3 w-full",children:[e.jsx("label",{className:"mb-1 block  font-iowan text-[16px] font-semibold capitalize text-[#1f1d1a]",children:"Members"}),e.jsxs("div",{className:"w-full",children:[e.jsxs(O.Button,{className:"relative w-full cursor-default rounded-md text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 sm:text-sm",children:[e.jsx(O.Input,{className:`focus:shadow-outline h-[2.6rem] w-full appearance-none rounded-[2px] border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal leading-tight text-[#1f1d1a]   placeholder:text-[14px] placeholder:text-[#1f1d1a] focus:outline-none md:h-[44px] ${(Q=h.error)!=null&&Q.message?"border-red-500":""}`,placeholder:"Type to search",onChange:t=>u(t.target.value),onBlur:x.onBlur,ref:x.ref,name:x.name,autoComplete:"off"}),e.jsx("div",{className:"absolute inset-y-0 right-0 flex items-center pr-2",children:e.jsx(ge,{className:"h-5 w-5 text-[#1f1d1a]","aria-hidden":"true"})})]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(Y=h.error)==null?void 0:Y.message})]}),e.jsx(q,{as:s.Fragment,leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",afterLeave:()=>u(""),children:e.jsx(O.Options,{className:"custom-overflow absolute mt-1 max-h-96 w-full overflow-auto rounded-md border bg-brown-main-bg py-1 text-base shadow-lg ring-1 ring-[#1f1d1a]/5 focus:outline-none",children:(C==null?void 0:C.length)===0&&r!==""?e.jsx("div",{className:"relative cursor-default select-none px-4 py-2 text-gray-700",children:"Nothing found."}):e.jsxs(e.Fragment,{children:[C==null?void 0:C.map(t=>{var _;return e.jsx(O.Option,{className:({active:g})=>`relative cursor-default select-none py-2 pl-10 pr-4 ${g?"bg-primary-black text-white":"text-gray-900"}`,value:(_=t==null?void 0:t.user)==null?void 0:_.id,children:({selected:g,active:G})=>{var k;return e.jsx(e.Fragment,{children:e.jsx("span",{className:"block truncate font-medium",children:(k=t==null?void 0:t.user)==null?void 0:k.email})})}},t.id)}),e.jsx("li",{value:"add_new",className:({active:t})=>`relative cursor-pointer select-none py-2 pl-10 pr-4 ${t?"bg-primary-black text-white":"text-gray-900"}`,onClick:()=>N(!0),children:({active:t})=>e.jsx("span",{className:`block truncate font-medium underline ${t?"text-white":"text-primary-black"}`,children:"+ Add New User"})})]})})})]}),e.jsx("div",{className:"custom-overflow mt-2 flex w-full flex-wrap gap-4 overflow-y-auto text-sm",children:l&&(l==null?void 0:l.map((t,_)=>{var k,F,T;const g=(k=t==null?void 0:t.user)==null?void 0:k.id,G=(a==null?void 0:a.includes(g))||(a==null?void 0:a.includes(g==null?void 0:g.toString()))||(a==null?void 0:a.includes(String(g)));if(console.log(`Checking member ${(F=t==null?void 0:t.user)==null?void 0:F.email} (ID: ${g}) - Selected: ${G}`),G)return e.jsxs("div",{className:"flex items-center gap-3 rounded-sm border border-[#1f1d1a] bg-brown-main-bg px-3 py-2",children:[e.jsx("span",{children:(T=t==null?void 0:t.user)==null?void 0:T.email}),e.jsx("button",{type:"button",onClick:()=>{const M=[...x.value],D=M.findIndex(U=>{var R;return U===((R=t==null?void 0:t.user)==null?void 0:R.id)});D!==-1&&M.splice(D,1),c(M)},children:e.jsx(re,{className:"h-4",strokeWidth:2})})]},_)}))})]}),e.jsx(je,{isOpen:y,closeModal:()=>N(!1)})]})}function we({groupId:n,onSuccess:i,onClose:c}){const{dispatch:d,state:v}=s.useContext(L),{dispatch:l,state:{update:o,createModel:r}}=s.useContext($),u=(r==null?void 0:r.loading)||!1;s.useState([]),ce();const p=K({members:me().min(1,"You must add at least one member").of(E())}),{register:b,handleSubmit:j,setError:m,watch:S,setValue:y,control:N,formState:{errors:a}}=X({resolver:V(p),defaultValues:{members:[]}}),f=async w=>{console.log(w);try{const h=await A(l,d,"recipient_group",{group_id:n,members:w.members.join(","),user_id:v.user},!1);await Promise.all(w.members.map(C=>A(l,d,"recipient_member",{recipient_group_id:h.data,user_id:C},!1))),o&&(o!=null&&o.id)&&x(n)}catch(h){I(d,h.message),m("group_id",{type:"manual",message:h.message})}},x=async w=>{try{const h=await A(l,d,"update_group",{update_id:o==null?void 0:o.id,group_id:w,type:1},!1);h!=null&&h.error||i&&i()}catch(h){I(d,h.message),m("group_id",{type:"manual",message:h.message})}};return e.jsxs("div",{className:"space-y-[1.375rem]",children:[e.jsx("p",{className:"mt-2 font-inter text-lg",children:"Use groups to organize your update recipients"}),e.jsxs("div",{className:"z-30",children:["Select One or Multiple",e.jsx(ve,{control:N,name:"members",setValue:w=>y("members",w)})]}),e.jsxs("div",{className:"mt-6 flex justify-end gap-4",children:[e.jsx("button",{className:"border border-[#0003] px-3 py-2 text-center font-iowan font-medium ",type:"button",onClick:()=>c(),children:"Cancel"}),e.jsx(le,{loading:u,disabled:u,onClick:j(f,w=>console.log("error >>",w)),className:"bg-primary-black px-3 py-2 text-center font-semibold text-white transition-colors duration-100 disabled:bg-disabled-black",children:"Create + Add Members"})]})]})}const ye={1:"Create New Group",2:"Add Members To New Group"};function Re({afterCreate:n,buttonRef:i,type:c="update"}){s.useContext(L),s.useContext($);const[d,v]=s.useState(0),[l,o]=s.useState(!1),[r,u]=s.useState(1);console.log("CreateGroupModal render - open state:",l);const p=()=>{console.log("CreateGroupModal: handleOpenModal called, setting open to true"),o(!0)},b=()=>{console.log("CreateGroupModal: handleCloseModal called, setting open to false"),o(!1)};return e.jsx(ie,{isOpen:l,modalCloseClick:b,modalHeader:!0,title:ye[r],classes:{modal:"w-full",modalDialog:"!w-[100%] md:!w-[40%]",modalContent:""},children:e.jsxs("div",{className:"relative",children:[e.jsx("button",{type:"button",hidden:!0,ref:i,onClick:p}),r==1?e.jsx(fe,{buttonText:c==="update"?"Next":"Add New Group",onSuccess:j=>{console.log("CreateGroupModal: CreateGroup onSuccess, groupId:",j),v(j),c==="update"?u(2):(o(!1),u(1),n&&n(j))},onClose:()=>{console.log("CreateGroupModal: CreateGroup onClose"),o(!1),u(1)}}):null,r==2?e.jsx(we,{groupId:d,onSuccess:j=>{console.log("CreateGroupModal: CreateRecipientGroup onSuccess"),o(!1),u(1),n()},onClose:()=>{console.log("CreateGroupModal: CreateRecipientGroup onClose"),o(!1),u(1)}}):null]})})}export{Re as C,ve as S,ge as a};
