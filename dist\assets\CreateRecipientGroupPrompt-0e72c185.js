import{j as o}from"./@nextui-org/listbox-0f38ca19.js";import{R as b,r as c,b as mo,i as go}from"./vendor-4cdf2bd1.js";import"./index-f373d1dd.js";import{u as po}from"./useGroups-d4ba52a9.js";import{u as ho}from"./useUpdateGroups-478802f2.js";import{A as B,u as V,q as $,r as F,s as L,v as fo,x as S,G as bo,p as xo}from"./index-46de5032.js";import{P as w}from"./index-f503aa1b.js";import{u as yo}from"./useCompanyMember-f698e8a0.js";import{B as Co}from"./BottomSheet-be14d402.js";import{C as ko}from"./CreateGroupModal-9562fe27.js";import{f as vo}from"./lucide-react-0b94883e.js";import"./@nextui-org/theme-345a09ed.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./MkdCustomInput-af54c64d.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";import"./yup-c41d85d2.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-hook-form-9f4fcfa9.js";import"./yup-342a5df4.js";import"./InteractiveButton-060359e0.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";import"./XMarkIcon-cfb26fe7.js";import"./index-6edcbb0d.js";const wo=(i={filter:[]})=>{const{state:y,dispatch:n}=b.useContext(B),{state:G,dispatch:d}=b.useContext(B),[P,j]=b.useState([]),[A,M]=b.useState(null),[N,x]=b.useState(!1),{profile:E}=V({isPublic:!1}),t=b.useCallback((g={filter:[]})=>{(async()=>{x(!0);try{const s=await $(d,n,"group",{filter:[...g==null?void 0:g.filter]});s!=null&&s.error||j(()=>s==null?void 0:s.data)}catch{}finally{x(!1)}})()},[E,n,d,$]),r=b.useCallback(g=>{if(F(g))return L(d,"Group id is Required!");(async()=>{x(!0);try{const s=await fo(d,n,"group",g);s!=null&&s.error||M(()=>s==null?void 0:s.data)}catch{}finally{x(!1)}})()},[E,n,d,$]),m=b.useCallback((g,s)=>{if(F(g))return L(d,"Group id is Required!");if(F(s))return L(d,"Payload is Required!");(async()=>{x(!0);try{const k=await S(d,n,"group",g,s,!1);k!=null&&k.error||t()}catch{}finally{x(!1)}})()},[t,n,d,S]);return b.useEffect(()=>{t(i)},[]),{groups:P,group:A,loading:N,getGroups:t,getGroup:r,updateGroup:m}},jo=({isOpen:i,onClose:y,next:n=1,title:G="Getting Started",data:d=null,onNext:P,updateNext:j,isManualTrigger:A=!1,update:M=null,notes:N=[],updateGroups:x=[],collaborator:E=null,templateSelectRef:t=null,recipientSelectRef:r=null})=>{const m=c.useRef(null);console.log("=== CreateRecipientGroupPrompt rendered ==="),console.log("Current step (next):",n),console.log("templateSelectRef received:",t),console.log("recipientSelectRef received:",r),console.log("templateSelectRef.current:",t==null?void 0:t.current),console.log("recipientSelectRef.current:",r==null?void 0:r.current);const[g,s]=c.useState(i),[k,T]=c.useState(i);c.useEffect(()=>{s(i),T(i)},[i]);const X=mo(),{dispatch:Po,state:{update:Y}}=c.useContext(bo);c.useContext(B);const{id:O}=go(),{updateGroups:R,refetch:H}=ho(O),{groups:u}=po(),{profile:a,updateProfile:oo}=V({isPublic:!1}),{group:Eo,groups:h,loading:eo}=wo(),{loading:q,companyMember:l,getMyCompanyMembers:C}=yo(),v=M||Y,U=N||[],D=x||R,to=E,z=()=>{oo({awareness:Math.max((a==null?void 0:a.awareness)||0,3)}),_()},_=c.useCallback(()=>{s(!1),T(!1),y(!0)},[y]),so=c.useCallback(e=>{e&&e.stopPropagation(),s(!1),y(!0)},[y]),ro=c.useCallback(e=>{e&&e.stopPropagation(),T(!1),y(!0)},[y]);c.useEffect(()=>{h!=null&&h.length&&(u!=null&&u.length)&&[1,2].includes(n)&&j(3),h!=null&&h.length&&!(u!=null&&u.length)&&[1,2].includes(n)&&j(2)},[h==null?void 0:h.length,u==null?void 0:u.length]),c.useEffect(()=>{I()&&n===1&&(console.log("Team members detected, advancing to step 2"),j(2))},[l==null?void 0:l.myMembers,n]),c.useEffect(()=>{C()},[]),c.useEffect(()=>{var e,p,f,J,K,Q;i&&((p=(e=a==null?void 0:a.companies)==null?void 0:e[0])!=null&&p.id)&&(C({filter:[`company_id,eq,${(J=(f=a==null?void 0:a.companies)==null?void 0:f[0])==null?void 0:J.id}`]}),console.log("profile?.companies?.[0]?.id",(Q=(K=a==null?void 0:a.companies)==null?void 0:K[0])==null?void 0:Q.id))},[i,C]),c.useEffect(()=>{var e,p;i&&sessionStorage.getItem("shouldRefreshCompanyMembers")==="true"&&(console.log("Refreshing company members data after team member addition..."),C({filter:[`company_id,eq,${(p=(e=a==null?void 0:a.companies)==null?void 0:e[0])==null?void 0:p.id}`]}),sessionStorage.removeItem("shouldRefreshCompanyMembers"))},[i,C]),c.useEffect(()=>{const e=()=>{var p,f;i&&(console.log("Window focused, refreshing company members data..."),C({filter:[`company_id,eq,${(f=(p=a==null?void 0:a.companies)==null?void 0:p[0])==null?void 0:f.id}`]}))};return window.addEventListener("focus",e),()=>window.removeEventListener("focus",e)},[i,C]);const I=()=>{var p,f;const e=((p=l==null?void 0:l.myMembers)==null?void 0:p.length)>0;return console.log("Checking hasTeamMembers:",{myMembers:l==null?void 0:l.myMembers,length:(f=l==null?void 0:l.myMembers)==null?void 0:f.length,hasMembers:e}),e},W=()=>(u==null?void 0:u.length)>0,ao=()=>(U==null?void 0:U.length)>0,no=()=>(D==null?void 0:D.length)>0,lo=()=>to!==null,co=()=>(v==null?void 0:v.sent_at)!==null&&(v==null?void 0:v.sent_at)!==void 0;console.log(I(),"djjdj",W(),l==null?void 0:l.myMembers,h,l);const io=()=>{if(console.log("=== STEP 3: Create Update clicked ==="),console.log("templateSelectRef:",t),console.log("templateSelectRef.current:",t==null?void 0:t.current),console.log("templateSelectRef.current type:",typeof(t==null?void 0:t.current)),_(),t!=null&&t.current){console.log("Attempting to click templateSelectRef.current");try{t.current.showPicker(),console.log("✅ Successfully clicked templateSelectRef.current")}catch(e){console.error("❌ Error clicking templateSelectRef.current:",e)}}else console.log("❌ templateSelectRef.current is not available"),console.log("Available refs:",{templateSelectRef:t,recipientSelectRef:r})},uo=()=>{if(console.log("=== STEP 4: Add Recipient clicked ==="),console.log("recipientSelectRef:",r),console.log("recipientSelectRef.current:",r==null?void 0:r.current),console.log("recipientSelectRef.current type:",typeof(r==null?void 0:r.current)),_(),r!=null&&r.current){console.log("Attempting to open recipient popover...");try{r.current.openPopover?(console.log("Using openPopover function"),r.current.openPopover(),console.log("✅ Successfully opened recipient popover")):(console.log("Falling back to clicking button"),r.current.click(),console.log("✅ Successfully clicked recipientSelectRef.current"))}catch(e){console.error("❌ Error opening recipient popover:",e)}}else console.log("❌ recipientSelectRef.current is not available"),console.log("Available refs:",{templateSelectRef:t,recipientSelectRef:r})},Z=()=>o.jsxs("div",{className:"flex flex-col",children:[o.jsxs("div",{className:"px-4 py-3 border-b border-gray-200 md:px-6 md:py-4",children:[o.jsxs("div",{className:"flex justify-between items-center",children:[o.jsxs("div",{className:"flex gap-2 items-center md:gap-3",children:[o.jsx("h2",{className:"font-iowan text-[16px] font-semibold text-gray-900 md:text-xl",children:"Start Here: Follow These Steps"}),o.jsx(vo,{className:"w-4 h-4 text-gray-500 md:h-5 md:w-5"})]}),o.jsx("button",{onClick:()=>z(),className:"rounded-md bg-transparent px-3 py-1 font-iowan text-[16px] font-medium text-black underline focus:outline-none focus:ring-2 focus:ring-black focus:ring-offset-2 md:px-4 md:py-2 md:text-sm",children:"Skip"})]}),o.jsx("p",{className:"hidden mt-1 text-sm text-black font-iowan-regular md:block md:text-base",children:"Complete these steps to set up your first update"})]}),o.jsxs("div",{className:"flex flex-col divide-y divide-gray-100",children:[o.jsx(w,{id:1,active:n==1,title:"Add a Team Member",onClick:()=>{sessionStorage.setItem("shouldRefreshCompanyMembers","true"),X(`/member/company/teams?trigger=add&update_id=${d==null?void 0:d.updateId}`)},paragraph:"Invite recipients to view and collaborate on our updates",badge:{show:!0,loading:q==null?void 0:q.myMembers,created:I()}}),o.jsx(w,{id:2,active:n==2,title:"Create a group",onClick:()=>{if(console.log("=== STEP 2: Create Group clicked ==="),console.log("createGroupModalRef:",m),console.log("createGroupModalRef.current:",m==null?void 0:m.current),m!=null&&m.current){console.log("Attempting to click createGroupModalRef.current");try{m.current.click(),console.log("✅ Successfully clicked createGroupModalRef.current")}catch(e){console.error("❌ Error clicking createGroupModalRef.current:",e)}}else console.log("❌ createGroupModalRef.current is not available")},paragraph:"Add and organize verified team members within a group",badge:{show:!0,loading:eo,created:W()}}),o.jsx(w,{id:3,active:n==3,title:"Create an Update",paragraph:"Select an update template or create from a blank",badge:{show:!0,loading:!1,created:ao()},onClick:()=>{console.log("=== PromptSection Step 3 clicked ==="),console.log("Calling handleCreateUpdate..."),io()}}),o.jsx(w,{id:4,active:n==4,title:"Add Recipient/Group",paragraph:"Add either a team member or a group as a recipient",badge:{show:!0,loading:!1,created:no()},onClick:()=>{console.log("=== PromptSection Step 4 clicked ==="),console.log("Calling handleAddRecipient..."),uo()}}),o.jsx(w,{id:5,active:n==5,title:"Add Collaborators",paragraph:"Invite team members to collaborate on your update",badge:{show:!0,loading:!1,created:lo()},onClick:()=>P(6)}),o.jsx(w,{id:6,active:n==6,title:"Send Update",paragraph:"Once complete, send an update to your team or investors",badge:{show:!0,loading:!1,created:co()},onClick:()=>P(7)})]}),o.jsx("div",{className:"px-4 py-3 border-t border-gray-200 md:px-6 md:py-4",children:o.jsxs("div",{className:"flex justify-between items-center",children:[o.jsxs("p",{className:"text-base text-black font-iowan md:text-lg",children:["Step ",n," of 6"]}),o.jsx("button",{onClick:()=>z(),className:"text-base font-medium text-gray-900 font-iowan md:text-lg",children:"Skip for now"})]})})]});return!i||(a==null?void 0:a.awareness)>=3&&!A?null:o.jsxs(o.Fragment,{children:[o.jsx("div",{className:"hidden md:block",children:o.jsx(xo,{modalHeader:!0,title:G,isOpen:g,modalCloseClick:so,classes:{modal:"",modalDialog:"!bg-brown-main-bg md:!min-w-[50%] md:!w-1/2 h-fit overflow-y-auto max-h-[90%] !w-full",modalContent:"!bg-brown-main-bg"},children:Z()})}),o.jsx("div",{className:"md:hidden",children:o.jsx(Co,{isOpen:k,onClose:ro,title:G,TitleclassName:"font-iowan text-lg",children:Z()})}),o.jsx(ko,{buttonRef:m,afterCreate:()=>{console.log("Group created successfully, refetching data..."),H&&H()},type:"update"})]})},he=c.memo(jo);export{he as default};
