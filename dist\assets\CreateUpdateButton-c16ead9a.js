import{j as r}from"./@nextui-org/listbox-0f38ca19.js";import{a as P,u as R,O as U,L}from"./index-46de5032.js";import{u as _}from"./useSubscription-58f7fe18.js";import{M as k}from"./index-dadd4882.js";import{r as o,b as F}from"./vendor-4cdf2bd1.js";import{P as I}from"./PlusIcon-26cedb5d.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const M={free_trial:1,pro:5,"pro yearly":5,business:10,"business yearly":10,enterprise:1/0},se=({setComposeUpdate:h})=>{const j=o.useRef(null),y=o.useRef(null),[u,i]=o.useState(!1),[g,c]=o.useState(!1),{globalState:n,setGlobalState:w}=P(),{loading:t,data:e,processRegisteredDate:v,getSubscription:S,getCustomerSubscription:a,getSentUpdates:b}=_(),{profile:s}=R();F();const N=()=>{var m,f,p,x;if(t!=null&&t.processRegisteredDate||t!=null&&t.subscription)return;if(!(e!=null&&e.subscription)&&!(e!=null&&e.trial_expired)){if((e==null?void 0:e.sentUpdates)>=1){i(!0);return}i(!1);return}if(e!=null&&e.trial_expired&&!(e!=null&&e.subscription)){i(!0);return}const l=(x=(p=(f=(m=e==null?void 0:e.object)==null?void 0:m.plan)==null?void 0:f.nickname)==null?void 0:p.split(" ")[0])==null?void 0:x.trim(),d=M[l]||(l!=null&&l.includes("enterprise")?1/0:0);(e==null?void 0:e.sentUpdates)>=d&&d!==1/0&&i(!0)},C=()=>{i(!1)};return o.useEffect(()=>{s!=null&&s.id&&(a(),S({filter:[`user_id,eq,${s==null?void 0:s.id}`]}),v(s==null?void 0:s.create_at),b(s),n!=null&&n.refreshSubscription&&w("refreshSubscription",!1))},[s==null?void 0:s.id,n==null?void 0:n.refreshSubscription]),r.jsxs(r.Fragment,{children:[r.jsxs("div",{onMouseOver:N,onMouseLeave:C,className:"relative grid h-[15.125rem] min-h-[16rem] grid-cols-1 grid-rows-12 rounded border-[.125rem] border-dashed border-[#1f1d1a] bg-[#F2DFCE] font-iowan text-lg font-medium shadow",children:[t!=null&&t.processRegisteredDate||t!=null&&t.subscription?r.jsx(U,{loading:!0}):null,u?null:r.jsx("div",{className:"row-span-12 flex h-full max-h-full min-h-full w-full items-center justify-center py-12",children:r.jsxs("button",{ref:j,onClick:()=>h(!0),className:"flex flex-col items-center justify-center gap-5",children:[r.jsx(I,{className:"h-6 w-6"}),r.jsx("span",{className:"font-iowan text-[1rem] font-[700] capitalize leading-5",children:"Create an update"})]})}),u?r.jsxs("div",{className:"row-span-12 grid h-full max-h-full min-h-full w-full grid-cols-1 grid-rows-12 items-center justify-center p-5",children:[r.jsx("div",{className:"row-span-8 text-center",children:e!=null&&e.subscription?r.jsx("p",{children:"You have reached your monthly update limit for your current plan."}):e!=null&&e.trial_expired?r.jsx("p",{children:"Please Upgrade your account to create an update!"}):r.jsx("p",{children:"You have used your free update. Please subscribe to send more updates!"})}),r.jsx("div",{className:"row-span-4 w-full",children:r.jsx("button",{ref:y,className:"flex w-full flex-col items-center justify-center gap-5 rounded-[.125rem] bg-primary-black px-4 py-2 font-iowan text-[1rem] font-[700] leading-5 text-white",onClick:()=>{c(!0)},children:e!=null&&e.subscription?"Upgrade Plan":"Subscribe"})})]}):null]}),r.jsx(L,{children:r.jsx(k,{isOpen:g,onClose:()=>{c(!1)},currentPlan:e==null?void 0:e.subscription,onSuccess:()=>{a()}})})]})};export{se as default};
