import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r,b as u,j as f}from"./vendor-4cdf2bd1.js";import"./index-f373d1dd.js";import{A as h,G as b,bE as g,p as j,I as v,w,M as N,t as k,s as y}from"./index-46de5032.js";import{h as n}from"./moment-a9aaa855.js";import{D as C}from"./DocumentTextIcon-54b5e200.js";import{D}from"./DocumentIcon-22c47322.js";import"./@nextui-org/theme-345a09ed.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const M=({onClose:a,title:E="Create an update",classes:Y={modal:"h-full",modalDialog:"h-[90%]",modalContent:""},page:_="",isOpen:c,disableCancel:I=!1})=>{var d;const{dispatch:o,state:i}=r.useContext(h),{dispatch:l,state:s}=r.useContext(b),p=u(),m=((d=s==null?void 0:s[g.createModel])==null?void 0:d.loading)||!1;async function x(){try{const t=await w(l,o,"updates",{name:`Update ${n().format("MMM D, YYYY")}`,user_id:i.user,mrr:0,arr:0,cash:0,burnrate:0,date:n().format("MMM D, YYYY"),public_link_enabled:0,private_link_open:1,company_id:i.company.id},!1);await new N().callRawAPI("/v3/api/custom/goodbadugly/activities/draft",{update_id:t.data},"POST"),t!=null&&t.error||(a&&a(),p(`/member/edit-updates/${t.data}?autofocus=true`))}catch(t){k(o,t.message),t.message!=="TOKEN_EXPIRED"&&y(l,t.message,5e3,"error")}}return e.jsx(j,{isOpen:c,modalCloseClick:a,clickOutToClose:!0,modalHeader:!0,classes:{modal:"w-full",modalDialog:"!w-[100%] md:!w-[40%]",modalContent:""},children:e.jsxs("div",{className:"divide-y divide-[#1f1d1a]/10",children:[e.jsx("div",{className:"flex w-full items-start gap-4 px-6 py-4 hover:bg-brown-main-bg",children:e.jsxs("div",{children:[e.jsxs("div",{className:"flex gap-2",children:["→",e.jsx("p",{className:"text-left text-xl font-semibold",children:"Start Here"})]}),e.jsx("p",{className:"mt-1 font-medium",children:"Create an update by selecting:"})]})}),e.jsxs(f,{className:"flex w-full items-start gap-4 px-6 py-4 hover:bg-brown-main-bg",to:"/member/select-template",children:[e.jsx("div",{className:"flex items-center justify-center gap-2 rounded-[.625rem] border border-[#1f1d1a] p-2",children:e.jsx(C,{className:"h-8 w-8 text-primary-black",strokeWidth:2})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-left text-base font-semibold",children:"New Template Update"}),e.jsx("p",{className:"mt-1 font-normal",children:"Start an update using an existing update templates"})]})]}),e.jsxs(v,{color:"#000000",loading:m,disabled:m,onClick:x,className:"!flex !w-full !items-start !justify-start !gap-4 !bg-brown-main-bg !px-6 !py-4 hover:!bg-brown-main-bg",children:[e.jsx("div",{className:"flex items-center justify-center gap-2 rounded-[.625rem] border border-[#1f1d1a] p-2",children:e.jsx(D,{className:"h-8 w-8 text-primary-black",strokeWidth:2})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-left text-base font-semibold",children:"New Blank Update"}),e.jsx("p",{className:"mt-1 font-normal",children:"Start an update from scratch"})]})]})]})})},Z=r.memo(M);export{Z as default};
