import{j as r}from"./@nextui-org/listbox-0f38ca19.js";import{h as k,r as p}from"./vendor-4cdf2bd1.js";import{A as E}from"./AddButton-51d1b2cd.js";import{P as O}from"./index-0d34eb6c.js";import{M as q}from"./index-dadd4882.js";import{a as A,u as D,O as F,L as c}from"./index-46de5032.js";import{u as I}from"./useDate-14dbf4c5.js";import{u as z}from"./useSubscription-58f7fe18.js";import"./@nextui-org/theme-345a09ed.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const B=new Map([["day","d"],["week","wk"],["month","mo"],["year","yr"]]),ie=()=>{var x,u,d,f,j,h,b,g,w,P,v,y,N;const{globalState:o}=A(),[l,M]=k(),[S,i]=p.useState(!1),{data:e,loading:s,getSubscription:C,getCustomerSubscription:a}=z(),{convertDate:_}=I(),{profile:n}=D(),m=t=>t?t==null?void 0:t.toFixed(2):"0.00";return p.useEffect(()=>{l.get("openManagePlan")==="true"&&(i(!0),l.delete("openManagePlan"),M(l))},[l]),p.useEffect(()=>{n!=null&&n.id&&(a(),C({filter:[`user_id,eq,${n==null?void 0:n.id}`,"cancelled,eq,0","status,eq,'active'","cancelled_at,is"],order:"id,desc",limit:1}))},[n==null?void 0:n.id,o==null?void 0:o.refreshSubscription]),console.log("CurrentPlan Debug:",{subscription:e==null?void 0:e.subscription,plan:e==null?void 0:e.plan,object:e==null?void 0:e.object}),r.jsxs(r.Fragment,{children:[r.jsxs("div",{className:"relative col-span-12 grid h-[19.9375rem] max-h-[19.9375rem] min-h-[19.9375rem] grid-cols-1 grid-rows-12 rounded-[.25rem] border border-primary-black p-[2.5rem] md:col-span-4",children:[[s==null?void 0:s.subscription,s==null?void 0:s.plan].includes(!0)?r.jsx(F,{loading:!0,color:"#1f1d1a",size:10}):null,r.jsx("div",{className:"row-span-2 flex gap-2 text-left font-iowan text-[24px] font-normal leading-[29.74px] ",children:r.jsxs(c,{children:[r.jsx(O,{name:((u=(x=e==null?void 0:e.plan)==null?void 0:x.stripe_product)==null?void 0:u.name)??"free"})," ",((f=(d=e==null?void 0:e.plan)==null?void 0:d.stripe_product)==null?void 0:f.name)??"free"]})}),r.jsxs("div",{className:"row-span-6 flex flex-col justify-between gap-[.875rem]",children:[r.jsxs("span",{className:"",children:[r.jsx("span",{className:"align-super font-iowan text-[36px] font-bold leading-[64.64px] ",children:r.jsx("sup",{children:"$"})}),r.jsxs("span",{className:"font-iowan text-[52px] font-bold leading-[64.64px] ",children:[(h=m((j=e==null?void 0:e.plan)==null?void 0:j.amount))==null?void 0:h.split(".")[0],".",(g=m((b=e==null?void 0:e.plan)==null?void 0:b.amount))==null?void 0:g.split(".")[1]]}),r.jsxs("span",{className:"font-iowan text-[16px] font-bold leading-[64.64px] ",children:["/",(P=(w=e==null?void 0:e.object)==null?void 0:w.plan)!=null&&P.interval?B.get((y=(v=e==null?void 0:e.object)==null?void 0:v.plan)==null?void 0:y.interval):"mo"]})]}),r.jsx("span",{className:"font-Inter text-[16px] font-normal leading-[24px] text-gray-600",children:e!=null&&e.subscription?r.jsxs(r.Fragment,{children:["Renews on ",_(((N=e==null?void 0:e.object)==null?void 0:N.current_period_end)*1e3)]}):null})]}),r.jsx("div",{className:"flex row-span-4 items-end",children:r.jsx(c,{children:r.jsx(E,{showPlus:!1,disabled:[s==null?void 0:s.subscription,s==null?void 0:s.plan].includes(!0),loading:[s==null?void 0:s.subscription,s==null?void 0:s.plan].includes(!0),onClick:()=>{i(!0)},className:"!rounded-[.125rem] !border !border-primary-black  px-4 py-2 ",children:e!=null&&e.subscription?"Manage Plan":"Subscribe"})})})]}),r.jsx(c,{children:r.jsx(q,{isOpen:S,onClose:()=>{i(!1)},currentPlan:e==null?void 0:e.subscription,onSuccess:()=>{a()}})})]})};export{B as IntervalMap,ie as default};
