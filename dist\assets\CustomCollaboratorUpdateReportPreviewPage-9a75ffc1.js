import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{i as v,r as i}from"./vendor-4cdf2bd1.js";import{u as g,a as w,b as k,c as _,L as D}from"./useUpdateCollaborators-e993df7b.js";import{A as L,S as M,z as h,H as A}from"./index-46de5032.js";import{h as l}from"./moment-a9aaa855.js";import{L as C}from"./LockClosedIcon-a004efdc.js";import{L as m,t as F}from"./@headlessui/react-cdd9213e.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const G=()=>{const{id:n}=v(),{update:s,loading:p}=g(n),{notes:o,loading:u}=w(n),{questions:j,loading:f}=k(n),{updateCollaborators:N,loading:b}=_(n),{state:y}=i.useContext(L);if(i.useEffect(()=>{const a=t=>{t.preventDefault()};return window.addEventListener("contextmenu",a),()=>{window.removeEventListener("contextmenu",a)}},[]),i.useEffect(()=>{const a=t=>{(t.key==="F12"||t.ctrlKey&&t.shiftKey&&t.key==="I"||t.ctrlKey&&t.shiftKey&&t.key==="J")&&t.preventDefault()};return window.addEventListener("keydown",a),()=>{window.removeEventListener("keydown",a)}},[]),p||u||f||b)return e.jsx(M,{size:40});const c=s.sent_at?l(s.sent_at).add(s.recipient_access??0,"days").diff(l(),"days"):0;return e.jsxs("div",{className:"h-screen -scroll-mt-6 overflow-y-auto scroll-smooth",children:[" ",e.jsxs("div",{className:"mx-auto w-full max-w-[1400px] px-2 pt-6 xl:px-0",children:[e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"px-8 py-6 xl:px-12",children:e.jsx("img",{src:s.companies.logo,alt:"",className:"h-32 w-36 rounded-[50%] object-cover"})}),e.jsxs("div",{className:"flex w-full items-start justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-normal",children:s.companies.name}),e.jsx("a",{href:s.companies.website,target:"_blank",rel:"noopener noreferrer",className:"block text-lg text-primary-black underline",children:s.companies.website}),e.jsx("p",{className:"mt-2 text-lg",children:s.companies.description}),e.jsxs("h1",{className:"mt-2 text-3xl font-bold xl:text-5xl",children:[e.jsx("span",{className:"",children:s.name})," -"," ",e.jsx("span",{className:"font-normal",children:l(s.date).format("MM/DD/YYYY")}),e.jsx("span",{className:"ml-4 font-normal text-gray-500",children:"{DRAFT}"})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("p",{className:"flex items-center gap-2 whitespace-nowrap rounded-sm bg-blue-100 px-4 py-2 font-medium text-blue-600",children:[e.jsx(C,{className:"h-4 w-4",strokeWidth:2}),"Private Update"]}),e.jsxs("button",{className:"flex h-[40px] items-center gap-2 rounded-md bg-brown-main-bg px-3 py-1",children:[e.jsx(D,{className:"h-4",strokeWidth:2}),"Share"]})]})]})]}),e.jsxs("div",{className:"mt-8 flex items-center justify-between",children:[e.jsxs("p",{className:"flex items-center gap-6 text-lg font-semibold",children:["→ Contributors:"," ",e.jsx("div",{className:"flex -space-x-1",children:N.map(a=>e.jsx(m,{className:"relative",children:({open:t})=>{var r,d,x;return e.jsxs(e.Fragment,{children:[e.jsx(m.Button,{as:"div",className:"cursor-pointer",children:e.jsx("img",{className:"h-7 w-7 rounded-[50%] object-cover",src:((r=a.user)==null?void 0:r.photo)||"/default.png"})}),e.jsx(F,{as:i.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",children:e.jsx(m.Panel,{className:"absolute left-0 z-10 mt-3 w-fit -translate-x-1/2 transform whitespace-nowrap px-4",children:e.jsx("div",{className:"overflow-hidden  rounded-lg bg-[#1f1d1a] p-4 text-white shadow-lg ring-1 ring-[#1f1d1a]/5",children:e.jsxs("div",{className:"text-sm font-medium",children:[(d=a.user)==null?void 0:d.first_name," ",(x=a.user)==null?void 0:x.last_name]})})})})]})}},a.id))})]}),e.jsxs("p",{className:"text-lg font-semibold",children:["→ Available for:"," ",e.jsxs("span",{children:[c<1?"":`${c} days,  `,s.sent_at?l(s.sent_at).add(s.recipient_access,"days").diff(l().add(c,"days"),"hours"):0," ","hrs"]})]})]}),e.jsxs("section",{className:"mt-4 flex items-start gap-4",children:[e.jsxs("aside",{className:"hidden max-w-sm flex-grow lg:block",children:[e.jsx("div",{className:"border border-gray-900",children:e.jsxs("ul",{className:"divide-y divide-gray-900",children:[o.map(a=>{const{blocks:t}=h(a.content,{blocks:[]});return t.length==0?null:e.jsxs("li",{className:"whitespace-nowrap",children:[" ",e.jsx("a",{className:"block px-4 py-6",href:`#${a.type}`,children:a.type})]},a.id)}),e.jsx("li",{children:e.jsx("a",{className:"block px-4 py-6",href:"#investor-asks",children:"Asks"})})]})}),e.jsx("div",{className:"mt-8 text-center",children:e.jsx("a",{href:y.profile.contact_link,target:"_blank",rel:"noopener noreferrer",className:"text-blue-500",children:"📆 Book a meeting"})})]}),e.jsxs("div",{className:"flex-grow",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-8",children:[s.show_financial_metrics==1?e.jsxs("div",{children:[e.jsxs("div",{className:"rounded-sm border border-gray-900 p-4",children:[e.jsx("h4",{className:"mb-3",children:"Financial Overview"}),e.jsxs("ul",{className:"space-y-4",children:[e.jsxs("li",{className:"mb-2 flex items-center justify-center",children:[e.jsx("span",{className:"font-medium",children:"MRR:"})," ",e.jsx("span",{className:"ml-auto block",children:s.mrr})]}),e.jsxs("li",{className:"mb-2 flex items-center justify-center",children:[e.jsx("span",{className:"font-medium",children:"ARR:"})," ",e.jsx("span",{className:"ml-auto block",children:s.arr})]}),e.jsxs("li",{className:"mb-2 flex items-center justify-center",children:[e.jsx("span",{className:"font-medium",children:"Cash:"})," ",e.jsx("span",{className:"ml-auto block",children:s.cash})]}),e.jsxs("li",{className:"mb-2 flex items-center justify-center",children:[e.jsx("span",{className:"font-medium",children:"Burnrate:"})," ",e.jsx("span",{className:"ml-auto block",children:s.burnrate})]}),e.jsxs("li",{className:"mb-2 flex items-center justify-center",children:[e.jsx("span",{className:"font-medium",children:"Runway (months):"})," ",e.jsx("span",{className:"ml-auto block",children:s.burnrate})]})]})]}),e.jsxs("div",{className:"my-2 flex w-full items-center justify-center px-4 text-[1rem]",children:[e.jsx("span",{className:"font-semibold",children:"Last sync:"})," ",e.jsx("span",{className:"ml-auto block",children:s.last_sync?l(s.last_sync).format("DD MMM hh:mm a"):"N/A"})]})]}):null,s.show_investment_metrics==1?e.jsxs("div",{className:"",children:[e.jsxs("div",{className:"rounded-sm border border-gray-900 p-4",children:[e.jsx("h4",{className:"mb-3",children:"Investment Overview"}),e.jsxs("ul",{className:"space-y-4",children:[e.jsxs("li",{className:"mb-2 flex items-center justify-center",children:[e.jsx("span",{className:"font-medium",children:"Investment stage:"})," ",e.jsx("span",{className:"ml-auto block",children:s.investment_stage})]}),e.jsxs("li",{className:"mb-2 flex items-center justify-center",children:[e.jsx("span",{className:"font-medium",children:"Invested to date:"})," ",e.jsx("span",{className:"ml-auto block",children:s.invested_to_date})]}),e.jsxs("li",{className:"mb-2 flex items-center justify-center",children:[e.jsx("span",{className:"font-medium",children:"Fund managers on Cap Table:"})," ",e.jsx("span",{className:"ml-auto block",children:s.investors_on_cap_table})]}),e.jsxs("li",{className:"mb-2 flex items-center justify-center",children:[e.jsx("span",{className:"font-medium",children:"Valuation of last round:"})," ",e.jsx("span",{className:"ml-auto block",children:s.valuation_at_last_round})]}),e.jsxs("li",{className:"mb-2 flex items-center justify-center",children:[e.jsx("span",{className:"font-medium",children:"Date of last round:"})," ",e.jsx("span",{className:"ml-auto block",children:s.date_of_last_round?l(s.date_of_last_round).format("MM/DD/YYYY"):"N/A"})]})]})]}),e.jsxs("div",{className:"my-2 flex w-full items-center justify-center px-4 text-[1rem]",children:[e.jsx("span",{className:"font-semibold",children:"Last sync:"})," ",e.jsx("span",{className:"ml-auto block",children:s.investment_details_last_sync?l(s.investment_details_last_sync).format("DD MMM hh:mm a z"):"N/A"})]})]}):null]}),e.jsxs("div",{className:"mt-20 space-y-20",children:[o.map(a=>{const{blocks:t}=h(a.content,{blocks:[]});if(t.length==0)return null;const r=A(t);return e.jsxs("section",{id:a.type,children:[e.jsxs("h4",{className:"text-lg font-bold",children:[a.type,":"]}),e.jsx("div",{dangerouslySetInnerHTML:{__html:r}})]},a.id)}),e.jsxs("section",{id:"investor-asks",children:[e.jsx("h4",{className:"text-lg font-bold",children:"Asks"}),e.jsx("div",{children:j.map(a=>e.jsx("p",{children:a.question},a.id))})]})]})]})]}),e.jsx("section",{children:e.jsxs("h5",{className:"mt-10 text-end font-medium text-gray-500",children:["Powered by ",e.jsx("span",{className:"font-bold",children:"update"}),"stack"]})})]})]})};export{G as default};
