import{j as i}from"./@nextui-org/listbox-0f38ca19.js";import{r as o}from"./vendor-4cdf2bd1.js";import{T as x}from"./@editorjs/editorjs-3bc58744.js";import{n as T}from"./@editorjs/paragraph-9d333c59.js";import{c as y}from"./@editorjs/header-da8c369a.js";import{d as E}from"./@editorjs/list-86b325f6.js";import{I as k}from"./@editorjs/link-7a38da73.js";import{n as j}from"./@editorjs/delimiter-89018da8.js";import{f as v}from"./@editorjs/checklist-1b2b7ac3.js";import{A as w,G as O,a7 as W,M as C,t as D,s as S}from"./index-46de5032.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const I={paragraph:{class:T,inlineToolbar:!0,config:{placeholder:"Write something",preserveBlank:!0}},checkList:{class:v,inlineToolbar:!0,config:{placeholder:"Write something"}},list:{class:E,inlineToolbar:!0,config:{placeholder:"Write something"}},header:{class:y,inlineToolbar:!0,config:{placeholder:"Write something"}},delimiter:{class:j,inlineToolbar:!0,config:{placeholder:"Write something"}},link:{class:k,inlineToolbar:!0,config:{placeholder:"Write something"}}},sr=o.memo(({data:a,editing:n=!0,editorID:c,note_id:l,afterEdit:m,setUpdated:N,updateSaved:U,report:p=!1,editorRef:s})=>{const r=o.useRef(),{dispatch:u}=o.useContext(w),{dispatch:d}=o.useContext(O),[A,f]=o.useState(!1);async function h(e){try{const g=await new C().callRawAPI(`/v4/api/records/notes/${l}`,{content:JSON.stringify(e)},"PUT");p&&m()}catch(t){D(u,t.message),t.message!=="TOKEN_EXPIRED"&&S(d,t.message,5e3,"error")}}return o.useEffect(()=>{if(!r.current){const e=new x({holder:c,minHeight:30,style:{nonce:W()},readOnly:!n,tools:I,data:a,async onChange(t,g){const b=await t.saver.save();h(b)},onReady:()=>{r.current=e,s&&(s.current=e)}})}return()=>{r.current&&r.current.destroy&&r.current.destroy(),s&&(s.current=null)}},[]),o.useEffect(()=>{var e,t;r.current&&((t=(e=r==null?void 0:r.current)==null?void 0:e.readOnly)==null||t.toggle(!n))},[n]),o.useEffect(()=>{var e,t;r.current&&((t=(e=r==null?void 0:r.current)==null?void 0:e.blocks)==null||t.render(a))},[a,r==null?void 0:r.current]),i.jsxs(i.Fragment,{children:[i.jsx("style",{children:`
      
      .editorjs-container-transparent {
        background-color: #fff0e5 !important;
      }
      .editorjs-container {
        background-color: #fff0e5 !important;
      }
      `}),i.jsx("div",{"data-placeholder":"Write something",className:`bg-brown-main-bg ${n?"editorjs-container border-0":"editorjs-container-transparent"} rounded   px-3 py-2`,id:c,onBlur:()=>f(!0)})]})});export{I as EDITOR_JS_TOOLS,sr as default};
