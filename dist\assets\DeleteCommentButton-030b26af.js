import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{A as g,G as b,I as j,M as y,s as c,t as v}from"./index-46de5032.js";import{r as t,i as w}from"./vendor-4cdf2bd1.js";import{X as C}from"./XMarkIcon-cfb26fe7.js";import{t as r,S as i}from"./@headlessui/react-cdd9213e.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const N=new y;function _({refetch:d,comment:m}){const[p,s]=t.useState(!1),{dispatch:x}=t.useContext(g),{dispatch:n}=t.useContext(b),[l,o]=t.useState(!1);w();const u=()=>{s(!0)},f=async h=>{o(!0);try{const a=await N.callRawAPI(`/v3/api/custom/goodbadugly/updates/comments/${h}`,{},"DELETE");o(!1),d(),c(n,"Deleted"),s(!1)}catch(a){o(!1),v(x,a.message),a.message!=="TOKEN_EXPIRED"&&c(n,a.message,5e3,"error")}};return e.jsxs(e.Fragment,{children:[e.jsx("span",{className:"text-red cursor-pointer text-[12px] font-[500] text-red-500",onClick:()=>u(),children:"Delete"}),e.jsx(r,{appear:!0,show:p,as:t.Fragment,children:e.jsxs(i,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>s(!1),children:[e.jsx(r.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(r.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(i.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-sm shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(i.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Delete this comment?"}),e.jsx("button",{onClick:()=>s(!1),type:"button",children:e.jsx(C,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a]/30 py-2 text-center font-iowan font-medium",type:"button",onClick:()=>s(!1),children:"Cancel"}),e.jsx(j,{loading:l,disabled:l,onClick:()=>f(m.id),className:"rounded-lg bg-[#1f1d1a] py-2 text-center font-iowan font-semibold text-white transition-colors duration-100 disabled:bg-opacity-60",children:"Delete"})]})]})})})})]})})]})}export{_ as default};
