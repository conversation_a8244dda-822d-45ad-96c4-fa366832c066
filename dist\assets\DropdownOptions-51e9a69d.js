import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{r as o}from"./vendor-4cdf2bd1.js";import{L as x,bs as h}from"./index-46de5032.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const v=()=>{const e=navigator.userAgent.toLowerCase();return e.indexOf("safari")!==-1&&e.indexOf("chrome")===-1},B=({icon:e,children:m,childrenWrapperClass:p="",iconWrapperClass:u="",className:l="",style:f={}})=>{const[n,a]=o.useState(!1),s=o.useRef(null),i=o.useRef(null),r=v();o.useEffect(()=>{if(!r)return;const c=d=>{s.current&&i.current&&!s.current.contains(d.target)&&!i.current.contains(d.target)&&a(!1)};return document.addEventListener("mousedown",c),()=>{document.removeEventListener("mousedown",c)}},[r]);const b=()=>{r&&a(!n)};return t.jsxs("div",{style:{...f},className:`flex relative justify-center items-center ${l}`,children:[t.jsx(x,{children:t.jsx("button",{ref:i,className:`relative peer ${u}`,onClick:b,children:e||t.jsx(h,{})})}),t.jsx("div",{ref:s,className:`absolute right-0 top-[85%] z-[9999999999] m-auto ${r?n?"block":"hidden":"hidden hover:block focus:block peer-focus:!block peer-focus-visible:!block"} divide-y-2 divide-[#1F1D1A1A] whitespace-nowrap rounded-lg border border-[#a8a8a8] bg-white text-sm text-[#525252] shadow-md ${p}`,children:m})]})};export{B as default};
