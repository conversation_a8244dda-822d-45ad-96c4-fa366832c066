import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{R as E,b as $,r as m,i as q}from"./vendor-4cdf2bd1.js";import{u as C}from"./react-hook-form-9f4fcfa9.js";import{o as G}from"./yup-c41d85d2.js";import{c as B,a as i}from"./yup-342a5df4.js";import{M as D,A as I,G as M,t as v,s as O}from"./index-46de5032.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";let n=new D;const ne=({activeId:c,setSidebar:U})=>{var h,b,g,f,j,y;const S=B({subject:i().required(),html:i().required(),tag:i().required()}).required(),{dispatch:d}=E.useContext(I),{dispatch:u}=E.useContext(M),k=$(),[T,A]=m.useState(0),[p,P]=m.useState(""),{register:o,handleSubmit:F,setError:x,setValue:l,formState:{errors:a}}=C({resolver:G(S)});q(),m.useEffect(function(){u({type:"SETPATH",payload:{path:"email"}}),async function(){try{n.setTable("email");const t=await n.callRestAPI({id:Number(c)},"GET");t.error||(l("subject",t.model.subject),l("html",t.model.html),l("tag",t.model.tag),P(t.model.slug),A(t.model.id))}catch(t){console.log("error",t),v(d,t.message)}}()},[c]);const R=async t=>{try{const s=await n.callRestAPI({id:T,slug:p,subject:t.subject,html:t.html,tag:t.tag},"PUT");if(!s.error)O(u,"Updated"),k("/admin/email");else if(s.validation){const w=Object.keys(s.validation);for(let r=0;r<w.length;r++){const N=w[r];x(N,{type:"manual",message:s.validation[N]})}}}catch(s){console.log("Error",s),x("html",{type:"manual",message:s.message}),v(d,s.message)}};return e.jsxs("div",{className:"p-5 mx-auto rounded",children:[e.jsx("h4",{className:"text-[16px] font-medium md:text-xl",children:"Edit Email"}),e.jsxs("form",{className:"w-full max-w-lg",onSubmit:F(R),children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"slug",children:"Email Type"}),e.jsx("input",{type:"text",placeholder:"Email Type",value:p,readOnly:!0,className:"focus:shadow-outline} mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-[#1f1d1a] shadow focus:outline-none"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"email",children:"Subject"}),e.jsx("input",{type:"text",placeholder:"subject",...o("subject"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-[#1f1d1a] shadow focus:outline-none ${(h=a.subject)!=null&&h.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(b=a.subject)==null?void 0:b.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"tag",children:"Tags"}),e.jsx("input",{type:"text",placeholder:"tag",...o("tag"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-[#1f1d1a] shadow focus:outline-none ${(g=a.tag)!=null&&g.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(f=a.tag)==null?void 0:f.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"html",children:"Email Body"}),e.jsx("textarea",{placeholder:"Email Body",className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-[#1f1d1a] shadow focus:outline-none ${(j=a.html)!=null&&j.message?"border-red-500":""}`,...o("html"),rows:15}),e.jsx("p",{className:"text-xs italic text-red-500",children:(y=a.html)==null?void 0:y.message})]}),e.jsx("button",{type:"submit",className:"px-4 py-2 font-bold text-white bg-black rounded focus:shadow-outline hover:bg-gray-700 focus:outline-none",children:"Submit"})]})]})};export{ne as default};
