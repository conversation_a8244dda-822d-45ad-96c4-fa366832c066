import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{R as E,b as W,r as c}from"./vendor-4cdf2bd1.js";import{u as Y}from"./react-hook-form-9f4fcfa9.js";import{o as z}from"./yup-c41d85d2.js";import{c as B,a as n}from"./yup-342a5df4.js";import{M as Q,T as X,A as Z,G as ee,t as F,s as R}from"./index-46de5032.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";let P=new Q,te=new X;const Te=({activeId:d,setSidebar:$})=>{var p,x,y,h,g,b,f,j,N,v,w,_,k,S;const q=B({product_id:n().required(),name:n().required(),amount:n().required(),type:n().required(),interval:n().when("type",{is:"recurring",then:t=>t.required(),otherwise:t=>t.notRequired()}),interval_count:n(),usage_type:n().when("type",{is:"recurring",then:t=>t.required(),otherwise:t=>t.notRequired()}),usage_limit:n(),trial_days:n()}).required(),{dispatch:u}=E.useContext(Z),{dispatch:m}=E.useContext(ee),C=W(),[se,A]=c.useState(0),[ae,I]=c.useState("one_time"),[re,M]=c.useState([]),[le,O]=c.useState(0),{register:l,handleSubmit:U,setError:D,setValue:o,watch:G,formState:{errors:a}}=Y({resolver:z(q)}),K=G("type"),L=[{key:"one_time",value:"ONE_TIME",display:"One Time"},{key:"recurring",value:"RECURRING",display:"Recurring"}],H=[{key:"day",value:"day",display:"Daily"},{key:"week",value:"week",display:"Weekly"},{key:"month",value:"month",display:"Monthly"},{key:"year",value:"year",display:"Yearly"}],J=[{key:"licensed",value:"licensed",display:"Licensed"},{key:"metered",value:"metered",display:"Metered"}];c.useEffect(()=>{m({type:"SETPATH",payload:{path:"prices"}}),async function(){try{const t=await P.getStripePrice(d);if(!t.error){const s=t.model,i=JSON.parse(s.object);o("product_id",s.product_id.toString()),o("name",s.name),o("amount",s.amount),o("type",s.type),I(s.type),o("is_usage_metered",s.is_usage_metered===1),o("usage_limit",s.usage_limit),o("trial_days",s.trial_days),A(s.id),O(!!s.status),s.type==="recurring"&&(o("interval",i.recurring.interval),o("interval_count",i.recurring.interval_count),o("usage_type",i.recurring.usage_type))}const r=await P.callRestAPI({},"GET","stripe/product");r.error||M(r.list)}catch(t){console.log("Error",t),F(u,t.message)}}()},[d]);const V=async t=>{try{const r=await te.update("stripe_price",d,{amount:Number(t==null?void 0:t.amount)});if(!r.error)R(m,"Updated",4e3),C("/admin/pricing");else if(r.validation){const s=Object.keys(r.validation);for(let i=0;i<s.length;i++){const T=s[i];D(T,{type:"manual",message:r.validation[T]})}}}catch(r){console.log("Error",r),R(m,r.message,4e3),F(u,r.message)}};return e.jsxs("div",{className:"p-5 mx-auto rounded",children:[e.jsx("h4",{className:"text-[16px] font-medium md:text-xl",children:"Edit Plan"}),e.jsxs("form",{className:"p-4 w-full max-w-lg text-left",onSubmit:U(V),children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"product_id",children:"Product"}),e.jsx("input",{type:"text",className:"px-3 py-2 mb-3 w-full leading-tight text-gray-700 rounded border shadow appearance-none focus:shadow-outline focus:outline-none",...l("product_id"),readOnly:!0}),e.jsx("p",{className:"text-xs italic text-red-500",children:(p=a.product_id)==null?void 0:p.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"name",children:"Name"}),e.jsx("input",{type:"text",placeholder:"Name",...l("name"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(x=a.name)!=null&&x.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(y=a.name)==null?void 0:y.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"amount",children:"Amount"}),e.jsx("input",{type:"number",min:.1,step:"any",placeholder:"Amount",...l("amount"),className:`focus:shadow-outline mb-3 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(h=a.amount)!=null&&h.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(g=a.amount)==null?void 0:g.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"type",children:"Type"}),e.jsx("select",{className:"px-3 py-2 mb-3 w-full leading-tight text-gray-700 rounded border shadow appearance-none focus:shadow-outline focus:outline-none",...l("type"),children:L.map(t=>e.jsx("option",{value:t.key,children:t.display},`type_${t.key}`))}),e.jsx("p",{className:"text-xs italic text-red-500",children:(b=a.type)==null?void 0:b.message})]}),K==="recurring"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"interval",children:"Interval"}),e.jsx("select",{className:"px-3 py-2 mb-3 w-full leading-tight text-gray-700 rounded border shadow appearance-none focus:shadow-outline focus:outline-none",...l("interval"),children:H.map(t=>e.jsx("option",{value:t.value,children:t.display},`interval_${t.key}`))}),e.jsx("p",{className:"text-xs italic text-red-500",children:(f=a.interval)==null?void 0:f.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"interval_count",children:"Interval Count"}),e.jsx("input",{type:"number",step:"1",placeholder:"Interval Count",...l("interval_count"),className:`focus:shadow-outline mb-3 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(j=a.interval_count)!=null&&j.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(N=a.interval_count)==null?void 0:N.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"usage_type",children:"Usage Type"}),e.jsx("select",{className:"px-3 py-2 mb-3 w-full leading-tight text-gray-700 rounded border shadow appearance-none focus:shadow-outline focus:outline-none",...l("usage_type"),children:J.map(t=>e.jsx("option",{value:t.value,children:t.display},`usage_type_${t.key}`))}),e.jsx("p",{className:"text-xs italic text-red-500",children:(v=a.usage_type)==null?void 0:v.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"trial_days",children:"Trial Days"}),e.jsx("input",{type:"number",step:"1",placeholder:"0",...l("trial_days"),className:`focus:shadow-outline mb-3 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(w=a.trial_days)!=null&&w.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(_=a.trial_days)==null?void 0:_.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"usage_limit",children:"Usage Limit"}),e.jsx("input",{type:"number",step:"1",placeholder:"1000",...l("usage_limit"),className:`focus:shadow-outline mb-3 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(k=a.usage_limit)!=null&&k.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(S=a.usage_limit)==null?void 0:S.message})]}),e.jsx("div",{className:"mb-5",children:e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",...l("is_usage_metered"),className:"w-5 h-5 text-blue-600 form-checkbox"}),e.jsx("span",{className:"ml-2 text-sm font-bold text-gray-700",children:"Is Usage Metered"})]})})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("button",{className:"px-4 py-2 font-bold text-white bg-gray-500 rounded focus:shadow-outline hover:bg-gray-700 focus:outline-none",onClick:()=>$(!1),type:"button",children:"Cancel"}),e.jsx("button",{type:"submit",className:"px-4 py-2 font-bold text-white bg-blue-500 rounded focus:shadow-outline hover:bg-blue-700 focus:outline-none",children:"Save"})]})]})]})};export{Te as default};
