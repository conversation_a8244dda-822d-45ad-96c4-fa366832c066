import{_ as h}from"./qr-scanner-cf010ec4.js";import{r as e,i as E}from"./vendor-4cdf2bd1.js";import{j as s}from"./@nextui-org/listbox-0f38ca19.js";import{A as _,G as y,M as w,s as u,t as b}from"./index-46de5032.js";import"./lodash-82bd9112.js";import{u as v}from"./useFundProfile-1f384383.js";import{M as C}from"./index.esm-6fcccbfe.js";import{_ as T}from"./MoonLoader-6f2b5db4.js";const O=e.lazy(()=>h(()=>import("./BackButton-a8bc01b0.js"),["assets/BackButton-a8bc01b0.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-46de5032.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-d88d3663.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css"]));function R({update:j,afterEdit:P}){const{dispatch:d}=e.useContext(_),{dispatch:i}=e.useContext(y),[c,r]=e.useState(!1),[t,l]=e.useState("");E();const[m,p]=e.useState(null),{loading:f,profileData:o,refetch:x}=v();async function g(n){r(!0);try{await new w().callRawAPI("/v3/api/goodbadugly/customer/fund-profile",{display_name:n},"POST"),x(),u(i,"Company name saved")}catch(a){b(d,a.message),a.message!=="TOKEN_EXPIRED"&&u(i,a.message,5e3,"error")}r(!1)}return console.log("profileData >>",o),e.useEffect(()=>{l(o==null?void 0:o.display_name)},[o]),s.jsx("div",{className:"flex flex-row items-center",children:f?s.jsx(T,{size:14}):s.jsxs(s.Fragment,{children:[s.jsx("input",{className:"no-box-shadow focus:shadow-outline appearance-none border-none bg-brown-main-bg p-0 text-3xl text-[18px] font-bold capitalize focus:outline-none",defaultValue:"Company name",value:t,onChange:n=>{l(n.target.value),m&&clearTimeout(m);const a=setTimeout(()=>g(n.target.value),2e3);p(a)},readOnly:c,style:{width:`${t?(t==null?void 0:t.length)+1:14}ch`}}),t?null:s.jsx(C,{size:16})]})})}export{O as B,R as E};
