import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import"./vendor-4cdf2bd1.js";import{p as r,L as p,ba as d}from"./index-46de5032.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const v=({isOpen:m,onClose:o,onSuccess:i,title:a="Edit Payment Method",zIndex:l=null})=>t.jsx(r,{modalHeader:!1,title:a,isOpen:m,zIndex:l,modalCloseClick:o,classes:{modalDialog:" min-h-fit max-h-fit w-full !max-h-[500px] md:!w-fit !max-w-full overflow-y-auto min-w-full md:!min-w-[595px] !min-h-[550px] md:!max-w-[595px] pt-5 !gap-0 !m-0  !rounded-t-2xl",modalContent:"!bg-brown-main-bg !z-10 !mt-0 overflow-y-auto w-full !pt-0",modal:"h-full"},children:m?t.jsx(p,{children:t.jsx(d,{onSuccess:i,onClose:o})}):null});export{v as default};
