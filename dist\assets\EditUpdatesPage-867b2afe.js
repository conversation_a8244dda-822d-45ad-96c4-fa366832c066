import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as t,i as Pe,b as st,L as Ve,u as Bt,h as Jt}from"./vendor-4cdf2bd1.js";import{G as oe,A as ie,u as at,s as $,n as Tt,t as se,M as re,K as Ut,I as Ft,q as Ht,y as Zt,w as Vt,a as bt,N as ae,z as Ze,P as es,Q as ht,V as Ge,p as ts,x as Wt,W as It,L as Mt,X as ss,Y as as,Z as Lt,_ as ns,T as rs}from"./index-46de5032.js";import{o as De}from"./yup-c41d85d2.js";import{u as Ie,C as Rt}from"./react-hook-form-9f4fcfa9.js";import{c as Le,a as ee,b as Ot}from"./yup-342a5df4.js";import{b as is,c as ls}from"./index.esm-be5e1c14.js";import{B as os,a as cs}from"./index.esm-bb52b9ca.js";import{M as Et}from"./index-af54e68d.js";import{a as $t,b as ds}from"./index.esm-3e7472af.js";import{M as Xt}from"./index-dc002f62.js";import{C as ms}from"./CreateGroupModal-9562fe27.js";import{B as Ae}from"./BottomSheet-be14d402.js";import"./react-tooltip-7630c8e3.js";import{u as us}from"./useUpdateQuestions-0b25f483.js";import{InteractiveButton2 as Oe}from"./InteractiveButton-060359e0.js";import{X as ue}from"./XMarkIcon-cfb26fe7.js";import{t as v,S as T,L as H}from"./@headlessui/react-cdd9213e.js";import{u as xs}from"./useUpdate-d2d686a3.js";import{u as hs}from"./useNotes-5360b2a2.js";import{u as fs}from"./useUpdateGroups-478802f2.js";import{h as U}from"./moment-a9aaa855.js";import{I as Ee}from"./InformationCircleIcon-5be2f140.js";import{u as Kt}from"./useDate-14dbf4c5.js";import{E as jt}from"./ExclamationTriangleIcon-2f987159.js";import{C as ps}from"./index-855999b8.js";import{g as gs}from"./react-audio-voice-recorder-a95781ec.js";import{S as bs,M as js}from"./MicrophoneIcon-ed3ea0f8.js";import{u as ws}from"./useNote-95029f8e.js";import"./lodash-82bd9112.js";import{C as ys}from"./Collaborators-21666bae.js";import{I as vs}from"./index.esm-7add6cfb.js";import{P as Ns}from"./index.esm-c839cefc.js";import{G as Re}from"./react-icons-36ae72b7.js";import{c as ft}from"./index.esm-6fcccbfe.js";import{T as He}from"./index-cb9e08c3.js";import{_ as At}from"./qr-scanner-cf010ec4.js";import{u as Qt}from"./useSubscription-58f7fe18.js";import{C as Cs,B as _s}from"./react-spinners-b860a5a3.js";import{D as ks,C as Ss,P as Ts}from"./react-beautiful-dnd-8c5bf182.js";import{C as Ms}from"./index-f503aa1b.js";import{u as Es}from"./useCompanyMember-f698e8a0.js";import{u as Fs}from"./useRecipientGroup-5985e2f4.js";import{u as As}from"./useUpdateCollaborator-daff0e7f.js";import{A as Ps}from"./index-a807e4ab.js";import{c as Ds,U as Is}from"./index-0d5645ff.js";import{C as zt}from"./ClockIcon-a30de2d8.js";import{X as Ls}from"./XMarkIcon-6ed09631.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@hookform/resolvers-fad2f3d1.js";import"./MkdCustomInput-af54c64d.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";import"./index-6edcbb0d.js";import"./@mantine/core-691d33c8.js";import"./react-quill-a78e6fc7.js";import"./useUpdateCollaborators-238d9e50.js";import"./PlusIcon-26cedb5d.js";import"./TrashIcon-e6ce5aef.js";import"./ChevronUpDownIcon-e0f342e0.js";import"./@emotion/react-e6c5671d.js";import"./redux-9de11428.js";import"./react-select-8cfd0d8f.js";import"./react-h5-audio-player-939d37df.js";function Rs({title:s,titleId:d,...r},n){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":d},r),s?t.createElement("title",{id:d},s):null,t.createElement("path",{fillRule:"evenodd",d:"M6.75 2.25A.75.75 0 0 1 7.5 3v1.5h9V3A.75.75 0 0 1 18 3v1.5h.75a3 3 0 0 1 3 3v11.25a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3V7.5a3 3 0 0 1 3-3H6V3a.75.75 0 0 1 .75-.75Zm13.5 9a1.5 1.5 0 0 0-1.5-1.5H5.25a1.5 1.5 0 0 0-1.5 1.5v7.5a1.5 1.5 0 0 0 1.5 1.5h13.5a1.5 1.5 0 0 0 1.5-1.5v-7.5Z",clipRule:"evenodd"}))}const Os=t.forwardRef(Rs),$s=Os;function zs({title:s,titleId:d,...r},n){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":d},r),s?t.createElement("title",{id:d},s):null,t.createElement("path",{fillRule:"evenodd",d:"M7.72 12.53a.75.75 0 0 1 0-1.06l7.5-7.5a.75.75 0 1 1 1.06 1.06L9.31 12l6.97 6.97a.75.75 0 1 1-1.06 1.06l-7.5-7.5Z",clipRule:"evenodd"}))}const qs=t.forwardRef(zs),Ys=qs;function Bs({title:s,titleId:d,...r},n){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":d},r),s?t.createElement("title",{id:d},s):null,t.createElement("path",{fillRule:"evenodd",d:"M16.28 11.47a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 0 1-1.06-1.06L14.69 12 7.72 5.03a.75.75 0 0 1 1.06-1.06l7.5 7.5Z",clipRule:"evenodd"}))}const Us=t.forwardRef(Bs),Hs=Us,Gt=t.forwardRef(({groups:s=[],refetch:d,updateGroups:r=[],refetchUpdateGroups:n,isOwner:i=!1,update:o},b)=>{const j=t.useRef(),M=t.useRef(),[g,u]=t.useState(!1),[c,P]=t.useState(!1),[L,S]=t.useState([]),[h,F]=t.useState([]),A="recipient-select-tooltip",[x,k]=t.useState(!1);console.log(h,"newlySelectedGroups",s,r);const{dispatch:_}=t.useContext(oe),{dispatch:f,state:w}=t.useContext(ie);t.useState([]);const[z,m]=t.useState([]),[X,Z]=t.useState(""),[J,Q]=t.useState(null),{id:V}=Pe();at({isPublic:!1});const pe=st();t.useEffect(()=>{b&&(b.current={button:M.current,openPopover:()=>{const y=document.querySelector(`[data-tooltip-id="${A}"]`);y&&y.click()}})},[b,A]);const je=Le({group_id:ee().required("This field is required")});console.log(s,z,"current"),Ie({resolver:De(je),defaultValues:{group_id:""}});async function C(y=[]){if(console.log(y,"current"),(y==null?void 0:y.length)===0){$(_,"Please select at least one group",5e3,"error");return}if(!x)try{k(!0),Q(()=>y[0]);const Y=await Tt(_,f,{method:"POST",endpoint:"/v3/api/custom/goodbadugly/updates/recipient/group",payload:{update_id:V,groups:y}});if(console.log("Group add response:",Y),!(Y!=null&&Y.error)){let te="Group(s) added successfully";if(console.log(y,"list","newly"),y.length===1&&y[0].group_name){const ge=y[0];ge.type===1||ge.recipient_member&&ge.recipient_member.length>1?te=`Group "${ge.group_name}" added successfully`:te=`${ge.group_name} added successfully`}if($(_,te,5e3,"success"),o!=null&&o.sent_at){const ge=y.map(ce=>ce.type===2?ce.id:ce.type===1&&ce.recipient_member?ce.recipient_member.map(ye=>ye.user_id):ce.id).flat();F(ce=>[...ce,...ge])}n&&n(),d&&d()}}catch(Y){se(f,Y.message),Y.message!=="TOKEN_EXPIRED"&&$(_,Y.message,5e3,"error")}finally{Q(null),k(!1)}}const a=y=>!!(r!=null&&r.find(Y=>Y.group_id==y))||null;async function D(y){const Y=r==null?void 0:r.find(te=>(te==null?void 0:te.group_id)==(y==null?void 0:y.id));if(Y&&!x)try{k(!0),Q(()=>y);const te=await Tt(_,f,{method:"DELETE",endpoint:`/v3/api/custom/goodbadugly/updates/recipient/${Y==null?void 0:Y.id}`});if(!(te!=null&&te.error)){let ge="Group removed successfully";y&&y.group_name&&(y.type===1?ge=`Group "${y.group_name}" removed successfully`:ge=`${y.group_name} removed successfully`),$(_,ge,5e3,"success"),o!=null&&o.sent_at&&F(ce=>ce.filter(ye=>ye.id!==y.id)),n(),d()}}catch(te){se(f,te.message),te.message!=="TOKEN_EXPIRED"&&$(_,te.message,5e3,"error")}finally{Q(()=>null),k(!1)}}t.useEffect(()=>{if(X){console.log(s,"groups");const y=s==null?void 0:s.map(Y=>(Y==null?void 0:Y.group_name.toLowerCase().replace(/\s+/g,"").includes(X.toLowerCase().replace(/\s+/g,"")))?Y:null).filter(Boolean);m(()=>[...y])}else m(()=>[...s])},[X,s==null?void 0:s.length]),console.log(r,s,h,"group"),t.useEffect(()=>{if(o!=null&&o.sent_at&&h.length>0){const y=h.map(Y=>s.find(te=>te.id===Y)).filter(Boolean);S(y)}else S([])},[h,o==null?void 0:o.sent_at,s]),t.useEffect(()=>{F([])},[V]);const B=async()=>{try{(await new re().callRawAPI(`/v3/api/custom/goodbadugly/updates/${V}/send-report`,{specific_groups:h},"POST")).error||($(_,"Update sent to new recipients successfully"),S([]),P(!1),d&&d())}catch(y){se(f,y.message),y.message!=="TOKEN_EXPIRED"&&$(_,y.message,5e3,"error")}},xe=()=>e.jsxs("div",{className:"!shadow-[#1f1d1a]/12 !shadow-0 !flex !w-full min-w-full max-w-full  !flex-col !rounded-[.125rem] !border-0 !border-transparent !bg-brown-main-bg !p-4 md:!border md:!border-[#1f1d1a]",children:[(o==null?void 0:o.sent_at)&&h.length>0&&e.jsxs("div",{className:"mb-4 flex items-center gap-2 border-b border-gray-200 pb-4",children:[e.jsx("input",{type:"checkbox",id:"sendToNewOnly",checked:c,onChange:y=>P(y.target.checked),className:"h-4 w-4 rounded border-gray-300"}),e.jsxs("label",{htmlFor:"sendToNewOnly",className:"text-sm",children:["Send update to newly added recipients (",h.length,")"]}),c&&e.jsx("button",{onClick:B,className:"ml-auto rounded bg-[#1f1d1a] px-3 py-1 text-sm text-white hover:bg-[#1f1d1a]/90",children:"Send"})]}),e.jsxs("div",{className:"relative line-clamp-1 inline-flex h-[2.5rem] w-full cursor-not-allowed overflow-x-hidden truncate rounded-[.125rem] !border !border-[#1f1d1a] text-left text-sm font-medium focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-2",children:e.jsx(os,{className:"h-5 w-5 text-gray-400","aria-hidden":"true"})}),e.jsx("input",{type:"text",value:X,onChange:y=>Z(y.target.value),className:"h-[2.5rem] w-full rounded-[.125rem] border border-transparent bg-transparent px-4 py-2 pl-10 text-sm outline-none focus:border-transparent focus:outline-none focus:ring-2 focus:ring-transparent",placeholder:"Search"})]}),e.jsx("div",{className:"hide-scrollbar relative left-[.0625rem] flex max-h-[17.5rem] flex-col justify-start gap-[6px] overflow-y-auto rounded bg-brown-main-bg pt-[10px] md:pt-[0]",children:(z==null?void 0:z.length)===0&&X!==""?e.jsx("div",{className:"relative cursor-default select-none bg-brown-main-bg p-2 text-[#1f1d1a]",children:"Nothing found."}):z==null?void 0:z.map(y=>e.jsx("button",{type:"button",disabled:!!J||x,"aria-label":"group name",onClick:()=>{a(y==null?void 0:y.id)?D(y):C([y])},className:`flex h-[1.75rem] min-h-[1.75rem] cursor-pointer items-center justify-between gap-2 rounded-[.125rem] border-b border-b-[#1F1D1A1A]  pb-[20px] pt-6 transition-all ease-out sm:h-[1.5rem] sm:max-h-[2.75rem] sm:min-h-[2.75rem] md:pb-2 md:pt-2 ${x?"cursor-not-allowed opacity-50":""}`,children:e.jsxs("span",{className:"flex w-full items-center justify-between gap-3 text-[12px] sm:text-sm",children:[e.jsx("span",{className:"whitespace-nowrap hover:font-bold",children:y==null?void 0:y.group_name}),(J==null?void 0:J.id)==(y==null?void 0:y.id)?e.jsx(Xt,{loading:!0,color:"#1f1d1a"}):a(y==null?void 0:y.id)?e.jsx(is,{}):null]})},y==null?void 0:y.id))}),e.jsx("button",{type:"button",disabled:x,onClick:()=>{var y;j!=null&&j.current&&((y=j==null?void 0:j.current)==null||y.click())},className:`mt-3 flex cursor-pointer items-center gap-2 rounded-[.125rem] p-2 text-left font-inter text-sm font-[600] leading-[1.21rem] underline transition-all ease-out   sm:text-[1rem] ${x?"cursor-not-allowed opacity-50":""}`,children:"+ Add new Group"}),e.jsx("button",{type:"button",disabled:x,onClick:()=>{pe(`/member/company/teams?trigger=add&update_id=${V}`)},className:`mt-2 flex cursor-pointer items-center gap-2 rounded-[.125rem] p-2 text-left font-inter text-sm font-[600] leading-[1.21rem] underline transition-all ease-out   sm:text-[1rem] ${x?"cursor-not-allowed opacity-50":""}`,children:"+ Add new Recipient"})]});return e.jsxs("div",{className:`w-fit min-w-fit max-w-fit sm:self-end ${i?"":"hidden"}`,children:[e.jsx("div",{className:"hidden md:block",children:e.jsx(Et,{display:e.jsxs("div",{className:"mt-2 flex w-fit min-w-fit max-w-fit flex-col gap-2 sm:mt-0 sm:flex-row sm:items-center","data-tooltip-id":A,children:[e.jsx("span",{className:"whitespace-nowrap",children:"Add Recipients"}),e.jsxs("button",{ref:M,disabled:!i,className:"relative flex h-[2.25rem] w-fit items-center justify-center gap-[.625rem] whitespace-nowrap rounded-[.1875rem] border border-[#1f1d1a] bg-brown-main-bg px-[1rem] py-[.5rem] font-iowan text-[#1f1d1a]",children:["Select Recipients",e.jsx($t,{size:25})]})]}),openOnClick:!0,backgroundColor:"#fff0e5",place:"bottom",tooltipClasses:"!w-fit  !rounded-[.125rem] !border-[#1f1d1a] !bg-brown-main-bg !p-0",classNameArrow:"!border-b !border-r !border-primary-black",children:xe()})}),e.jsxs("div",{className:"md:hidden",children:[e.jsxs("div",{className:"mt-2 flex w-fit min-w-fit max-w-fit flex-col gap-2 sm:mt-0 sm:flex-row sm:items-center",children:[e.jsx("span",{className:"whitespace-nowrap",children:"Add Recipients"}),e.jsxs("button",{ref:M,disabled:!i,onClick:()=>u(!0),className:"relative flex h-[2.25rem] w-fit items-center justify-center gap-[.625rem] whitespace-nowrap rounded-[.1875rem] border border-[#1f1d1a] bg-brown-main-bg px-[1rem] py-[.5rem] font-iowan text-[#1f1d1a]",children:["Select Recipients",e.jsx($t,{size:25})]})]}),e.jsx(Ae,{isOpen:g,onClose:()=>u(!1),title:"",children:xe()})]}),e.jsx(ms,{afterCreate:()=>{n(),d()},buttonRef:j})]})}),Zs=Object.freeze(Object.defineProperty({__proto__:null,default:Gt},Symbol.toStringTag,{value:"Module"}));function Vs({investors:s}){const{id:d}=Pe(),{questions:r,refetch:n}=us(d),[i,o]=t.useState(!1),[b,j]=t.useState(!1),[M,g]=t.useState(!1),[u,c]=t.useState(!1),[P,L]=t.useState(!1),[S,h]=t.useState(""),[F,A]=t.useState(""),[x,k]=t.useState(!1),{dispatch:_}=t.useContext(ie),{dispatch:f}=t.useContext(oe),w=Le({question:ee().required("This field is required"),investor_id:ee().required("This field is required")});Ie({resolver:De(w),defaultValues:{question:"",investor_id:""}});const z=t.useCallback(a=>{a&&a.stopPropagation(),window.innerWidth<768?g(!0):j(!0)},[]),m=t.useCallback(a=>{a&&a.stopPropagation(),j(!1)},[]),X=t.useCallback(a=>{a&&a.stopPropagation(),g(!1)},[]),Z=t.useCallback(a=>{a&&a.stopPropagation(),c(!1)},[]),J=t.useCallback(a=>{a&&a.stopPropagation(),L(!1)},[]),Q=()=>{if(!S.trim()){$(f,"Please enter a question",3e3,"error");return}j(!1),g(!1),window.innerWidth<768?L(!0):c(!0)},V=async()=>{if(!F){$(f,"Please select a respondent",3e3,"error");return}k(!0);try{await new re().callRawAPI("/v4/api/records/update_questions",{update_id:d,investor_id:F,question:S},"POST"),$(f,"Question added successfully",5e3,"success"),h(""),A(""),c(!1),L(!1),n()}catch(a){se(_,a.message),a.message!=="TOKEN_EXPIRED"&&$(f,a.message,5e3,"error")}k(!1)};async function pe(a){o(!0);try{await new re().callRawAPI(`/v4/api/records/update_questions/${a}`,{},"DELETE"),$(f,"Question deleted successfully",5e3,"success"),n()}catch(D){se(_,D.message),D.message!=="TOKEN_EXPIRED"&&$(f,D.message,5e3,"error")}o(!1)}const je=()=>e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("h3",{className:"font-iowan text-lg font-semibold leading-6 text-gray-900 md:text-xl",children:"Ask a Question"}),e.jsx("button",{onClick:window.innerWidth<768?X:m,type:"button",children:e.jsx(ue,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-2",children:[e.jsx("p",{className:"mb-4 text-sm text-black",children:"Create the question"}),e.jsx("textarea",{value:S,onChange:a=>h(a.target.value),className:"h-32 w-full rounded-sm border border-[#1f1d1a] bg-brown-main-bg p-3 placeholder:text-[#1f1d1a]/50 focus:outline-none",placeholder:"Enter the question..."})]}),e.jsxs("div",{className:"mt-4 flex justify-end gap-3",children:[e.jsx("button",{type:"button",className:"rounded-sm border border-[#1f1d1a] bg-transparent px-4 py-2 text-sm font-medium text-[#1f1d1a] hover:bg-gray-100",onClick:window.innerWidth<768?X:m,children:"Cancel"}),e.jsx("button",{type:"button",className:"rounded-sm bg-[#1f1d1a] px-4 py-2 text-sm font-medium text-white hover:bg-[#1f1d1a]/90",onClick:Q,children:"Continue"})]})]}),C=()=>e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("h3",{className:"font-iowan text-lg font-semibold leading-6 text-gray-900 md:text-xl",children:"Select Respondent"}),e.jsx("button",{onClick:window.innerWidth<768?J:Z,type:"button",children:e.jsx(ue,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-2",children:[e.jsx("p",{className:"mb-4 text-sm text-black",children:"Create an ask by selecting:"}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:"Respondent"}),e.jsxs("select",{value:F,onChange:a=>A(a.target.value),className:"h-10 w-full rounded-sm border border-[#1f1d1a] bg-brown-main-bg px-3 focus:outline-none",children:[e.jsx("option",{value:"",children:"Select respondent"}),s.map(a=>e.jsx("option",{value:a.id,children:a!=null&&a.first_name&&(a!=null&&a.last_name)?`${a.first_name} ${a.last_name}`:a.email},a.id))]})]})]}),e.jsxs("div",{className:"mt-4 flex justify-end gap-3",children:[e.jsx("button",{type:"button",className:"rounded-sm border border-[#1f1d1a] bg-transparent px-4 py-2 text-sm font-medium text-[#1f1d1a] hover:bg-gray-100",onClick:window.innerWidth<768?J:Z,children:"Cancel"}),e.jsx("button",{type:"button",className:"rounded bg-[#1f1d1a] px-4 py-2 text-sm font-medium text-white hover:bg-[#1f1d1a]/90",onClick:V,disabled:x,children:x?"Saving...":"Save"})]})]});return e.jsxs("div",{className:"mt-12 min-h-[100px] rounded bg-[#F2DFCE] p-5",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("h4",{className:"text-xl font-semibold sm:font-bold",children:"Asks"}),e.jsx("span",{className:"font-iowan-regular text-[12px] font-medium text-black/80 sm:text-sm md:text-base",children:"(only selected respondents will see questions they are asked)"})]}),e.jsx("div",{className:"mt-4 space-y-2",children:r.map(a=>{var D;return e.jsxs("div",{className:"group flex max-w-xl flex-col items-start justify-between",children:[e.jsxs("p",{className:"flex items-center gap-2 font-iowan",children:[a.question,"?",e.jsx("button",{className:"hidden hover:text-red-500 group-hover:block",onClick:()=>pe(a.id),disabled:i,children:e.jsx(ls,{size:15})})]}),e.jsxs("div",{className:"mt-2 flex items-center gap-2",children:[e.jsx("img",{src:((D=a==null?void 0:a.user)==null?void 0:D.photo)||"/default.png",alt:"avatar",className:"h-[20px] min-h-[20px] w-[20px] min-w-[20px] rounded-full object-cover"}),e.jsx("p",{className:"font-iowan text-[14px] text-black",children:a.user.first_name+" "+a.user.last_name})]})]},a.id)})}),e.jsx("div",{className:"my-5 h-[2px] w-full bg-[#1f1d1a]/10"}),e.jsx("div",{className:"mt-4",children:e.jsx("p",{onClick:z,className:"focus:shadow-outline flex h-[33px] w-full cursor-pointer appearance-none items-center rounded-sm border border-[#1f1d1a] bg-brown-main-bg px-3 py-2 leading-tight text-[#1f1d1a] shadow outline-none focus:outline-none md:h-[44px]",children:"Ask a question..."})}),e.jsx("div",{className:"hidden md:block",children:e.jsx(v,{appear:!0,show:b,as:t.Fragment,children:e.jsxs(T,{as:"div",className:"relative z-[50]",onClose:m,children:[e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsx(T.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-lg border border-[#1f1d1a] bg-brown-main-bg p-6 text-left align-middle shadow-xl transition-all",children:je()})})})})]})})}),e.jsx("div",{className:"md:hidden",children:e.jsx(Ae,{isOpen:M,onClose:X,className:"px-1",children:je()})}),e.jsx("div",{className:"hidden md:block",children:e.jsx(v,{appear:!0,show:u,as:t.Fragment,children:e.jsxs(T,{as:"div",className:"relative z-[50]",onClose:Z,children:[e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsx(T.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-lg border border-[#1f1d1a] bg-brown-main-bg p-6 text-left align-middle shadow-xl transition-all",children:C()})})})})]})})}),e.jsx("div",{className:"md:hidden",children:e.jsx(Ae,{isOpen:P,onClose:J,className:"px-1",children:C()})})]})}function Ws({afterRestore:s,template:d,isOwner:r=!1}){const[n,i]=t.useState(!1),{dispatch:o}=t.useContext(ie),{dispatch:b}=t.useContext(oe),[j,M]=t.useState(!1),{id:g}=Pe();console.log(g,"update_id");const u=Ut.filter(P=>P.title===d);async function c(){var P,L,S;M(!0);try{const h=await Ht(b,o,"notes",{filter:[`update_id,eq,${g}`]});if(console.log(h,"noteResult"),((P=h==null?void 0:h.data)==null?void 0:P.length)>0){const F=h.data.map(x=>Zt(b,o,"notes",x.id));await Promise.all(F),console.log("start");const A=(S=(L=u[0])==null?void 0:L.template)==null?void 0:S.map(x=>Vt(b,o,"notes",{update_id:g,type:x,status:0}));await Promise.all(A),console.log("end")}i(!1),s(),$(b,"Default template restored")}catch(h){se(o,h.message),h.message!=="TOKEN_EXPIRED"&&$(b,h.message,5e3,"error")}M(!1)}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"font-iowan font-medium text-[#1f1d1a] underline underline-offset-2",onClick:()=>i(!0),disabled:!r,children:"Clear template"}),e.jsx(v,{appear:!0,show:n,as:t.Fragment,children:e.jsxs(T,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>i(!1),children:[e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(T.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(T.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Are you sure"}),e.jsx("button",{onClick:()=>i(!1),type:"button",children:e.jsx(ue,{className:"h-6 w-6"})})]}),e.jsx("p",{className:"mt-2",children:"Are you sure you want to restore to default?"}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-[.125rem] border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>i(!1),children:"Cancel"}),e.jsx(Ft,{loading:j,disabled:j,onClick:c,className:"disabled:bg-disabledblack h-[36px] !w-auto !rounded-[.125rem] bg-primary-black py-2 text-center font-iowan font-semibold text-white transition-colors duration-100",children:"Yes, restore"})]})]})})})})]})})]})}const pt=({children:s})=>{const[d,r]=t.useState(!1),n=t.useRef(null),i=()=>{n.current&&clearTimeout(n.current),r(!0)},o=()=>{n.current=setTimeout(()=>{r(!1)},100)};return e.jsx(H,{className:"relative inline",children:e.jsxs("div",{onMouseEnter:i,onMouseLeave:o,children:[e.jsx(H.Button,{className:"ml-0",children:e.jsx(Ee,{className:"mt-[3px] h-4 w-4 cursor-pointer",pathClasses:"text-[#1f1d1a]",stroke:"white"})}),d&&e.jsx(v,{show:!0,as:t.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",children:e.jsx(H.Panel,{static:!0,className:"absolute left-0 z-10 mt-3 w-screen max-w-[200px] -translate-x-[30%] transform px-4 text-sm",onMouseEnter:i,onMouseLeave:o,children:e.jsxs("div",{className:"relative rounded-lg bg-[#1f1d1a] px-4 py-3 text-white shadow-lg ring-1 ring-[#1f1d1a]/5",children:[e.jsx("div",{className:"font-medium",children:s}),e.jsx("div",{className:"absolute left-1/2 top-0 h-3 w-3 -translate-x-1/2 -translate-y-1/2 rotate-45 bg-[#1f1d1a]"})]})})})]})})};function Xs({update:s,afterEdit:d,setLastSyncFromModifyModal:r,integrations:n,loading:i}){var m,X,Z,J,Q,V,pe,je;const[o,b]=t.useState(!1);at();const{getMany:j,showToast:M}=bt(),{dispatch:g}=t.useContext(ie),{dispatch:u}=t.useContext(oe),[c,P]=t.useState({mrr:!1,arr:!1}),L=C=>{if(!C)return"";const D=C.toString().replace(/[^\d.]/g,"").split(".");D.length>2&&D.splice(2);const B=D.join("."),xe=parseFloat(B);return isNaN(xe)?"":D.length===2?B:xe.toFixed(2)},S=(C,a)=>{const D=C.target.value;if(D===""){k(a,"");return}k(a,L(D))},h=(C,a)=>{a==="stripe"&&w(C)},F=Le({mrr:ee().required("This field is required"),arr:ee().required("This field is required"),cash:ee(),burnrate:ee()}),{register:A,handleSubmit:x,setValue:k,formState:{isSubmitting:_,errors:f}}=Ie({resolver:De(F),defaultValues:{mrr:s.mrr,arr:s.arr,cash:s.cash,burnrate:s.burnrate}}),w=async C=>{if(!n.stripe){M("No Stripe integration found. Please set up Stripe integration first.",5e3,"error");return}P(a=>({...a,[C]:!0}));try{const a=new re,D=C==="mrr"?"?pull_mrr=1":"?pull_arr=1",B=await a.callRawAPI(`/v3/api/custom/goodbadugly/integrations/stripe/metrics/${n.stripe.id}/${s.id}${D}`,{},"GET");B.data&&(r(B.data.last_sync_at),C==="mrr"&&B.data.mrr!==void 0?(k("mrr",B.data.mrr.toFixed(2).toString()),M("MRR pulled from Stripe successfully",3e3,"success")):C==="arr"&&B.data.arr!==void 0&&(k("arr",B.data.arr.toFixed(2).toString()),M("ARR pulled from Stripe successfully",3e3,"success")))}catch(a){se(g,a.message),a.message!=="TOKEN_EXPIRED"&&M(u,a.message,5e3,"error")}finally{P(a=>({...a,[C]:!1}))}};async function z(C){try{await new re().callRawAPI(`/v4/api/records/updates/${s.id}`,{mrr:C.mrr,arr:C.arr,cash:C.cash,burnrate:C.burnrate,sync:ae.MANUAL},"PUT"),b(!1),d()}catch(a){se(g,a.message),a.message!=="TOKEN_EXPIRED"&&M(u,a.message,5e3,"error")}}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"rounded-md bg-primary-black p-2 font-iowan font-medium text-white",onClick:()=>b(!0),children:"Modify"}),e.jsx(v,{appear:!0,show:o,as:t.Fragment,children:e.jsxs(T,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>b(!1),children:[e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(T.Panel,{as:"form",className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",onSubmit:x(z),children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(T.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Edit update"}),e.jsx("button",{onClick:()=>b(!1),type:"button",children:e.jsx(ue,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("label",{className:"mb-2 flex items-center gap-1 text-sm font-semibold capitalize text-[#1f1d1a]",children:["MRR",e.jsx(pt,{children:"Monthly Recurring Revenue - The predictable revenue generated each month from subscriptions and recurring payments."})]})}),e.jsxs("div",{className:"relative flex items-center gap-2",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx("span",{className:"absolute left-3 top-1/2 -translate-y-1/2 text-gray-500",children:"$"}),e.jsx("input",{type:"text",...A("mrr"),onChange:C=>S(C,"mrr"),className:`focus:shadow-outline w-full appearance-none rounded border border-[#1f1d1a] bg-transparent py-2 pl-7 pr-3 text-sm font-normal leading-tight text-[#1d1f1a] shadow focus:outline-none ${(m=f.mrr)!=null&&m.message?"border-red-500":""}`})]}),e.jsxs(H,{className:"relative",children:[e.jsxs(H.Button,{className:"flex items-center gap-2 rounded border border-[#1f1d1a] px-3 py-2 text-xs capitalize text-[#1f1d1a]",children:["Fetch from",e.jsx("svg",{width:"10",height:"6",viewBox:"0 0 10 6",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M1 1L5 5L9 1",stroke:"#1F1D1A",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})]}),e.jsx(H.Panel,{className:"absolute right-0 z-10 mt-2 w-48 rounded-md bg-brown-main-bg shadow-lg ring-1 ring-black ring-opacity-5",children:e.jsx("div",{className:"py-1",children:e.jsxs("button",{type:"button",onClick:()=>h("mrr","stripe"),className:"flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-brown-main-bg/60",disabled:i||c.mrr,children:[e.jsx("img",{src:"https://cdn.brandfetch.io/idxAg10C0L/w/400/h/400/theme/dark/icon.jpeg",alt:"Stripe",className:`mr-2 h-4 w-4 ${c.mrr?"animate-spin":""}`}),c.mrr?"Fetching...":"Stripe"]})})})]})]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(X=f.mrr)==null?void 0:X.message}),e.jsx("p",{className:"mt-1 text-xs text-gray-700",children:"Last 30 days"})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("label",{className:"mb-2 flex items-center gap-1 text-sm font-semibold capitalize text-[#1f1d1a]",children:["ARR",e.jsx(pt,{children:"Annual Recurring Revenue - The predictable revenue generated each year from subscriptions and recurring payments."})]})}),e.jsxs("div",{className:"relative flex items-center gap-2",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx("span",{className:"absolute left-3 top-1/2 -translate-y-1/2 text-gray-500",children:"$"}),e.jsx("input",{type:"text",...A("arr"),onChange:C=>S(C,"arr"),className:`focus:shadow-outline w-full appearance-none rounded border border-[#1f1d1a] bg-transparent py-2 pl-7 pr-3 text-sm font-normal leading-tight text-[#1d1f1a] shadow focus:outline-none ${(Z=f.arr)!=null&&Z.message?"border-red-500":""}`})]}),e.jsxs(H,{className:"relative",children:[e.jsxs(H.Button,{className:"flex items-center gap-2 rounded border border-[#1f1d1a] px-3 py-2 text-xs capitalize text-[#1f1d1a]",children:["Fetch from",e.jsx("svg",{width:"10",height:"6",viewBox:"0 0 10 6",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M1 1L5 5L9 1",stroke:"#1F1D1A",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})]}),e.jsx(H.Panel,{className:"absolute right-0 z-10 mt-2 w-48 rounded-md bg-brown-main-bg shadow-lg ring-1 ring-black ring-opacity-5",children:e.jsx("div",{className:"py-1",children:e.jsxs("button",{type:"button",onClick:()=>h("arr","stripe"),className:"flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-brown-main-bg/60",disabled:i||c.arr,children:[e.jsx("img",{src:"https://cdn.brandfetch.io/idxAg10C0L/w/400/h/400/theme/dark/icon.jpeg",alt:"Stripe",className:`mr-2 h-4 w-4 ${c.arr?"animate-spin":""}`}),c.arr?"Fetching...":"Stripe"]})})})]})]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(J=f.arr)==null?void 0:J.message}),e.jsx("p",{className:"mt-1 text-xs text-gray-700",children:"Last 12 months"})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("label",{className:"mb-2 flex items-center gap-1 text-sm font-semibold capitalize text-[#1f1d1a]",children:["Cash",e.jsx(pt,{children:"Available cash and cash equivalents that can be quickly accessed for business operations and investments."})]})}),e.jsxs("div",{className:"relative flex-1",children:[e.jsx("span",{className:"absolute left-3 top-1/2 -translate-y-1/2 text-gray-500",children:"$"}),e.jsx("input",{type:"text",...A("cash"),onChange:C=>S(C,"cash"),className:`focus:shadow-outline w-full appearance-none rounded border border-[#1f1d1a] bg-transparent py-2 pl-7 pr-3 text-sm font-normal leading-tight text-[#1d1f1a] shadow focus:outline-none ${(Q=f.cash)!=null&&Q.message?"border-red-500":""}`})]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(V=f.cash)==null?void 0:V.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("label",{className:"mb-2 flex items-center gap-1 text-sm font-semibold capitalize text-[#1f1d1a]",children:["Burnrate",e.jsx(pt,{children:"The rate at which a company spends its cash reserves on operating expenses over time."})]})}),e.jsxs("div",{className:"relative flex-1",children:[e.jsx("span",{className:"absolute left-3 top-1/2 -translate-y-1/2 text-gray-500",children:"$"}),e.jsx("input",{type:"text",...A("burnrate"),onChange:C=>S(C,"burnrate"),className:`focus:shadow-outline w-full appearance-none rounded border border-[#1f1d1a] bg-transparent py-2 pl-7 pr-3 text-sm font-normal leading-tight text-[#1d1f1a] shadow focus:outline-none ${(pe=f.burnrate)!=null&&pe.message?"border-red-500":""}`})]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(je=f.burnrate)==null?void 0:je.message}),e.jsx("p",{className:"mt-1 text-xs text-gray-700",children:"Monthly"})]}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>b(!1),children:"Cancel"}),e.jsx(Oe,{loading:_,disabled:_,type:"submit",className:"disabled:bg-disabledblack rounded-lg bg-primary-black py-2 text-center font-semibold text-white transition-colors duration-100",children:"Save"})]})]})})})})]})})]})}const gt=({children:s,last:d=!1})=>{const[r,n]=t.useState(!1),i=t.useRef(null),o=()=>{i.current&&clearTimeout(i.current),n(!0)},b=()=>{i.current=setTimeout(()=>{n(!1)},100)};return e.jsx(H,{className:"relative inline",children:e.jsxs("div",{onMouseEnter:o,onMouseLeave:b,children:[e.jsx(H.Button,{className:"ml-0",children:e.jsx(Ee,{className:"h-4 w-4 cursor-pointer",pathClasses:"text-[#1f1d1a]",stroke:"white"})}),r&&e.jsx(v,{show:!0,as:t.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",children:e.jsx(H.Panel,{static:!0,className:`absolute left-0 z-10 mt-3 w-screen max-w-[200px] -translate-x-[35%] transform px-4 text-sm ${d?"top-0":"top-full"}`,onMouseEnter:o,onMouseLeave:b,children:e.jsxs("div",{className:"relative rounded-lg bg-[#1f1d1a] px-4 py-3 text-white shadow-lg ring-1 ring-[#1f1d1a]/5",children:[e.jsx("div",{className:"font-medium",children:s}),e.jsx("div",{className:"absolute left-1/2 top-0 h-3 w-3 -translate-x-1/2 -translate-y-1/2 rotate-45 bg-[#1f1d1a]"})]})})})]})})};function Ks({update:s,refetchUpdate:d,showMetric:r}){var k,_;const[n,i]=t.useState(!1);t.useContext(ie),t.useContext(oe),t.useState(!1);const[o,b]=t.useState(!1),[j,M]=t.useState(!1),{profile:g}=at(),{getMany:u,showToast:c}=bt(),{convertDate:P}=Kt(),[L,S]=t.useState(!1),[h,F]=t.useState({stripe:null,plaid:null}),A=f=>{if(!f&&f!==0)return"$0.00";const w=parseFloat(f);return isNaN(w)?"$0.00":`$${w.toFixed(2)}`},x=async()=>{var f,w,z,m,X,Z;if(g!=null&&g.id)try{S(!0);const J=await u("stripe_integration",{filter:[`user_id,eq,${g==null?void 0:g.id}`,`company_id,eq,${(w=(f=g==null?void 0:g.companies)==null?void 0:f[0])==null?void 0:w.id}`]}),Q=await u("plaid_integration",{filter:[`user_id,eq,${g==null?void 0:g.id}`,`company_id,eq,${(m=(z=g==null?void 0:g.companies)==null?void 0:z[0])==null?void 0:m.id}`]});F({stripe:((X=J==null?void 0:J.data)==null?void 0:X[0])||null,plaid:((Z=Q==null?void 0:Q.data)==null?void 0:Z[0])||null})}catch(J){console.error(J),c("Error fetching integrations",5e3,"error")}finally{S(!1)}};return t.useEffect(()=>{(async()=>g!=null&&g.id&&await x())()},[g==null?void 0:g.id]),console.log(h),e.jsxs(e.Fragment,{children:[e.jsxs("button",{className:"flex w-full items-center justify-between text-[16px] font-medium text-[#1f1d1a] md:text-[18px] ",onClick:()=>i(r),children:[e.jsx("span",{children:"Show financial metrics"})," ",r?e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M17.5 18.3333H2.5C2.15833 18.3333 1.875 18.05 1.875 17.7083C1.875 17.3667 2.15833 17.0833 2.5 17.0833H17.5C17.8417 17.0833 18.125 17.3667 18.125 17.7083C18.125 18.05 17.8417 18.3333 17.5 18.3333Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M15.8495 2.89999C14.2328 1.28332 12.6495 1.24166 10.9912 2.89999L9.98283 3.90832C9.89949 3.99166 9.86616 4.12499 9.89949 4.24166C10.5328 6.44999 12.2995 8.21666 14.5078 8.84999C14.5412 8.85832 14.5745 8.86666 14.6078 8.86666C14.6995 8.86666 14.7828 8.83332 14.8495 8.76666L15.8495 7.75832C16.6745 6.94166 17.0745 6.14999 17.0745 5.34999C17.0828 4.52499 16.6828 3.72499 15.8495 2.89999Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M13.0089 9.60832C12.7673 9.49166 12.5339 9.37499 12.3089 9.24166C12.1256 9.13332 11.9506 9.01666 11.7756 8.89166C11.6339 8.79999 11.4673 8.66666 11.3089 8.53332C11.2923 8.52499 11.2339 8.47499 11.1673 8.40832C10.8923 8.17499 10.5839 7.87499 10.3089 7.54166C10.2839 7.52499 10.2423 7.46666 10.1839 7.39166C10.1006 7.29166 9.95892 7.12499 9.83392 6.93332C9.73392 6.80832 9.61726 6.62499 9.50892 6.44166C9.37559 6.21666 9.25892 5.99166 9.14226 5.75832C9.1246 5.72049 9.10752 5.68286 9.09096 5.64544C8.96798 5.36767 8.60578 5.28647 8.39098 5.50126L3.61726 10.275C3.50892 10.3833 3.40892 10.5917 3.38392 10.7333L2.93392 13.925C2.85059 14.4917 3.00892 15.025 3.35892 15.3833C3.65892 15.675 4.07559 15.8333 4.52559 15.8333C4.62559 15.8333 4.72559 15.825 4.82559 15.8083L8.02559 15.3583C8.17559 15.3333 8.38392 15.2333 8.48392 15.125L13.2517 10.3572C13.468 10.1409 13.3864 9.76972 13.105 9.64967C13.0734 9.63615 13.0414 9.62238 13.0089 9.60832Z",fill:"#1F1D1A"})]}):!1]}),e.jsx(v,{appear:!0,show:n,as:t.Fragment,children:e.jsxs(T,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>i(!1),children:[e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(T.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(T.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Financial Metrics"}),e.jsx("button",{onClick:()=>i(!1),type:"button",children:e.jsx(ue,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-4 space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex flex-row items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==ae.MANUAL?"*":"","MRR",e.jsx(gt,{children:"Monthly Recurring Revenue - The predictable revenue generated each month from subscriptions and recurring payments."})]}),e.jsx("p",{children:A(s.mrr)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex flex-row items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==ae.MANUAL?"*":"","ARR",e.jsx(gt,{children:"Annual Recurring Revenue - The yearly value of all recurring revenue normalized for a 12-month period."})]}),e.jsx("p",{children:A(s.arr)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex flex-row items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==ae.MANUAL?"*":"","Cash",e.jsx(gt,{children:"Available cash and cash equivalents that can be quickly accessed for business operations and investments."})]}),e.jsx("p",{children:A(s.cash)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex flex-row items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==ae.MANUAL?"*":"","Burnrate",e.jsx(gt,{last:!0,children:"The rate at which a company spends its cash reserves on operating expenses over time."})]}),e.jsx("p",{children:A(s.burnrate)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:"font-normal",children:"Last sync"}),e.jsxs("p",{children:[" ",j?P(j,{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!0,year:"numeric",timeZoneName:"short",timeZone:"America/Los_Angeles"}).replace(", "," - "):h!=null&&h.stripe?P(Math.max(new Date(((k=h.stripe)==null?void 0:k.last_sync_at)||0).getTime(),new Date(((_=h.plaid)==null?void 0:_.last_sync_at)||0).getTime()),{month:"short",day:"numeric",year:"numeric",hour:"2-digit",minute:"2-digit",hour12:!0,timeZoneName:"short",timeZone:"America/Los_Angeles"}).replace(", "," - "):"N/A"]})]}),e.jsx("div",{className:"grid grid-cols-2 gap-4",children:s.id?e.jsx(Xs,{update:s,loading:L,integrations:h,setLastSyncFromModifyModal:M,afterEdit:d}):null}),o?e.jsxs("div",{className:"mt-4 border border-black/60 bg-brown-main-bg p-3",children:[e.jsxs("p",{className:"flex items-center gap-3 font-semibold",children:[e.jsx(jt,{className:"h-5 text-yellow-500",strokeWidth:2}),"No integrations setup currently."]}),e.jsxs("p",{className:"mt-1",children:["In order to populate financial information in your updates, you must setup integrations with your current financial services"," ",e.jsx(Ve,{to:"/member/integrations",className:"font-semibold text-primary-black underline",children:"here"})]})]}):null]})]})})})})]})})]})}function Qs({update:s,afterEdit:d}){var P,L,S,h,F,A,x,k,_,f;const[r,n]=t.useState(!1),{dispatch:i}=t.useContext(ie),{dispatch:o}=t.useContext(oe),b=Le({investment_stage:ee().required("This field is required"),invested_to_date:ee().required("This field is required"),investors_on_cap_table:ee().required("This field is required"),valuation_at_last_round:ee().required("This field is required"),date_of_last_round:ee().required("This field is required")}),{register:j,handleSubmit:M,formState:{isSubmitting:g,errors:u}}=Ie({resolver:De(b),defaultValues:{investment_stage:s.investment_stage,invested_to_date:s.invested_to_date,investors_on_cap_table:s.investors_on_cap_table,valuation_at_last_round:s.valuation_at_last_round,date_of_last_round:s.date_of_last_round}});async function c(w){try{await new re().callRawAPI(`/v4/api/records/updates/${s.id}`,{investment_stage:w.investment_stage,invested_to_date:w.invested_to_date,valuation_at_last_round:w.valuation_at_last_round,investors_on_cap_table:w.investors_on_cap_table,date_of_last_round:w.date_of_last_round,investment_details_sync:ae.MANUAL},"PUT"),n(!1),d()}catch(z){se(i,z.message),z.message!=="TOKEN_EXPIRED"&&$(o,z.message,5e3,"error")}}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"rounded-md bg-primary-black p-2 font-iowan font-medium text-white",onClick:()=>n(!0),children:"Edit manually"}),e.jsx(v,{appear:!0,show:r,as:t.Fragment,children:e.jsxs(T,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>n(!1),children:[e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(T.Panel,{as:"form",className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",onSubmit:M(c),children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(T.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Edit update"}),e.jsx("button",{onClick:()=>n(!1),type:"button",children:e.jsx(ue,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Investment Stage"}),e.jsx("input",{type:"text",...j("investment_stage"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(P=u.investment_stage)!=null&&P.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(L=u.investment_stage)==null?void 0:L.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Invested to Date"}),e.jsx("input",{type:"text",...j("invested_to_date"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(S=u.invested_to_date)!=null&&S.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(h=u.invested_to_date)==null?void 0:h.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Fund managers on Cap Table"}),e.jsx("input",{type:"text",...j("investors_on_cap_table"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(F=u.investors_on_cap_table)!=null&&F.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(A=u.investors_on_cap_table)==null?void 0:A.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Valuation at Last Round"}),e.jsx("input",{type:"text",...j("valuation_at_last_round"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(x=u.valuation_at_last_round)!=null&&x.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(k=u.valuation_at_last_round)==null?void 0:k.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Date of Last Round"}),e.jsx("input",{type:"date",...j("date_of_last_round"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 pr-5 text-sm font-normal leading-tight   text-[#1d1f1a] shadow focus:outline-none sm:pr-3 ${(_=u.date_of_last_round)!=null&&_.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(f=u.date_of_last_round)==null?void 0:f.message})]}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>n(!1),children:"Cancel"}),e.jsx(Oe,{loading:g,disabled:g,type:"submit",className:"disabled:bg-disabledblack rounded-lg bg-primary-black py-2 text-center font-semibold text-white transition-colors duration-100",children:"Save"})]})]})})})})]})})]})}function Gs({update:s,refetchUpdate:d,showMetric:r}){const[n,i]=t.useState(!1);return t.useContext(ie),t.useContext(oe),t.useState(!1),e.jsxs(e.Fragment,{children:[e.jsxs("button",{className:"flex w-full items-center justify-between text-[16px] font-medium text-[#1f1d1a] md:text-[18px]",onClick:()=>i(r),children:["Show investment metrics"," ",r?e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M17.5 18.3333H2.5C2.15833 18.3333 1.875 18.05 1.875 17.7083C1.875 17.3667 2.15833 17.0833 2.5 17.0833H17.5C17.8417 17.0833 18.125 17.3667 18.125 17.7083C18.125 18.05 17.8417 18.3333 17.5 18.3333Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M15.8495 2.89999C14.2328 1.28332 12.6495 1.24166 10.9912 2.89999L9.98283 3.90832C9.89949 3.99166 9.86616 4.12499 9.89949 4.24166C10.5328 6.44999 12.2995 8.21666 14.5078 8.84999C14.5412 8.85832 14.5745 8.86666 14.6078 8.86666C14.6995 8.86666 14.7828 8.83332 14.8495 8.76666L15.8495 7.75832C16.6745 6.94166 17.0745 6.14999 17.0745 5.34999C17.0828 4.52499 16.6828 3.72499 15.8495 2.89999Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M13.0089 9.60832C12.7673 9.49166 12.5339 9.37499 12.3089 9.24166C12.1256 9.13332 11.9506 9.01666 11.7756 8.89166C11.6339 8.79999 11.4673 8.66666 11.3089 8.53332C11.2923 8.52499 11.2339 8.47499 11.1673 8.40832C10.8923 8.17499 10.5839 7.87499 10.3089 7.54166C10.2839 7.52499 10.2423 7.46666 10.1839 7.39166C10.1006 7.29166 9.95892 7.12499 9.83392 6.93332C9.73392 6.80832 9.61726 6.62499 9.50892 6.44166C9.37559 6.21666 9.25892 5.99166 9.14226 5.75832C9.1246 5.72049 9.10752 5.68286 9.09096 5.64544C8.96798 5.36767 8.60578 5.28647 8.39098 5.50126L3.61726 10.275C3.50892 10.3833 3.40892 10.5917 3.38392 10.7333L2.93392 13.925C2.85059 14.4917 3.00892 15.025 3.35892 15.3833C3.65892 15.675 4.07559 15.8333 4.52559 15.8333C4.62559 15.8333 4.72559 15.825 4.82559 15.8083L8.02559 15.3583C8.17559 15.3333 8.38392 15.2333 8.48392 15.125L13.2517 10.3572C13.468 10.1409 13.3864 9.76972 13.105 9.64967C13.0734 9.63615 13.0414 9.62238 13.0089 9.60832Z",fill:"#1F1D1A"})]}):!1]}),e.jsx(v,{appear:!0,show:n,as:t.Fragment,children:e.jsxs(T,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>i(!1),children:[e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(T.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(T.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Investment Metrics"}),e.jsx("button",{onClick:()=>i(!1),type:"button",children:e.jsx(ue,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-4 space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"text-sm font-normal md:text-base",children:[s.investment_details_sync==ae.MANUAL?"*":"","Investment Stage"]}),e.jsx("p",{children:s.investment_stage??"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"text-sm font-normal md:text-base",children:[s.investment_details_sync==ae.MANUAL?"*":"","Invested to Date"]}),e.jsx("p",{children:s.invested_to_date??"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"text-sm font-normal md:text-base",children:[s.investment_details_sync==ae.MANUAL?"*":"","Fund managers on Cap Table"]}),e.jsx("p",{children:s.investors_on_cap_table??"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"text-sm font-normal md:text-base",children:[s.investment_details_sync==ae.MANUAL?"*":"","Valuation at Last Round"]}),e.jsx("p",{children:s.valuation_at_last_round??"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:"text-sm font-normal md:text-base",children:"Date of Last Round"}),e.jsxs("p",{children:[" ",s.date_of_last_round?U(s.date_of_last_round).format("DD MMM"):"N/A"]})]}),e.jsx("div",{className:"grid grid-cols-2 gap-4",children:s.id?e.jsx(Qs,{update:s,afterEdit:d}):null})]})]})})})})]})})]})}function Js({note_id:s,afterDelete:d}){const[r,n]=t.useState(!1),{dispatch:i}=t.useContext(ie),{dispatch:o}=t.useContext(oe),[b,j]=t.useState(!1);async function M(){j(!0);try{await new re().callRawAPI(`/v4/api/records/notes/${s}`,{},"DELETE"),n(!1),d()}catch(g){se(i,g.message),g.message!=="TOKEN_EXPIRED"&&$(o,g.message,5e3,"error")}j(!1)}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"h-[2.5625rem] w-[4.375rem] min-w-fit rounded-[.125rem] border-[.0625rem] border-[#1f1d1a] bg-brown-main-bg px-4 py-2 font-iowan text-[14px] font-medium text-[#1f1d1a] sm:w-[80px] md:text-[14px]",onClick:()=>n(!0),children:"Delete"}),e.jsx(v,{appear:!0,show:r,as:t.Fragment,children:e.jsxs(T,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>n(!1),children:[e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(T.Panel,{className:"w-full transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all sm:max-w-md",children:[e.jsxs("div",{className:"flex items-center justify-between gap-2",children:[e.jsx(T.Title,{as:"h3",className:"font-iowan text-lg font-semibold leading-6 text-gray-900",children:"Are you sure you want to delete this entry?"}),e.jsx("button",{onClick:()=>n(!1),type:"button",children:e.jsx(ue,{className:"h-6 w-6"})})]}),e.jsx("p",{className:"mt-2",children:"This action cannot be undone."}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>n(!1),children:"Cancel"}),e.jsx(Oe,{loading:b,disabled:b,onClick:M,className:"rounded-lg bg-[#1f1d1a] py-2 text-center font-iowan font-semibold text-white transition-colors duration-100 disabled:bg-opacity-60",children:"Yes, delete"})]})]})})})})]})})]})}function ea({note:s,afterAsking:d,getCurrentContent:r}){const[n,i]=t.useState(!1),[o,b]=t.useState(!1),{dispatch:j}=t.useContext(ie),{dispatch:M}=t.useContext(oe),[g,u]=t.useState(""),[c,P]=t.useState(s==null?void 0:s.content),[L,S]=t.useState(s==null?void 0:s.content);t.useEffect(()=>{P(s==null?void 0:s.content),S(s==null?void 0:s.content)},[s==null?void 0:s.content]),t.useEffect(()=>{if(n||o){const a=async()=>{if(r)try{const B=await r();console.log("Editor content updated:",B),S(B)}catch(B){console.error("Error getting editor content:",B)}};a();const D=setInterval(a,500);return()=>clearInterval(D)}},[n,o,r]);const h=()=>{const a=L||c;if(!a)return!1;const D=Ze(a,{blocks:[]});return pe(D).trim().length>0},F=async()=>{try{if(r){const xe=await r();if(xe){const y=Ze(xe,{blocks:[]});return pe(y).trim().length>0}}const a=L||c;if(!a)return!1;const D=Ze(a,{blocks:[]});return pe(D).trim().length>0}catch(a){return console.error("Error checking content:",a),!1}},A=Le({expandOrShorten:ee().nullable().optional(),rephrase:Ot(),correctGrammar:Ot()}),{register:x,handleSubmit:k,control:_,formState:{isSubmitting:f,errors:w},reset:z,setValue:m,watch:X}=Ie({resolver:De(A),defaultValues:{expandOrShorten:"",rephrase:!1,correctGrammar:!1}});t.useEffect(()=>{console.log("Form state:",{isSubmitting:f,errors:w,expandOption:g,rephrase:X("rephrase"),correctGrammar:X("correctGrammar")})},[f,w,g,X("rephrase"),X("correctGrammar")]);const Z=a=>{g===a?(u(""),m("expandOrShorten","")):(u(a),m("expandOrShorten",a))},J=t.useCallback(a=>{a&&a.stopPropagation(),window.innerWidth<768?b(!0):i(!0)},[]),Q=t.useCallback(a=>{a&&a.stopPropagation(),i(!1)},[]),V=t.useCallback(a=>{a&&a.stopPropagation(),b(!1)},[]);function pe(a){return!a||!a.blocks?"":a.blocks.map(D=>D.type==="header"?`${D.data.text}`:D.type==="list"?D.data.items.map(B=>`• ${B}`).join(`
`):D.type==="paragraph"?D.data.text:"").join(`

`)}async function je(a){var D;console.log("onSubmit called with data:",a),console.log("Form data:",{expandOrShorten:a.expandOrShorten,rephrase:a.rephrase,correctGrammar:a.correctGrammar});try{if(console.log("Starting form submission..."),!await F()){console.log("No content available"),$(M,"No content available to process",3e3,"error");return}if(!a.expandOrShorten&&!a.correctGrammar&&!a.rephrase){console.log("No options selected"),$(M,"Please select at least one AI option to process",3e3,"warning");return}console.log("Content and options validation passed, getting content...");let B=L||c;if(r)try{B=await r(),console.log("Got content from editor:",B)}catch(de){console.error("Error getting current content:",de)}let xe=pe(Ze(B,{blocks:[]}));console.log("Plain text to process:",xe);const y=new re,te=`
        ${a.expandOrShorten?`${a.expandOrShorten} text. 
`:""}
        ${a.rephrase?`Rewrite text. 
`:""}
        ${a.correctGrammar?`Fix grammar. 
`:""}
            ${xe}
        `.trim(),ge=[{role:"system",content:"update the text based on the user request"},{role:"user",content:te}];console.log("Calling AI API with prompt:",ge);const ce=await y.callRawAPI("/v3/api/custom/goodbadugly/ai/ask",{temperature:1,prompt:ge},"POST");if(console.log("AI API result:",ce),!((D=ce==null?void 0:ce.data)!=null&&D.content))throw new Error("No response received from AI service");xe=ce.data.content;const ye=JSON.stringify({time:Date.now(),blocks:[{id:Math.floor(Math.random()*999)+1,type:"paragraph",data:{style:"unordered",text:xe}}]});console.log("Saving new content:",ye),await y.callRawAPI(`/v4/api/records/notes/${s.id}`,{content:ye},"PUT"),P(ye),d(ye),$(M,"Content processed successfully!",3e3,"success"),i(!1),b(!1),z({expandOrShorten:"",rephrase:!1,correctGrammar:!1}),u(""),m("expandOrShorten",""),console.log("Form submission completed successfully")}catch(B){console.error("AI processing error:",B),se(j,B.message),B.message!=="TOKEN_EXPIRED"&&$(M,B.message,5e3,"error")}}const C=()=>e.jsxs("div",{className:"flex flex-col",children:[!h()&&e.jsx("div",{className:"mb-6 rounded-md border border-yellow-200 bg-yellow-50 p-4",children:e.jsxs("p",{className:"font-iowan text-sm text-yellow-800",children:[e.jsx("strong",{children:"No content available."})," Please add some content to this section first to use AI features."]})}),e.jsxs("form",{onSubmit:k(je),className:"flex flex-col",children:[e.jsx("div",{className:"gap--[80px] flex flex-col",children:e.jsxs("div",{className:"flex items-center gap-8",children:[e.jsxs("div",{className:"bold flex items-center gap-4 text-lg",children:[e.jsx("input",{type:"radio",id:"expand",name:"expandShorten",checked:g==="expand",onChange:()=>Z("expand"),className:"h-4 w-4 border-[2px] border-[#1f1d1a] bg-brown-main-bg checked:bg-[#1f1d1a]"}),e.jsx("label",{htmlFor:"expand",children:"Expand"})]}),e.jsxs("div",{className:"bold flex items-center gap-4 text-lg",children:[e.jsx("input",{type:"radio",id:"shorten",name:"expandShorten",checked:g==="shorten",onChange:()=>Z("shorten"),className:"h-4 w-4 border-[2px] border-[#1f1d1a] bg-brown-main-bg checked:bg-[#1f1d1a]"}),e.jsx("label",{htmlFor:"shorten",children:"Shorten"})]})]})}),e.jsxs("div",{className:"bold mt-4 flex items-center gap-4 text-lg",children:[e.jsx(Rt,{name:"rephrase",control:_,render:({field:a})=>e.jsx("input",{type:"checkbox",checked:a.value,onChange:D=>a.onChange(D.target.checked),className:"border-[2px] border-[#1f1d1a] bg-brown-main-bg accent-black"})}),e.jsx("label",{children:"Rephrase"})]}),e.jsxs("div",{className:"bold mt-4 flex items-center gap-4 text-lg",children:[e.jsx(Rt,{name:"correctGrammar",control:_,render:({field:a})=>e.jsx("input",{type:"checkbox",checked:a.value,onChange:D=>a.onChange(D.target.checked),className:"border-[2px] border-[#1f1d1a] bg-brown-main-bg accent-black"})}),e.jsx("label",{children:"Correct Grammar"})]}),e.jsx(Oe,{type:"submit",loading:f,disabled:f||!h()||!g&&!X("rephrase")&&!X("correctGrammar"),onClick:()=>console.log("Process button clicked, isSubmitting:",f),className:"mt-6 w-full rounded bg-[#1f1d1a] px-4 py-2 font-iowan font-bold text-white disabled:cursor-not-allowed disabled:bg-gray-400",children:"Process"})]})]});return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"h-[2.5625rem] w-[4.375rem] whitespace-nowrap rounded-[.125rem] border-[.0625rem] border-[#1f1d1a] bg-[#1f1d1a] px-2 py-2 font-iowan text-[.875rem] font-medium text-white hover:bg-[#1f1d1a]/90 sm:w-[5rem] sm:px-4 sm:text-[.875rem] md:text-sm",onClick:J,title:"Ask AI to improve your content",children:"Ask AI"}),e.jsx("div",{className:"hidden md:block",children:e.jsx(v,{appear:!0,show:n,as:t.Fragment,children:e.jsxs(T,{as:"div",className:"relative z-[50] hidden md:block",onClose:Q,children:[e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 hidden bg-[#1f1d1a] bg-opacity-25 md:block"})}),e.jsx("div",{className:"fixed inset-0 hidden overflow-y-auto md:block",children:e.jsx("div",{className:"hidden min-h-full items-center justify-center p-4 text-center md:flex",children:e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(T.Panel,{className:"askai-dialog w-full max-w-full transform overflow-hidden rounded-md border border-[#1f1d1a] bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all sm:w-[400px]",children:[e.jsxs("div",{className:"flex items-center justify-between pb-5",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("svg",{width:"24",height:"25",viewBox:"0 0 24 25",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M12.9476 6.89924C12.7636 6.52965 12.2364 6.52965 12.0524 6.89924L10.0822 10.8574C10.0337 10.9548 9.95475 11.0337 9.85736 11.0822L5.89924 13.0524C5.52965 13.2364 5.52965 13.7636 5.89924 13.9476L9.85736 15.9178C9.95475 15.9663 10.0337 16.0452 10.0822 16.1426L12.0524 20.1008C12.2364 20.4704 12.7636 20.4704 12.9476 20.1008L14.9178 16.1426C14.9663 16.0452 15.0452 15.9663 15.1426 15.9178L19.1008 13.9476C19.4704 13.7636 19.4704 13.2364 19.1008 13.0524L15.1426 11.0822C15.0452 11.0337 14.9663 10.9548 14.9178 10.8574L12.9476 6.89924Z",stroke:"#1F1D1A",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M5.5 4.5C5.13084 5.72076 4.70251 6.1348 3.5 6.5C4.6962 6.87751 5.136 7.31356 5.5 8.5C5.85347 7.28434 6.30137 6.84994 7.5 6.5C6.30999 6.14978 5.86661 5.71912 5.5 4.5Z",stroke:"#1F1D1A",strokeWidth:"2",strokeLinejoin:"round"})]}),e.jsx(T.Title,{as:"h3",className:"font-iowan text-xl font-bold leading-6 text-black",children:"Ask AI"})]}),e.jsx("button",{onClick:Q,type:"button",children:e.jsx(ue,{className:"h-6 w-6"})})]}),C()]})})})})]})})}),e.jsx("div",{className:"md:hidden",children:e.jsxs(Ae,{isOpen:o,onClose:V,children:[e.jsxs("div",{className:"flex items-center justify-between pb-5",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("svg",{width:"24",height:"25",viewBox:"0 0 24 25",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M12.9476 6.89924C12.7636 6.52965 12.2364 6.52965 12.0524 6.89924L10.0822 10.8574C10.0337 10.9548 9.95475 11.0337 9.85736 11.0822L5.89924 13.0524C5.52965 13.2364 5.52965 13.7636 5.89924 13.9476L9.85736 15.9178C9.95475 15.9663 10.0337 16.0452 10.0822 16.1426L12.0524 20.1008C12.2364 20.4704 12.7636 20.4704 12.9476 20.1008L14.9178 16.1426C14.9663 16.0452 15.0452 15.9663 15.1426 15.9178L19.1008 13.9476C19.4704 13.7636 19.4704 13.2364 19.1008 13.0524L15.1426 11.0822C15.0452 11.0337 14.9663 10.9548 14.9178 10.8574L12.9476 6.89924Z",stroke:"#1F1D1A",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M5.5 4.5C5.13084 5.72076 4.70251 6.1348 3.5 6.5C4.6962 6.87751 5.136 7.31356 5.5 8.5C5.85347 7.28434 6.30137 6.84994 7.5 6.5C6.30999 6.14978 5.86661 5.71912 5.5 4.5Z",stroke:"#1F1D1A",strokeWidth:"2",strokeLinejoin:"round"})]}),e.jsx("h3",{className:"font-iowan text-xl font-bold leading-6 text-black",children:"Ask AI"})]}),e.jsx("button",{onClick:V,type:"button",children:e.jsx(ue,{className:"h-6 w-6"})})]}),C()]})})]})}function ta({note:s,afterEdit:d}){const[r,n]=t.useState(!1),{dispatch:i}=t.useContext(ie),{dispatch:o}=t.useContext(oe),[b,j]=t.useState(!1),[M,g]=t.useState(null),[u,c]=t.useState(!1),{startRecording:P,stopRecording:L,recordingBlob:S,isRecording:h}=gs();t.useEffect(()=>{S&&(console.log("setting blob"),g(S))},[S]),t.useEffect(()=>{!M||!b||F()},[M,b]);async function F(){n(!0);try{let x=new FormData;const k=new File([M],"audio.wav",{type:"audio/wav"});x.append("file",k),console.log("f",M);const f=await new re().callTranscribe("/v3/api/custom/goodbadugly/ai/transcribe-audio",x,"POST");console.log(f);const w=Ze(s.content,{blocks:[]});A(JSON.stringify({time:Date.now(),blocks:[...w.blocks,{id:Math.floor(Math.random()*999)+1,type:"list",data:{style:"unordered",items:[f.data]}}]}))}catch(x){se(i,x.message),x.message!=="TOKEN_EXPIRED"&&$(o,x.message,5e3,"error")}n(!1),j(!1),g(null)}async function A(x){c(!0);try{await new re().callRawAPI(`/v4/api/records/notes/${s.id}`,{content:x},"PUT"),d()}catch(k){se(i,k.message),k.message!=="TOKEN_EXPIRED"&&$(o,k.message,5e3,"error")}c(!1)}return e.jsx("button",{onClick:async()=>{h?(L(),j(!0)):P()},disabled:r||u,className:`focus:shadow-outline rounded-[.125rem] ${h?"animate-pulse border-red-500":"border-[#1f1d1a]"}  border-[.0625rem]  px-4 py-2`,title:"Transcribe more",children:h?e.jsx("div",{className:"h-6 w-6 rounded-[50%]  bg-red-500",children:e.jsx(bs,{className:"h-6 w-6 text-white"})}):e.jsx(js,{className:"h-6 w-6 text-[#1f1d1a]",strokeWidth:2})})}function sa({note:s,afterEdit:d}){const{dispatch:r}=t.useContext(ie),{dispatch:n}=t.useContext(oe),[i,o]=t.useState(!1),[b,j]=t.useState(""),[M,g]=t.useState(null);t.useEffect(()=>{j(s.type)},[s]);async function u(c){o(!0);try{await new re().callRawAPI(`/v4/api/records/notes/${s.id}`,{type:c},"PUT"),d(),$(n,"Saved")}catch(P){se(r,P.message),P.message!=="TOKEN_EXPIRED"&&$(n,P.message,5e3,"error")}o(!1)}return e.jsx(e.Fragment,{children:e.jsx("input",{className:`no-box-shadow w-full border-none font-inter text-[1.25rem] font-[600] leading-[1.5rem] ${b==="Section title"?"bg-brown-main-bg":"bg-transparent"}  p-0 text-xl font-semibold ring-transparent`,value:b,autoFocus:b==="Section title",readOnly:b==="Section title"?!1:i,onChange:c=>{j(c.target.value),M&&clearTimeout(M);const P=setTimeout(()=>u(c.target.value),2e3);g(P)}})})}const aa=t.memo(({note:s,refetch:d,dragHandleProps:r,setEdited:n,isOwner:i=!1,collaborator:o=null,isCollaborator:b=!1})=>{const[j,M]=t.useState(!1),[g,u]=t.useState(s),{note:c,arrayData:P,refetch:L}=ws(s.id,s);t.useContext(ie);const{convertDate:S}=Kt(),h=t.useRef(null),F=t.useMemo(()=>Ze(c==null?void 0:c.content,{blocks:[{id:"zbGZFPM-iI",type:"paragraph",data:{text:""}}]}),[c==null?void 0:c.content]),A=t.useCallback(()=>{M(!0),setTimeout(()=>M(!1),2e3)},[]),x=t.useCallback(async()=>{if(h.current)try{const w=await h.current.saver.save();return JSON.stringify(w)}catch(w){return console.error("Error getting editor content:",w),(c==null?void 0:c.content)||""}return(c==null?void 0:c.content)||""},[c==null?void 0:c.content]);t.useEffect(()=>{s&&L()},[s==null?void 0:s.id]),t.useEffect(()=>{c&&c!==g&&u(c)},[c]);const k=t.useCallback(async w=>{u(z=>({...z,content:w,update_at:new Date().toISOString()})),await d(),await L(),A()},[d,L]),_=t.useCallback(w=>{const z=Math.abs(U(w).diff(U(),"hours")),m=Math.abs(U(w).diff(U(),"years"));return z<24?S(w,{hour:"2-digit",minute:"2-digit",hour12:!0,timeZoneName:"short",timeZone:"America/Los_Angeles"}).replace(", "," - "):m>0?S(w,{year:"numeric",month:"numeric",day:"numeric",timeZoneName:"short",timeZone:"America/Los_Angeles"}).replace(", "," - "):S(w,{month:"numeric",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!0,timeZoneName:"short",timeZone:"America/Los_Angeles"}).replace(", "," - ")},[S]),f=t.useMemo(()=>`editorjs-container-${c.id}`,[c.id]);return e.jsx("div",{className:`${!(i||b&&(o==null?void 0:o.note_id)==(c==null?void 0:c.id))&&"overlayed bg-[#F2DFCE] p-4"}
        relative flex flex-col justify-between gap-4`,children:e.jsxs("div",{className:"flex flex-col justify-between",children:[e.jsxs("div",{className:"flex w-full flex-col items-start justify-between sm:flex-row sm:items-center",children:[e.jsxs("div",{className:"group mt-5 flex flex-col gap-[.25rem]",children:[e.jsxs("div",{className:"flex items-center gap-[.25rem]",children:[e.jsx("div",{...r,className:"cursor-grab",children:e.jsx(es,{})}),e.jsx(sa,{note:c,afterEdit:L})]}),e.jsxs("p",{className:"flex flex-row items-center gap-2 pl-1 font-iowan text-[1rem] font-[700] leading-[1.25rem]",children:[j&&e.jsx(vs,{color:"green",size:20}),e.jsx("span",{className:"",children:"Last Saved"}),_(c.update_at)]})]}),e.jsx(ys,{isOwner:i,note_id:c.id})]}),e.jsxs("div",{className:"relative mt-4 w-full space-y-4 self-end",children:[!(i||b&&(o==null?void 0:o.note_id)==(c==null?void 0:c.id))&&e.jsx("div",{className:"absolute inset-0 left-[-10px] top-[-10px] z-[9999] h-full max-h-full min-h-[110%] w-full min-w-[102%] max-w-full  md:min-h-[107%]"}),e.jsxs("div",{className:"",children:[e.jsx(ps,{data:F,note_id:c.id,editorID:f,afterEdit:L,setUpdated:M,updateSaved:A,editorRef:h}),e.jsx("div",{className:"mt-4 flex items-center justify-start md:justify-end",children:e.jsxs("div",{className:"flex items-center gap-2 sm:gap-4",children:[e.jsx(ta,{note:c,afterEdit:L}),e.jsx(Js,{note_id:c.id,afterDelete:d}),e.jsx(ea,{note:c,afterAsking:k,getCurrentContent:x},`ask-ai-${c.id}-${c.content?c.content.length:0}`)]})})]})]})]})})},(s,d)=>{var r,n;return s.note.id===d.note.id&&s.note.content===d.note.content&&s.note.update_at===d.note.update_at&&s.isOwner===d.isOwner&&s.isCollaborator===d.isCollaborator&&((r=s.collaborator)==null?void 0:r.note_id)===((n=d.collaborator)==null?void 0:n.note_id)});function na(s){return Re({tag:"svg",attr:{fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.5 18.75h-9m9 0a3 3 0 013 3h-15a3 3 0 013-3m9 0v-3.375c0-.621-.503-1.125-1.125-1.125h-.871M7.5 18.75v-3.375c0-.621.504-1.125 1.125-1.125h.872m5.007 0H9.497m5.007 0a7.454 7.454 0 01-.982-3.172M9.497 14.25a7.454 7.454 0 00.981-3.172M5.25 4.236c-.982.143-1.954.317-2.916.52A6.003 6.003 0 007.73 9.728M5.25 4.236V4.5c0 2.108.966 3.99 2.48 5.228M5.25 4.236V2.721C7.456 2.41 9.71 2.25 12 2.25c2.291 0 4.545.16 6.75.47v1.516M7.73 9.728a6.726 6.726 0 002.748 1.35m8.272-6.842V4.5c0 2.108-.966 3.99-2.48 5.228m2.48-5.492a46.32 46.32 0 012.916.52 6.003 6.003 0 01-5.395 4.972m0 0a6.726 6.726 0 01-2.749 1.35m0 0a6.772 6.772 0 01-3.044 0"}}]})(s)}function ra(s){return Re({tag:"svg",attr:{viewBox:"0 0 24 24"},child:[{tag:"g",attr:{id:"Microphone_On"},child:[{tag:"g",attr:{},child:[{tag:"path",attr:{d:"M11.989,2.065a4.507,4.507,0,0,0-4.5,4.5v5.76a4.5,4.5,0,0,0,9,0V6.565A4.507,4.507,0,0,0,11.989,2.065Zm0,13.76a3.5,3.5,0,0,1-3.5-3.5V6.565a3.5,3.5,0,0,1,6.94-.62h-1.87a.5.5,0,0,0-.5.5.5.5,0,0,0,.5.5h1.93v2h-1.93a.5.5,0,0,0-.5.5.508.508,0,0,0,.5.5h1.93v2h-1.94a.508.508,0,0,0-.5.5.515.515,0,0,0,.5.5h1.88A3.492,3.492,0,0,1,11.989,15.825Z"}},{tag:"path",attr:{d:"M12.489,18.925v2.01h3.5a.5.5,0,0,1,0,1h-8a.5.5,0,0,1,0-1h3.5v-1.99a6.055,6.055,0,0,1-2.74-.88,6.291,6.291,0,0,1-2.97-5.14c-.03-1.04,0-2.09,0-3.13a.5.5,0,0,1,1,0c0,1.04-.03,2.09,0,3.13A5.212,5.212,0,0,0,17.2,12.7c.01-.96,0-1.93,0-2.9a.5.5,0,0,1,1,0,26.322,26.322,0,0,1-.08,3.97A6.235,6.235,0,0,1,12.489,18.925Z"}}]}]}]})(s)}function ia(s){return Re({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"rect",attr:{x:"2",y:"3",width:"20",height:"14",rx:"2",ry:"2"}},{tag:"line",attr:{x1:"8",y1:"21",x2:"16",y2:"21"}},{tag:"line",attr:{x1:"12",y1:"17",x2:"12",y2:"21"}}]})(s)}function la(s){return Re({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M311.133 257.95a15.074 15.074 0 0 1-12.845 17.026l-147.248 20.61-21.33 32.522-82.637 11.57 21.33-32.568-24.547 3.44c-.278 0-.555.12-.843.165a15.218 15.218 0 0 1-2.108.144 15.074 15.074 0 0 1-2.074-30.016c.29 0 .567 0 .855-.078l24.547-3.438-29.45-25.512 82.582-11.547 29.45 25.51 147.26-20.608a15.196 15.196 0 0 1 2.107-.145 15.085 15.085 0 0 1 14.953 12.923zm-36.704-38.546a32.4 32.4 0 0 1 10.847-10.326 23.427 23.427 0 0 1 13.422-3.04 27.875 27.875 0 0 1 13.542 5.047 44.557 44.557 0 0 1 11.924 12.59 66.342 66.342 0 0 1 8.386 19.134 77.48 77.48 0 0 1 2.562 21.995 67.895 67.895 0 0 1-3.494 19.966 46.132 46.132 0 0 1-8.54 15.352 28.163 28.163 0 0 1-12.402 8.552 23.382 23.382 0 0 1-13.765.255 32.012 32.012 0 0 1-12.512-7.122 45.478 45.478 0 0 1-5.734-6.2l-32.278 4.514a131.154 131.154 0 0 0 7.1 15.973 104.566 104.566 0 0 0 18.656 25.512 69.016 69.016 0 0 0 23.893 15.806 48.373 48.373 0 0 0 27.597 2.22 53.43 53.43 0 0 0 26.31-14.876c7.898-7.853 14.42-18.258 19.112-30.514a131.997 131.997 0 0 0 8.32-41.995 153.26 153.26 0 0 0-5.48-46.92 128.758 128.758 0 0 0-18.49-39.932c-7.6-10.726-16.417-18.946-25.78-24.403a52.998 52.998 0 0 0-27.962-7.62 48.573 48.573 0 0 0-26.278 8.718 69.88 69.88 0 0 0-20.165 21.897 107.505 107.505 0 0 0-11.99 29.516A137.144 137.144 0 0 0 237.68 235l31.192-4.37a49.172 49.172 0 0 1 5.557-11.226zm198.305-34.984c-10.926-35.274-27.287-64.757-46.842-87.374-18.557-21.518-39.544-36.26-61.118-44.213-20.155-7.41-40.564-8.74-59.953-4.248-18.058 4.204-35.196 13.466-50.603 27.62-14.42 13.21-26.09 29.626-35.185 47.807a233.224 233.224 0 0 0-19.29 56.57 286.023 286.023 0 0 0-5.856 60.674l22.582-3.16a209.143 209.143 0 0 1 5.047-40.344 166.26 166.26 0 0 1 15.972-42.926c7.365-13.4 16.716-25.124 27.997-34.087 11.89-9.44 24.88-14.986 38.3-16.64 14.165-1.774 28.773.744 42.938 7.51 14.863 7.1 29.084 18.78 41.485 34.774 12.856 16.572 23.515 37.46 30.66 61.917a225.515 225.515 0 0 1 8.74 74.65c-1.254 24.05-6.4 46.422-14.72 65.656-8.042 18.58-18.857 33.887-31.824 44.88-12.313 10.47-26.345 16.915-41.463 18.656a76.226 76.226 0 0 1-41.163-7.1c-12.313-5.722-23.826-14.485-34.03-25.51a162.212 162.212 0 0 1-25.724-37.637 204.584 204.584 0 0 1-14.542-38.578l-22.484 3.106a280.965 280.965 0 0 0 19.966 57.823 228.2 228.2 0 0 0 32.168 50.092c12.99 15.186 27.82 27.83 43.914 36.793 17.18 9.574 36.027 15.064 55.705 14.865 21.263-.21 41.44-7.022 59.52-19.778 19.356-13.654 36.005-33.897 48.617-59.432 13.244-26.82 21.697-58.788 24.048-93.64a300.742 300.742 0 0 0-12.856-108.76z"}}]})(s)}function oa(s){return Re({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M87.49 27.99C58.7 27.99 35.5 51.17 35.5 80c0 28.8 23.2 52 52 52s52-23.2 52-52c0-28.83-23.2-52.01-52.01-52.01zM219.5 54.55c-2.6 4.13-4 9.58-4.5 15.32-20.8-.7-39.2-1.03-58.3-.73.8 6.29 1.1 12.15.4 18 19.8-.25 39.1 0 58.2.77.3 3.12.7 5.96 1.2 8.26-11.9 24.43-25.4 44.13-32.3 70.43 2.3 24.6 5.2 53.2 23.1 77.7 5 19.9 9.1 39.7 14.6 59.6 2.1-25.1 7.6-51.9 21.4-79.2-1.9-9.1-2.1-17.2-1.3-25.7-4.2-8.1-9.8-16.2-19.2-24 12.9-23.8 13.2-46.2 17.6-71.8l19.5-4.78c1.8-2.39 3.3-4.92 4.4-7.56 31 2.54 61.2 6.27 90.6 10.94.6-6.09 2-11.99 4.1-17.56-29.6-4.75-60-8.55-91.2-11.17 0-4.55-.4-9.28-1.1-14.13-17.1-2.57-31.6-6.06-47.2-4.39zm205 2.44c-28.8 0-52 23.18-52 52.01 0 28.8 23.2 52 52 52s52-23.2 52-52c0-28.83-23.2-52.01-52-52.01zm-135 108.11c-7.5 0-14.6 3.9-20.3 11.6-5.8 7.6-9.7 18.8-9.7 31.3 0 6.6 1.1 13.6 3 19.1 8.9-3.1 18.1-7.5 26.7-15 7.9 6.4 16.6 10.9 26.8 15 2.2-5.9 3.5-11.9 3.5-19.1 0-12.5-3.9-23.7-9.7-31.3-5.7-7.7-12.8-11.6-20.3-11.6zm-.3 58.9c-6.8 10.6-8.8 14.7-21.7 12.9 5.3 7.4 12.7 14.1 22 14 10.4-.1 17.2-6.5 21.8-13.8-11.2.8-16.9-3.1-22.1-13.1zm60.8 28.3c2.4 25.1 3.6 39.4 1.5 63.8 2.2 3.2 2.8 19.1 11.1 22.5-12.8 6.5-17.6 24.1-24.6 31-18.2 21.6-31.1 55.3-43.6 86.3 3.7 12.8 8.3 25.5 13.7 38.1H365c-5.6-27.2-13.6-54.7-22.2-82.1 3.4-5.4 3-4 6.5-23 14.9-10.2 47.2-27.3 52.6-49.6-5-19.9-11.7-32.8-23.5-49.9 2.8-24.2-16.2-30.8-28.4-37.1zm-100.3 3.4c-3.5 10.4-5.9 20.7-7.6 30.9l13.9-1.2c-3.2-9.7-4.9-20.1-6.3-29.7zm65.9 3.5c-7.4 6-16.4 9.7-26.1 9.7-7.4 0-14.3-2.1-20.4-5.7.5 2.4.9 4.8 1.5 7.2 1.9 8.5 4.7 16.8 8 22.4 3.2 5.5 6 7.5 8.9 7.7 3.1.2 6.6-1.5 10.9-6.7 4.4-5.2 8.7-13.2 12.2-21.4 1.9-4.5 3.6-9 5-13.2zm17.4 5c-2.8 7.9-5.3 14.6-8.6 21.5l9.8.5c-.2-7.3-.6-14.6-1.2-22zm-69.4 38.7l-23.7 2c-.6 7.7-.9 15.4-1.1 23l94.6-1.5c.7-7.3 1-14.7 1.1-22.1l-20.5-1.1c-7.6 8.6-18.5 15.7-27.7 15.3-10.3-.7-17.8-7.7-22.7-15.6zm65.6 41.5l-90.4 1.5c-.9 9.3 6.9 16.2 12.3 20.3l66.7-.2c7.2-8 8.6-11.2 11.4-21.6zM236 376.8c-14.4 39-29.7 77.9-41.2 117.2h53.1c6.7-12.3 12.8-24.9 18-37.8-.7-31.9-14.5-62.9-29.9-79.4zm70.3 7.3l-41.9.1c7.8 13.1 11.8 28.8 15 45.6l8.3-.8c6.1-15.2 10.6-30.7 18.6-44.9z"}}]})(s)}function ca(s){return Re({tag:"svg",attr:{viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor",fill:"none",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{stroke:"none",d:"M0 0h24v24H0z",fill:"none"}},{tag:"path",attr:{d:"M9.5 3h5a1.5 1.5 0 0 1 1.5 1.5a3.5 3.5 0 0 1 -3.5 3.5h-1a3.5 3.5 0 0 1 -3.5 -3.5a1.5 1.5 0 0 1 1.5 -1.5z"}},{tag:"path",attr:{d:"M4 17v-1a8 8 0 1 1 16 0v1a4 4 0 0 1 -4 4h-8a4 4 0 0 1 -4 -4z"}}]})(s)}function da(s){return Re({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M459.94 53.25a16.06 16.06 0 00-23.22-.56L424.35 65a8 8 0 000 11.31l11.34 11.32a8 8 0 0011.34 0l12.06-12c6.1-6.09 6.67-16.01.85-22.38zM399.34 90L218.82 270.2a9 9 0 00-2.31 3.93L208.16 299a3.91 3.91 0 004.86 4.86l24.85-8.35a9 9 0 003.93-2.31L422 112.66a9 9 0 000-12.66l-9.95-10a9 9 0 00-12.71 0z"}},{tag:"path",attr:{d:"M386.34 193.66L264.45 315.79A41.08 41.08 0 01247.58 326l-25.9 8.67a35.92 35.92 0 01-44.33-44.33l8.67-25.9a41.08 41.08 0 0110.19-16.87l122.13-121.91a8 8 0 00-5.65-13.66H104a56 56 0 00-56 56v240a56 56 0 0056 56h240a56 56 0 0056-56V199.31a8 8 0 00-13.66-5.65z"}}]})(s)}function ma(s){return Re({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M160 240h147.37l-64-64L266 153.37 368.63 256 266 358.63 243.37 336l64-64H160v148a12 12 0 0012 12h296a12 12 0 0012-12V92a12 12 0 00-12-12H172a12 12 0 00-12 12zm-128 0h128v32H32z"}}]})(s)}function ua({afterAdd:s}){const[d,r]=t.useState(!1),[n,i]=t.useState(!1),{dispatch:o}=t.useContext(ie),{dispatch:b}=t.useContext(oe),[j,M]=t.useState(!1),[g,u]=t.useState(""),{id:c}=Pe(),P=t.useCallback(x=>{x&&x.stopPropagation(),window.innerWidth<768?i(!0):r(!0)},[]),L=t.useCallback(x=>{x&&x.stopPropagation(),r(!1)},[]),S=t.useCallback(x=>{x&&x.stopPropagation(),i(!1)},[]);async function h(x){M(!0),r(!1),i(!1),u(x);try{await new re().callRawAPI("/v4/api/records/notes",{type:x,update_id:c,status:0},"POST"),s(),$(b,"New section added"),u("")}catch(k){se(o,k.message),k.message!=="TOKEN_EXPIRED"&&$(b,k.message,5e3,"error"),u("")}M(!1)}const F=[{id:1,title:"Shout outs",key:"Shout outs",icon:e.jsx(Ns,{size:20})},{id:2,title:"Achievements",key:"Achievements",icon:e.jsx(na,{size:20})},{id:3,title:"Announcements",key:"Announcements",icon:e.jsx(ra,{size:20})},{id:4,title:"Marketing strategies",key:"Marketing strategies",icon:e.jsx(la,{size:20})},{id:5,title:"Product",key:"Product",icon:e.jsx(ia,{size:20})},{id:6,title:"Challenges",key:"Challenges",icon:e.jsx(oa,{size:22})},{id:7,title:"Financials",key:"Financials",icon:e.jsx(ca,{size:22})},{id:8,title:"KPIs",key:"KPIs",icon:e.jsx(cs,{size:17})},{id:9,title:"Intro",key:"Intro",icon:e.jsx(ma,{size:22})},{id:10,title:"What's next?",key:"What's next?",icon:e.jsx(ft,{size:20})},{id:12,title:"TL:DR (too long; didn't read)",key:"TL:DR (too long; didn't read)",icon:e.jsx(ft,{size:20})},{id:13,title:"NSM (North Star Metric)",key:"NSM (North Star Metric)",icon:e.jsx(ft,{size:20})},{id:14,title:"Customer Quote(s)",key:"Customer Quote(s)",icon:e.jsx(ft,{size:20})},{id:11,title:"Custom",key:"Section title",icon:e.jsx(da,{size:22})}],A=()=>e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("h3",{className:"font-iowan text-lg font-semibold leading-6 text-gray-900 md:text-xl",children:"Add section name"}),e.jsx("button",{onClick:window.innerWidth<768?S:L,type:"button",children:e.jsx(ue,{className:"h-6 w-6"})})]}),e.jsx("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:F.map(x=>e.jsxs("div",{className:`flex h-[44px] w-full cursor-pointer flex-row items-center gap-2 rounded border border-[#1f1d1a] px-2 font-medium ${g===x.key&&"border-[2px] border-blue-400"}`,onClick:()=>h(x.key),children:[e.jsx("span",{children:x.icon}),e.jsx("span",{className:"truncate text-sm md:text-base",children:x.title})]},x.id))})]});return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"mt-6 block w-full rounded-[2px] border-[1px] border-[#1f1d1a] bg-brown-main-bg py-2 font-iowan font-semibold text-[#1f1d1a]",onClick:P,children:"Add section +"}),e.jsx("div",{className:"hidden md:block",children:e.jsx(v,{appear:!0,show:d,as:t.Fragment,children:e.jsxs(T,{as:"div",className:"relative z-[50]",onClose:L,children:[e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsx(T.Panel,{className:"custom-overflow max-h-[90vh] w-fit max-w-fit transform overflow-hidden overflow-y-auto rounded-md bg-brown-main-bg p-6 text-left align-middle text-sm shadow-xl transition-all",children:A()})})})})]})})}),e.jsx("div",{className:"md:hidden",children:e.jsx(Ae,{isOpen:n,onClose:S,className:"px-1",children:A()})})]})}function xa({update:s,afterEdit:d,isOwner:r=!1}){const{dispatch:n}=t.useContext(ie),{dispatch:i}=t.useContext(oe),[o,b]=t.useState(!1),[j,M]=t.useState(""),[g,u]=t.useState(30),{id:c}=Pe(),[P,L]=t.useState(null),S=t.useRef(null),h=Bt(),F=new URLSearchParams(h.search).get("autofocus")==="true",[A,x]=t.useState(F),k=f=>window.innerWidth<658?f<=20?18:f<=30?17:f<=40?14:f<=50?12:10:f<=20?30:f<=30?26:f<=40?22:f<=50?18:16;t.useEffect(()=>{var f;F&&S.current?(S.current.focus(),S.current.select()):(M(s.name),u(k(((f=s.name)==null?void 0:f.length)||0)))},[s,F]);async function _(f){b(!0);try{await new re().callRawAPI(`/v4/api/records/updates/${c}`,{name:f},"PUT"),$(i,"Saved"),x(!1)}catch(w){se(n,w.message),w.message!=="TOKEN_EXPIRED"&&$(i,w.message,5e3,"error")}b(!1)}return e.jsxs("div",{className:`flex  flex-col items-start gap-2 md:w-auto ${(j==null?void 0:j.length)<=17?"w-[140px] max-w-[500px]":"w-full min-w-[310px]"}`,children:[e.jsx("input",{ref:S,maxLength:30,placeholder:A?"Edit Title":"",disabled:!r,className:"no-box-shadow focus:shadow-outline  w-full max-w-[500px] appearance-none border-none bg-brown-main-bg p-0 font-bold transition-all duration-200 placeholder:text-gray-500 focus:outline-none",value:j,onChange:f=>{M(f.target.value),u(k(f.target.value.length)),P&&clearTimeout(P);const w=setTimeout(()=>_(f.target.value),5e3);L(w)},readOnly:o,style:{fontSize:`${g}px`}}),A&&e.jsx("div",{className:"rounded-md bg-[yellow] p-2 text-sm  text-black",children:"Modify the default update title to begin working on it."})]})}const qt=t.lazy(()=>At(()=>import("./PrivacyLink-d1600e2b.js"),["assets/PrivacyLink-d1600e2b.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-46de5032.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-d88d3663.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css"]));t.lazy(()=>At(()=>Promise.resolve().then(()=>Zs),void 0));t.lazy(()=>At(()=>import("./ScheduleSendButton-732458e4.js"),["assets/ScheduleSendButton-732458e4.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-46de5032.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-d88d3663.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css"]));const ha={1:"Only logged in users that have been invited as a recipient can view this update (no sharing w/ 3rd parties) on UpdateStack with the following link",2:"Both logged out and logged in users that have the public UpdateStack link below can view and share this update"},fa={0:"Enable Shareable Link",1:"Disable Shareable Link"};function Yt({update:s,refetch:d,isOwner:r=!1}){const[n,i]=t.useState(!1),[o,b]=t.useState(!1),[j,M]=t.useState(!1),{dispatch:g}=t.useContext(ie),{dispatch:u}=t.useContext(oe),[c,P]=t.useState(!1),[L,S]=t.useState(s.private_link_open==ht.YES),{id:h}=Pe(),[F,A]=t.useState(s.public_link_id),[x,k]=t.useState(s.private_link_access==0?1:s.private_link_access);console.log("update.private_link_access",s.private_link_access);const[_,f]=t.useState(s.public_link_enabled==1),w=()=>s.public_link_id&&[1].includes(x)?L?1:0:s.public_link_id&&[2].includes(x)&&_?1:0,z=()=>{s.public_link_id&&[1].includes(x)?m():s.public_link_id&&[2].includes(x)&&X()},m=()=>{S(!L)},X=()=>{f(!_)};async function Z(){P(!0);try{const C=await Wt(u,g,"updates",h,{private_link_access:x,private_link_open:x==1?L?ht.YES:ht.NO:0,public_link_id:[1,2].includes(x)?F:"",public_link_enabled:x==2?_:0});C!=null&&C.error||(d(),$(u,"Saved"),i(!1),b(!1))}catch(C){se(g,C.message),C.message!=="TOKEN_EXPIRED"&&$(u,C.message,5e3,"error")}P(!1)}function J(C){const a=new Date().getTime();k(C),console.log("access",C),A(btoa(`${It(15)}_${Ge[C]}_${a}`)),console.log(btoa(`${It(15)}_${Ge[C]}_${a}`)),console.log(w(),"dhdhd"),M(!0)}t.useEffect(()=>{k((s==null?void 0:s.private_link_access)==0?1:s==null?void 0:s.private_link_access),f(s==null?void 0:s.public_link_enabled),A(s==null?void 0:s.public_link_id),S((s==null?void 0:s.private_link_open)==ht.YES),s!=null&&s.public_link_id&&M(!0)},[s==null?void 0:s.private_link_access,s==null?void 0:s.public_link_enabled,s==null?void 0:s.public_link_id,s==null?void 0:s.private_link_open]);const Q=t.useCallback(C=>{C&&C.stopPropagation(),window.innerWidth<768?b(!0):i(!0)},[]),V=t.useCallback(C=>{C&&C.stopPropagation(),i(!1)},[]),pe=t.useCallback(C=>{C&&C.stopPropagation(),b(!1)},[]),je=()=>e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mt-6 flex flex-col gap-4 rounded-md border-[1px] border-[#1f1d1a] bg-brown-main-bg p-4 font-medium text-gray-800 sm:flex-row sm:items-center",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("p",{className:"text-lg font-semibold text-[#1f1d1a]",children:[Ge[x]," Access"]}),e.jsx("p",{className:"mt-3 text-sm font-normal md:text-base",children:ha[x]})]}),e.jsx("select",{className:"focus:shadow-outline appearance-none  border bg-brown-main-bg py-2 pl-3 pr-8 leading-tight text-[#1f1d1a] shadow focus:outline-none ",value:x,onChange:C=>J(Number(C.target.value)),children:Object.entries(Ge).filter(([C])=>C!=="0").map(([C,a])=>e.jsx("option",{value:C,children:a},C))})]}),e.jsx(qt,{link:`${window.location.origin}/update/view/${h}`}),x===2&&e.jsxs("div",{className:"mt-4 flex items-center gap-4",children:[e.jsx(He,{enabled:L,setEnabled:S}),e.jsx("p",{className:"text-sm md:text-base",children:"Allow users to share update with third parties by providing their email"})]}),[2].includes(x)&&s.public_link_id?e.jsxs("div",{className:"mt-6 rounded-md border border-black/60 p-4",children:[e.jsx("p",{className:"text-[16px] font-bold",children:"Shareable link"}),e.jsx("p",{className:"mt-2",children:"Anyone with the shareable link can view this update."}),j?e.jsx(qt,{className:"bg-[#F5D9D8]",inputProps:{style:{color:"#CE0000"},disabled:!w(),type:w()?"text":"password"},iconFill:"#CE0000",buttonProps:{style:{color:"#CE0000"},disabled:!F||!w()},link:`${window.location.origin}/update/public/view/${h}/${F}`}):null,e.jsxs("div",{className:"mt-6 flex items-center gap-3",children:[e.jsx("button",{className:"rounded-[.125rem] bg-[#1f1d1a] px-4 py-2 font-iowan font-medium text-white",onClick:()=>J(x),children:"Generate New Shareable Link"}),s.public_link_id&&j?e.jsxs("button",{className:"rounded-[.125rem] border border-[#1f1d1a] px-4 py-2 font-iowan font-medium",onClick:z,children:[" ",fa[w()]]}):null]})]}):null,e.jsx("div",{className:"mt-12 flex justify-end",children:e.jsx(Ft,{loading:c,disabled:c,onClick:Z,className:"disabled:bg-disabledblack w-full rounded bg-[#1f1d1a] px-6  py-2 text-center font-iowan font-semibold text-white transition-colors duration-100 md:w-auto",children:"Save"})})]});return e.jsxs(e.Fragment,{children:[e.jsxs("button",{disabled:!r,className:"flex h-[28px] w-[28px] items-center justify-center gap-2 rounded-[3px] border-[1px] border-[#1f1d1a] bg-brown-main-bg px-0 font-iowan sm:h-[36px] sm:w-auto sm:justify-start md:px-5",onClick:Q,children:[e.jsx(ds,{className:"min-h-[14px] min-w-[14px] text-[14px] md:min-h-[20px] md:min-w-[20px] md:text-[20px]"}),e.jsx("span",{className:"hidden sm:block",children:Ge[s.private_link_access]})]}),e.jsx("div",{className:"hidden md:block",children:e.jsx(ts,{modalHeader:!0,isOpen:n,modalCloseClick:V,title:"Update Privacy Settings",classes:{modal:"w-full",modalDialog:"!w-[100%] md:!w-[60%]",modalContent:""},children:je()})}),e.jsx("div",{className:"md:hidden",children:e.jsx(Ae,{isOpen:o,onClose:pe,title:"Update Privacy Settings",children:je()})})]})}function pa({code:s,afterLeave:d}){const[r,n]=t.useState(!1);return t.useEffect(()=>{s&&n(!0)},[s]),e.jsx(e.Fragment,{children:e.jsx(v,{appear:!0,show:r,as:t.Fragment,afterLeave:d,children:e.jsxs(T,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>n(!1),children:[e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(T.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",as:"div",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{}),e.jsx(T.Title,{as:"h3",className:"text-center text-xl font-semibold leading-6 text-gray-900",children:"Sending Update"}),e.jsx("button",{onClick:()=>n(!1),type:"button",children:e.jsx(ue,{className:"h-6 w-6"})})]}),s=="NO_FREE_USES_LEFT"?e.jsx("p",{className:"mt-4",children:"You have exceeded your limit of free reports."}):null,s=="TOO_MANY_INVITEES"?e.jsx("p",{className:"mt-4",children:"With the free plan, you're limited to sending reports to a maximum of 5 Fund managers."}):null,s=="UPDATE_LIMIT_REACHED"?e.jsx("p",{className:"mt-4",children:"You have reached your monthly update limit for your current plan."}):null,s=="FREE_TRIAL_LIMIT_REACHED"?e.jsx("p",{className:"mt-4",children:"You have used your free trial update. Please subscribe to send more updates!"}):null,s=="TRIAL_EXPIRED"?e.jsx("p",{className:"mt-4",children:"Please Upgrade your account to send an update!"}):null,s=="TOO_MANY_INVITEES"?e.jsx("p",{className:"mt-4 text-center",children:"To send to as many Fund managers as you want, please subscribe to our plans"}):s=="UPDATE_LIMIT_REACHED"?e.jsx("p",{className:"mt-4 text-center",children:"To send more updates, please upgrade your plan"}):s=="FREE_TRIAL_LIMIT_REACHED"?e.jsx("p",{className:"mt-4 text-center",children:"To send more updates, please subscribe to our plans"}):s=="TRIAL_EXPIRED"?e.jsx("p",{className:"mt-4 text-center",children:"To send updates, please subscribe to our plans"}):e.jsx("p",{className:"mt-4 text-center",children:"To send plans to Fund managers, please subscribe to our plans"}),e.jsx(Ve,{to:"/member/billing",className:"mt-6 block w-full rounded bg-primary-black px-4 py-2 text-center font-bold text-white",children:"See Plans"})]})})})})]})})})}const ga=()=>e.jsx("svg",{width:"101",height:"100",viewBox:"0 0 101 100",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"mx-auto mb-6",children:e.jsx("path",{d:"M69.4414 37.3086C69.8784 37.7441 70.2252 38.2615 70.4617 38.8313C70.6983 39.4011 70.8201 40.012 70.8201 40.6289C70.8201 41.2458 70.6983 41.8567 70.4617 42.4265C70.2252 42.9963 69.8784 43.5137 69.4414 43.9492L47.5664 65.8242C47.1309 66.2612 46.6135 66.608 46.0437 66.8445C45.4739 67.0811 44.8631 67.2029 44.2461 67.2029C43.6292 67.2029 43.0183 67.0811 42.4485 66.8445C41.8787 66.608 41.3613 66.2612 40.9258 65.8242L31.5508 56.4492C31.1148 56.0132 30.7689 55.4955 30.5329 54.9258C30.2969 54.3561 30.1755 53.7455 30.1755 53.1289C30.1755 52.5123 30.2969 51.9017 30.5329 51.332C30.7689 50.7623 31.1148 50.2446 31.5508 49.8086C31.9868 49.3726 32.5045 49.0267 33.0742 48.7907C33.6439 48.5547 34.2545 48.4333 34.8711 48.4333C35.4877 48.4333 36.0984 48.5547 36.6681 48.7907C37.2378 49.0267 37.7554 49.3726 38.1914 49.8086L44.25 55.8594L62.8086 37.2969C63.2447 36.8618 63.7623 36.517 64.3318 36.2821C64.9013 36.0472 65.5114 35.9269 66.1274 35.928C66.7435 35.929 67.3532 36.0515 67.9218 36.2884C68.4905 36.5253 69.0069 36.872 69.4414 37.3086ZM92.6875 50C92.6875 58.3439 90.2133 66.5004 85.5776 73.4381C80.942 80.3758 74.3532 85.7831 66.6445 88.9762C58.9357 92.1692 50.4532 93.0047 42.2696 91.3769C34.0861 89.7491 26.569 85.7311 20.669 79.8311C14.7689 73.931 10.751 66.4139 9.12314 58.2304C7.49533 50.0468 8.33078 41.5643 11.5239 33.8555C14.7169 26.1468 20.1242 19.558 27.0619 14.9224C33.9996 10.2868 42.1561 7.8125 50.5 7.8125C61.685 7.82491 72.4084 12.2736 80.3174 20.1826C88.2264 28.0916 92.6751 38.815 92.6875 50ZM83.3125 50C83.3125 43.5103 81.3881 37.1663 77.7826 31.7704C74.1771 26.3744 69.0525 22.1687 63.0568 19.6852C57.0611 17.2017 50.4636 16.5519 44.0986 17.818C37.7336 19.0841 31.887 22.2091 27.2981 26.7981C22.7092 31.387 19.5841 37.2336 18.318 43.5986C17.0519 49.9636 17.7017 56.5611 20.1852 62.5568C22.6687 68.5525 26.8744 73.6771 32.2704 77.2826C37.6664 80.8881 44.0103 82.8125 50.5 82.8125C59.1996 82.8032 67.5402 79.3432 73.6917 73.1917C79.8432 67.0401 83.3032 58.6996 83.3125 50Z",fill:"#9DD321"})});function ba({report_sent_count:s,afterLeave:d,isScheduled:r,scheduledDateTime:n}){const[i,o]=t.useState(!1),b=st();t.useEffect(()=>{(s||r)&&o(!0)},[s,r]);const j=()=>r?e.jsxs("p",{className:"mb-8 text-base",children:["Your update has been successfully scheduled to send on"," ",e.jsx("span",{className:"font-semibold",children:U(n).format("MMM D, YYYY [at] h:mm A")})]}):e.jsxs("p",{className:"mb-8 text-base",children:["Your update has been successfully sent to ",s," ","recipient(s)"]});return e.jsx(e.Fragment,{children:e.jsx(v,{appear:!0,show:i,as:t.Fragment,afterLeave:d,children:e.jsxs(T,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>o(!1),children:[e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(T.Panel,{className:"w-full max-w-lg transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle shadow-xl transition-all",as:"div",children:[e.jsx("div",{className:"absolute right-6 top-6",children:e.jsx("button",{onClick:()=>o(!1),type:"button",children:e.jsx(ue,{className:"h-6 w-6"})})}),e.jsxs("div",{className:"mt-6 text-center",children:[e.jsx(ga,{}),e.jsx(T.Title,{as:"h3",className:"mb-4 font-iowan text-2xl font-semibold",children:r?"Successfully Scheduled!":"Congrats!"}),j(),e.jsx("button",{onClick:()=>{o(!1),b("/member/dashboard")},className:"w-full rounded-sm bg-[#1f1d1a] px-4 py-2 font-iowan text-base font-semibold text-white hover:bg-[#2a2724]",children:"Return to Dashboard"})]})]})})})})]})})})}function ja({reportDate:s,selectedTemplate:d,ccMyself:r,recipientAccess:n,updateGroups:i,className:o,sendUpdateRef:b=null,UpdateCadence:j=null,refetch:M,nextScheduledUpdate:g,setNextScheduledUpdate:u}){var Qe,l,E;const[c,P]=t.useState(!1),[L,S]=t.useState(!1),[h,F]=t.useState(!1),[A,x]=t.useState(!1),{globalState:k,setGlobalState:_}=bt(),[f,w]=t.useState(!1),{dispatch:z}=t.useContext(ie),{dispatch:m}=t.useContext(oe),[X,Z]=t.useState(!1),[J,Q]=t.useState(""),{id:V}=Pe(),[pe,je]=t.useState(null),[C,a]=t.useState(null),[D,B]=t.useState(null),[xe,y]=t.useState(!1),[Y,te]=t.useState(null),[ge,ce]=t.useState([]),[ye,de]=t.useState(""),[$e,wt]=t.useState(null),[nt,rt]=t.useState(!1),[Ne,le]=t.useState(!1),[ke,yt]=t.useState(!1),ze=st(),{profile:R}=at(),{loading:Pt,data:N,processRegisteredDate:vt,getSubscription:it,getCustomerSubscription:Nt,getSentUpdates:Ce}=Qt(),ve=(E=(l=(Qe=N==null?void 0:N.object)==null?void 0:Qe.plan)==null?void 0:l.nickname)==null?void 0:E.toLowerCase(),Te=!(N!=null&&N.subscription)&&!(N!=null&&N.trial_expired)?1:ve!=null&&ve.includes("enterprise")?1/0:ve!=null&&ve.includes("business")?10:ve!=null&&ve.includes("pro")?5:0;console.log("subscriptionData",N,"limittt",Te,ve),t.useEffect(()=>{(async()=>await qe())(),!h&&!A&&(w(!1),de(""))},[h,A]),t.useEffect(()=>{R!=null&&R.id&&(Nt(),it({filter:[`user_id,eq,${R==null?void 0:R.id}`]}),vt(R==null?void 0:R.create_at),Ce(R),k!=null&&k.refreshSubscription&&_("refreshSubscription",!1))},[R==null?void 0:R.id,k==null?void 0:k.refreshSubscription]),t.useEffect(()=>{if(N){if(!(N!=null&&N.subscription)&&!(N!=null&&N.trial_expired)){if((N==null?void 0:N.sentUpdates)>=1){le(!0);return}le(!1);return}if(N!=null&&N.trial_expired&&!(N!=null&&N.subscription)){le(!0);return}(N==null?void 0:N.sentUpdates)>=Te&&Te!==1/0?le(!0):le(!1)}},[N==null?void 0:N.sentUpdates,Te,N==null?void 0:N.trial_expired,N==null?void 0:N.subscription]);const qe=async()=>{const p=new re;try{const O=await p.callRawAPI(`/v3/api/custom/goodbadugly/updates/${V}/scheduled-sends`,[],"GET");if(!O.error&&O.data){const I=O.data.map(W=>({...W,scheduled_at:U.utc(W.scheduled_date).local().format(),status:W.status}));ce(I);const G=I.filter(W=>W.status===0);if(G.length>0){const W=G.sort((K,q)=>U(K.scheduled_at).valueOf()-U(q.scheduled_at).valueOf());u(W[0])}else u(null);if(I.length>0){const W=[...I].sort((K,q)=>U(q.scheduled_at).valueOf()-U(K.scheduled_at).valueOf());wt(W[0].scheduled_at)}}}catch(O){console.error("Error fetching scheduled updates:",O)}},Ye=()=>{const p=U().add(1,"day"),O=U().day()===1?U().add(7,"days"):U().day(8);return[{label:"Tomorrow Morning",date:p.format("YYYY-MM-DD"),time:"08:00",displayDate:p.format("MMM D"),displayTime:"8:00 AM"},{label:"Tomorrow Afternoon",date:p.format("YYYY-MM-DD"),time:"13:00",displayDate:p.format("MMM D"),displayTime:"1:00 PM"},{label:"Monday Morning",date:O.format("YYYY-MM-DD"),time:"08:00",displayDate:O.format("MMM D"),displayTime:"8:00 AM"}]},We=p=>{Be(p.date)&&(B(p.date),te(p.time),w(!0))},Be=p=>{const O=ge.filter(I=>I.status===0);for(const I of O){const G=U(I.scheduled_at),W=U(`${p} ${Y||"00:00"}`,"YYYY-MM-DD HH:mm");if(Math.abs(G.diff(W,"hours"))<24)return de(`Cannot schedule update. An update is already scheduled for ${G.format("MMM D, YYYY [at] h:mm A")}. Please choose a time at least 24 hours apart.`),!1}return de(""),!0},lt=p=>{Be(p)&&(B(p),w(!0))};t.useEffect(()=>{qe(),j&&Q(j.toString())},[j]),t.useEffect(()=>{if(D&&Y){const[p,O]=Y.split(":"),I=U(),G=U(`${D} ${p}:${O}`,"YYYY-MM-DD HH:mm");yt(G.isBefore(I))}},[D,Y]);async function Ue(p,O){Z(!0);try{const I=new re;if(!s)throw new Error("Report date is required");if(i.length<=0)throw new Error("Please select a recipient");if(!(N!=null&&N.subscription)&&!(N!=null&&N.trial_expired)&&(N==null?void 0:N.sentUpdates)>=1)throw new Error("FREE_TRIAL_LIMIT_REACHED");if(N!=null&&N.trial_expired&&!(N!=null&&N.subscription))throw new Error("TRIAL_EXPIRED");if((N==null?void 0:N.sentUpdates)>=Te&&Te!==1/0)throw new Error("UPDATE_LIMIT_REACHED");if(D&&Y){const W=`${D} ${Y}`,K=U(W,"YYYY-MM-DD HH:mm").utc().format("YYYY-MM-DD HH:mm:ss");console.log("Scheduling for UTC time:",K),(await I.callRawAPI(`/v3/api/custom/goodbadugly/updates/${V}/schedule-sends`,{scheduled_dates:[K]},"POST")).error||(F(!1),x(!1),y(!0),$(m,"Update scheduled successfully",3e3),await qe(),_("refreshSubscription",!(k!=null&&k.refreshSubscription)))}else{const W={date:s,template_id:d||"Custom",cc_myself:r?1:0,recipient_access:n,cadence:p||0},K=await I.callRawAPI(`/v3/api/custom/goodbadugly/updates/${V}/send-report`,W,"POST");a(K.report_sent_count),_("refreshSubscription",!(k!=null&&k.refreshSubscription))}P(!1),S(!1),M()}catch(I){se(z,I.message),["TOO_MANY_INVITEES","NO_FREE_USES_LEFT","UPDATE_LIMIT_REACHED","TRIAL_EXPIRED"].includes(I.message)?je(I.message):I.message!=="TOKEN_EXPIRED"&&$(m,I.message,5e3,"error")}Z(!1)}const Fe=({selectedDate:p,onDateSelect:O})=>{const[I,G]=t.useState(U()),W=I.daysInMonth(),K=U(I).startOf("month").day(),q=[];let he=[];for(let ne=0;ne<K;ne++)he.push(e.jsx("td",{className:"p-2"},`empty-${ne}`));for(let ne=1;ne<=W;ne++){const _e=U(I).date(ne),Me=p===_e.format("YYYY-MM-DD"),Se=_e.isSame(U(),"day"),me=_e.isBefore(U(),"day");he.push(e.jsx("td",{className:"p-1",children:e.jsx("button",{disabled:me,onClick:()=>O(_e.format("YYYY-MM-DD")),className:`flex h-8 w-8 items-center justify-center rounded-full transition-colors
              ${me?"cursor-not-allowed text-gray-300":"hover:bg-black/50"}
              ${Me?"bg-[#1f1d1a] text-white hover:bg-[#1f1d1a]":""}
              ${Se?"border border-[#1f1d1a]":""}`,children:ne})},ne)),he.length===7&&(q.push(e.jsx("tr",{children:he},`week-${q.length}`)),he=[])}return he.length>0&&q.push(e.jsx("tr",{children:he},`week-${q.length}`)),e.jsxs("div",{className:"p-4",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("button",{onClick:()=>G(U(I).subtract(1,"month")),className:"rounded-full p-1 hover:bg-black/50",children:e.jsx(Ys,{className:"h-5 w-5"})}),e.jsx("h2",{className:"font-iowan font-semibold",children:I.format("MMMM YYYY")}),e.jsx("button",{onClick:()=>G(U(I).add(1,"month")),className:"rounded-full p-1 font-iowan hover:bg-black/50",children:e.jsx(Hs,{className:"h-5 w-5"})})]}),e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{children:e.jsx("tr",{children:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"].map(ne=>e.jsx("th",{className:"p-2 text-xs font-medium",children:ne},ne))})}),e.jsx("tbody",{children:q})]})]})},ot=({selectedTime:p,onTimeSelect:O})=>{const[I,G]=t.useState(p?p.split(":")[0]:""),[W,K]=t.useState(p?p.split(":")[1]:""),[q,he]=t.useState("AM"),[ne,_e]=t.useState(""),Me=(me,fe)=>{const we=parseInt(me),be=parseInt(fe);return isNaN(we)||we<1||we>12?(_e("Hour must be between 1 and 12"),!1):isNaN(be)||be<0||be>59?(_e("Minutes must be between 0 and 59"),!1):(_e(""),!0)},Se=(me,fe,we)=>{if(me===""||fe===""){G(me),K(fe);return}if(me&&fe&&Me(me,fe)){let be=parseInt(me);we==="PM"&&be!==12?be+=12:we==="AM"&&be===12&&(be=0);const St=be.toString().padStart(2,"0"),Dt=fe.toString().padStart(2,"0");O(`${St}:${Dt}`)}};return t.useEffect(()=>{if(p){const[me,fe]=p.split(":"),we=parseInt(me);let be=we%12;be===0&&(be=12),G(be.toString()),K(fe),he(we>=12?"PM":"AM")}},[p]),e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsxs("div",{className:"flex items-center justify-start gap-2",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("input",{type:"text",value:I,onChange:me=>{const fe=me.target.value.replace(/\D/g,"");G(fe),Se(fe,W,q)},placeholder:"HH",className:"w-12 rounded border border-[#1f1d1a] bg-transparent px-2 py-1 text-center font-iowan",maxLength:2}),e.jsx("span",{className:"font-iowan",children:":"}),e.jsx("input",{type:"text",value:W,onChange:me=>{const fe=me.target.value.replace(/\D/g,"");K(fe),Se(I,fe,q)},placeholder:"MM",className:"w-12 rounded border border-[#1f1d1a] bg-transparent px-2 py-1 text-center font-iowan",maxLength:2})]}),e.jsxs("div",{className:"flex rounded-full border border-[#1f1d1a] p-1",children:[e.jsx("button",{onClick:()=>{he("AM"),Se(I,W,"AM")},className:`rounded-full px-3 py-1 text-sm transition-colors ${q==="AM"?"bg-[#1f1d1a] text-white":"text-[#1f1d1a] hover:bg-black/50"}`,children:"AM"}),e.jsx("button",{onClick:()=>{he("PM"),Se(I,W,"PM")},className:`rounded-full px-3 py-1 text-sm transition-colors ${q==="PM"?"bg-[#1f1d1a] text-white":"text-[#1f1d1a] hover:bg-black/50"}`,children:"PM"})]})]}),ne&&e.jsx("div",{className:"text-sm text-red-500",children:ne})]})},ct=Ye(),Xe=({showTimeSelect:p,lastScheduledTime:O,quickSelectOptions:I,handleQuickSelect:G,setShowTimeSelect:W,selectedDate:K,Calendar:q,handleDateSelect:he,dateError:ne,TimePicker:_e,selectedTime:Me,setSelectedTime:Se,sendReport:me,globalDispatch:fe})=>e.jsx("div",{className:"flex flex-col",children:p?e.jsxs("div",{className:"p-4",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx(q,{selectedDate:K,onDateSelect:he}),ne&&e.jsx("div",{className:"mt-2 text-sm text-red-500",children:ne})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex flex-col justify-start gap-2 rounded p-2",children:[e.jsx("label",{className:"font-iowan-regular text-[18px]",htmlFor:"time",children:"Time:"}),e.jsx(_e,{selectedTime:Me,onTimeSelect:Se})]}),K&&Me&&e.jsxs(e.Fragment,{children:[ke&&e.jsx("div",{className:"mt-2 text-sm text-red-500",children:"Please select a time in the future"}),e.jsx("button",{onClick:()=>{const[we,be]=Me.split(":");if(we==="00"&&be==="00"){$(fe,"Please select a valid time",3e3,"error");return}if(K<U().format("YYYY-MM-DD")){$(fe,"Please select a date in the future",3e3,"error");return}if(K===U().format("YYYY-MM-DD")){const St=U();if(U(`${K} ${we}:${be}`,"YYYY-MM-DD HH:mm").isBefore(St)){$(fe,"Please select a time in the future",3e3,"error");return}}me()},disabled:ke,className:"mt-4 w-full rounded bg-[#1f1d1a] px-4 py-2 text-white hover:bg-[#1f1d1a]/90 disabled:cursor-not-allowed disabled:opacity-50",children:"Save"})]})]})]}):e.jsxs("div",{className:"p-4",children:[O&&e.jsxs("div",{className:"mb-2 flex items-center justify-between border-b border-gray-200 p-3 font-semibold",children:[e.jsx("span",{children:"Last scheduled time"}),e.jsx("span",{children:U(O).format("ddd, MMM D, h:mm A")})]}),e.jsx("div",{className:"mb-4",children:I.map((we,be)=>e.jsxs("div",{onClick:()=>G(we),className:"flex cursor-pointer items-center justify-between border-b border-gray-200 p-3 hover:bg-black/30",children:[e.jsx("span",{children:we.label}),e.jsxs("span",{children:[we.displayDate,", ",we.displayTime]})]},be))}),e.jsxs("button",{onClick:()=>W(!0),className:"flex w-full items-center justify-between rounded border border-[#1f1d1a] px-4 py-3 hover:bg-[#1f1d1a]/50",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx($s,{className:"mr-2 h-5 w-5"}),e.jsx("span",{children:"Pick date & time"})]}),e.jsx("svg",{className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})})]})]})}),dt=({cadenceDuration:p,setCadenceDuration:O,UpdateCadence:I,sending:G,sendReport:W})=>e.jsxs("div",{className:"flex flex-col p-0",children:[e.jsxs("div",{className:"mb-6 items-center gap-2 md:hidden",children:[e.jsx("h3",{className:"font-iowan text-xl font-bold text-[#1f1d1a]   md:text-lg",children:"Create a scheduled cadence notification for this update template"}),e.jsx(H,{className:"relative inline",children:({open:K})=>e.jsxs(e.Fragment,{children:[e.jsx(H.Button,{className:"ml-2",children:e.jsx(Ee,{className:"h-5 w-5"})}),e.jsx(v,{as:t.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",children:e.jsx(H.Panel,{className:"absolute left-0 z-10 mt-3 w-screen max-w-[250px] translate-x-[0%] transform px-4 text-sm  sm:max-w-xl md:-translate-x-1/2",children:e.jsxs("div",{className:"relative rounded-lg bg-[#1f1d1a] px-6 py-4 text-white shadow-lg ring-1 ring-[#1f1d1a]/5",children:[e.jsxs("div",{className:"font-medium",children:[e.jsx(Ee,{className:"mr-2 inline h-5 w-5"})," ","You can create a scheduled cadence notification (ie. weekly, bi-weekly, monthly, quarterly, bi-annually, annually, etc), using this update template. You will be notified 24hrs prior to each update so you can continue to edit prior to sending."]}),e.jsx("div",{className:"absolute left-1/2 top-0 h-3 w-3 -translate-x-1/2 -translate-y-1/2 rotate-45 bg-[#1f1d1a]"})]})})})]})})]}),e.jsxs("div",{className:"mt-4 flex flex-col gap-4 md:flex-row md:items-center",children:[e.jsx("p",{className:"whitespace-nowrap font-medium",children:"Select cadence notification"}),e.jsxs("select",{value:p,onChange:K=>O(K.target.value),className:"focus:shadow-outline h-[40px] w-full appearance-none rounded border border-black bg-transparent py-2 pl-6 pr-8 font-iowan text-[16px] leading-tight text-[#1f1d1a] focus:outline-none",children:[e.jsx("option",{value:"",children:"Select a cadence..."}),e.jsx("option",{value:"7",children:"Weekly"}),e.jsx("option",{value:"14",children:"Bi-weekly"}),e.jsx("option",{value:"30",children:"Monthly"}),e.jsx("option",{value:"120",children:"Quarterly"}),e.jsx("option",{value:"365",children:"Annually"}),e.jsx("option",{value:"730",children:"Bi-annually"})]})]}),I&&I!==0?e.jsxs("div",{className:"mt-4 text-sm text-gray-600",children:["Current cadence: ",I?`${I} days`:""]}):null,e.jsx("div",{className:"mt-5 flex flex-col gap-4 md:mt-8 md:flex-row",children:G?e.jsx(_s,{size:18}):e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"flex h-[40px] w-full items-center justify-center rounded border border-[#1f1d1a] py-3 text-center font-iowan text-[16px] text-[#1f1d1a]",type:"button",onClick:()=>W(p,!1),disabled:G,children:"No, Continue to send without"}),e.jsx("button",{onClick:()=>W(p,!0),disabled:G,className:"flex h-[40px] w-full items-center justify-center rounded border bg-[#1f1d1a] py-3 text-center font-iowan text-[16px] text-brown-main-bg",children:"Select cadence and Send"})]})})]}),Ke=t.useCallback(p=>{if(p&&p.stopPropagation(),Ne)return ze("/member/billing?openManagePlan=true");window.innerWidth<768?S(!0):P(!0)},[Ne,ze]),Ct=t.useCallback(p=>{p&&p.stopPropagation(),P(!1)},[]),_t=t.useCallback(p=>{p&&p.stopPropagation(),S(!1)},[]),mt=t.useCallback(p=>{if(p&&p.stopPropagation(),Ne)return ze("/member/billing?openManagePlan=true");window.innerWidth<768?x(!0):F(!0)},[Ne,ze]),ut=t.useCallback(p=>{p&&p.stopPropagation(),F(!1)},[]),kt=t.useCallback(p=>{p&&p.stopPropagation(),x(!1)},[]),xt=async()=>{if(!g)return;rt(!0);const p=new re;try{await p.callRawAPI(`/v3/api/custom/goodbadugly/scheduled-sends/${g.id}`,{},"DELETE"),$(m,"Scheduled update cancelled successfully",3e3),await qe()}catch(O){se(z,O.message),$(m,"Failed to cancel scheduled update",5e3,"error")}rt(!1)};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"relative flex w-full flex-col items-center justify-center sm:w-auto",children:[e.jsx("div",{className:"absolute top-0",children:e.jsx(Mt,{children:e.jsx(Et,{display:e.jsx("button",{className:"m-0 h-0 w-full p-0",ref:b}),openOnClick:!0,backgroundColor:"#1f1d1a",children:e.jsx("span",{className:"text-white",children:"Click to Send Update"})})})}),e.jsx(Et,{display:e.jsx("button",{className:`focus:shadow-outline ml-2 flex h-[2.75rem] !w-[calc(100%_-_8px)] items-center justify-center whitespace-nowrap rounded-[2px] bg-[#1f1d1a] px-6 py-2 font-iowan text-base font-semibold text-white focus:outline-none sm:w-fit md:!w-full ${o} ${Ne?"opacity-75":""}`,onClick:Ke,children:Ne?N!=null&&N.subscription?"Upgrade to Send":(N!=null&&N.trial_expired,"Subscribe to Send"):"Send Update"}),openOnClick:!1,place:"top",tooltipClasses:"!bg-transparent !shadow-none !-top-[75px]",backgroundColor:"transparent",className:"w-full",children:e.jsx("div",{className:"flex flex-col gap-2 p-2",children:g?e.jsx("button",{onClick:xt,disabled:nt,className:"flex h-[3.5rem] items-center justify-center gap-2 rounded-[.25rem] border border-red-500 bg-brown-main-bg p-[1rem] px-4 py-2 transition duration-200 hover:bg-red-50",children:nt?e.jsx(Cs,{size:20,color:"#EF4444"}):e.jsxs(e.Fragment,{children:[e.jsx(ue,{className:"h-5 w-5 text-red-500"}),e.jsxs("span",{className:"text-[12px] font-medium text-red-500",children:["Cancel Scheduled update (",U(g.scheduled_at).format("MMM D, h:mm A"),")"]})]})}):e.jsxs("button",{onClick:mt,className:`flex h-[3.5rem] items-center justify-center gap-2 rounded-[.25rem] border border-primary-black bg-brown-main-bg p-[1rem] px-4 py-2 transition duration-200 ${Ne?"opacity-75":""}`,children:[e.jsx(ss,{}),e.jsx("span",{className:"font-medium text-black",children:Ne?N!=null&&N.trial_expired&&!(N!=null&&N.subscription)?"Subscribe to Schedule":"Upgrade to Schedule":"Schedule send"})]})})})]}),e.jsx("div",{className:"hidden md:block",children:e.jsx(v,{appear:!0,show:h,as:t.Fragment,children:e.jsxs(T,{as:"div",className:"relative z-[50]",onClose:ut,children:[e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(T.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-lg border border-[#1f1d1a] bg-brown-main-bg text-left align-middle shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between border-b border-[#1f1d1a] px-4 py-3",children:[e.jsx(T.Title,{className:"font-iowan text-lg font-semibold",children:f?"Pick date & time":"Schedule Send"}),e.jsx("button",{onClick:ut,className:"rounded-full p-1 hover:bg-[#1f1d1a]/10",children:e.jsx(ue,{className:"h-5 w-5"})})]}),Xe({showTimeSelect:f,lastScheduledTime:$e,quickSelectOptions:ct,handleQuickSelect:We,setShowTimeSelect:w,selectedDate:D,Calendar:Fe,handleDateSelect:lt,dateError:ye,TimePicker:ot,selectedTime:Y,setSelectedTime:te,sendReport:Ue,globalDispatch:m})]})})})})]})})}),e.jsx("div",{className:"md:hidden",children:e.jsx(Ae,{isOpen:A,onClose:kt,title:f?"Pick date & time":"Schedule Send",children:Xe({showTimeSelect:f,lastScheduledTime:$e,quickSelectOptions:ct,handleQuickSelect:We,setShowTimeSelect:w,selectedDate:D,Calendar:Fe,handleDateSelect:lt,dateError:ye,TimePicker:ot,selectedTime:Y,setSelectedTime:te,sendReport:Ue,globalDispatch:m})})}),e.jsx("div",{className:"hidden md:block",children:e.jsx(v,{appear:!0,show:c,as:t.Fragment,children:e.jsxs(T,{as:"div",className:"relative z-[50]",onClose:Ct,children:[e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(T.Panel,{className:"w-[600px]  transform rounded border-[1px] border-[#1f1d1a] bg-brown-main-bg p-2 px-8 pb-7 pt-5 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs(T.Title,{as:"h3",className:" pt-4 font-iowan text-[20px] font-bold text-[#1f1d1a] md:text-xl",children:["Create a scheduled cadence notification for this update template?",e.jsx(H,{className:"relative inline",children:({open:p})=>e.jsxs(e.Fragment,{children:[e.jsx(H.Button,{className:"ml-2",children:e.jsx(Ee,{className:"h-5 w-5"})}),e.jsx(v,{as:t.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",children:e.jsx(H.Panel,{className:"absolute left-0 z-10 mt-3 w-screen max-w-[250px] -translate-x-1/2 transform px-4  text-sm sm:max-w-xl",children:e.jsxs("div",{className:"relative rounded-lg bg-[#1f1d1a] px-6 py-4 text-white shadow-lg ring-1 ring-[#1f1d1a]/5",children:[e.jsxs("div",{className:"font-medium",children:[e.jsx(Ee,{className:"mr-2 inline h-5 w-5"})," ","You can create a scheduled cadence notification (ie. weekly, bi-weekly, monthly, quarterly, bi-annually, annually, etc), using this update template. You will be notified 24hrs prior to each update so you can continue to edit prior to sending."]}),e.jsx("div",{className:"absolute left-1/2 top-0 h-3 w-3 -translate-x-1/2 -translate-y-1/2 rotate-45 bg-[#1f1d1a]"})]})})})]})})]}),dt({cadenceDuration:J,setCadenceDuration:Q,UpdateCadence:j,sending:X,sendReport:Ue})]})})})})]})})}),e.jsx("div",{className:"md:hidden",children:e.jsx(Ae,{isOpen:L,onClose:_t,title:"",children:dt({cadenceDuration:J,setCadenceDuration:Q,UpdateCadence:j,sending:X,sendReport:Ue})})}),e.jsx(pa,{code:pe,afterLeave:()=>je(null)}),e.jsx(ba,{report_sent_count:C,afterLeave:()=>{a(null),y(!1)},isScheduled:xe,scheduledDateTime:D&&Y?`${D} ${Y}`:null})]})}function wa({update:s,afterEdit:d}){var P,L,S,h,F,A,x,k,_,f;const[r,n]=t.useState(!1),{dispatch:i}=t.useContext(ie),{dispatch:o}=t.useContext(oe),b=Le({cac:ee().required("This field is required"),marketing_activations:ee().required("This field is required"),churn_rate:ee().required("This field is required"),acv:ee().required("This field is required"),csat:ee().required("This field is required")}),{register:j,handleSubmit:M,formState:{isSubmitting:g,errors:u}}=Ie({resolver:De(b),defaultValues:{cac:s.cac,marketing_activations:s.marketing_activations,churn_rate:s.churn_rate,acv:s.acv,csat:s.csat}});async function c(w){try{await new re().callRawAPI(`/v4/api/records/updates/${s.id}`,{cac:w.cac,marketing_activations:w.marketing_activations,churn_rate:w.churn_rate,acv:w.acv,csat:w.csat},"PUT"),n(!1),d()}catch(z){se(i,z.message),z.message!=="TOKEN_EXPIRED"&&$(o,z.message,5e3,"error")}}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"rounded-md bg-primary-black p-2 font-iowan font-medium text-white",onClick:()=>n(!0),children:"Edit manually"}),e.jsx(v,{appear:!0,show:r,as:t.Fragment,children:e.jsxs(T,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>n(!1),children:[e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(T.Panel,{as:"form",className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",onSubmit:M(c),children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(T.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Edit update"}),e.jsx("button",{onClick:()=>n(!1),type:"button",children:e.jsx(ue,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Customer Acquisition Cost (CAC)"}),e.jsx("input",{type:"text",...j("cac"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(P=u.cac)!=null&&P.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(L=u.cac)==null?void 0:L.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Activations / Daily active users"}),e.jsx("input",{type:"text",...j("marketing_activations"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(S=u.marketing_activations)!=null&&S.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(h=u.marketing_activations)==null?void 0:h.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Retention / Churn rate (Monthly)"}),e.jsx("input",{type:"text",...j("churn_rate"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(F=u.churn_rate)!=null&&F.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(A=u.churn_rate)==null?void 0:A.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Annual contract value (ACV)"}),e.jsx("input",{type:"text",...j("acv"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(x=u.acv)!=null&&x.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(k=u.acv)==null?void 0:k.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Customer Satisfaction Score (CSAT/NPS)"}),e.jsx("input",{type:"text",...j("csat"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(_=u.csat)!=null&&_.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(f=u.csat)==null?void 0:f.message})]}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>n(!1),children:"Cancel"}),e.jsx(Oe,{loading:g,disabled:g,type:"submit",className:"disabled:bg-disabledblack rounded-lg bg-primary-black py-2 text-center font-semibold text-white transition-colors duration-100",children:"Save"})]})]})})})})]})})]})}const Je=({children:s,last:d=!1})=>{const[r,n]=t.useState(!1),i=t.useRef(null),o=()=>{i.current&&clearTimeout(i.current),n(!0)},b=()=>{i.current=setTimeout(()=>{n(!1)},100)};return e.jsx(H,{className:"relative inline",children:e.jsxs("div",{onMouseEnter:o,onMouseLeave:b,children:[e.jsx(H.Button,{className:"ml-0",children:e.jsx(Ee,{className:"h-4 w-4 cursor-pointer",pathClasses:"text-[#1f1d1a]",stroke:"white"})}),r&&e.jsx(v,{show:!0,as:t.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",children:e.jsx(H.Panel,{static:!0,className:`absolute left-0 z-10 mt-3 w-screen max-w-[200px] -translate-x-[35%] transform px-4 text-sm ${d?"top-0":"top-full"}`,onMouseEnter:o,onMouseLeave:b,children:e.jsxs("div",{className:"relative rounded-lg bg-[#1f1d1a] px-4 py-3 text-white shadow-lg ring-1 ring-[#1f1d1a]/5",children:[e.jsx("div",{className:"font-medium",children:s}),e.jsx("div",{className:"absolute left-1/2 top-0 h-3 w-3 -translate-x-1/2 -translate-y-1/2 rotate-45 bg-[#1f1d1a]"})]})})})]})})};function ya({update:s,refetchUpdate:d,showMetric:r}){const[n,i]=t.useState(!1);t.useContext(ie),t.useContext(oe),t.useState(!1);const[o,b]=t.useState(!1);return e.jsxs(e.Fragment,{children:[e.jsxs("button",{className:"flex w-full items-center justify-between text-[16px] font-medium text-[#1f1d1a] md:text-[18px] ",onClick:()=>i(r),children:["Show marketing metrics"," ",r?e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M17.5 18.3333H2.5C2.15833 18.3333 1.875 18.05 1.875 17.7083C1.875 17.3667 2.15833 17.0833 2.5 17.0833H17.5C17.8417 17.0833 18.125 17.3667 18.125 17.7083C18.125 18.05 17.8417 18.3333 17.5 18.3333Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M15.8495 2.89999C14.2328 1.28332 12.6495 1.24166 10.9912 2.89999L9.98283 3.90832C9.89949 3.99166 9.86616 4.12499 9.89949 4.24166C10.5328 6.44999 12.2995 8.21666 14.5078 8.84999C14.5412 8.85832 14.5745 8.86666 14.6078 8.86666C14.6995 8.86666 14.7828 8.83332 14.8495 8.76666L15.8495 7.75832C16.6745 6.94166 17.0745 6.14999 17.0745 5.34999C17.0828 4.52499 16.6828 3.72499 15.8495 2.89999Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M13.0089 9.60832C12.7673 9.49166 12.5339 9.37499 12.3089 9.24166C12.1256 9.13332 11.9506 9.01666 11.7756 8.89166C11.6339 8.79999 11.4673 8.66666 11.3089 8.53332C11.2923 8.52499 11.2339 8.47499 11.1673 8.40832C10.8923 8.17499 10.5839 7.87499 10.3089 7.54166C10.2839 7.52499 10.2423 7.46666 10.1839 7.39166C10.1006 7.29166 9.95892 7.12499 9.83392 6.93332C9.73392 6.80832 9.61726 6.62499 9.50892 6.44166C9.37559 6.21666 9.25892 5.99166 9.14226 5.75832C9.1246 5.72049 9.10752 5.68286 9.09096 5.64544C8.96798 5.36767 8.60578 5.28647 8.39098 5.50126L3.61726 10.275C3.50892 10.3833 3.40892 10.5917 3.38392 10.7333L2.93392 13.925C2.85059 14.4917 3.00892 15.025 3.35892 15.3833C3.65892 15.675 4.07559 15.8333 4.52559 15.8333C4.62559 15.8333 4.72559 15.825 4.82559 15.8083L8.02559 15.3583C8.17559 15.3333 8.38392 15.2333 8.48392 15.125L13.2517 10.3572C13.468 10.1409 13.3864 9.76972 13.105 9.64967C13.0734 9.63615 13.0414 9.62238 13.0089 9.60832Z",fill:"#1F1D1A"})]}):!1]}),e.jsx(v,{appear:!0,show:n,as:t.Fragment,children:e.jsxs(T,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>i(!1),children:[e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(T.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(T.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Marketing Metrics"}),e.jsx("button",{onClick:()=>i(!1),type:"button",children:e.jsx(ue,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-4 space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex flex-row items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==ae.MANUAL?"*":"","Customer Acquisition Cost (CAC)",e.jsx(Je,{children:"The average cost a business incurs to acquire a new customer."})]}),e.jsx("p",{children:s.cac})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex flex-row items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==ae.MANUAL?"*":"","Activations / Daily active users",e.jsx(Je,{children:"Number of new users who become active and engage with your product daily."})]}),e.jsx("p",{children:s.marketing_activations})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex flex-row items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==ae.MANUAL?"*":"","Retention / Churn rate (Monthly)",e.jsx(Je,{children:"Percentage of customers who stay (retention) vs those who leave (churn) each month."})]}),e.jsx("p",{children:s.churn_rate})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex flex-row items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==ae.MANUAL?"*":"","Annual contract value (ACV)",e.jsx(Je,{children:"Average yearly revenue generated from each customer contract."})]}),e.jsx("p",{children:s.acv})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex flex-row items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==ae.MANUAL?"*":"","Customer Satisfaction Score (CSAT/NPS)",e.jsx(Je,{last:!0,children:"Metrics measuring customer satisfaction and likelihood to recommend your product."})]}),e.jsx("p",{children:s.csat})]}),e.jsx("div",{className:"grid grid-cols-2 gap-4",children:s.id?e.jsx(wa,{update:s,afterEdit:d}):null}),o?e.jsxs("div",{className:"mt-4 border border-black/60 bg-brown-main-bg p-3",children:[e.jsxs("p",{className:"flex items-center gap-3 font-semibold",children:[e.jsx(jt,{className:"h-5 text-yellow-500",strokeWidth:2}),"No integrations setup currently."]}),e.jsxs("p",{className:"mt-1",children:["In order to populate financial information in your updates, you must setup integrations with your current financial services"," ",e.jsx(Ve,{to:"/member/integrations",className:"font-semibold text-primary-black underline",children:"here"})]})]}):null]})]})})})})]})})]})}function va({update:s,afterEdit:d}){var P,L,S,h,F,A,x,k,_,f;const[r,n]=t.useState(!1),{dispatch:i}=t.useContext(ie),{dispatch:o}=t.useContext(oe),b=Le({headcount:ee().required("This field is required"),turnover:ee().required("This field is required"),retention_hr:ee().required("This field is required"),satisfaction:ee().required("This field is required"),revenue_per_employee:ee().required("This field is required")}),{register:j,handleSubmit:M,formState:{isSubmitting:g,errors:u}}=Ie({resolver:De(b),defaultValues:{headcount:s.headcount,turnover:s.turnover,retention_hr:s.retention_hr,satisfaction:s.satisfaction,revenue_per_employee:s.revenue_per_employee}});async function c(w){try{await new re().callRawAPI(`/v4/api/records/updates/${s.id}`,{headcount:w.headcount,turnover:w.turnover,retention_hr:w.retention_hr,satisfaction:w.satisfaction,revenue_per_employee:w.revenue_per_employee},"PUT"),n(!1),d()}catch(z){se(i,z.message),z.message!=="TOKEN_EXPIRED"&&$(o,z.message,5e3,"error")}}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"rounded-md bg-primary-black p-2 font-iowan font-medium text-white",onClick:()=>n(!0),children:"Edit manually"}),e.jsx(v,{appear:!0,show:r,as:t.Fragment,children:e.jsxs(T,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>n(!1),children:[e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(T.Panel,{as:"form",className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",onSubmit:M(c),children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(T.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Edit update"}),e.jsx("button",{onClick:()=>n(!1),type:"button",children:e.jsx(ue,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Headcount"}),e.jsx("input",{type:"text",...j("headcount"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(P=u.headcount)!=null&&P.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(L=u.headcount)==null?void 0:L.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Turnover Rate (annual)"}),e.jsx("input",{type:"text",...j("turnover"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(S=u.turnover)!=null&&S.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(h=u.turnover)==null?void 0:h.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Retention Rate (annual)"}),e.jsx("input",{type:"text",...j("retention_hr"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(F=u.retention_hr)!=null&&F.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(A=u.retention_hr)==null?void 0:A.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Employee Satisfaction (employee NPS)"}),e.jsx("input",{type:"text",...j("satisfaction"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(x=u.satisfaction)!=null&&x.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(k=u.satisfaction)==null?void 0:k.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Revenue per Employee"}),e.jsx("input",{type:"text",...j("revenue_per_employee"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(_=u.revenue_per_employee)!=null&&_.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(f=u.revenue_per_employee)==null?void 0:f.message})]}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>n(!1),children:"Cancel"}),e.jsx(Oe,{loading:g,disabled:g,type:"submit",className:"disabled:bg-disabledblack rounded-lg bg-primary-black py-2 text-center font-semibold text-white transition-colors duration-100",children:"Save"})]})]})})})})]})})]})}function Na({update:s,afterEdit:d}){var P,L,S,h,F,A,x,k,_,f;const[r,n]=t.useState(!1),{dispatch:i}=t.useContext(ie),{dispatch:o}=t.useContext(oe),b=Le({new_product_release:ee().required("This field is required"),new_product_sales:ee().required("This field is required"),roi:ee().required("This field is required"),rd:ee().required("This field is required"),on_time_delivery:ee().required("This field is required")}),{register:j,handleSubmit:M,formState:{isSubmitting:g,errors:u}}=Ie({resolver:De(b),defaultValues:{nps:s.new_product_sales,npr:s.new_product_release,roi:s.roi,rd:s.rd,on_time_delivery:s.on_time_deliverye}});async function c(w){console.log(w);try{await new re().callRawAPI(`/v4/api/records/updates/${s.id}`,{nps:w.new_product_release,npr:w.new_product_sales,roi:w.roi,rd:w.rd,on_time_delivery:w.on_time_delivery},"PUT"),n(!1),d()}catch(z){se(i,z.message),z.message!=="TOKEN_EXPIRED"&&$(o,z.message,5e3,"error")}}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"rounded-md bg-primary-black p-2 font-iowan font-medium text-white",onClick:()=>n(!0),children:"Edit manually"}),e.jsx(v,{appear:!0,show:r,as:t.Fragment,children:e.jsxs(T,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>n(!1),children:[e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(T.Panel,{as:"form",className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",onSubmit:M(c),children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(T.Title,{as:"h3",className:"text-[16px] font-semibold leading-6 text-[#1f1d1a] md:text-xl",children:"Edit update"}),e.jsx("button",{onClick:()=>n(!1),type:"button",children:e.jsx(ue,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"New Product Releases"}),e.jsx("input",{type:"text",...j("new_product_release"),className:`focus:shadow-outline  h-[41.6px] w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 font-Inter    text-sm font-normal leading-tight text-[#1d1f1a] shadow focus:outline-none ${(P=u.new_product_release)!=null&&P.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(L=u.new_product_release)==null?void 0:L.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"New Product Sales"}),e.jsx("input",{type:"text",...j("new_product_sales"),className:`focus:shadow-outline  h-[41.6px] w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 font-Inter    text-sm font-normal leading-tight text-[#1d1f1a] shadow focus:outline-none ${(S=u.new_product_sales)!=null&&S.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(h=u.new_product_sales)==null?void 0:h.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"ROI"}),e.jsx("input",{type:"text",...j("roi"),className:`focus:shadow-outline  h-[41.6px] w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 font-Inter    text-sm font-normal leading-tight text-[#1d1f1a] shadow focus:outline-none ${(F=u.roi)!=null&&F.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(A=u.roi)==null?void 0:A.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"R&D (as a % of sales)"}),e.jsx("input",{type:"text",...j("rd"),className:`focus:shadow-outline  h-[41.6px] w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 font-Inter    text-sm font-normal leading-tight text-[#1d1f1a] shadow focus:outline-none ${(x=u.rd)!=null&&x.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(k=u.rd)==null?void 0:k.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"On-time delivery"}),e.jsx("input",{type:"text",...j("on_time_delivery"),className:`focus:shadow-outline  h-[41.6px] w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 font-Inter    text-sm font-normal leading-tight text-[#1d1f1a] shadow focus:outline-none ${(_=u.on_time_delivery)!=null&&_.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(f=u.on_time_delivery)==null?void 0:f.message})]}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>n(!1),children:"Cancel"}),e.jsx(Oe,{loading:g,disabled:g,type:"submit",className:"disabled:bg-disabledblack rounded-lg bg-primary-black py-2 text-center font-semibold text-white transition-colors duration-100",children:"Save"})]})]})})})})]})})]})}const et=({children:s})=>{const[d,r]=t.useState(!1),n=t.useRef(null),i=()=>{n.current&&clearTimeout(n.current),r(!0)},o=()=>{n.current=setTimeout(()=>{r(!1)},100)};return e.jsx(H,{className:"relative inline",children:e.jsxs("div",{onMouseEnter:i,onMouseLeave:o,children:[e.jsx(H.Button,{className:"ml-0",children:e.jsx(Ee,{className:"mt-[3px] h-4 w-4 cursor-pointer",pathClasses:"text-[#1f1d1a]",stroke:"white"})}),d&&e.jsx(v,{show:!0,as:t.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",children:e.jsx(H.Panel,{static:!0,className:"absolute left-0 z-10 mt-3 w-screen max-w-[200px] -translate-x-[30%] transform px-4 text-sm",onMouseEnter:i,onMouseLeave:o,children:e.jsxs("div",{className:"relative rounded-lg bg-[#1f1d1a] px-4 py-3 text-white shadow-lg ring-1 ring-[#1f1d1a]/5",children:[e.jsx("div",{className:"font-medium",children:s}),e.jsx("div",{className:"absolute left-1/2 top-0 h-3 w-3 -translate-x-1/2 -translate-y-1/2 rotate-45 bg-[#1f1d1a]"})]})})})]})})};function Ca({update:s,refetchUpdate:d,showMetric:r}){const[n,i]=t.useState(!1);t.useContext(ie),t.useContext(oe),t.useState(!1);const[o,b]=t.useState(!1);return e.jsxs(e.Fragment,{children:[e.jsxs("button",{className:"flex w-full items-center justify-between text-[16px] font-medium text-[#1f1d1a] md:text-[18px] ",onClick:()=>i(r),children:[e.jsx("span",{children:"Show product metrics"})," ",r?e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M17.5 18.3333H2.5C2.15833 18.3333 1.875 18.05 1.875 17.7083C1.875 17.3667 2.15833 17.0833 2.5 17.0833H17.5C17.8417 17.0833 18.125 17.3667 18.125 17.7083C18.125 18.05 17.8417 18.3333 17.5 18.3333Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M15.8495 2.89999C14.2328 1.28332 12.6495 1.24166 10.9912 2.89999L9.98283 3.90832C9.89949 3.99166 9.86616 4.12499 9.89949 4.24166C10.5328 6.44999 12.2995 8.21666 14.5078 8.84999C14.5412 8.85832 14.5745 8.86666 14.6078 8.86666C14.6995 8.86666 14.7828 8.83332 14.8495 8.76666L15.8495 7.75832C16.6745 6.94166 17.0745 6.14999 17.0745 5.34999C17.0828 4.52499 16.6828 3.72499 15.8495 2.89999Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M13.0089 9.60832C12.7673 9.49166 12.5339 9.37499 12.3089 9.24166C12.1256 9.13332 11.9506 9.01666 11.7756 8.89166C11.6339 8.79999 11.4673 8.66666 11.3089 8.53332C11.2923 8.52499 11.2339 8.47499 11.1673 8.40832C10.8923 8.17499 10.5839 7.87499 10.3089 7.54166C10.2839 7.52499 10.2423 7.46666 10.1839 7.39166C10.1006 7.29166 9.95892 7.12499 9.83392 6.93332C9.73392 6.80832 9.61726 6.62499 9.50892 6.44166C9.37559 6.21666 9.25892 5.99166 9.14226 5.75832C9.1246 5.72049 9.10752 5.68286 9.09096 5.64544C8.96798 5.36767 8.60578 5.28647 8.39098 5.50126L3.61726 10.275C3.50892 10.3833 3.40892 10.5917 3.38392 10.7333L2.93392 13.925C2.85059 14.4917 3.00892 15.025 3.35892 15.3833C3.65892 15.675 4.07559 15.8333 4.52559 15.8333C4.62559 15.8333 4.72559 15.825 4.82559 15.8083L8.02559 15.3583C8.17559 15.3333 8.38392 15.2333 8.48392 15.125L13.2517 10.3572C13.468 10.1409 13.3864 9.76972 13.105 9.64967C13.0734 9.63615 13.0414 9.62238 13.0089 9.60832Z",fill:"#1F1D1A"})]}):!1]}),e.jsx(v,{appear:!0,show:n,as:t.Fragment,children:e.jsxs(T,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>i(!1),children:[e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(T.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(T.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Product Metrics"}),e.jsx("button",{onClick:()=>i(!1),type:"button",children:e.jsx(ue,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-4 space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==ae.MANUAL?"*":"","New Product Releases",e.jsx(et,{children:"Number of new product versions, features, or updates released during this period."})]}),e.jsx("p",{children:s.npr})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==ae.MANUAL?"*":"","New Product Sales",e.jsx(et,{children:"Total number of new product sales or subscriptions acquired in this period."})]}),e.jsx("p",{children:s.nps})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==ae.MANUAL?"*":"","ROI",e.jsx(et,{children:"Return on Investment - Measures the profitability of product investments relative to their costs."})]}),e.jsx("p",{children:s.roi})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==ae.MANUAL?"*":"","R&D (as a % of sales)",e.jsx(et,{children:"Research and Development expenses as a percentage of total sales, indicating investment in innovation."})]}),e.jsx("p",{children:s.rd})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==ae.MANUAL?"*":"","On-time delivery",e.jsx(et,{children:"Percentage of product deliveries or releases that meet scheduled deadlines."})]}),e.jsx("p",{children:s.on_time_delivery})]}),e.jsx("div",{className:"grid grid-cols-2 gap-4",children:s.id?e.jsx(Na,{update:s,afterEdit:d}):null}),o?e.jsxs("div",{className:"mt-4 border border-black/60 bg-brown-main-bg p-3",children:[e.jsxs("p",{className:"flex items-center gap-3 font-semibold",children:[e.jsx(jt,{className:"h-5 text-yellow-500",strokeWidth:2}),"No integrations setup currently."]}),e.jsxs("p",{className:"mt-1",children:["In order to populate financial information in your updates, you must setup integrations with your current financial services"," ",e.jsx(Ve,{to:"/member/integrations",className:"font-semibold text-primary-black underline",children:"here"})]})]}):null]})]})})})})]})})]})}const tt=({children:s})=>{const[d,r]=t.useState(!1),n=t.useRef(null),i=()=>{n.current&&clearTimeout(n.current),r(!0)},o=()=>{n.current=setTimeout(()=>{r(!1)},100)};return e.jsx(H,{className:"relative inline",children:e.jsxs("div",{onMouseEnter:i,onMouseLeave:o,children:[e.jsx(H.Button,{className:"ml-0",children:e.jsx(Ee,{className:"mt-[3px] h-4 w-4 cursor-pointer",pathClasses:"text-[#1f1d1a]",stroke:"white"})}),d&&e.jsx(v,{show:!0,as:t.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",children:e.jsx(H.Panel,{static:!0,className:"absolute left-0 z-10 mt-3 w-screen max-w-[200px] -translate-x-[30%] transform px-4 text-sm",onMouseEnter:i,onMouseLeave:o,children:e.jsxs("div",{className:"relative rounded-lg bg-[#1f1d1a] px-4 py-3 text-white shadow-lg ring-1 ring-[#1f1d1a]/5",children:[e.jsx("div",{className:"font-medium",children:s}),e.jsx("div",{className:"absolute left-1/2 top-0 h-3 w-3 -translate-x-1/2 -translate-y-1/2 rotate-45 bg-[#1f1d1a]"})]})})})]})})};function _a({update:s,refetchUpdate:d,showMetric:r}){const[n,i]=t.useState(!1);t.useContext(ie),t.useContext(oe),t.useState(!1);const[o,b]=t.useState(!1);return e.jsxs(e.Fragment,{children:[e.jsxs("button",{className:"flex w-full items-center justify-between text-[16px] font-medium text-[#1f1d1a] md:text-[18px]  ",onClick:()=>i(r),children:[e.jsx("span",{children:"Show team/HR metrics"})," ",r?e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M17.5 18.3333H2.5C2.15833 18.3333 1.875 18.05 1.875 17.7083C1.875 17.3667 2.15833 17.0833 2.5 17.0833H17.5C17.8417 17.0833 18.125 17.3667 18.125 17.7083C18.125 18.05 17.8417 18.3333 17.5 18.3333Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M15.8495 2.89999C14.2328 1.28332 12.6495 1.24166 10.9912 2.89999L9.98283 3.90832C9.89949 3.99166 9.86616 4.12499 9.89949 4.24166C10.5328 6.44999 12.2995 8.21666 14.5078 8.84999C14.5412 8.85832 14.5745 8.86666 14.6078 8.86666C14.6995 8.86666 14.7828 8.83332 14.8495 8.76666L15.8495 7.75832C16.6745 6.94166 17.0745 6.14999 17.0745 5.34999C17.0828 4.52499 16.6828 3.72499 15.8495 2.89999Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M13.0089 9.60832C12.7673 9.49166 12.5339 9.37499 12.3089 9.24166C12.1256 9.13332 11.9506 9.01666 11.7756 8.89166C11.6339 8.79999 11.4673 8.66666 11.3089 8.53332C11.2923 8.52499 11.2339 8.47499 11.1673 8.40832C10.8923 8.17499 10.5839 7.87499 10.3089 7.54166C10.2839 7.52499 10.2423 7.46666 10.1839 7.39166C10.1006 7.29166 9.95892 7.12499 9.83392 6.93332C9.73392 6.80832 9.61726 6.62499 9.50892 6.44166C9.37559 6.21666 9.25892 5.99166 9.14226 5.75832C9.1246 5.72049 9.10752 5.68286 9.09096 5.64544C8.96798 5.36767 8.60578 5.28647 8.39098 5.50126L3.61726 10.275C3.50892 10.3833 3.40892 10.5917 3.38392 10.7333L2.93392 13.925C2.85059 14.4917 3.00892 15.025 3.35892 15.3833C3.65892 15.675 4.07559 15.8333 4.52559 15.8333C4.62559 15.8333 4.72559 15.825 4.82559 15.8083L8.02559 15.3583C8.17559 15.3333 8.38392 15.2333 8.48392 15.125L13.2517 10.3572C13.468 10.1409 13.3864 9.76972 13.105 9.64967C13.0734 9.63615 13.0414 9.62238 13.0089 9.60832Z",fill:"#1F1D1A"})]}):!1]}),e.jsx(v,{appear:!0,show:n,as:t.Fragment,children:e.jsxs(T,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>i(!1),children:[e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(T.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(T.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Team/HR Metrics"}),e.jsx("button",{onClick:()=>i(!1),type:"button",children:e.jsx(ue,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-4 space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==ae.MANUAL?"*":"","Headcount",e.jsx(tt,{children:"Total number of full-time employees currently working in your organization."})]}),e.jsx("p",{children:s.headcount})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==ae.MANUAL?"*":"","Turnover Rate (annual)",e.jsx(tt,{children:"Percentage of employees who left the organization over the past year relative to average total employees."})]}),e.jsx("p",{children:s.turnover})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==ae.MANUAL?"*":"","Retention Rate (annual)",e.jsx(tt,{children:"Percentage of employees who remained with the organization over the past year."})]}),e.jsx("p",{children:s.retention_hr})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==ae.MANUAL?"*":"","Employee Satisfaction",e.jsx(tt,{children:"Employee Net Promoter Score (eNPS) measuring employee satisfaction and loyalty on a -100 to +100 scale."})]}),e.jsx("p",{children:s.satisfaction})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==ae.MANUAL?"*":"","Revenue Per Employee",e.jsx(tt,{children:"Average revenue generated per employee, calculated by dividing total revenue by number of employees."})]}),e.jsx("p",{children:s.revenue_per_employee})]}),e.jsx("div",{className:"grid grid-cols-2 gap-4",children:s.id?e.jsx(va,{update:s,afterEdit:d}):null}),o?e.jsxs("div",{className:"mt-4 border border-black/60 bg-brown-main-bg p-3",children:[e.jsxs("p",{className:"flex items-center gap-3 font-semibold",children:[e.jsx(jt,{className:"h-5 text-yellow-500",strokeWidth:2}),"No integrations setup currently."]}),e.jsxs("p",{className:"mt-1",children:["In order to populate financial information in your updates, you must setup integrations with your current financial services"," ",e.jsx(Ve,{to:"/member/integrations",className:"font-semibold text-primary-black underline",children:"here"})]})]}):null]})]})})})})]})})]})}const ka=({open:s,setOpen:d,loading:r,changeTemplate:n,setSelectedTemplate:i,defaultTemplate:o})=>{st(),t.useContext(ie),t.useContext(oe),t.useState();const b=()=>{d(!1),i(o)};return e.jsx(v,{appear:!0,show:s,as:t.Fragment,children:e.jsxs(T,{as:"div",className:"relative z-[7000]",onClose:()=>b(),children:[e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"overflow-y-auto fixed inset-0",children:e.jsx("div",{className:"flex justify-center items-center p-4 min-h-full text-center",children:e.jsx(v.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(T.Panel,{className:"w-full max-w-[500px] transform space-y-10 overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-sm shadow-xl transition-all",children:[e.jsxs("div",{className:"flex flex-col justify-between",children:[e.jsxs(T.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:["Are you sure you want to change your update template? ",e.jsx("br",{})]}),e.jsx("p",{className:"mt-2 text-base font-iowan",children:"All contents of existing update will be erased"})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mt-6",children:[e.jsx("button",{className:"rounded-[.125rem] border-2 border-gray-400 py-2 text-center",type:"button",onClick:()=>b(),children:"Cancel"}),e.jsx(Ft,{loading:r,disabled:r,onClick:()=>n(),className:"!w-auto !rounded-[.125rem] bg-[#1f1d1a] py-2 text-center font-semibold text-white transition-colors duration-100 disabled:bg-opacity-60",children:"Change"})]})]})})})})]})})},Jn=()=>{var xt,Qe;const s=t.useRef(null),d=t.useRef(null),r=t.useRef(null),n=t.useRef(null),i=Bt(),[o]=Jt(),b=o.get("next");(xt=i==null?void 0:i.state)==null||xt.templateTitle;const[j,M]=t.useState(!1),[g,u]=t.useState(!1),[c,P]=t.useState(),[L,S]=t.useState([]);st();const{globalDispatch:h,authDispatch:F,authState:A}=bt(),[x,k]=t.useState(null),{id:_}=Pe(),{data:f}=Qt(),w=o.get("new"),z=async()=>{try{await new re().callRawAPI(`/v3/api/custom/goodbadugly/activities/${m==null?void 0:m.id}/view`,{},"PUT")}catch(l){console.error("Error calling view endpoint:",l)}};t.useEffect(()=>{w=="new"&&z()},[]);const{update:m,loading:{single:X},refetch:Z}=xs(_),{notes:J,refetch:Q}=hs(_),{updateGroups:V,refetch:pe}=fs(_);t.useEffect(()=>{if((V==null?void 0:V.length)>0){const l=V.map(E=>E.group_id);localStorage.setItem(`update_${_}_groups`,JSON.stringify(l))}},[V,_]),t.useEffect(()=>{localStorage.removeItem(`update_${_}_groups`)},[_]);const[je,C]=t.useState(""),[a,D]=t.useState(!1),[B,xe]=t.useState(""),[y,Y]=t.useState(!1),[te,ge]=t.useState(!1),[ce,ye]=t.useState("7");t.useState("text");const[de,$e]=t.useState({show_financial_metrics:null,show_product_metric:null,show_hr_metric:null,show_marketing_metrics:null,show_investment_metrics:null}),[wt,nt]=t.useState(!1),[rt,Ne]=t.useState(!1),[le,ke]=t.useState({showPrompt:!1,updateId:_,createdGroupId:null,skipAwareness:!1,isManualTrigger:!1}),[yt,ze]=t.useState([]),{profile:R}=at({isPublic:!1}),{getMyCompanyMembers:Pt}=Es(),{getRecipientGroups:N}=Fs({}),{data:{isCollaborator:vt,collaborator:it},getUpdateCollaborator:Nt}=As({filter:[`update_id,eq,${_}`]}),Ce=(R==null?void 0:R.id)==(m==null?void 0:m.user_id),[ve,Te]=t.useState([]),[qe,Ye]=t.useState(!1),[We,Be]=t.useState(!1);console.log(We),t.useEffect(()=>{Te(J)},[J]);async function lt(l){try{const E=await Tt(h,F,{method:"DELETE",endpoint:`/v3/api/custom/goodbadugly/updates/recipient/${l}`});E!=null&&E.error||($(h,"Group removed successfully",5e3,"success"),pe(),Q())}catch(E){se(F,E.message),E.message!=="TOKEN_EXPIRED"&&$(h,E.message,5e3,"error")}}const Ue=async()=>{var I,G,W,K;const l=await N({filter:[`goodbadugly_recipient_group.user_id,eq,${R==null?void 0:R.id}`]}),E=await Pt({filter:[`goodbadugly_company_member.company_id,eq,${(G=(I=R==null?void 0:R.companies)==null?void 0:I[0])==null?void 0:G.id}`]}),p=(W=l==null?void 0:l.data)==null?void 0:W.map(q=>{var he,ne;return{recipient_member:q==null?void 0:q.recipient_member,group_name:(he=q==null?void 0:q.group)==null?void 0:he.group_name,id:(ne=q==null?void 0:q.group)==null?void 0:ne.id,type:1}}),O=(K=E==null?void 0:E.data)==null?void 0:K.map(q=>{var he,ne,_e,Me,Se,me;return{group_name:(he=q==null?void 0:q.user)!=null&&he.first_name||(ne=q==null?void 0:q.user)!=null&&ne.last_name?[(_e=q==null?void 0:q.user)==null?void 0:_e.first_name,(Me=q==null?void 0:q.user)==null?void 0:Me.last_name].join(" "):(Se=q==null?void 0:q.user)==null?void 0:Se.email,id:(me=q==null?void 0:q.user)==null?void 0:me.id,type:2}});ze(()=>[...p,...O])};async function Fe(l){const E={...de};m[l],$e({...de,[l]:!(de!=null&&de[l])});try{await new re().callRawAPI(`/v4/api/records/updates/${_}`,{[l]:m[l]?0:1},"PUT"),Z(),$(h,"Updated")}catch(p){$e(E),se(F,p.message),p.message!=="TOKEN_EXPIRED"&&$(h,p.message,5e3,"error")}}async function ot(){var l,E;{u(!0);try{const p=new rs,O=await Wt(h,F,"updates",_,{template_name:c==null?void 0:c.title,user_id:A.user,mrr:0,arr:0,cash:0,burnrate:0,public_link_enabled:0,private_link_open:1,date:U().format("MMM D, YYYY"),company_id:A.company.id});if(!(O!=null&&O.error)){const I=await Ht(h,F,"notes",{filter:[`update_id,eq,${_}`]}),G=(l=I==null?void 0:I.data)==null?void 0:l.map(K=>{Zt(h,F,"notes",K.id)});await Promise.all(G);const W=(E=c==null?void 0:c.template)==null?void 0:E.map(K=>Vt(h,F,"notes",{update_id:_,type:K,status:0}));await Promise.all(W),Z(),Q(),M(!1),u(!1),$(h,"Template updated")}}catch(p){u(!1),se(F,p.message),p.message!=="TOKEN_EXPIRED"&&$(h,p.message,5e3,"error")}}}const ct=l=>{const E=JSON.parse(l==null?void 0:l.target.selectedOptions[0].dataset.template);xe(l==null?void 0:l.target.value),M(!0),P(E)};t.useEffect(()=>{xe(m==null?void 0:m.template_name),as(h,m,"update")},[m]),t.useEffect(()=>{C(m.date),ye(m.recipient_access||"7"),$e({show_financial_metrics:m.show_financial_metrics===1,show_hr_metric:m.show_hr_metric===1,show_product_metric:m.show_product_metric===1,show_marketing_metrics:m.show_marketing_metrics===1,show_investment_metrics:m.show_investment_metrics===1})},[m]),t.useEffect(()=>{h({type:"SETPATH",payload:{path:"updates"}})},[]),t.useEffect(()=>{R!=null&&R.id&&Ue()},[R==null?void 0:R.id]),t.useEffect(()=>{R!=null&&R.id&&_&&Nt({filter:[`update_id,eq,${_}`],join:[]})},[R==null?void 0:R.id,_]),t.useEffect(()=>{R!=null&&R.id&&((R==null?void 0:R.awareness)<3&&!(le!=null&&le.skipAwareness)?ke(l=>({...l,showPrompt:!0,isManualTrigger:!1})):ke(l=>({...l,showPrompt:!1,isManualTrigger:!1})))},[R==null?void 0:R.id,R==null?void 0:R.awareness,le==null?void 0:le.skipAwareness]),t.useEffect(()=>{b&&ke(l=>({...l,next:b}))},[b]);const Xe=V.length===4?4:3,dt=V.slice(0,Xe),Ke=V.slice(Xe),Ct=()=>{const l=document.querySelectorAll(".section-collaborators");l.length>0&&(l[0].scrollIntoView({behavior:"smooth"}),l.forEach(E=>{E.click()}))},_t=async l=>{if(!l.destination)return;const E=Array.from(ve),[p]=E.splice(l.source.index,1);E.splice(l.destination.index,0,p),Te(E);try{await new re().callRawAPI("/v3/api/custom/goodbadugly/notes/reorder",{notes:E.map((I,G)=>({id:I.id,order:G})),update_id:m.id},"POST"),Q()}catch(O){Te(J),se(F,O.message),O.message!=="TOKEN_EXPIRED"&&$(h,O.message,5e3,"error")}},mt=()=>{if(!(m!=null&&m.sent_at)||!(m!=null&&m.cadence))return!1;const l=new Date(m.sent_at),E=new Date(l);return E.setDate(l.getDate()+m.cadence),new Date<E},ut=()=>{if(!(m!=null&&m.sent_at)||!(m!=null&&m.cadence))return null;const l=new Date(m.sent_at),E=new Date(l);return E.setDate(l.getDate()+m.cadence),E.toDateString()},kt=async()=>{try{await new re().callRawAPI(`/v4/api/records/updates/${_}`,{cadence:null},"PUT"),Z(),Be(!1),$(h,"Cadence stopped successfully")}catch(l){se(F,l.message),l.message!=="TOKEN_EXPIRED"&&$(h,l.message,5e3,"error")}};return t.useEffect(()=>{console.log("=== EditUpdatesPage refs status ==="),console.log("templateSelectRef.current:",r==null?void 0:r.current),console.log("recipientSelectRef.current:",n==null?void 0:n.current),console.log("createGroupModalRef.current:",s==null?void 0:s.current)},[r.current,n.current,s.current]),e.jsxs("div",{className:`relative h-full w-full ${X&&"overflow-hidden"}`,children:[X&&e.jsx("div",{className:"absolute z-[9999] h-full w-full bg-black/20",children:e.jsx("div",{className:"flex h-full items-center justify-center",children:e.jsx(Xt,{loading:X,color:"#1f1d1a"})})}),e.jsx("div",{className:"flex w-full flex-col border-2 border-black p-4 md:hidden",children:e.jsxs("div",{className:"flex w-full flex-wrap items-center justify-between",children:[e.jsx("span",{className:"font-Inter text-xl font-bold",children:((Qe=A.company)==null?void 0:Qe.name)||"Company Name"}),e.jsxs("div",{className:"flex gap-2",children:[mt()&&e.jsx("button",{className:"  flex h-[28px]  w-[28px] items-center justify-center gap-2  rounded border border-[#1f1d1a] bg-transparent text-[#1f1d1a] sm:h-[36px] sm:w-[36px]",title:"Active Scheduled Update Cadence",onClick:()=>Ye(!0),children:e.jsx(zt,{className:"h-4 w-4 sm:h-5 sm:w-5"})}),e.jsx("div",{className:"",children:e.jsx(Yt,{update:m,refetch:Z,isOwner:Ce})}),e.jsxs("button",{disabled:!Ce,className:"flex h-[2.25rem] w-fit min-w-fit items-center justify-center gap-2 bg-transparent px-2 font-iowan text-[1rem] font-[700] leading-[1.25rem] text-[#1f1d1a]",onClick:()=>ke(l=>({...l,showPrompt:!0,isManualTrigger:!0})),children:[e.jsx(Lt,{className:"h-4 w-4"})," Help"]})]}),e.jsx("div",{className:`${f!=null&&f.subscription?"":"hidden"}`,children:e.jsx(Mt,{children:e.jsx(Ds,{})})})]})}),e.jsxs("div",{className:"mx-auto rounded px-5 py-5 pt-8 shadow-md sm:px-8 xl:px-8",children:[e.jsxs("div",{className:"flex w-full grid-cols-2 flex-wrap  items-center justify-between gap-3 border-b-[2px] border-[#1F1D1A1A]/10 pb-[12px] sm:border-b-0 sm:pb-0 md:grid md:grid-cols-2",children:[e.jsx("div",{className:"",children:e.jsx(xa,{update:m,afterEdit:Z,isOwner:Ce})}),e.jsxs("div",{className:"flex justify-end gap-2 md:justify-end",children:[e.jsx("div",{className:"md:hidden",children:e.jsx(Is,{nextScheduledUpdate:x})}),mt()&&e.jsx("button",{className:" hidden h-[28px] w-[28px]  items-center justify-center gap-2 rounded  border border-[#1f1d1a] bg-transparent text-[#1f1d1a] sm:h-[36px] sm:w-[36px] md:flex",title:"Active Scheduled Update Cadence",onClick:()=>Ye(!0),children:e.jsx(zt,{className:"h-4 w-4 sm:h-5 sm:w-5"})}),e.jsx("div",{className:"hidden md:block",children:e.jsx(Yt,{update:m,refetch:Z,isOwner:Ce})}),e.jsxs("button",{disabled:!Ce,className:" hidden h-[2.25rem] w-fit min-w-fit items-center justify-center gap-2 bg-transparent px-2 font-iowan text-[1rem] font-[700] leading-[1.25rem] text-[#1f1d1a] md:flex",onClick:()=>ke(l=>({...l,showPrompt:!0,isManualTrigger:!0})),children:[e.jsx(Lt,{className:"h-4 w-4"})," Help"]})]})]}),e.jsx("hr",{className:"hidden border-[1px] border-transparent  md:my-10  md:block md:border-[#1F1D1A1A] "}),e.jsxs("div",{className:"mt-6 flex w-full flex-col",children:[e.jsxs("div",{className:"flex flex-col gap-5 md:flex-row",children:[e.jsxs("div",{className:"flex w-full flex-wrap items-center gap-3 md:justify-start",children:[e.jsx("div",{className:"flex max-w-[390px] grow-[1] flex-row flex-wrap items-center gap-6 sm:max-w-fit md:w-auto md:flex-row",children:e.jsxs("select",{ref:r,disabled:!Ce,value:B,onChange:ct,className:"focus:shadow-outline font h-[36px] w-full appearance-none rounded-[3px] border-[1px] border-[#1f1d1a] bg-brown-main-bg py-1 pl-4 pr-5 font-iowan leading-tight text-[#1f1d1a] focus:outline-none sm:w-[172px] md:w-auto ",children:[e.jsx("option",{value:"",children:"Select Template"}),Ut.map(l=>e.jsx("option",{"data-template":JSON.stringify(l),value:l.title,children:l.title},l.id))]})}),e.jsx(Ws,{afterRestore:Q,template:m==null?void 0:m.template_name,isOwner:Ce})]}),e.jsx(Gt,{isOwner:Ce,createGroupModalRef:s,groups:yt,updateGroups:V,refetch:Q,refetchUpdateGroups:pe,setIsOpen:nt,setSelectedGroupIDs:S,ref:n,isOpen:wt,selectedGroupIDs:L,update:m})]}),e.jsx("div",{className:"flex w-full sm:justify-end",children:e.jsxs("div",{className:"mt-2 flex w-full min-w-full max-w-full flex-wrap gap-2 sm:justify-end",children:[dt.map((l,E)=>e.jsx(H,{className:`relative  ${E!==0?"z-[${8 - index - 1}]}":"z-[8]"}`,children:({open:p})=>e.jsxs(e.Fragment,{children:[e.jsxs(H.Button,{as:"div",onMouseEnter:()=>D(l.id),onMouseLeave:()=>D(!1),className:"line-clamp-1 inline-flex cursor-pointer items-center gap-1 overflow-hidden text-ellipsis whitespace-nowrap rounded-[50px] border border-[#1f1d1a] bg-[#BCBBBA] px-1 py-[2px] text-[10px] hover:text-gray-900 md:line-clamp-2 md:gap-2 md:whitespace-normal md:px-[6px] md:py-[4px] md:text-[12px]",children:[e.jsx("span",{className:"max-w-[60px] truncate md:max-w-none",children:l.group_name}),Ce?e.jsx("button",{type:"button",onClick:O=>{lt(l.id)},className:"ml-0.5 focus:outline-none md:ml-1",children:e.jsx(Ls,{className:"h-2 w-2 md:h-3 md:w-3",strokeWidth:2})}):null]}),e.jsx(v,{as:t.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",show:l.id===a,onMouseEnter:()=>D(l.id),onMouseLeave:()=>D(!1),children:e.jsx(H.Panel,{className:`absolute ${E===0||E===2?"-translate-x-[15%] md:-translate-x-1/2":"left-0"}  mt-2 w-fit -translate-x-1/2 transform whitespace-nowrap px-2 z-[${8-E-1}]`,children:e.jsx("div",{className:"overflow-hidden  rounded-lg bg-[#1f1d1a] p-[3px] text-white shadow-lg ring-1 ring-[#1f1d1a]/5 md:p-[6px]",children:e.jsxs("div",{className:"text-[8px] font-medium md:text-[12px]",children:[l.members.length==0?e.jsx("span",{children:"No members"}):null,l.members.map((O,I,G)=>e.jsxs("span",{className:"mr-2",children:[O.first_name," ",O.last_name," ",I==G.length-1?"":"•"]},O.id))]})})})})]})},l.id)),Ke.length>0&&V.length>4&&e.jsx(H,{className:"relative z-[4]",children:({open:l})=>e.jsxs(e.Fragment,{children:[e.jsx(H.Button,{as:"div",onMouseEnter:()=>Ne(!0),onMouseLeave:()=>Ne(!1),className:"line-clamp-2 inline-flex cursor-pointer items-center gap-2 overflow-x-hidden whitespace-nowrap rounded-[50px] border border-[#1f1d1a] bg-[#BCBBBA] px-[3px] py-[2px] text-[10px] hover:text-gray-900 md:px-[6px] md:py-1 md:text-[12px]",children:e.jsxs("span",{className:"max-w-[60px] md:max-w-none",children:["+",Ke.length," more"]})}),e.jsx(v,{show:rt,as:t.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",children:e.jsx(H.Panel,{static:!0,className:"absolute left-0 z-[4] mt-2 w-fit -translate-x-1/2 transform whitespace-nowrap px-2",onMouseEnter:()=>Ne(!0),onMouseLeave:()=>Ne(!1),children:e.jsx("div",{className:"overflow-hidden rounded-lg bg-[#1f1d1a] p-[3px]  text-white shadow-lg ring-1 ring-[#1f1d1a]/5 md:p-[6px]",children:e.jsx("div",{className:"text-[8px] font-medium md:text-[12px]",children:Ke.map((E,p)=>e.jsx("div",{className:"flex items-center justify-between gap-2 py-1",children:e.jsx("span",{children:E.group_name})},E.id))})})})})]})})]})})]}),e.jsx("hr",{className:"my-2 mt-4 border-[1px]  border-[#1f1d1a]/10 sm:my-5 md:my-10 md:border-[#1F1D1A1A]"}),e.jsxs("div",{className:"",children:[e.jsxs("div",{className:"mt-5 items-start justify-between sm:mt-12",children:[e.jsxs("div",{className:"mb-5 flex w-full items-center justify-between",children:[" ",e.jsx("h3",{className:"text-[1.125rem] font-semibold sm:text-[1.25rem]",children:"Edit Update Metrics"}),e.jsxs("div",{onClick:()=>{Y(!y)},className:"flex cursor-pointer items-center gap-2",children:[e.jsx("p",{className:"font-iowan underline underline-offset-2",children:y?"Close":"Open"}),e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M12.3536 14.6464L16.1464 10.8536C16.4614 10.5386 16.2383 10 15.7929 10L8.20711 10C7.76165 10 7.53857 10.5386 7.85355 10.8536L11.6464 14.6464C11.8417 14.8417 12.1583 14.8417 12.3536 14.6464Z",fill:"black"})})]})]}),y?e.jsxs("div",{className:"flex w-full flex-col justify-between md:flex-row md:gap-5",children:[e.jsxs("div",{className:"w-full  md:w-[45%]",children:[" ",e.jsxs("div",{className:"flex h-[50px] w-full items-center justify-between gap-4 md:justify-start",children:[e.jsx(He,{enabled:de.show_product_metric,setEnabled:()=>Fe("show_product_metric")}),e.jsx(Ca,{update:m,refetchUpdate:Z,showMetric:de.show_product_metric})]}),e.jsxs("div",{className:"flex h-[40px] w-full items-center justify-between gap-4 md:justify-start",children:[e.jsx(He,{enabled:de.show_financial_metrics,setEnabled:()=>Fe("show_financial_metrics")}),e.jsx(Ks,{update:m,refetchUpdate:Z,showMetric:de.show_financial_metrics})]}),e.jsxs("div",{className:"flex h-[40px] w-full items-center justify-between gap-4 md:justify-start",children:[e.jsx(He,{enabled:de.show_hr_metric,setEnabled:()=>Fe("show_hr_metric")}),e.jsx(_a,{update:m,refetchUpdate:Z,showMetric:de.show_hr_metric})]})]}),e.jsxs("div",{className:"w-full md:w-[45%]",children:[e.jsxs("div",{className:"flex h-[40px] w-full items-center justify-between gap-4 md:justify-start",children:[e.jsx(He,{enabled:de.show_marketing_metrics,setEnabled:()=>Fe("show_marketing_metrics")}),e.jsx(ya,{update:m,refetchUpdate:Z,showMetric:de.show_marketing_metrics})]}),e.jsxs("div",{className:"flex h-[40px] w-full items-center justify-between gap-4 md:justify-start",children:[e.jsx(He,{enabled:de.show_investment_metrics,setEnabled:()=>Fe("show_investment_metrics")}),e.jsx(Gs,{update:m,refetchUpdate:Z,showMetric:de.show_investment_metrics})]})]})]}):e.jsx("div",{className:"border-2 border-[#1F1D1A]"}),e.jsx("div",{className:"",children:A.company.id?e.jsx("div",{className:"mt-6 flex flex-col items-end"}):null})]}),e.jsx(ks,{onDragEnd:_t,children:e.jsx(Ss,{droppableId:"sections",children:l=>e.jsxs("div",{className:"space-y-12",...l.droppableProps,ref:l.innerRef,children:[ve.map((E,p)=>e.jsx(Ts,{draggableId:E.id.toString(),index:p,children:(O,I)=>e.jsx("div",{ref:O.innerRef,...O.draggableProps,style:{...O.draggableProps.style,opacity:I.isDragging?.8:1},children:e.jsx(aa,{note:E,refetch:Q,dragHandleProps:O.dragHandleProps,update:m,isOwner:Ce,collaborator:it,isCollaborator:vt})})},E.id)),l.placeholder]})})}),Ce?e.jsxs(e.Fragment,{children:[e.jsx(ua,{afterAdd:Q}),e.jsx(Vs,{investors:V.flatMap(l=>l.members).filter((l,E,p)=>p.findIndex(O=>O.id===l.id)===E)}),e.jsxs("div",{className:"mt-8 flex flex-col items-start justify-between md:flex-row md:items-center",children:[e.jsxs("label",{className:"flex items-center gap-3 text-lg font-medium capitalize",children:[e.jsx("input",{type:"checkbox",checked:te,className:"bg-brown-main-bg",onChange:()=>ge(l=>!l)})," ","CC myself"]}),e.jsxs("div",{className:"mt-4 flex w-fit flex-wrap items-center justify-between gap-4 md:mt-0 md:justify-normal",children:[e.jsxs("p",{className:"whitespace-nowrap font-iowan md:w-auto",children:["Give recipients access for"," "]}),e.jsxs("select",{value:ce,onChange:l=>ye(l.target.value),className:"focus:shadow-outline font w-auto appearance-none rounded border border-[#1f1d1a] bg-brown-main-bg py-2 pl-6 pr-8 font-iowan leading-tight text-[#1f1d1a] focus:outline-none md:w-auto ",children:[e.jsx("option",{value:"1",children:"1 day"}),e.jsx("option",{value:"7",children:"7 days"}),e.jsx("option",{value:"14",children:"2 weeks"}),e.jsx("option",{value:"30",children:"1 month"}),e.jsx("option",{value:"90",children:"3 months"})]}),e.jsxs("div",{className:"flex w-full justify-between sm:w-auto sm:justify-normal md:gap-2",children:[e.jsx(Ve,{to:`/${ns[R==null?void 0:R.role]}/update/preview/${_}?mode=preview`,className:"focus:shadow-outline font t flex !w-full items-center justify-center whitespace-nowrap rounded-[2px] bg-[#1f1d1a] px-4 py-2 text-center font-iowan text-base  font-bold text-white focus:outline-none sm:text-base md:w-auto",children:"Preview Update"}),e.jsx(ja,{nextScheduledUpdate:x,setNextScheduledUpdate:k,refetch:Z,UpdateCadence:m==null?void 0:m.cadence,updateGroups:V,reportDate:je,recipientAccess:ce,ccMyself:te,selectedTemplate:B,className:"send-update-button !w-full md:!w-auto",sendUpdateRef:d})]})]})]})]}):null]}),e.jsx(ka,{open:j,setOpen:M,changeTemplate:ot,loading:g,setSelectedTemplate:xe,defaultTemplate:(m==null?void 0:m.template_name)||""}),e.jsx(Ms,{data:le,next:le==null?void 0:le.next,title:e.jsx(e.Fragment,{children:"Getting Started With Updates?"}),isOpen:le==null?void 0:le.showPrompt,isManualTrigger:le==null?void 0:le.isManualTrigger,createGroupModalRef:s,templateSelectRef:r,recipientSelectRef:n,update:m,notes:ve,updateGroups:V,collaborator:it,onClose:(l=!1)=>ke(E=>({...E,showPrompt:!1,skipAwareness:l,isManualTrigger:!1})),updateNext:l=>{ke(E=>({...E,next:l}))},onNext:l=>{var E;console.log("=== onNext called with step:",l),console.log("Current refs status:"),console.log("templateSelectRef.current:",r==null?void 0:r.current),console.log("recipientSelectRef.current:",n==null?void 0:n.current),ke(p=>({...p,next:l,showPrompt:!1,isManualTrigger:!1})),l==5&&(console.log("Step 5 triggered - clicking recipientSelectRef"),console.log("recipientSelectRef?.current",n==null?void 0:n.current),(E=n==null?void 0:n.current)==null||E.click()),l==6&&(console.log("Step 6 triggered - calling handleCollaboratorsClick"),Ct()),l==7&&(console.log("Step 7 triggered - scrolling to send button"),setTimeout(()=>{var p,O;(p=document.querySelector(".send-update-button"))==null||p.scrollIntoView({behavior:"smooth"}),d!=null&&d.current&&((O=d==null?void 0:d.current)==null||O.click())},250))}}),qe&&e.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50",children:e.jsxs("div",{className:"w-full max-w-xl transform rounded-md border-[1px] border-[#1f1d1a] bg-brown-main-bg p-8 text-left align-middle text-base shadow-xl transition-all",children:[e.jsx("h2",{className:"text-lg font-bold",children:"Scheduled Updates Cadence"}),e.jsxs("p",{children:["Current Cadence: ",m.cadence," days"]}),e.jsxs("p",{children:["Next Send Date: ",ut()]}),e.jsx("button",{className:"mt-4 rounded bg-red-500 px-4 py-2 text-white",onClick:()=>{Ye(!1),Be(!0)},children:"Stop Cadence"}),e.jsx("button",{className:"ml-2 mt-4 rounded bg-gray-500 px-4 py-2 text-white",onClick:()=>Ye(!1),children:"Close"})]})}),e.jsx(Mt,{children:e.jsx(Ps,{title:"Stop Cadence",mode:"manual",action:"Stop",multiple:!1,onSuccess:kt,inputConfirmation:!1,onClose:()=>Be(!1),customMessage:e.jsx(e.Fragment,{children:"Are you sure you want to stop the cadence?"}),isOpen:We})})]})]})};export{Jn as default};
