import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as o,h as z,b as G,L as K}from"./vendor-4cdf2bd1.js";import{A as I,G as M,M as A,t as W,s as q,L as B,a3 as H}from"./index-46de5032.js";import{u as R}from"./react-hook-form-9f4fcfa9.js";import{o as F}from"./yup-c41d85d2.js";import{c as U,a as _}from"./yup-342a5df4.js";import"./index-d0de8b06.js";import{L as X}from"./index-6edcbb0d.js";import{h as J}from"./moment-a9aaa855.js";import{P as Q}from"./index-3283c9b7.js";import{R as Z}from"./tableWrapper-ca490fb1.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@hookform/resolvers-fad2f3d1.js";import"./index.esm-7add6cfb.js";import"./react-icons-36ae72b7.js";const ee=s=>Object.fromEntries(Object.entries(s).filter(([g,d])=>d!==null&&d!==""));function V(s){const[g,d]=o.useState(!0),[h,E]=o.useState([]),[i,b]=o.useState(0),[n,p]=z(),{dispatch:S}=o.useContext(I),{dispatch:j}=o.useContext(M),u=parseInt(n.get("limit")||"30"),r=parseInt(n.get("page")||"1"),y=n.get("search"),f=n.get("datefrom"),v=n.get("dateto"),N=n.get("update_title"),w=n.get("section"),a=n.get("commenter");async function c(){d(!0);try{const m=new A,D=ee({comment:y,from:f,to:v,title:N,section:w,commenter:a,page:r.toString(),limit:u.toString()}),k=new URLSearchParams(D).toString(),x=await m.callRawAPI(`/v3/api/custom/goodbadugly/member/get-engagements?${k}`);E((x==null?void 0:x.engagements)||[]),b((x==null?void 0:x.total)||0)}catch(m){W(S,m.message),m.message!=="TOKEN_EXPIRED"&&q(j,m.message,5e3,"error")}d(!1)}o.useEffect(()=>{c()},[u,r,y,f,v,N,w,a]);const P=Math.ceil(i/u);return{loading:g,engagements:h,refetch:c,totalCount:i,currentPage:r,pageCount:P,pageSize:u,updatePageSize:m=>{n.set("limit",m.toString()),n.set("page","1"),p(n)},previousPage:()=>{r>1&&(n.set("page",(r-1).toString()),p(n))},nextPage:()=>{r<P&&(n.set("page",(r+1).toString()),p(n))},canPreviousPage:r>1,canNextPage:r<P}}const te=()=>{var x,T,O,$;const[s,g]=z(),[d,h]=o.useState(""),[E,i]=o.useState(new Date().toISOString().split("T")[0]),{engagements:b,refetch:n,loading:p}=V(),[S,j]=o.useState(!1),[u,r]=o.useState(""),[y,f]=o.useState({});s.get("update_title"),s.get("section"),s.get("commenter");const v=U({update_title:_(),section:_(),commenter:_()}),N=Object.values(b.reduce((t,l)=>(t[l.section]=l,t),{})),w=Object.values(b.reduce((t,l)=>(t[l.commenter]=l,t),{})),a=Object.values(b.reduce((t,l)=>(t[l.title]=l,t),{})),{register:c,handleSubmit:P,setError:Y,reset:C,resetField:L,formState:{errors:m}}=R({resolver:F(v),defaultValues:{update_title:s.get("update_title")||"",section:s.get("section")||"",commenter:s.get("commenter")||""}});o.useEffect(()=>{const t={update_title:s.get("update_title")||"",section:s.get("section")||"",commenter:s.get("commenter")||"",datefrom:s.get("datefrom")||"",dateto:s.get("dateto")||"",search:s.get("search")||""};f(t),console.log(t),["update_title","section","commenter"].forEach(l=>L(l,{defaultValue:t[l]})),h(t.datefrom),r(t.search)},[s,C]);function D(t){const l={datefrom:d,dateto:E,update_title:t.update_title,section:t.section,commenter:t.commenter,search:u};f(l),g(l),j(!S)}const k=t=>{h(t.target.value)};return console.log(u),e.jsxs("div",{className:"flex flex-col ",children:[e.jsx("label",{className:"mb-2 block  text-lg font-bold capitalize capitalize text-[#1f1d1a]",children:"Search"}),e.jsx("form",{className:"flex w-full flex-row flex-wrap ",onSubmit:t=>{t.preventDefault(),P(D)()},children:e.jsxs("div",{className:"flex w-full flex-row flex-wrap items-center ",children:[e.jsxs("div",{className:"flex w-full flex-row flex-wrap items-end gap-4",children:[e.jsxs("div",{className:"flex w-full flex-row items-center sm:w-auto",children:[e.jsx("input",{type:"text",value:u,placeholder:"Search comment",onChange:t=>r(t.target.value),className:`focus:shadow-outline w-full appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm text-sm text-sm font-normal font-normal font-normal capitalize leading-tight text-[#1d1f1a] shadow focus:outline-none sm:w-[180px] ${(x=m.group_name)!=null&&x.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(T=m.group_name)==null?void 0:T.message})]}),e.jsxs("div",{className:"w-full sm:w-auto",children:[e.jsx("div",{className:"flex w-full flex-row items-center justify-between gap-4 ",children:e.jsx("div",{className:"w-[100%] sm:w-auto",children:e.jsx("input",{type:"date",className:`h-[34.5px] w-full appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 pr-5 text-sm text-sm text-sm font-normal font-normal font-normal leading-tight text-[#1d1f1a] shadow sm:w-[180px] sm:pr-3 sm:pr-3 ${(O=m.date)!=null&&O.message?"border-red-500":""}`,value:d,onChange:k})})}),e.jsx("p",{className:"text-field-error italic text-red-500",children:($=m.date)==null?void 0:$.message})]}),e.jsxs("select",{...c("update_title"),className:"focus:shadow-outline w-full appearance-none text-ellipsis rounded border border-[#1f1d1a] bg-transparent py-2 pl-3 pr-8 text-sm font-normal capitalize leading-tight text-[#1f1d1a] shadow focus:outline-none sm:w-[180px]",children:[e.jsxs("option",{value:"",disabled:!0,children:[" ","- title -"," "]}),a.map(t=>e.jsx("option",{value:t.title,children:t.title},t.id))]}),e.jsxs("select",{...c("section"),className:"focus:shadow-outline w-full appearance-none rounded border border-[#1f1d1a] bg-transparent  py-2 pl-3 pr-8 text-sm font-normal leading-tight text-[#1f1d1a] shadow focus:outline-none sm:w-[180px]",children:[e.jsxs("option",{value:"",disabled:!0,children:[" ","- Section -"," "]}),N.map(t=>e.jsx("option",{value:t.section,children:t.section},t.id))]}),e.jsxs("select",{...c("commenter"),className:"focus:shadow-outline w-full appearance-none rounded border border-[#1f1d1a] bg-transparent  py-2 pl-3 pr-8 text-sm font-normal leading-tight text-[#1f1d1a] shadow focus:outline-none sm:w-[180px]",children:[e.jsxs("option",{value:"",disabled:!0,children:[" ","- Commenter -"," "]}),w.map(t=>e.jsx("option",{value:t.commenter,children:t.commenter},t.id))]})]}),e.jsxs("div",{className:"mt-4 flex items-center gap-4 self-end",children:[e.jsx("button",{type:"submit",disabled:p,className:"font-iowan-regular  rounded-md bg-primary-black/80 px-4 py-1 font-semibold text-white hover:bg-primary-black",children:"Search"}),e.jsx("button",{type:"button",onClick:()=>{C({update_title:"",section:"",commenter:""}),r(""),h(""),s.delete("search"),s.delete("datefrom"),s.delete("dateto"),s.delete("update_title"),s.delete("section"),s.delete("commenter"),g(s)},disabled:p,className:"rounded-md px-4 py-1 font-semibold text-[#1f1d1a]",children:"Clear"})]})]})})]})};new A;const ae=[{header:"Title ",accessor:"title "},{header:"Section",accessor:"section "},{header:"Comment ",accessor:"comment "},{header:"Commenter ",accessor:"commenter "},{header:"Date/Time ",accessor:"time/date "},{header:"Action ",accessor:"Action "}],ke=()=>{var w;const{dispatch:s,state:g}=o.useContext(I),{dispatch:d}=o.useContext(M);G();const[h,E]=z(),{engagements:i,refetch:b,loading:n,totalCount:p,currentPage:S,pageSize:j,updatePageSize:u,previousPage:r,nextPage:y,canPreviousPage:f,canNextPage:v}=V(g.user),N=U({group_name:_(),members:_()});return console.log(i,n,p),R({resolver:F(N),defaultValues:async()=>{const a=h.get("group_name")??"",c=h.get("members")??"";return{group_name:a,members:c}}}),o.useEffect(()=>{d({type:"SETPATH",payload:{path:"engagements"}})},[]),e.jsx(e.Fragment,{children:n?e.jsx(X,{}):e.jsx(e.Fragment,{children:e.jsxs("div",{className:"p-5 rounded bg-brown-main-bg md:pl-8",children:[e.jsx("div",{className:"flex justify-between mb-3 w-full item-center"}),e.jsx(te,{}),e.jsxs("div",{className:"overflow-x-auto p-5 px-0 mt-10 rounded custom-overflow bg-brown-main-bg md:mt-8",children:[e.jsx("div",{className:"flex justify-between items-center mb-3 w-full text-center",children:e.jsx("h4",{className:"text-left text-[16px] font-[600] sm:text-[20px]",children:"Engagements"})}),e.jsx("div",{className:"overflow-x-auto w-full",children:e.jsx(Z,{children:e.jsxs("table",{className:"min-w-full divide-y divide-[#1f1d1a]/10",children:[e.jsx("thead",{children:e.jsx("tr",{children:ae.map((a,c)=>e.jsx("th",{scope:"col",className:"font  whitespace-nowrap border-b-[3px] border-none  border-b-[#1f1d1a]/10  px-4 text-left capitalize tracking-wider text-[#1f1d1a]  md:border-dashed md:px-6 md:py-3",children:a.header},c))})}),e.jsx("tbody",{className:"font-iowan-regular  divide-y divide-[#1f1d1a]/10 ",children:(w=i==null?void 0:i.sort((a,c)=>new Date(c.update_at)-new Date(a.update_at)))==null?void 0:w.map((a,c)=>e.jsxs("tr",{className:"  md:h-[60px]",children:[e.jsx("td",{className:"whitespace-nowrap px-3  font-iowan md:h-[60px] md:max-h-[60px] md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:e.jsx("p",{className:"whitespace-nowrap",children:a.title.startsWith("Update ")&&a.title.slice(7).match(/^\d{4}-\d{2}-\d{2}$/)?`Update ${new Date(a.title.slice(7)).toLocaleString("en-US",{month:"short",day:"2-digit",year:"numeric"}).replace(/,/g,",")}`:a.title})}),e.jsx("td",{className:"px-3 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:a.section}),e.jsx("td",{className:"px-3 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:a.comment.length<33?a.comment:a.comment.substring(0,33-3)+"..."}),e.jsx("td",{className:"px-3 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:a.commenter}),e.jsxs("td",{className:"px-3 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:[e.jsxs("p",{className:"whitespace-nowrap",children:[" ",J(a.update_at).format("MMM DD, YYYY")]})," "]}),e.jsx("td",{className:"px-3 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:e.jsx(K,{className:"cursor-pointer font-medium text-[#292829fd] underline hover:underline disabled:cursor-not-allowed disabled:text-gray-400",to:`${a.update_link}#comment-${a.id}`,state:{commentId:a.id,noteId:a.note_id},children:"View"})})]},c))})]})})}),(i==null?void 0:i.length)==0?e.jsxs("div",{className:"mb-[20px] mt-24 flex flex-col items-center",children:[e.jsx(B,{children:e.jsx(H,{fill:"black",className:"!h-[5rem] !w-[5rem]"})}),e.jsx("p",{className:"mt-4 text-base font-medium text-center",children:"No Engagement"})]}):null]}),e.jsx(Q,{currentPage:S,pageCount:Math.ceil(p/j),pageSize:j,canPreviousPage:f,canNextPage:v,updatePageSize:u,previousPage:r,nextPage:y,dataLoading:n,totalCount:p})]})})})};export{ke as default};
