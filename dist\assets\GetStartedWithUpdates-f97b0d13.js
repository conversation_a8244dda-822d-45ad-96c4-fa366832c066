import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as m,b as U,j as I}from"./vendor-4cdf2bd1.js";import"./index-f373d1dd.js";import{A as M,G as P,bE as R,L as y,I as v,w as Y,M as B,t as A,s as k}from"./index-46de5032.js";import{h as G}from"./moment-a9aaa855.js";import{M as _}from"./index-dc002f62.js";import{D as W}from"./DocumentTextIcon-54b5e200.js";import{D as $}from"./DocumentIcon-22c47322.js";import"./@nextui-org/theme-345a09ed.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const z=({onClose:l,title:K="Create an update",classes:q={modal:"h-full",modalDialog:"h-[90%]",modalContent:""},page:C="",isOpen:F,disableCancel:O=!1,showModifyRecentOption:o=!1,onModifyRecent:u,limitReached:E=!1,limitChecking:s=!1,subscriptionData:t=null})=>{var w;const{dispatch:g,state:j}=m.useContext(M),{dispatch:c,state:f}=m.useContext(P),N=U(),[x,n]=m.useState(!1),[b,i]=m.useState(null),p=((w=f==null?void 0:f[R.createModel])==null?void 0:w.loading)||!1,d=p||x,h=!(t!=null&&t.subscription)&&!(t!=null&&t.trial_expired)&&(t==null?void 0:t.sentUpdates)>=1,r=E||h;async function L(){if(!(d||s||r))try{if(n(!0),i("blank"),r){l&&l(),n(!1),i(null);return}const a=await Y(c,g,"updates",{name:"Update Title",user_id:j.user,mrr:0,arr:0,cash:0,burnrate:0,date:G().format("MMM D, YYYY"),public_link_enabled:0,private_link_open:1,company_id:j.company.id},!1);await new B().callRawAPI("/v3/api/custom/goodbadugly/activities/draft",{update_id:a.data},"POST"),a!=null&&a.error||(l&&l(),N(`/member/edit-updates/${a.data}?autofocus=true`))}catch(a){A(g,a.message),a.message!=="TOKEN_EXPIRED"&&k(c,a.message,5e3,"error")}finally{n(!1),i(null)}}const S=async()=>{if(!(d||s||r))try{n(!0),i("recent"),u&&await u()}catch(a){console.error(a),a.message!=="TOKEN_EXPIRED"&&k(c,a.message,5e3,"error")}finally{n(!1),i(null)}},T=()=>d||s||r?!1:(l&&l(),!0);return e.jsx(y,{children:s?e.jsx("div",{className:"flex h-full w-full items-center justify-center",children:e.jsx(y,{children:e.jsx("p",{children:"Checking update limits..."})})}):r?e.jsxs("div",{className:"row-span-12 grid h-full max-h-full min-h-full w-full grid-cols-1 grid-rows-12 items-center justify-center p-5 md:w-[250px]",children:[e.jsx("div",{className:"row-span-8 text-center",children:e.jsx("p",{children:h?"You have used your free update. Please subscribe to send more updates!":t!=null&&t.trial_expired&&!(t!=null&&t.subscription)?"Please Upgrade your account to create an update!":"You have reached your monthly update limit for your current plan."})}),e.jsx("div",{className:"row-span-4 w-full",children:e.jsx("button",{className:"flex w-full flex-col items-center justify-center gap-5 rounded-[.125rem] bg-primary-black px-4 py-2 font-iowan text-[1rem] font-[700] leading-5 text-white",onClick:()=>{N("/member/billing?openManagePlan=true")},children:h||t!=null&&t.trial_expired&&!(t!=null&&t.subscription)?"Subscribe":"Upgrade Plan"})})]}):e.jsxs("div",{className:"h-fit max-h-fit min-h-fit",children:[e.jsx("div",{className:`flex w-full items-start gap-4 px-6 py-4 hover:bg-brown-main-bg  ${o?"hidden":""}`,children:e.jsxs("div",{children:[e.jsx("div",{className:"flex items-center gap-3",children:e.jsx("span",{className:"text-left font-iowan text-[22px] font-semibold ",children:o?"":"Start Here"})}),e.jsx("p",{className:"mt-1 text-[14px] font-medium",children:"Create an update by selecting:"})]})}),o&&e.jsxs(v,{color:"#000000",disabled:d||s||r,onClick:S,className:"!flex !h-fit !max-h-fit !min-h-fit !w-full !items-start !justify-start !gap-4 border-b border-b-[#1f1d1a]/10 !bg-brown-main-bg !px-6 !py-4 !opacity-100 hover:!bg-brown-main-bg",children:[e.jsx("div",{className:"flex items-center justify-center gap-2 rounded-[.625rem]  p-0",children:e.jsx("div",{className:"flex items-center justify-center gap-2 rounded-[.625rem]  border border-[#1f1d1a] p-2",children:e.jsx("img",{src:"/assets/edit-2.svg",alt:"",className:"h-8 w-8"})})}),e.jsxs("div",{className:"flex-grow",children:[e.jsx("p",{className:"text-left text-[14px] font-semibold",children:"Modify with Recent"}),e.jsx("p",{className:"mt-1 text-left font-medium",children:"Start with most recent sent update"})]}),x&&b==="recent"&&!p&&e.jsx("div",{className:"flex items-center",children:e.jsx(_,{size:10,color:"#000000",loading:!0,type:"beat",className:"ml-2"})})]}),e.jsxs(I,{className:`flex w-full items-start gap-4 border-b border-b-[#1f1d1a]/10 px-6 py-4 hover:bg-brown-main-bg ${d||s||r?"pointer-events-none opacity-50":""}`,to:"/member/select-template",onClick:a=>{T()||a.preventDefault()},children:[e.jsx("div",{className:"flex items-center justify-center gap-2 rounded-[.625rem] border border-[#1f1d1a] p-2",children:e.jsx(W,{className:"h-8 w-8 text-primary-black",strokeWidth:2})}),e.jsxs("div",{className:"flex-grow",children:[e.jsx("p",{className:"text-left text-[14px] font-semibold",children:"New Template Update"}),e.jsx("p",{className:"mt-1 text-left font-medium",children:"Start an update using an existing update templates"})]})]}),e.jsxs(v,{color:"#000000",disabled:d||s||r,onClick:L,className:"!flex !h-fit !max-h-fit !min-h-fit !w-full !items-start !justify-start !gap-4 border-b border-b-[#1f1d1a]/10 !bg-brown-main-bg !px-6 !py-4 !opacity-100 hover:!bg-brown-main-bg",children:[e.jsx("div",{className:"flex items-center justify-center gap-2 rounded-[.625rem] border border-[#1f1d1a] p-2",children:e.jsx($,{className:"h-8 w-8 text-primary-black",strokeWidth:2})}),e.jsxs("div",{className:"flex-grow",children:[e.jsx("p",{className:"text-left text-[14px] font-semibold",children:"New Blank Update"}),e.jsx("p",{className:"mt-1 text-left font-medium",children:"Start an update from scratch"})]}),x&&b==="blank"&&!p&&e.jsx("div",{className:"flex items-center",children:e.jsx(_,{size:10,color:"#000000",loading:!0,type:"beat",className:"ml-2"})})]})]})})},ue=m.memo(z);export{ue as default};
