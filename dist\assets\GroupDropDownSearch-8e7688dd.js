import{j as s}from"./@nextui-org/listbox-0f38ca19.js";import{S as h}from"./index-0087bc92.js";import{u as x}from"./index-46de5032.js";import{u as g}from"./useRecipientGroup-5985e2f4.js";import{r as m}from"./vendor-4cdf2bd1.js";import"./@nextui-org/theme-345a09ed.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";function $({onSelect:n,value:u,refreshRef:c,clearRef:d}){var p;const{profile:r}=x(),{recipientGroup:t,loading:e,getRecipientGroups:l}=g(),a=m.useRef(null),f=async()=>{await l({filter:[`goodbadugly_recipient_group.user_id,eq,${r==null?void 0:r.id}`]})};return m.useEffect(()=>{r!=null&&r.id&&f()},[r==null?void 0:r.id]),s.jsx("div",{ref:a,children:s.jsx(h,{className:"",value:u,useExternalData:!0,display:"name",label:"Groups:",onSelect:n,showSearchIcon:!0,uniqueKey:"group_id",placeholder:"Search Groups",externalDataLoading:e==null?void 0:e.list,inputClassName:"!h-[2.5rem] !max-h-[2.5rem] !min-h-[2.5rem]",externalDataOptions:(p=t==null?void 0:t.list)==null?void 0:p.map(o=>{var i;return{...o,name:(i=o==null?void 0:o.group)==null?void 0:i.group_name}}),refreshRef:c,clearRef:d,containerRef:a})})}export{$ as default};
