import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{R as o,b as n,L as p}from"./vendor-4cdf2bd1.js";import{G as l,A as m,u as d,L as s,bL as c,aa as x}from"./index-46de5032.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const A=()=>{const{state:e,dispatch:i}=o.useContext(l);o.useContext(m),n(),d();let r=a=>{i({type:"OPEN_SIDEBAR",payload:{isOpen:a}})};return t.jsxs("div",{className:"relative flex w-full items-center justify-between gap-1",children:[!e.isOpen&&t.jsx(s,{children:t.jsx(c,{className:""})}),e.isOpen&&t.jsx(p,{to:"/member/dashboard",children:t.jsx(s,{children:t.jsx(x,{})})}),t.jsxs("svg",{onClick:()=>r(!e.isOpen),width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:` cursor-pointer  ${e.isOpen?"rotate-180 md:block":"hidden"}`,children:[t.jsx("rect",{x:"0.5",y:"0.5",width:"19",height:"19",rx:"9.5",fill:"#1F1D1A"}),t.jsx("rect",{x:"0.5",y:"0.5",width:"19",height:"19",rx:"9.5",stroke:"#FFF0E5"}),t.jsx("path",{d:"M8.66602 6.66667L11.9993 10L8.66602 13.3333",stroke:"#FFF0E5","stroke-width":"1.33333","stroke-linecap":"round","stroke-linejoin":"round"})]})]})};export{A as default};
