import{j as r}from"./@nextui-org/listbox-0f38ca19.js";import{L as o}from"./index-46de5032.js";import{M as m}from"./index-af54e68d.js";import{r as a}from"./vendor-4cdf2bd1.js";import{e as i}from"./index.esm-6fcccbfe.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./react-icons-36ae72b7.js";const z=a.memo(({src:t,onPopoverStateChange:e})=>r.jsx(o,{children:r.jsx(m,{display:r.jsx(i,{className:"peer h-8 w-8"}),openOnClick:!1,zIndex:999999999999999,onPopoverStateChange:e,place:"left-start",tooltipClasses:"whitespace-nowrap h-fit min-h-[1rem] max-h-fit w-[18.75rem] !rounded-lg border border-[#a8a8a8] !bg-white p-2 text-sm text-[#525252] shadow-md",children:r.jsx(o,{className:"h-[18.75rem] w-[18.75rem] whitespace-nowrap !rounded-lg border border-[#a8a8a8] !bg-white p-2 text-sm text-[#525252] shadow-md",children:r.jsx("img",{src:t,className:"w-[18.75rem]",alt:""})})})}));export{z as default};
