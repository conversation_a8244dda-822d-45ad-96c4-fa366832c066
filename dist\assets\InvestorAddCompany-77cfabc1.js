import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as o,b as v}from"./vendor-4cdf2bd1.js";import{c as S,a as g}from"./yup-342a5df4.js";import{u as k}from"./react-hook-form-9f4fcfa9.js";import"./index-bdf26615.js";import{o as q}from"./yup-c41d85d2.js";import{G as C,A as E,u as R,I,ax as T,h as l,s as c,q as _,n as A}from"./index-46de5032.js";import{M as x}from"./MkdInput-a0090fba.js";import{u as L}from"./useLocalStorage-53cfe2d8.js";import"./index-6edcbb0d.js";import"./@nextui-org/theme-345a09ed.js";import"./qr-scanner-cf010ec4.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";const be=({role:M=T.INVESTOR,updateStep:O=null})=>{const{dispatch:r,state:{companyCreation:P,membershipModel:$}}=o.useContext(C),{dispatch:d}=o.useContext(E);o.useState([1]);const i=v(),{localStorageData:D,setLocalStorage:p}=L(["step"]),{profile:a,updateProfile:u}=R({isPublic:!0}),y=S({company_name:g().required("This field is required"),email:g().required("This field is required").test("is-valid-emails","Some emails are invalid",s=>s?s.split(",").map(n=>n.trim()).every(n=>/^\S+@\S+\.\S+$/.test(n)):!1)}),{register:f,handleSubmit:b,setError:m,watch:G,formState:{errors:h}}=k({resolver:q(y)}),w=async()=>{try{l(r,!0,"skipStep"),u({step:5,is_onboarded:1}),p("step",5),i("/fundmanager/dashboard"),l(r,!1,"skipStep")}catch(s){console.log("Error",s),l(r,!1,"skipStep"),c(r,s.message,5e3,"error"),m("team_name",{type:"manual",message:s.message})}},j=async()=>{var s;try{const t=await _(r,d,"company_member",{filter:[`member_id,eq,${a==null?void 0:a.id}`]},"membershipModel");t!=null&&t.error||((s=t==null?void 0:t.data)==null?void 0:s.length)>0&&i("/fundmanager/update_requests")}catch(t){console.log("Error",t),c(r,t.message,5e3,"error"),m("company_name",{type:"manual",message:t.message})}},N=async s=>{try{const t=await A(r,d,{endpoint:"/v3/api/custom/goodbadugly/investor/add-company",method:"POST",payload:{company_email:s==null?void 0:s.company_email,company_name:s==null?void 0:s.company_name}},"companyCreation");t!=null&&t.error||(u({step:5,is_onboarded:1}),p("step",5),i("/fundmanager/update_requests"))}catch(t){console.log("Error",t),c(r,t.message,5e3,"error"),m("email1",{type:"manual",message:t.message})}};return o.useEffect(()=>{a!=null&&a.id&&j()},[a==null?void 0:a.id]),e.jsxs(e.Fragment,{children:[e.jsx("style",{children:`
      .bd {
        border: 1px solid #1f1d1a;
      }
        .style {
          
        }
    `}),e.jsx("form",{onSubmit:b(N,s=>{console.log("ERROR >>",s)}),className:"flex h-full max-h-full min-h-full w-full flex-col justify-center md:w-[60%] lg:w-[70%]",children:e.jsxs("div",{className:"w-full",children:[e.jsxs("div",{className:"space-y-5",children:[e.jsxs("div",{className:"font-iowan text-[2.5rem] font-[700] leading-[3rem] ",children:["Welcome ",a==null?void 0:a.first_name]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-inter text-[1rem] font-[700] leading-[1.5rem]",children:"Next, add your first portfolio company"}),e.jsx("h5",{className:"font-inter text-[1rem] font-[400] leading-[1.5rem]",children:"In Order to request an update, add one or more portfolio companies below."})]}),e.jsx("p",{className:"font-inter text-[1rem] font-[400] leading-[1.5rem]",children:"Add a portfolio company by adding the main point of contact email and company name"})]}),e.jsx("div",{className:"scrollbar-hide h-full max-h-full min-h-full w-full overflow-y-auto pb-5",children:e.jsx("div",{className:"my-5 mt-2 grid grid-cols-1 gap-4",children:e.jsxs("div",{className:"w-full items-end justify-start gap-2 space-y-[2rem]",children:[e.jsxs("div",{className:"flex flex-col gap-[1rem]",children:[e.jsx("div",{className:"grid grow grid-cols-1",children:e.jsx(x,{type:"text",name:"company_name",label:"Company Name",errors:h,register:f,className:"focus:shadow-outline !h-[2.75rem] w-full appearance-none !rounded-[.125rem] border border-[#1f1d1a] !bg-transparent text-sm leading-tight text-[#1d1f1a] shadow focus:outline-none",placeholder:"stack"})}),e.jsx("div",{className:"grid grow grid-cols-1",children:e.jsx(x,{type:"text",name:"email",errors:h,label:"Company Email",register:f,className:"focus:shadow-outline !h-[2.75rem] w-full appearance-none !rounded-[.125rem] border border-[#1f1d1a] !bg-transparent text-sm leading-tight text-[#1d1f1a] shadow focus:outline-none",placeholder:"<EMAIL>,<EMAIL>"})})]}),e.jsxs("div",{className:"flex w-full flex-col items-center gap-[2.5rem] ",children:[e.jsx(I,{type:"submit",className:"my-4 flex h-[2.75rem] !w-full items-center justify-center rounded-sm bg-[#1f1d1a] !px-3 py-2 tracking-wide text-white outline-none focus:outline-none",children:e.jsx("span",{className:"font-iowan capitalize",children:"Go To UpdateStack"})}),e.jsx("button",{type:"buttons",onClick:()=>w(),className:"w-fit cursor-pointer whitespace-nowrap text-sm font-medium md:text-base",children:"Skip,  Add Later →"})]})]})})})]})})]})};export{be as default};
