import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{u as B,r as o,b as G}from"./vendor-4cdf2bd1.js";import{u as X}from"./react-hook-form-9f4fcfa9.js";import{c as z,a as F}from"./yup-342a5df4.js";import{o as K}from"./yup-c41d85d2.js";import{aM as U,G as W,A as Y,u as J,x as Q,M as f,s as C,t as E}from"./index-46de5032.js";import{InteractiveButton2 as e1}from"./InteractiveButton-060359e0.js";import{L as s1}from"./index-6edcbb0d.js";import{X as t1}from"./XMarkIcon-6ed09631.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";const a1=({updateStep:i,trial:p=null})=>e.jsx(U,{updateStep:i,trial:p}),r1=a1;function F1(){var H,V,S,v,D,L,M,A;const{state:i}=B(),[p,b]=o.useState(!!(i!=null&&i.first_login)),{dispatch:n,state:{subStatus:h,onboardingSteps:l}}=o.useContext(W),[o1,g]=o.useState(!1),[u,k]=o.useState(!!(i!=null&&i.first_login)),{dispatch:x,state:s}=o.useContext(Y),[Z,d]=o.useState(!1),I=G(),{profile:r}=J(),j=o.useRef(!1);console.log(s);const _=async t=>{d(!0);try{const a=await Q(n,x,"user",Number(localStorage.getItem("user")),{step:t},!1);a!=null&&a.error||(console.log("ppp"),n({type:"SET_ONBOARDING",payload:1}),d(!1))}catch{d(!1)}};o.useEffect(()=>(j.current=!0,()=>{j.current=!1}),[]);const O=t=>{n({type:"SET_ONBOARDING",payload:t})};console.log(l);async function R(){var t;d(!0);try{const m=await new f().getCustomerStripeSubscription();g(m==null?void 0:m.customer),console.log((t=m==null?void 0:m.customer)==null?void 0:t.subId,"popp"),O(r==null?void 0:r.step),d(!1)}catch(a){d(!1),console.error(a)}}o.useEffect(()=>{r!=null&&r.id&&R()},[r==null?void 0:r.id]),o.useEffect(()=>{h&&g(!0)},[h]);const T=z({email:F().required("This field is required"),company_name:F().required("This field is required")});console.log(s);const{register:y,handleSubmit:P,reset:$,formState:{errors:c,isSubmitting:w}}=X({resolver:K(T),defaultValues:{email:"",company_name:""}});async function q(t){console.log("ddgdgd");try{await new f().callRawAPI("/v3/api/custom/goodbadugly/investor/invite-startup",{email:t.email,company_name:t.company_name},"POST"),b(!0),k(!1),$(),C(n,"Added"),N()}catch(a){E(x,a.message),a.message!=="TOKEN_EXPIRED"&&C(n,a.message,5e3,"error")}}async function N(){try{await new f().callRawAPI(`/v4/api/records/user/${s.user}`,{id:localStorage.getItem("user"),is_onboarded:1},"PUT"),I("/fundmanager/dashboard")}catch(t){E(x,t.message),t.message!=="TOKEN_EXPIRED"&&C(n,t.message,5e3,"error")}}return console.log("PROFILE >>",r),Z||[-1].includes(l)?e.jsx(s1,{}):e.jsxs("div",{className:"mx-auto flex min-h-screen w-full flex-col items-center bg-brown-main-bg pb-10 pt-8",children:[p?e.jsxs("div",{className:"flex items-center justify-between bg-[#aaeed4] px-4 py-5",children:[e.jsx("span",{}),u?e.jsxs("p",{className:"text-lg font-medium text-[#01633d]",children:[e.jsx("span",{className:"font-semibold",children:"Success! "})," Your email has been successfully verified"]}):e.jsxs("p",{className:"text-lg font-medium text-[#01633d]",children:[e.jsx("span",{className:"font-semibold",children:"Success! "})," Company added successfully"]}),e.jsx("button",{onClick:()=>b(!1),children:e.jsx(t1,{className:"ml-4 h-4 w-4 text-[#01633d]",strokeWidth:5})})]}):null,e.jsxs("div",{className:"mx-auto w-full px-5 pt-10 sm:px-10 lg:px-[50px] xl:max-w-[1000px] 2xl:max-w-[1400px]",children:[e.jsxs("svg",{onClick:()=>window.location.href="https://updatestack.com",className:"h-[32px] w-[180px] cursor-pointer sm:h-auto sm:w-auto",width:"294",height:"40",viewBox:"0 0 294 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M56.011 3.94476V29.6915H8.29451V9.07431H17.8659H32.1526H51.5972V3.94226L3.91203 3.92002V3.94771H3.13672V34.8457H61.1688V3.94771L56.011 3.94476Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M12.707 21.101V26.2552H51.5962V12.5105H32.1516H17.8648H12.707V17.6648H46.4386V21.101H12.707Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M72.2383 20.2053V5.81549H77.2012V19.9748C77.2012 22.8572 79.0662 24.6829 81.7057 24.6829C84.3387 24.6829 86.2044 22.8572 86.2044 19.9748V5.81549H91.1674V20.2053C91.1674 25.6144 87.1177 29.3931 81.7057 29.3931C76.288 29.3931 72.2383 25.6144 72.2383 20.2053Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M105.12 10.5858C110.443 10.5858 114.379 14.5954 114.379 19.9895C114.379 25.3773 110.443 29.4202 105.12 29.4202C103.29 29.4202 101.628 28.8765 100.259 27.9216V35.3057H95.4805V11.0896H98.6402L99.4561 12.7078C100.957 11.3687 102.917 10.5865 105.121 10.5865L105.12 10.5858ZM109.541 19.9887C109.541 17.1934 107.506 15.1105 104.73 15.1105C101.955 15.1105 99.913 17.1993 99.913 19.9887C99.913 22.7789 101.955 24.8673 104.73 24.8673C107.506 24.8673 109.541 22.7847 109.541 19.9887Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M135.561 4.67397V28.8897H132.032L131.468 27.4079C129.984 28.679 128.075 29.421 125.933 29.421C120.571 29.421 116.641 25.378 116.641 19.9902C116.641 14.5961 120.571 10.5865 125.933 10.5865C127.758 10.5865 129.415 11.1203 130.783 12.061V4.67397H135.561ZM131.139 19.9902C131.139 17.2 129.099 15.1116 126.322 15.1116C123.546 15.1116 121.511 17.1942 121.511 19.9902C121.511 22.7862 123.546 24.8687 126.322 24.8687C129.098 24.8687 131.139 22.7738 131.139 19.9902Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M158.147 11.0629V28.8634H154.401L153.99 27.2703C152.472 28.6087 150.492 29.3931 148.263 29.3931C142.947 29.3931 138.992 25.351 138.992 19.9631C138.992 14.5961 142.947 10.5865 148.263 10.5865C150.532 10.5865 152.541 11.3918 154.069 12.7634L154.58 11.0629H158.147ZM153.463 19.9631C153.463 17.1671 151.428 15.0845 148.652 15.0845C145.877 15.0845 143.835 17.173 143.835 19.9631C143.835 22.7533 145.877 24.8417 148.652 24.8417C151.428 24.8417 153.463 22.7591 153.463 19.9631Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M173.544 24.6046V28.8897H170.149C166.261 28.8897 163.872 26.4837 163.872 22.5337V14.9587H160.676V13.9185L167.656 6.44479H168.57V11.0896H173.446V14.9587H168.65V21.8957C168.65 23.6153 169.632 24.6046 171.373 24.6046L173.544 24.6046Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M193.704 21.3381H180.328C180.743 23.7675 182.329 25.1051 184.647 25.1051C186.307 25.1051 187.67 24.3141 188.396 23.0401H193.418C192.133 26.9206 188.75 29.3931 184.647 29.3931C179.428 29.3931 175.48 25.3239 175.48 19.9895C175.48 14.6287 179.402 10.5861 184.647 10.5861C190.074 10.5861 193.796 14.7995 193.796 19.9265C193.796 20.397 193.764 20.8675 193.704 21.3381ZM180.454 18.0511H189.019C188.343 15.8748 186.791 14.6945 184.647 14.6945C182.52 14.6945 181.009 15.9443 180.454 18.0511Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M196.238 21.4127H201.233C201.233 23.6423 203.063 24.7187 205.09 24.7187C206.955 24.7187 208.779 23.7265 208.779 22.0061C208.779 20.2178 206.69 19.726 204.206 19.1421C200.752 18.2852 196.557 17.2747 196.557 12.3035C196.557 7.87756 199.793 5.38412 204.867 5.38412C210.133 5.38412 213.126 8.21599 213.126 12.8128H208.228C208.228 10.8261 206.601 9.89425 204.747 9.89425C203.141 9.89425 201.514 10.5803 201.514 12.091C201.514 13.7147 203.504 14.2064 205.929 14.7907C209.421 15.6809 213.807 16.7778 213.807 21.9469C213.807 26.9301 209.865 29.3485 205.122 29.3485C199.862 29.3485 196.238 26.3725 196.238 21.4127Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M228.032 24.6046V28.8897H224.637C220.749 28.8897 218.36 26.4837 218.36 22.5337V14.9587H215.164V13.9185L222.145 6.44479H223.058V11.0896H227.934V14.9587H223.139V21.8957C223.139 23.6153 224.12 24.6046 225.861 24.6046L228.032 24.6046Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M249.123 11.0629V28.8634H245.377L244.967 27.2703C243.449 28.6087 241.469 29.3931 239.239 29.3931C233.923 29.3931 229.969 25.351 229.969 19.9631C229.969 14.5961 233.923 10.5865 239.239 10.5865C241.508 10.5865 243.518 11.3918 245.046 12.7634L245.557 11.0629H249.123ZM244.44 19.9631C244.44 17.1671 242.405 15.0845 239.629 15.0845C236.853 15.0845 234.812 17.173 234.812 19.9631C234.812 22.7533 236.853 24.8417 239.629 24.8417C242.405 24.8417 244.44 22.7591 244.44 19.9631Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M261.935 29.3924C256.618 29.3924 252.551 25.269 252.551 19.9353C252.551 14.6012 256.591 10.5858 261.962 10.5858C266.538 10.5858 270.09 13.5076 270.95 17.8886H266.205C265.431 16.139 263.839 15.1109 261.935 15.1109C259.343 15.1109 257.394 17.2059 257.394 19.9624C257.394 22.7196 259.376 24.868 261.935 24.868C263.866 24.868 265.392 23.7865 266.199 21.8832H271.009C270.177 26.3579 266.558 29.3931 261.935 29.3931L261.935 29.3924Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M285.032 28.8897L279.376 20.3743V28.8897H274.598V4.67397H279.376V18.9987L284.681 11.0896H290.185L284.207 19.5658L290.822 28.8897H285.032Z",fill:"#1F1D1A"})]}),e.jsxs("div",{className:"mt-7 flex items-center justify-between",children:[e.jsx("h3",{className:"text-3xl font-semibold capitalize xl:text-3xl",children:((H=s==null?void 0:s.userDetails)!=null&&H.firstName?(V=s==null?void 0:s.userDetails)==null?void 0:V.firstName:"")+" "+((S=s==null?void 0:s.userDetails)!=null&&S.lastName?(v=s==null?void 0:s.userDetails)==null?void 0:v.lastName:"")}),e.jsxs("div",{className:"flex items-center gap-3 text-base font-semibold",children:[e.jsx("div",{disabled:l!==0,className:`  flex h-7 w-7  items-center justify-center  border border-black/30 transition-all duration-200 ${l>=0?" bg-black text-white ":" bg-transparent"}`,children:"1"}),e.jsx("div",{disabled:l!==1,className:`  flex h-7 w-7  items-center justify-center  border border-black/30 transition-all duration-200 ${l>=1?" bg-black text-white ":" bg-transparent"}`,children:"2"})]})]})," ",l===0?e.jsx(r1,{updateStep:()=>_(1),trial:"Try FREE For 14 days"}):null,l==1&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mt-6 max-w-[500px]",children:[e.jsx("h3",{className:"text-lg md:text-xl",children:u?"First, add one of your portfolio companies":"Add an additional portfolio company"}),e.jsxs("p",{className:"mt-2 font-iowan text-sm font-bold",children:["Request automated company updates from team ",e.jsx("added",{})]})]}),e.jsxs("form",{onSubmit:P(q),className:"w-full",children:[e.jsxs("div",{className:"max-w-[500px] text-lg",children:[e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-1 font-iowan text-sm font-bold capitalize text-gray-900",children:"Email Address"}),e.jsx("input",{type:"email",autoComplete:"off",...y("email"),className:`w-full appearance-none rounded-md border border-[#1f1d1a] bg-transparent py-2 font-medium text-gray-900 focus:bg-transparent focus:outline-none ${(D=c.email)!=null&&D.message?"border-red-500":""}`,placeholder:"Email"}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(L=c.email)==null?void 0:L.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-1 font-iowan text-sm font-bold capitalize text-gray-900",children:"Company Name"}),e.jsx("input",{placeholder:"Company Name",type:"text",autoComplete:"off",...y("company_name"),className:`w-full appearance-none rounded-md border border-[#1f1d1a] bg-transparent py-2 font-medium text-gray-900 focus:bg-transparent focus:outline-none ${(M=c.company_name)!=null&&M.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(A=c.company_name)==null?void 0:A.message})]})]}),e.jsx(e1,{loading:w,disabled:w,type:"submit",className:"mt-8 h-[fit-content] min-w-[90px] cursor-pointer rounded-[4px] border border-[#1f1d1a] bg-[#1f1d1a] px-3 py-2 font-iowan font-medium text-white sm:min-w-[110px] sm:px-5",children:"Add"}),e.jsx("div",{className:"mt-[56px] flex  w-full flex-row justify-end gap-3 pb-[40px]",children:e.jsxs("span",{onClick:N,className:"cursor-pointer text-sm font-medium md:text-sm",children:["Skip,"," ",u?"add company later":"add additional companies later"," ","→"]})})]})]})]})]})}export{F1 as default};
