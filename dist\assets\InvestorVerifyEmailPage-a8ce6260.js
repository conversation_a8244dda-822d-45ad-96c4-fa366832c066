import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{G as f,A as x,M as h,x as g}from"./index-46de5032.js";import{u as y}from"./useLocalStorage-53cfe2d8.js";import{h as v,r as s,b,L as j}from"./vendor-4cdf2bd1.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";function K(){const[p]=v(),[r,i]=s.useState(!0),[o,n]=s.useState(""),{dispatch:l}=s.useContext(f),{dispatch:c}=s.useContext(x),d=b(),{setLocalStorage:a}=y([]);async function u(){i(!0);try{const e=await new h().callRawAPI(`/v2/api/lambda/verify-email?token=${p.get("token")}`);n(""),c({type:"LOGIN",payload:e}),a("verified",!0),a("token",e==null?void 0:e.token),a("user",e==null?void 0:e.user_id),a("role",e==null?void 0:e.role),a("step",2),await g(l,c,"user",e==null?void 0:e.user_id,{step:2},!1),d("/fundmanager/get-started",{state:{first_login:!0}})}catch(m){n(m.message)}i(!1)}return s.useEffect(()=>{u()},[]),t.jsxs("div",{className:"flex min-h-screen w-full justify-center",children:[t.jsxs("div",{className:"mx-auto mt-32 max-w-lg text-center",children:[" ",t.jsx("h1",{className:"mb-3 text-3xl font-semibold",children:r?"Verifying...":o?t.jsx("p",{className:"text-red-500 empty:hidden",children:o}):"Account verification complete!"})," ",t.jsxs("p",{children:[r||o?"":"Your account has been verified, you can now "," ",t.jsxs(j,{to:"/fundmanager/login",className:`font-semibold text-primary-black underline ${r||o?"hidden":""}`,children:[" ","Login"]})]})," "]})," "]})}export{K as default};
