import{j as r}from"./@nextui-org/listbox-0f38ca19.js";import{r as o}from"./vendor-4cdf2bd1.js";import{a as s}from"./index-0d5645ff.js";import{_ as t}from"./qr-scanner-cf010ec4.js";import{L as i}from"./index-6edcbb0d.js";import{L as l}from"./index-46de5032.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const m=o.lazy(()=>t(()=>import("./InvestorHeader2-e358188d.js"),["assets/InvestorHeader2-e358188d.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index.esm-6fcccbfe.js","assets/react-icons-36ae72b7.js","assets/index-46de5032.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-d88d3663.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/index.esm-54e24cf9.js","assets/UserIcon-63b89263.js","assets/BriefcaseIcon-6beed5a1.js"])),a=({children:e})=>r.jsx("div",{id:"investor_wrapper",className:"flex w-full max-w-full flex-col bg-brown-main-bg",children:r.jsxs("div",{className:"flex h-screen w-full max-w-full",children:[r.jsx(l,{children:r.jsx(m,{})}),r.jsxs("div",{className:"h-full w-full overflow-y-auto pb-[150px]",children:[r.jsx(s,{}),r.jsx(o.Suspense,{fallback:r.jsx("div",{className:"flex h-screen w-full items-center justify-center",children:r.jsx(i,{size:100,color:"#2CC9D5"})}),children:r.jsx("div",{className:"w-full overflow-y-auto overflow-x-hidden",children:e})})]})]})}),g=o.memo(a);export{g as default};
