import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{R as a,b as U}from"./vendor-4cdf2bd1.js";import{u as T}from"./react-hook-form-9f4fcfa9.js";import{o as D}from"./yup-c41d85d2.js";import{c as F,a as i}from"./yup-342a5df4.js";import{G as S,A as M,I as G,M as $,s as R,t as q}from"./index-46de5032.js";import"./react-quill-a78e6fc7.js";/* empty css                   */import{M as c}from"./MkdInput-a0090fba.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@craftjs/core-a2cdaeb4.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@uppy/compressor-4bcbc734.js";const je=()=>{var b,j,y,w;const{dispatch:x}=a.useContext(S),L=F({user_id:i(),logo:i(),email:i(),address:i(),name:i(),phone:i()}).required(),{dispatch:g}=a.useContext(S),{state:A}=a.useContext(M),[t,E]=a.useState({}),[f,u]=a.useState(!1),C=U(),{register:d,handleSubmit:k,setError:h,setValue:I,formState:{errors:m}}=T({resolver:D(L)});a.useState([]);const O=(o,l)=>{let n=t;n[o]={file:l.files[0],tempURL:URL.createObjectURL(l.files[0])},E({...n})},P=async o=>{var n,N;let l=new $;u(!0);try{if(t&&t.logo&&((n=t.logo)!=null&&n.file)){let p=new FormData;p.append("file",(N=t.logo)==null?void 0:N.file);let r=await l.uploadImage(p);console.log("uploadResult"),console.log(r),o.logo=r.url,R(g,"Logo Uploaded",1e3)}l.setTable("companies");const s=await l.callRestAPI({user_id:o.user_id,logo:o.logo,email:o.email,address:o.address,name:o.name,phone:o.phone},"POST");if(!s.error)R(x,"Added"),C("/member/companies");else if(s.validation){const p=Object.keys(s.validation);for(let r=0;r<p.length;r++){const v=p[r];h(v,{type:"manual",message:s.validation[v]})}}u(!1)}catch(s){u(!1),console.log("Error",s),h("user_id",{type:"manual",message:s.message}),q(g,s.message)}};return a.useEffect(()=>{x({type:"SETPATH",payload:{path:"companies"}}),I("user_id",A.user)},[]),e.jsxs("div",{className:" mx-auto rounded  p-5 shadow-md",children:[e.jsx("h4",{className:"text-[16px] font-medium md:text-xl",children:"Add Companies"}),e.jsxs("form",{className:" w-full max-w-lg",onSubmit:k(P),children:[e.jsxs("div",{className:"relative mb-4 h-[100px] w-fit py-5",children:[t&&((b=t.logo)!=null&&b.tempURL)?e.jsx("div",{className:"flex h-[80px] w-[80px] items-center rounded-2xl",children:e.jsx("img",{className:"h-[80px] w-[80px] rounded-2xl object-cover",src:(j=t.logo)==null?void 0:j.tempURL,alt:""})}):null,(y=t.logo)!=null&&y.file?null:e.jsx("div",{className:"flex h-[80px] w-full items-center justify-center rounded border border-2 border-dashed border-slate-400 px-6 py-4",children:"Select a Logo"}),e.jsx("input",{className:"focus:shadow-outline absolute left-0 top-0 h-full w-[100px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 leading-tight text-[#1d1f1a] opacity-0   shadow focus:outline-none sm:w-[180px]",id:"logo",type:"file",placeholder:"Logo",name:"logo",onChange:o=>O("logo",o.target)}),e.jsx("p",{className:"text-xs italic text-red-500",children:(w=m.logo)==null?void 0:w.message})]}),e.jsx(c,{type:"text",page:"companies",name:"email",errors:m,label:"Email",placeholder:"Email",register:d,className:""}),e.jsx(c,{type:"text",page:"companies",name:"address",errors:m,label:"Address",placeholder:"Address",register:d,className:""}),e.jsx(c,{type:"text",page:"companies",name:"name",errors:m,label:"Name",placeholder:"Name",register:d,className:""}),e.jsx(c,{type:"text",page:"companies",name:"phone",errors:m,label:"Phone",placeholder:"Phone",register:d,className:""}),e.jsx(G,{type:"submit",loading:f,disabled:f,className:"focus:shadow-outline rounded bg-primary-black px-4 py-2 font-bold text-white focus:outline-none",children:"Submit"})]})]})};export{je as default};
