import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as M,R as a,b as $e}from"./vendor-4cdf2bd1.js";import{u as Me}from"./react-hook-form-9f4fcfa9.js";import{o as _e}from"./yup-c41d85d2.js";import{c as Le,a as I}from"./yup-342a5df4.js";import{M as fe,T as He,I as T,f as Je,G as he,A as Ke,t as Qe}from"./index-46de5032.js";import"./react-quill-a78e6fc7.js";/* empty css                   */import{M as ge}from"./MkdInput-a0090fba.js";import"./@tinymce/tinymce-react-81dfa8ae.js";import{g as Ve}from"./react-audio-voice-recorder-a95781ec.js";import{T as qe}from"./@editorjs/editorjs-3bc58744.js";import{n as ze}from"./@editorjs/paragraph-9d333c59.js";import{c as We}from"./@editorjs/header-da8c369a.js";import{d as Xe}from"./@editorjs/list-86b325f6.js";import{I as Ye}from"./@editorjs/link-7a38da73.js";import{n as Ze}from"./@editorjs/delimiter-89018da8.js";import{f as et}from"./@editorjs/checklist-1b2b7ac3.js";import{E as tt}from"./editorjs-html-377e228b.js";import{F as st,a as at}from"./index.esm-be5e1c14.js";import{a as ot}from"./index.esm-6fcccbfe.js";import"./index-855999b8.js";import{Modal as rt}from"./Modal-75fdd375.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@craftjs/core-a2cdaeb4.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@uppy/compressor-4bcbc734.js";import"./react-icons-36ae72b7.js";const nt={paragraph:ze,checkList:et,list:Xe,header:We,delimiter:Ze,link:Ye},lt=({data:f,onChange:c,editorblock:p})=>{const d=M.useRef();return M.useEffect(()=>{if(!d.current){const u=new qe({holder:p,minHeight:30,tools:nt,hideToolbar:!1,data:f,async onChange(w,b){const i=await w.saver.save();c(i)}});d.current=u}return()=>{d.current&&d.current.destroy&&d.current.destroy()}},[]),e.jsx("div",{className:"shadow-md",id:p})};tt();new fe;let ct=new He;const it=()=>{const[f,c]=M.useState(""),[p,d]=M.useState([]),[u,w]=M.useState([]),b=async()=>{const n=await ct.getPaginate("recipient_group",{});n.error||w(n.list)};a.useEffect(()=>{(async()=>b())()},[]);const i=n=>{const N=u.find(m=>m.id===parseInt(n));d(m=>[...m,N]),c("")},C=n=>{d(N=>N.filter(m=>m.id!==n))};return e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h4",{children:"Invite Groups"}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex gap-4",children:[e.jsxs("select",{value:f,onChange:n=>{c(n.target.value)},className:"rounded border px-8 py-2 focus:outline-none",children:[e.jsx("option",{value:"",disabled:!0,children:"Select Group"}),u.map(n=>e.jsx("option",{className:"font-bold text-white",value:n.id,children:n.group_name},n.id))]}),e.jsx("button",{onClick:()=>{i(f),console.log(p)},className:"rounded bg-primary-black px-4 py-2 text-white focus:outline-none",children:"Add"})]}),e.jsx("div",{className:"mt-2",children:p.map((n,N)=>e.jsxs("div",{className:"m-1 inline-block rounded bg-brown-main-bg px-4 py-2",children:[n==null?void 0:n.group_name,e.jsx("button",{onClick:()=>C(n==null?void 0:n.id),className:"ml-1 text-red-500 focus:outline-none",children:"X"})]},n==null?void 0:n.id))})]})]})},z=({title:f,sectionType:c,selectedSection:p,handleSectionButtonClick:d,handleDoneButtonClick:u,editorContent:w,setEditorContent:b,isRecording:i,recordingBlob:C,startRecording:n,stopRecording:N,recorderControls:m,recordingStates:B,isTranscribing:X})=>{const[x,G]=a.useState(!1),[A,_]=a.useState(null),[L,H]=a.useState([]);let v;a.useEffect(()=>(B.isRecording?v=setInterval(()=>{G(h=>!h)},500):(clearInterval(v),G(!1)),()=>{clearInterval(v)}),[i]);const J=h=>{console.log(h),b(E=>({...E,[c]:h})),A&&clearTimeout(A);const j=setTimeout(()=>{O()},3e3);_(j)},O=()=>{console.log("Auto-saving..."),u(c),console.log(L)},K=h=>{H(j=>{const E=[...j];return E.splice(h,1),E})};return e.jsxs("div",{className:"flex flex-col justify-between",children:[e.jsx("div",{children:e.jsx("p",{className:"text-xl font-bold",children:f})}),e.jsxs("div",{className:"my-5 flex flex-col items-center justify-between rounded border border-2 border-dashed py-4",children:[e.jsx("div",{children:e.jsxs("p",{className:"text-lg font-normal",children:["Say or Type your ",c]})}),e.jsxs("div",{className:"my-3 flex items-center justify-end gap-4",children:[e.jsx(T,{onClick:()=>B.isRecording?N(c,C):n(c),className:`focus:shadow-outline rounded-full p-4 font-bold text-white focus:outline-none ${B.isRecording?`bg-red-500 ${x?"opacity-50":"opacity-100"}`:"bg-primary-black"}`,children:B.isRecording?e.jsx(st,{size:15}):e.jsx(at,{})}),e.jsx("p",{children:"Or"}),e.jsx(T,{onClick:()=>d(c,"text"),className:"focus:shadow-outline rounded-full bg-green-500 p-4 font-bold text-white focus:outline-none",children:e.jsx(ot,{})})]})]}),e.jsx("div",{className:"w-full self-end",children:e.jsxs(e.Fragment,{children:[p==="voice"&&C&&e.jsx("p",{}),X&&c&&w[c]===""&&e.jsx(Je,{}),p==="text"&&e.jsx(e.Fragment,{children:e.jsx(lt,{data:w[c],onChange:J,editorblock:"editorjs-container"})}),L.map((h,j)=>e.jsx("div",{children:e.jsxs("div",{className:"flex gap-4",children:[e.jsx(T,{className:"mt-5 rounded bg-green-500 px-4 py-2 text-white shadow",children:"Ask AI"}),e.jsx(T,{className:"mt-5 rounded bg-red-500 px-4 py-2 text-white shadow",onClick:()=>K(j),children:"Delete"})]})},j))]})})]})},W={time:new Date().getTime(),blocks:[{type:"paragraph",data:{text:""}}]};let g=new fe;const ls=()=>{const{dispatch:f}=a.useContext(he),c=Le({user_id:I(),mrr:I(),arr:I(),cash:I(),burnrate:I(),date:I()}).required(),{dispatch:p}=a.useContext(he),{state:d}=a.useContext(Ke),[u,w]=a.useState({}),[b,i]=a.useState(!1),[C,n]=a.useState(!0);a.useState("");const[N,m]=a.useState(!1),[B,X]=a.useState(""),[x,G]=a.useState([]),[A,_]=a.useState([]),[L,H]=a.useState([]),[v,J]=a.useState([]),[O,K]=a.useState([]),[h,j]=a.useState(""),[E,we]=a.useState(""),[U,D]=a.useState({weeklyUpdate:W,goodNews:W,badNews:W,uglyNews:W}),[Y,be]=a.useState(null),[te,Ne]=a.useState(!1),[se,je]=a.useState(!1),[ae,ye]=a.useState(!1),[oe,ke]=a.useState(null),[Se,ve]=a.useState(!1),[Re,Ce]=a.useState(!1),[re,Ae]=a.useState(!1);$e();const{register:ne,handleSubmit:Ee,setError:le,setValue:Ue,getValues:De,formState:{errors:ce},watch:y}=Me({resolver:_e(c)});a.useState([]);const[F,ie]=a.useState({weeklyUpdate:"",goodNews:"",badNews:"",uglyNews:""}),[k,Z]=a.useState({weeklyUpdate:{isRecording:!1,recordingBlob:null},goodNews:{isRecording:!1,recordingBlob:null},badNews:{isRecording:!1,recordingBlob:null},uglyNews:{isRecording:!1,recordingBlob:null}}),{startRecording:Ie,stopRecording:Te,togglePauseResume:dt,recordingBlob:S,isRecording:mt,isPaused:ut,recordingTime:pt,mediaRecorder:xt}=Ve();a.useEffect(()=>{y("date")&&de({user_id:y("user_id"),mrr:y("mrr"),arr:y("arr"),cash:y("cash"),burnrate:y("burnrate"),date:y("date")})},[y("date")]);const de=async t=>{i(!0);try{if(!Y){for(let o in u){let r=new FormData;r.append("file",u[o].file);let l=await g.uploadImage(r);t[o]=l.url}g.setTable("updates");const s=await g.callRestAPI({user_id:t.user_id,mrr:t.mrr?t.mrr:0,arr:t.arr?t.arr:0,cash:t.cash?t.cash:0,burnrate:t.burnrate?t.burnrate:0,date:t.date},"POST");if(!s.error)console.log("API Response:",s),be(s.data);else if(s.validation){const o=Object.keys(s.validation);for(let r=0;r<o.length;r++){const l=o[r];le(l,{type:"manual",message:s.validation[l]})}}}i(!1)}catch(s){i(!1),console.log("Error",s),le("user_id",{type:"manual",message:s.message}),Qe(p,s.message)}},Q=t=>{console.log("star",t),Ie(),Z(s=>({...s,[t]:{...s[t],isRecording:!0}}))},V=(t,s)=>{Te(),Z(o=>({...o,[t]:{...o[t],isRecording:!1,recordingBlob:s}})),j(t)};a.useEffect(()=>{S&&(console.log("Recording Blob:",S),Fe(h,S))},[S]),a.useEffect(()=>{f({type:"SETPATH",payload:{path:"updates"}}),Ue("user_id",d.user)},[]),a.useEffect(()=>{console.log("changed",U)},[L,A,v,O]);const Be=t=>!t||!t.blocks?"":t.blocks.map(s=>s.type==="header"?`${s.data.text}`:s.type==="list"?s.data.items.map(o=>`• ${o}`).join(`
`):s.type==="paragraph"?s.data.text:"").join(`

`),q=async t=>{i(!0);const s=Be(U[t]);let o=0;switch(t){case"goodNews":o=1;break;case"badNews":o=2;break;case"uglyNews":o=3;break}g.setTable("notes");const r=await g.callRestAPI({update_id:Y,type:o,status:0,content:s},"POST");if(!r.error){switch(t){case"weeklyUpdate":G(l=>[...l,{id:r.data,content:s}]);break;case"goodNews":_(l=>[...l,{id:r.data,content:s}]);break;case"badNews":J(l=>[...l,{id:r.data,content:s}]);break;case"uglyNews":K(l=>[...l,{id:r.data,content:s}]);break}console.log("plain:",r),D(l=>({...l,[t]:""})),console.log(x),i(!1)}i(!1)},me=t=>{G(s=>[...s.slice(0,t),...s.slice(t+1)])},P=t=>{const{name:s,checked:o}=t.target;switch(s){case"extractGoodNews":Ne(o);break;case"extractBadNews":je(o);break;case"extractUglyNews":ye(o);break;case"rephrase":ve(o);break;case"correctGrammar":Ce(o);break}},ue=t=>{ke(t.target.value)},ee=async(t,s)=>{i(!0),X(t);let o=x[s].id,r=0;switch(t){case"goodNews":r=1;break;case"badNews":r=2;break;case"uglyNews":r=3;break}g.setTable("notes");const l=await g.callRestAPI({id:o,type:r},"PUT");switch(console.log(l),t){case"askAi":H(R=>[...R,{content:x[s].content}]);break;case"goodNews":_(R=>[...R,{content:x[s].content}]);break;case"badNews":J(R=>[...R,{content:x[s].content}]);break;case"uglyNews":K(R=>[...R,{content:x[s].content}]);break}i(!1),me(s)},Ge=async t=>{m(!0);let s=new FormData;try{s.append("file",u[`${t}_audio`]),console.log(u[`${t}_audio`],"payload");const o=await g.callTranscribe("/v3/api/custom/goodbadugly/ai/transcribe-audio",s,"POST");if(!o.error){console.log(o);const r={id:Math.floor(Math.random()*999)+1,type:"list",data:{style:"unordered",items:[o.data]}};D(l=>({...l,[t]:{time:new Date().getTime(),blocks:[...l[t].blocks,r]}})),$(t),m(!1)}m(!1)}catch(o){m(!1),ie([t],"text"),console.error("Error transcribing audio:",o)}finally{m(!1)}},Oe=async()=>{Ae(!0);const s=`
			Extract the ${te?"good news":""}
				${se?"bad news":""}
				${ae?"ugly news":""}
			from the text delimited by ### 
				### ${E} ###

			Format response as JSON
			Example: {goodnews: "", badnews: "", uglynews: ""}
				if you have no value for any property set it as null
			
		`.trim(),o=[{role:"system",content:"You are an expert auditor auditing for a startup. create and update for the investor do not explain"},{role:"user",content:s}],r=await g.callRawAPI("/v3/api/custom/goodbadugly/ai/ask",{temperature:1,prompt:o},"POST");r.error||H(!1),console.log(r,"ask ai")},$=t=>{ie(s=>({...s,[t]:F[t]==="text"?"":"text"}))},Fe=async(t,s)=>{const o=new File([s],"audio.wav",{type:"audio/wav"});w(r=>({...r,[`${t}_audio`]:o})),Z(r=>({...r,[t]:{...r[t],recordingBlob:s}})),console.log(o),await Ge(t)},pe=new Date,xe=new Date(pe);xe.setDate(pe.getDate()+1);const Pe=xe.toISOString().split("T")[0];return e.jsxs("div",{className:"mx-auto rounded p-5 shadow-md",children:[e.jsxs("h4",{className:"text-[16px] font-medium md:text-xl",children:["New Updates ",Y?`- ${De("date")}`:""," "]}),e.jsx("p",{className:"text-lg font-bold",children:b?"Saving..":""}),C&&e.jsxs(rt,{modalHeader:!0,modalCloseClick:()=>n(!1),title:"Ask AI",children:[e.jsxs("div",{className:"bold flex items-center gap-4 font-iowan text-lg",children:[e.jsx("input",{type:"radio",id:"expandOption",name:"expandOrShorten",value:"expand",checked:oe==="expand",onChange:ue}),e.jsx("label",{htmlFor:"expandOption",children:"Expand"}),e.jsx("input",{type:"radio",id:"shortenOption",name:"expandOrShorten",value:"shorten",checked:oe==="shorten",onChange:ue}),e.jsx("label",{htmlFor:"shortenOption",children:"Shorten"})]}),e.jsxs("div",{className:"bold flex items-center gap-4 font-iowan text-lg",children:[e.jsx("input",{type:"checkbox",id:"rephrase",name:"rephrase",checked:Se,onChange:P}),e.jsx("label",{htmlFor:"rephrase",children:"Rephrase"})]}),e.jsxs("div",{className:"bold flex items-center gap-4 font-iowan text-lg",children:[e.jsx("input",{type:"checkbox",id:"correctGrammar",name:"correctGrammar",checked:Re,onChange:P}),e.jsx("label",{htmlFor:"correctGrammar",children:"Correct Grammar"})]}),e.jsx("hr",{className:"my-5"}),e.jsxs("div",{className:"bold flex items-center gap-4 font-iowan text-lg",children:[e.jsx("input",{type:"checkbox",id:"extractGoodNews",name:"extractGoodNews",checked:te,onChange:P}),e.jsx("label",{htmlFor:"extractGoodNews",children:"Extract Good News"})]}),e.jsxs("div",{className:"bold flex items-center gap-4 font-iowan text-lg",children:[e.jsx("input",{type:"checkbox",id:"extractBadNews",name:"extractBadNews",checked:se,onChange:P}),e.jsx("label",{htmlFor:"extractBadNews",children:"Extract Bad News"})]}),e.jsxs("div",{className:"bold flex items-center gap-4 font-iowan text-lg",children:[e.jsx("input",{type:"checkbox",id:"extractUglyNews",name:"extractUglyNews",checked:ae,onChange:P}),e.jsx("label",{htmlFor:"extractUglyNews",children:"Extract Ugly News"})]}),e.jsx(T,{loading:re,disabled:re,onClick:()=>Oe(),className:"w-full rounded bg-purple-500 px-4 py-2 font-bold text-white",children:"Done"})]}),e.jsxs("form",{className:"flex w-full max-w-[100vw] flex-col gap-6",onSubmit:Ee(de),children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:"text-xl font-bold",children:"Report Date"}),e.jsx("div",{children:e.jsx(ge,{type:"date",page:"updates",name:"date",errors:ce,label:"Date",placeholder:"Date",register:ne,value:Pe,className:""})})]}),e.jsx(it,{}),e.jsx("div",{className:"flex flex-row justify-end",children:x.length>0&&e.jsxs("div",{className:"mb-5",children:[e.jsx("p",{className:"text-xl font-bold",children:"Saved Updates"}),e.jsx("ul",{className:"",children:x.map((t,s)=>e.jsxs("div",{children:[e.jsxs("li",{className:"mb-2 flex list-disc justify-between rounded border p-2",children:[t.content,e.jsx("span",{className:"cursor-pointe ml-2",onClick:()=>me(s),children:"✖"})]},s),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:()=>{n(!0),we(t.content)},className:"bg-purple-500 px-4 py-2",children:"Ask AI"}),e.jsx("button",{onClick:()=>ee("goodNews",s),className:"bg-green-500 px-4 py-2",children:"Add Good News"}),e.jsx("button",{onClick:()=>ee("badNews",s),className:"bg-red-500 px-4 py-2",children:"Add Bad News"}),e.jsx("button",{onClick:()=>ee("uglyNews",s),className:"bg-red-700 px-4 py-2",children:"Add Ugly News"})]})]},s))})]})}),e.jsx(z,{title:"Weekly Update Notes",sectionType:"weeklyUpdate",selectedSection:F.weeklyUpdate,handleSectionButtonClick:$,handleDoneButtonClick:q,editorContent:U,setEditorContent:D,isRecording:k.weeklyUpdate.isRecording,recordingBlob:S,startRecording:Q,stopRecording:V,recordingStates:k.weeklyUpdate,isTranscribing:N}),e.jsxs("div",{className:"flex flex-col justify-between gap-4",children:[e.jsx(z,{title:"Good News",sectionType:"goodNews",selectedSection:F.goodNews,handleSectionButtonClick:$,handleDoneButtonClick:q,editorContent:U,setEditorContent:D,isRecording:k.goodNews.isRecording,recordingBlob:S,startRecording:Q,stopRecording:V,recordingStates:k.goodNews}),A.length>0&&e.jsx("div",{className:"mb-5",children:e.jsx("ul",{className:"",children:A.map((t,s)=>e.jsx("li",{className:"mb-2 flex list-disc justify-between rounded border p-4",children:t.content},s))})})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-4",children:[e.jsx(z,{title:"Bad News",sectionType:"badNews",selectedSection:F.badNews,handleSectionButtonClick:$,handleDoneButtonClick:q,editorContent:U,setEditorContent:D,isRecording:k.badNews.isRecording,recordingBlob:S,startRecording:Q,stopRecording:V,recordingStates:k.badNews}),v.length>0&&e.jsx("div",{className:"mb-5",children:e.jsx("ul",{className:"",children:v.map((t,s)=>e.jsx("li",{className:"mb-2 flex list-disc justify-between rounded border p-4",children:t.content},s))})})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-4",children:[e.jsx(z,{title:"Ugly News",sectionType:"uglyNews",selectedSection:F.uglyNews,handleSectionButtonClick:$,handleDoneButtonClick:q,editorContent:U,setEditorContent:D,isRecording:k.uglyNews.isRecording,recordingBlob:S,startRecording:Q,stopRecording:V,recordingStates:k.uglyNews}),O.length>0&&e.jsx("div",{className:"mb-5",children:e.jsx("ul",{className:"",children:O.map((t,s)=>e.jsx("li",{className:"mb-2 flex list-disc justify-between rounded border p-4",children:t.content},s))})})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:"text-xl font-bold",children:"Report Type"}),e.jsx("div",{children:e.jsx(ge,{type:"dropdown",page:"updates",name:"type",errors:ce,placeholder:"Report Type",register:ne,className:"w-contain px-4",options:["Template 1","Template 2","Template 3","Template 4","Template 5"]})})]}),e.jsx(T,{type:"submit",loading:b,disabled:b,className:"focus:shadow-outline rounded bg-primary-black px-4 py-2 font-bold text-white focus:outline-none",children:"Send Update"})]})]})};export{ls as default};
