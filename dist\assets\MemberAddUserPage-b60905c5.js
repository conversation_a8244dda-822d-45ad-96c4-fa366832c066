import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as l,h as S,b as _}from"./vendor-4cdf2bd1.js";import{u as E}from"./react-hook-form-9f4fcfa9.js";import{o as N}from"./yup-c41d85d2.js";import{c as R,a as o}from"./yup-342a5df4.js";import{G as T,A,M as P,g as m,s as x}from"./index-46de5032.js";import{InteractiveButton2 as I}from"./InteractiveButton-060359e0.js";import{M as n}from"./MkdInput-a0090fba.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";const ut=({onSuccess:d=null})=>{const{dispatch:r}=l.useContext(T),{dispatch:k,state:g}=l.useContext(A),[b]=S();b.get("next");const y=R({first_name:o().nullable(),last_name:o().nullable(),email:o().email("Invalid Email").required("This field is required")});_();const{register:s,handleSubmit:j,setError:C,formState:{errors:i,isSubmitting:p}}=E({resolver:N(y),defaultValues:{first_name:"",last_name:"",email:"",role:""}}),v=async a=>{var u,c,f,h;const w=new P;try{const t=await w.callRawAPI(`/v3/api/custom/goodbadugly/member/${g.company.id}/invite-to-team`,{email:m(a.email),first_name:m(a.first_name),last_name:m(a.last_name)},"POST");console.log("RESULT >>",t),t.error||(x(r,"Invite Sent",500,"success"),d&&d({...a,user_id:((u=t.data)==null?void 0:u.user_id)||((c=t.data)==null?void 0:c.id),id:((f=t.data)==null?void 0:f.user_id)||((h=t.data)==null?void 0:h.id),...t.data})),console.log("RESULT >>",t)}catch(t){console.log("ERROR >>",t),x(r,(t==null?void 0:t.message)||"Email already exist",5e3,"error")}};return l.useEffect(()=>{r({type:"SETPATH",payload:{path:"users"}})},[]),e.jsx("div",{className:"mx-auto grid h-full max-h-full min-h-full w-full grid-cols-1 grid-rows-1 rounded p-5",children:e.jsxs("form",{className:"h-full max-h-full min-h-full w-full max-w-full",onSubmit:j(v,a=>{console.error("ERROR >>",a)}),children:[e.jsxs("div",{className:"w-full space-y-5",children:[e.jsxs("div",{className:"grid w-full grid-cols-1 gap-2 md:grid-cols-2",children:[e.jsx("div",{children:e.jsx(n,{type:"text",register:s,errors:i,name:"first_name",label:"First Name"})}),e.jsx("div",{children:e.jsx(n,{type:"text",register:s,errors:i,name:"last_name",label:"Last Name"})})]}),e.jsx("div",{className:"w-full",children:e.jsx(n,{type:"text",register:s,errors:i,name:"email",required:!0,label:"Email Address"})})]}),e.jsx(I,{type:"submit",loading:p,disabled:p,className:"mt-6 !w-full !rounded-[.125rem] !bg-primary-black !px-4 !py-4 !font-bold !text-white ",children:"Submit"})]})})};export{ut as default};
