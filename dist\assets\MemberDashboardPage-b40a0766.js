import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as s,b as f}from"./vendor-4cdf2bd1.js";import"./moment-a9aaa855.js";import{b as x,a as u,L as i}from"./index-46de5032.js";import{U as p}from"./index-8a8a991b.js";import{_ as o}from"./qr-scanner-cf010ec4.js";import{c as h}from"./index-0d5645ff.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const b=s.lazy(()=>o(()=>import("./UpdateActivity-d1b727ec.js"),["assets/UpdateActivity-d1b727ec.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-46de5032.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-d88d3663.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/index-d0de8b06.js","assets/index-a9c1e6e9.js","assets/index-abd77600.js","assets/index-8a8a991b.js","assets/MkdPopover-dd21a7e9.js","assets/react-tooltip-7630c8e3.js","assets/@mantine/core-691d33c8.js","assets/@uppy/dashboard-51133bb7.js","assets/@fullcalendar/core-085b11ae.js","assets/core-b9802b0d.css","assets/@uppy/core-a4ba4b97.js","assets/@uppy/aws-s3-a6b02742.js","assets/@craftjs/core-a2cdaeb4.js","assets/@uppy/compressor-4bcbc734.js","assets/MkdListTableV2-e3b0c442.css","assets/index-af54e68d.js","assets/useDate-14dbf4c5.js"]));s.lazy(()=>o(()=>import("./UpdateEngagements-da8fdc7e.js"),["assets/UpdateEngagements-da8fdc7e.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-46de5032.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-d88d3663.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/index-d0de8b06.js","assets/useDate-14dbf4c5.js","assets/index-a9c1e6e9.js","assets/index-8a8a991b.js","assets/useGetEngagements-8188941a.js"]));s.lazy(()=>o(()=>import("./ActivityUpdateName-1ce64667.js"),["assets/ActivityUpdateName-1ce64667.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js"]));const G=()=>{var m,n;x();const{authDispatch:j,authState:t,globalDispatch:c}=u(),l=s.useRef(null);s.useState({}),s.useState(!0),s.useState(!1),f();const d=()=>{console.log("refreshActivities","success"),l.current&&(console.log("refreshActivities1","success"),l.current())},a=new Date().toLocaleTimeString("en-US",{hour:"numeric",hour12:!1});let r;return a<12?r="Good morning":a<18?r="Good afternoon":r="Good evening",s.useEffect(()=>{c({type:"SETPATH",payload:{path:"dashboard"}})},[]),e.jsx(s.Fragment,{children:e.jsxs("div",{className:"block h-full max-h-full min-h-full w-full grid-rows-[auto_auto_auto_1fr] bg-brown-main-bg px-0 md:grid md:px-8",children:[e.jsx("div",{className:"flex w-full flex-col p-4 md:hidden",children:e.jsxs("div",{className:"flex w-full flex-wrap items-center justify-between",children:[e.jsx("span",{className:"font-Inter text-xl font-bold",children:((m=t.company)==null?void 0:m.name)||"Company Name"}),e.jsx(i,{children:e.jsx(h,{})})]})}),e.jsx("div",{className:"block h-[.125rem] w-full border-[.125rem] border-black bg-black md:hidden"}),e.jsx("div",{className:"mb-[18px] mt-[24px] flex w-full flex-col px-5 sm:flex-row sm:items-start sm:justify-between md:px-0",children:e.jsxs("div",{className:"flex flex-col",children:[e.jsxs("span",{className:"font-iowan text-[24px] font-[700] capitalize sm:text-[32px]",children:[r,", ",t.profile.first_name,"!"," "]}),e.jsx("span",{className:"font-iowan-regular text-[16px] font-medium text-[#1F1D1A] sm:text-[1.0625rem]",children:"Welcome to Your Update Center"})]})}),e.jsx("div",{className:"",children:e.jsx(i,{children:e.jsx(p,{onUpdateSuccess:d})})}),e.jsx("div",{className:"h-[.125rem] w-full border-[.125rem] border-black bg-black"}),e.jsx("div",{className:"mx-auto w-full min-w-full max-w-full grid-cols-1 grid-rows-1 flex-col justify-between rounded bg-transparent pt-7 md:grid",children:e.jsx("div",{className:"mx-auto flex w-full min-w-full max-w-full flex-col justify-between gap-12 rounded",children:(n=t==null?void 0:t.profile)!=null&&n.id?e.jsx("div",{className:"mb-10 flex w-full flex-col items-start justify-center gap-[1.5rem] md:mb-0 md:min-h-full md:flex-row",children:e.jsx("div",{className:" w-full rounded-[.625rem]",children:e.jsx(i,{children:e.jsx(b,{refreshRef:l})})})}):null})})]})})};export{G as default};
