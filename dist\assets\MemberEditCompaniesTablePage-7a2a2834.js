import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{R as m,b as q,r as s,i as B}from"./vendor-4cdf2bd1.js";import{u as H}from"./react-hook-form-9f4fcfa9.js";import{o as K}from"./yup-c41d85d2.js";import{c as V,a as n}from"./yup-342a5df4.js";import{M as z,A as J,G as Q,t as W,f as X,I as Y,s as Z}from"./index-46de5032.js";import"./react-quill-a78e6fc7.js";/* empty css                   */import{M as f}from"./MkdInput-a0090fba.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@craftjs/core-a2cdaeb4.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@uppy/compressor-4bcbc734.js";let p=new z;const Fe=()=>{var S,N,E;const{dispatch:v}=m.useContext(J),L=V({user_id:n(),logo:n(),email:n(),address:n(),name:n(),phone:n()}).required(),{dispatch:j}=m.useContext(Q),[l,P]=m.useState({}),[y,x]=m.useState(!1),[R,h]=m.useState(!1),I=q(),[ee,k]=s.useState(""),[te,A]=s.useState(""),[oe,U]=s.useState(""),[se,C]=s.useState(""),[ae,T]=s.useState(""),[le,O]=s.useState(""),[g,D]=s.useState(""),[F,M]=s.useState(0),{register:c,handleSubmit:G,setError:w,setValue:r,formState:{errors:d}}=H({resolver:K(L)}),b=B();s.useEffect(function(){(async function(){try{h(!0),p.setTable("companies");const e=await p.callRestAPI({id:Number(b==null?void 0:b.id)},"GET");e.error||(r("user_id",e.model.user_id),r("logo",e.model.logo),r("email",e.model.email),r("address",e.model.address),r("name",e.model.name),r("phone",e.model.phone),k(e.model.user_id),A(e.model.logo),U(e.model.email),C(e.model.address),T(e.model.name),O(e.model.phone),D(e.logo),M(e.model.id),h(!1))}catch(e){h(!1),console.log("error",e),W(v,e.message)}})()},[]);const _=(e,o)=>{let a=l;a[e]={file:o.files[0],tempURL:URL.createObjectURL(o.files[0])},P({...a})},$=async e=>{x(!0);try{p.setTable("companies");for(let a in l){let i=new FormData;i.append("file",l[a].file);let u=await p.uploadImage(i);e[a]=u.url}const o=await p.callRestAPI({id:F,user_id:e.user_id,logo:e.logo,email:e.email,address:e.address,name:e.name,phone:e.phone},"PUT");if(!o.error)Z(j,"Updated"),I("/member/companies");else if(o.validation){const a=Object.keys(o.validation);for(let i=0;i<a.length;i++){const u=a[i];w(u,{type:"manual",message:o.validation[u]})}}x(!1)}catch(o){x(!1),console.log("Error",o),w("user_id",{type:"manual",message:o.message})}};return m.useEffect(()=>{j({type:"SETPATH",payload:{path:"companies"}})},[]),t.jsxs("div",{className:" mx-auto rounded   p-5 shadow-md",children:[t.jsx("h4",{className:"text-[16px] font-medium md:text-xl",children:"Edit Companies"}),R?t.jsx(X,{}):t.jsxs("form",{className:" w-full max-w-lg",onSubmit:G($),children:[t.jsxs("div",{className:"relative mb-4 h-[100px] w-fit py-5",children:[l&&((S=l.logo)!=null&&S.tempURL)||g?t.jsx("div",{className:"flex h-[80px] w-[80px] items-center rounded-2xl",children:t.jsx("img",{className:"h-[80px] w-[80px] rounded-2xl object-cover",src:l.logo.tempURL||g,alt:""})}):null,g||(N=l.logo)!=null&&N.file?null:t.jsx("div",{className:"flex h-[80px] w-full items-center justify-center border border-dashed border-slate-300 px-6 py-2",children:"Select a picture"}),t.jsx("input",{className:"focus:shadow-outline absolute left-0 top-0 h-full w-[100px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 leading-tight text-[#1d1f1a] opacity-0   shadow focus:outline-none sm:w-[180px]",id:"logo",type:"file",placeholder:"Logo",name:"logo",onChange:e=>_("logo",e.target)}),t.jsx("p",{className:"text-xs italic text-red-500",children:(E=d.logo)==null?void 0:E.message})]}),t.jsx(f,{type:"text",page:"edit",name:"email",errors:d,label:"Email",placeholder:"Email",register:c,className:""}),t.jsx(f,{type:"text",page:"edit",name:"address",errors:d,label:"Address",placeholder:"Address",register:c,className:""}),t.jsx(f,{type:"text",page:"edit",name:"name",errors:d,label:"Name",placeholder:"Name",register:c,className:""}),t.jsx(f,{type:"text",page:"edit",name:"phone",errors:d,label:"Phone",placeholder:"Phone",register:c,className:""}),t.jsx(Y,{type:"submit",className:"focus:shadow-outline rounded bg-primary-black px-4 py-2 font-bold text-white focus:outline-none",loading:y,disable:y,children:"Submit"})]})]})};export{Fe as default};
