import{j as a}from"./@nextui-org/listbox-0f38ca19.js";import{r as p,b as L,i as C}from"./vendor-4cdf2bd1.js";import{u as N}from"./react-hook-form-9f4fcfa9.js";import{o as V}from"./yup-c41d85d2.js";import{c as $,a as x,d as q}from"./yup-342a5df4.js";import{G as F,A as O,M as _,q as U,t as E,s as B}from"./index-46de5032.js";import{S as H}from"./SelectGroupType-3d2b3831.js";import{S as K}from"./CreateGroupModal-9562fe27.js";import{L as M}from"./index-6edcbb0d.js";import{InteractiveButton2 as Y}from"./InteractiveButton-060359e0.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./XMarkIcon-6ed09631.js";import"./MkdCustomInput-af54c64d.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";import"./XMarkIcon-cfb26fe7.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";const Ce=()=>{const{dispatch:n}=p.useContext(F),{dispatch:d,state:w}=p.useContext(O),S=L(),{id:u}=C(),[z,T]=p.useState([]),[J,G]=p.useState(null),P=$({group_id:x().required("This field is required"),members:q().min(1,"You must add at least one member").of(x())}),{register:Q,handleSubmit:y,setError:j,setValue:m,formState:{errors:R,isSubmitting:c,isLoading:v,defaultValues:W},control:g,clearErrors:A,watch:f}=N({resolver:V(P)});async function D(){var t,r;try{const l=await new _().callRawAPI(`/v4/api/records/recipient_group/${u}`,void 0,"GET");if(!l.model)throw new Error("Recipient group not found");const e=await U(n,d,"recipient_member",{filter:[`recipient_group_id,eq,${u}`],join:["user"]});e.error||T(()=>{var o;return(o=e==null?void 0:e.data)==null?void 0:o.map(s=>{var h;return(h=s==null?void 0:s.user)==null?void 0:h.id})}),m("group_id",l.model.group_id),m("members",(t=e==null?void 0:e.data)==null?void 0:t.map(o=>{var s;return(s=o==null?void 0:o.user)==null?void 0:s.id})),m("old_members",(r=e==null?void 0:e.data)==null?void 0:r.map(o=>{var s;return(s=o==null?void 0:o.user)==null?void 0:s.id})),G(l.model.group_id)}catch(i){return E(d,i.message),{group_id:"",members:[]}}}const I=async t=>{let r=new _;try{r.setTable("recipient_group"),await r.callRestAPI({id:u,group_id:t.group_id,members:t.members.join(","),user_id:w.user},"PUT");const i=t.members.filter(e=>!t.old_members.includes(e)),l=t.old_members.filter(e=>!t.members.includes(e));r.setTable("recipient_member"),await Promise.all(i.map(e=>r.callRestAPI({user_id:e,recipient_group_id:u},"POST"))),await Promise.all(l.map(e=>r.callRawAPI("/v4/api/records/recipient_member",{user_id:e,recipient_group_id:u},"DELETE"))),B(n,"Edit successful"),S("/member/recipient_group")}catch(i){E(d,i.message),j("group_id",{type:"manual",message:i.message})}};p.useEffect(()=>{n({type:"SETPATH",payload:{path:"recipient_group"}})},[]);const[b,k]=f(["members","group_id"]);return p.useEffect(()=>{A(["members","group_id"])},[b,k]),v?a.jsx(M,{}):a.jsxs("div",{className:"mx-auto rounded p-5 pt-8 md:px-8",children:[a.jsx("h4",{className:"text-xl font-semibold text-[#1f1d1a]",children:"Edit Recipient Group"}),a.jsxs("form",{className:"mt-5 w-full max-w-[500px] pb-80",onSubmit:y(I),children:[a.jsx(H,{control:g,name:"group_id",errors:R,onReady:()=>D(),setValue:t=>m("group_id",t),allowedRoles:["investor","stakeholder","NULL"],onGroupChange:(t,r)=>{console.log("EDIT PAGE - onGroupChange called with:",t,r),m("members",r||[]),console.log("EDIT PAGE - Form members value after setValue:",f("members"))}}),a.jsx(K,{defaultSelectedMembers:b,control:g,name:"members",setValue:t=>m("members",t)}),a.jsx(Y,{type:"submit",loading:c,disabled:c,className:"focus:shadow-outline mt-6 h-[40px] w-[90px] rounded bg-primary-black px-4 py-2 text-sm font-bold text-white focus:outline-none",children:c?"":"Submit"})]})]})};export{Ce as default};
