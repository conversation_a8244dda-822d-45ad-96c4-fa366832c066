import{j as s}from"./@nextui-org/listbox-0f38ca19.js";import{r as S,R as E,L as R}from"./vendor-4cdf2bd1.js";import{u as L}from"./react-hook-form-9f4fcfa9.js";import{o as C}from"./yup-c41d85d2.js";import{c as F,a as P}from"./yup-342a5df4.js";import{G as q,I as B,M as G,s as I,t as M}from"./index-46de5032.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const oe=()=>{var l;const[i,t]=S.useState(!1),b=F({email:P().email().required()}).required(),{register:j,handleSubmit:y,setError:m,formState:{errors:a}}=L({resolver:C(b)}),{dispatch:n}=E.useContext(q),N=async v=>{var r,c,d,p,x,f,u,h;let k=new G;try{t(!0);const e=await k.forgot(v.email,"member");if(!e.error)I(n,"Reset Code Sent");else if(e.validation){const g=Object.keys(e.validation);for(let o=0;o<g.length;o++){const w=g[o];m(w,{type:"manual",message:e.validation[w]})}}t(!1)}catch(e){t(!1),console.log("Error",e),m("email",{type:"manual",message:(c=(r=e==null?void 0:e.response)==null?void 0:r.data)!=null&&c.message?(p=(d=e==null?void 0:e.response)==null?void 0:d.data)==null?void 0:p.message:e==null?void 0:e.message}),M(n,(f=(x=e==null?void 0:e.response)==null?void 0:x.data)!=null&&f.message?(h=(u=e==null?void 0:e.response)==null?void 0:u.data)==null?void 0:h.message:e==null?void 0:e.message)}};return s.jsx("main",{className:"min-h-screen bg-brown-main-bg bg-no-repeat",children:s.jsx("div",{className:"flex min-h-screen flex-col items-center justify-center",children:s.jsxs("div",{className:"my-12 mt-3 flex flex w-[300px] flex-col items-center rounded-lg p-4 sm:w-[520px] lg:max-w-[700px]",children:[s.jsx("div",{className:"my-2 text-xl font-semibold text-[#262626]",children:s.jsx("svg",{onClick:()=>window.location.href="https://updatestack.com",width:"294",height:"40",viewBox:"0 0 294 40",fill:"none",xmlns:"http://www.w3.org/2000/svg"})}),s.jsxs("div",{className:"mb-3 flex flex-col items-center justify-center gap-2",children:[s.jsx("span",{className:"max-w-[400px] whitespace-nowrap font-iowan text-[24px] font-semibold sm:text-[40px]",children:"Forgot Password"}),s.jsx("span",{className:"text-center text-[14px] font-[500] lg:text-base",children:"Enter your email to reset your password"})]}),s.jsxs("form",{className:"w-[350px] max-w-[96%] space-y-3 md:min-w-[70%] md:space-y-6",onSubmit:y(N),children:[s.jsxs("div",{className:"mt-[14px] flex flex-col space-y-2 text-sm",children:[s.jsx("label",{htmlFor:"",className:"font-iowan text-[16px] font-[700]",children:"Email"}),s.jsx("input",{className:"h-[44px] border-[2px] border-[#1f1d1a] bg-transparent px-3 py-2 text-[#1f1d1a] outline-none focus:border-[#1f1d1a] focus:shadow-none focus:outline-none",type:"email",placeholder:"<EMAIL>",...j("email")}),s.jsx("p",{className:"text-xs italic text-red-500",children:(l=a==null?void 0:a.email)==null?void 0:l.message})]}),s.jsx(B,{type:"submit",className:"ca my-12 flex h-[44px] w-full items-center justify-center rounded-sm bg-[#1f1d1a] py-2 tracking-wide text-white outline-none focus:outline-none",loading:i,disabled:i,children:s.jsx("span",{className:"capitalize",children:"Reset Password"})})]}),s.jsxs("div",{className:"mt-4 flex items-center text-sm",children:[s.jsxs("span",{className:"mr-1 font-medium text-[#1f1d1a]",children:["Remember your password?"," "]})," ",s.jsx(R,{to:"/member/login",className:"font-bold text-[#1f1d1a] underline",children:"Login here"})]})]})})})};export{oe as default};
