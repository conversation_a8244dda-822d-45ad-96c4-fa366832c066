import{j as r}from"./@nextui-org/listbox-0f38ca19.js";import{b as U,u as T,r as p,j as z}from"./vendor-4cdf2bd1.js";import{a as W,u as G,bb as H,bc as V,bd as Q,be as X,bf as J,bg as K,bh as Y,bi as Z,bj as S,bk as g,bl as ee}from"./index-46de5032.js";import{H as se}from"./index-59df21c1.js";import{S as re}from"./MemberWrapper-74a315f4.js";import{c as te}from"./index.esm-54e24cf9.js";import{u as ie}from"./useRecentEngagements-74872108.js";import{u as ne}from"./react-responsive-19663c38.js";import{c as de}from"./@popperjs/core-f3391c26.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./index-0d5645ff.js";import"./react-icons-36ae72b7.js";const Pe=()=>{var O,E;const{globalState:e,globalDispatch:o,authState:a,authDispatch:pe}=W(),m=U(),v=T(),{profile:n}=G(),{engagements:c,refetch:y,totals:R,activeParams:A,resetView:C}=ie(),w=p.useRef(null),j=p.useRef(null),f=p.useRef(null),h=p.useRef(null),[x,l]=p.useState(!1),[oe,D]=p.useState(null),u=ne({maxWidth:767});p.useEffect(()=>(w.current=setInterval(y,18e4),()=>{w.current&&clearInterval(w.current)}),[y]);const b=p.useCallback(()=>f.current&&h.current?de(f.current,h.current,{placement:e!=null&&e.isOpen?"top-end":"top-start",modifiers:[{name:"offset",options:{offset:[0,8]}},{name:"preventOverflow",options:{boundary:"viewport",padding:8}},{name:"flip",options:{fallbackPlacements:["bottom-end","bottom-start","right","left"],padding:8}},{name:"zIndex",enabled:!0,phase:"beforeWrite",fn({state:t}){return t.styles.popper.zIndex=9999999,t}}]}):null,[e==null?void 0:e.isOpen]);p.useEffect(()=>{if(x){const t=b();return D(t),()=>{t&&t.destroy()}}},[x,b]),p.useEffect(()=>{const t=i=>{u&&j.current&&!j.current.contains(i.target)&&(e!=null&&e.isOpen)&&o({type:"OPEN_SIDEBAR",payload:{isOpen:!1}}),x&&h.current&&f.current&&!h.current.contains(i.target)&&!f.current.contains(i.target)&&l(!1)};return document.addEventListener("mousedown",t),document.addEventListener("touchstart",t),()=>{document.removeEventListener("mousedown",t),document.removeEventListener("touchstart",t)}},[u,e==null?void 0:e.isOpen,o,x]);const F=({to:t,children:i,className:d})=>r.jsx(z,{to:t,className:d,onClick:()=>{u&&o({type:"OPEN_SIDEBAR",payload:{isOpen:!1}})},children:i});console.log(e==null?void 0:e.requestedUpdates,"requested_updates");const _=(O=a==null?void 0:a.requested_updates)==null?void 0:O.list.reduce((t,i)=>{const d=i.investor_id;return(!t[d]||t[d].update_request_id<i.update_request_id)&&(t[d]=i),t},{});console.log(_,"grouped"),Object.values(_);const P=(E=a==null?void 0:a.requested_times)==null?void 0:E.list.reduce((t,i)=>{const d=i.investor_id;return(!t[d]||t[d].id<i.id)&&(t[d]=i),t},{});console.log(e==null?void 0:e.updateSideNotes,"notedp"),Object.values(P),console.log(e==null?void 0:e.updateQuestions,"quest");const B=p.useMemo(()=>{var t,i,d,k,I,$;return[{to:"/member/dashboard",text:"Dashboard",icon:r.jsx(H,{fillOpacity:`${(e==null?void 0:e.path)==="dashboard"?"1":"0.6"}`}),value:"dashboard"},{to:"/member/updates?availability=available",text:"Updates",icon:r.jsx(V,{fillOpacity:`${(e==null?void 0:e.path)==="updates"?"1":"0.6"}`}),value:"updates",children:[...e!=null&&e.updateSideNotes&&((t=e==null?void 0:e.updateSideNotes)!=null&&t.length)?[{belong:"current-update",type:"title",to:"",text:e==null?void 0:e.currentUpdate,value:"current-update"},{belong:"current-update",type:"link",to:"#overview",text:"Overview",value:"overview"}].concat((d=(i=e==null?void 0:e.updateSideNotes)==null?void 0:i.filter(s=>s.content))==null?void 0:d.sort((s,N)=>{const q=s.order!==void 0&&s.order!==null?s.order:Number.MAX_SAFE_INTEGER,L=N.order!==void 0&&N.order!==null?N.order:Number.MAX_SAFE_INTEGER;return q-L}).map(s=>({belong:"current-update",id:s==null?void 0:s.id,type:"link",to:`#${s==null?void 0:s.type}`,text:s==null?void 0:s.type,value:s==null?void 0:s.type}))).concat(((k=e==null?void 0:e.updateQuestions)==null?void 0:k.length)>0?[{belong:"current-update",id:null,type:"link",to:"#update-asks",text:"Asks",value:"update-asks"}]:[]).concat([{belong:"current-update",id:null,type:"link",to:"#summary",text:"Summary",value:"summary"}]):[]]},(e==null?void 0:e.requestedUpdates)>0?{to:"/member/updates?availability=available&view=my_updates",text:r.jsxs("div",{className:"flex justify-between items-center w-full",children:["Requested updates"," ",r.jsx("div",{className:"ml-[48px] flex  h-[20px] w-[20px] items-center justify-center  rounded-full bg-[#F6A03C] text-xs text-[#1F1D1A] sm:h-[20px] sm:min-h-[20px] sm:w-[20px] sm:min-w-[20px]",children:r.jsx("span",{className:"text-xs",children:e==null?void 0:e.requestedUpdates})})]}),icon:r.jsx(Q,{}),value:"requested_updates"}:null,{to:"",text:"Engagements",icon:r.jsx(X,{fillOpacity:`${(e==null?void 0:e.path)==="dashboard"?"1":"0.6"}`}),value:"engagements",children:[...((I=c==null?void 0:c.myUpdates)==null?void 0:I.length)>0?[{belong:"engagement",type:"title",text:"My Updates",value:"my-updates-title"},...c.myUpdates.map(s=>({belong:"engagement",id:s==null?void 0:s.id,type:"href",to:`/member/update/private/view/${s==null?void 0:s.update_id}#${s==null?void 0:s.section}?from=engagement&engagement_id=${s==null?void 0:s.id}&engagement_type=${(s==null?void 0:s.type)||"comment"}`,text:`${s==null?void 0:s.commenter_first_name} ${s==null?void 0:s.commenter_last_name}`,value:"engagement"}))]:[{belong:"engagement",id:null,type:null,to:null,text:"No updates",value:"engagement"}],...(($=c==null?void 0:c.teamUpdates)==null?void 0:$.length)>0?[{belong:"engagement",type:"title",text:"Team Updates",value:"team-updates-title"},...c.teamUpdates.map(s=>({belong:"engagement",id:s==null?void 0:s.id,type:"href",to:`/member/update/private/view/${s==null?void 0:s.update_id}#${s==null?void 0:s.section}?from=engagement&engagement_id=${s==null?void 0:s.id}&engagement_type=${(s==null?void 0:s.type)||"comment"}`,text:`${s==null?void 0:s.commenter_first_name} ${s==null?void 0:s.commenter_last_name}`,value:"engagement"}))]:[]]},{to:"/member/select-template",text:"  Templates",icon:r.jsx(J,{fillOpacity:`${(e==null?void 0:e.path)==="templates"?"1":"0.6"}`}),value:"templates"},{to:"/member/recipient_group",text:"  Recipient Groups",icon:r.jsx(K,{fillOpacity:`${(e==null?void 0:e.path)==="recipient_group"?"1":"0.6"}`}),value:"recipient_group"}].filter(s=>s)},[e==null?void 0:e.path,e==null?void 0:e.updateSideNotes,c]);let M=t=>{o({type:"OPEN_SIDEBAR",payload:{isOpen:t}})};return r.jsxs("div",{ref:j,className:`absolute z-[9999999] flex flex-col bg-[#1F1D1A]  text-[#A8A8A8] transition-all lg:relative ${e!=null&&e.isOpen?"absolute left-0 top-0 h-full w-[19rem] px-5 sm:min-w-[19rem] sm:max-w-[19rem] ":"h-full w-0 bg-[#1f1d1a] text-white sm:min-w-[5rem] sm:max-w-[5.25rem] md:w-[5.25rem] md:px-5 lg:relative"} `,children:[r.jsxs("div",{className:"relative grid h-screen max-h-full min-h-full min-w-full max-w-full grid-cols-1 grid-rows-[auto_1fr_auto] flex-col overflow-y-clip   pt-[1.5rem] ",children:[r.jsx("div",{className:`text-[#393939] ${e!=null&&e.isOpen?"flex w-full":"flex items-center justify-center"} `,children:r.jsx(se,{})}),r.jsx("div",{className:"overflow-auto h-full min-h-full max-h-full scrollbar-hide",children:r.jsx("div",{className:`sidebar mt-6 flex h-fit w-auto flex-1   ${e!=null&&e.isOpen?"justify-start":"justify-center"}`,children:r.jsx("div",{className:"w-full",children:r.jsx("ul",{className:"flex flex-col gap-[.75rem]   text-sm ",children:B.map(t=>{var i;return r.jsxs(r.Fragment,{children:[r.jsx("li",{className:`flex h-[2.5rem] w-full list-none items-center rounded-[.25rem] font-inter text-[.875rem] font-[600] leading-[1.0588rem] hover:bg-[#F7F5D714] ${(e==null?void 0:e.path)==t.value?"bg-[#F7F5D714] text-white":""}`,children:r.jsxs(F,{to:t.to,className:`flex w-full items-center gap-3 whitespace-nowrap px-[.8456rem] ${e!=null&&e.isOpen?"w-full justify-start":"w-[2.5rem] justify-center"}`,children:[r.jsx("span",{children:t.icon}),(e==null?void 0:e.isOpen)&&r.jsx("span",{children:t.text})]})},t.value),(i=t==null?void 0:t.children)!=null&&i.length&&(e!=null&&e.isOpen)?r.jsx(re,{menus:(()=>(console.log("Passing children to SubMenus:",{itemValue:t.value,children:t==null?void 0:t.children,currentPath:v.pathname,currentHash:v.hash,updateSideNotes:e==null?void 0:e.updateSideNotes}),t==null?void 0:t.children))(),engagements:c,totals:R,refetch:y,activeParams:A,resetView:C},`${t.value}-${v.pathname}-${v.hash}`):null]})})})})})}),r.jsx("div",{className:"flex justify-end pb-5",children:r.jsxs("div",{className:"relative z-[9999999] w-full space-y-3 px-2 text-[#262626]",children:[r.jsxs("button",{ref:f,className:"flex w-full items-center justify-center gap-[13px] border-t border-t-white/10 pb-1 pt-4 sm:gap-6 md:gap-[13px]",onClick:()=>p.startTransition(()=>l(!x)),children:[r.jsx("img",{className:`${e!=null&&e.isOpen?"h-10 w-10":"h-5 w-5 xl:h-6 xl:w-6"} rounded-[50%] object-cover`,src:(n==null?void 0:n.photo)??"/default.png",alt:""}),e!=null&&e.isOpen?r.jsxs(r.Fragment,{children:[r.jsxs("div",{className:"text-left text-white",children:[r.jsxs("p",{className:"w-32 text-sm font-medium truncate",children:[n==null?void 0:n.first_name," ",n==null?void 0:n.last_name]}),r.jsx("p",{className:"mt-1 w-[120px] truncate text-xs md:w-[150px]",children:n==null?void 0:n.email})]}),r.jsx(te,{className:"ml-[-15px] min-h-5 min-w-5 text-white"})]}):null]}),x&&r.jsx("div",{ref:h,className:"fixed z-[9999999] w-[160px] divide-y divide-gray-100 rounded-md border border-[#1f1d1a] bg-brown-main-bg font-bold text-[#1f1d1a] shadow-lg ring-1 ring-[#1f1d1a] ring-opacity-5 focus:outline-none",children:r.jsxs("div",{className:"px-1 py-1",children:[r.jsxs("button",{className:"flex items-center px-3 py-3 w-full text-sm border-b group border-b-transparent hover:border-b-black/20",onClick:()=>{u&&o({type:"OPEN_SIDEBAR",payload:{isOpen:!1}}),m("/member/account"),l(!1)},children:[r.jsx(Y,{className:"mr-2 w-5 h-5"}),"Account"]}),r.jsxs("button",{className:"flex items-center px-3 py-3 w-full text-sm border-b group border-b-transparent hover:border-b-black/20",onClick:()=>{u&&o({type:"OPEN_SIDEBAR",payload:{isOpen:!1}}),m("/member/company"),l(!1)},children:[r.jsx(Z,{className:"mr-2 w-5 h-5"}),"Company"]}),r.jsxs("button",{className:"flex items-center px-3 py-3 w-full text-sm border-b group border-b-transparent hover:border-b-black/20",onClick:()=>{u&&o({type:"OPEN_SIDEBAR",payload:{isOpen:!1}}),m("/member/integrations"),l(!1)},children:[r.jsx("img",{src:"/assets/cpu-setting.svg",className:"mr-2 min-h-5 min-w-5"}),"Integrations"]}),r.jsxs("button",{className:"flex items-center px-3 py-3 w-full text-sm border-b group border-b-transparent hover:border-b-black/20",onClick:()=>{u&&o({type:"OPEN_SIDEBAR",payload:{isOpen:!1}}),m("/member/billing"),l(!1)},children:[r.jsx(S,{className:"mr-2 w-5 h-5"}),"Billing"]}),r.jsxs("button",{className:"flex items-center px-3 py-3 w-full text-sm border-b group border-b-transparent hover:border-b-black/20",onClick:()=>{u&&o({type:"OPEN_SIDEBAR",payload:{isOpen:!1}}),m("/member/company/teams"),l(!1)},children:[r.jsx(g,{className:"mr-2 w-5 h-5"}),"Team"]}),r.jsxs("button",{className:"group flex w-full items-center px-3 py-3 text-sm text-[#CE0000]",onClick:()=>{u&&o({type:"OPEN_SIDEBAR",payload:{isOpen:!1}}),localStorage.removeItem("user"),localStorage.removeItem("token"),m("/member/login"),l(!1)},children:[r.jsx(ee,{className:"mr-2 w-5 h-5"}),"Logout"]})]})})]})})]}),r.jsx("div",{className:" absolute -right-3 top-[1.5rem] z-[5] hidden cursor-pointer md:block",children:r.jsx("div",{onClick:()=>M(!(e!=null&&e.isOpen)),children:r.jsx("span",{children:r.jsxs("svg",{width:"25",height:"25",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:`${e!=null&&e.isOpen?"hidden":""}`,children:[r.jsx("rect",{x:"0.5",y:"0.5",width:"19",height:"19",rx:"9.5",fill:"#1F1D1A"}),r.jsx("rect",{x:"0.5",y:"0.5",width:"19",height:"19",rx:"9.5",stroke:"#FFF0E5"}),r.jsx("path",{d:"M8.66602 6.66667L11.9993 10L8.66602 13.3333",stroke:"#FFF0E5","stroke-width":"1.33333","stroke-linecap":"round","stroke-linejoin":"round"})]})})})})]})};export{Pe as StartupHeader,Pe as default};
