import{j as r}from"./@nextui-org/listbox-0f38ca19.js";import{r as n,R as se,b as te,h as oe}from"./vendor-4cdf2bd1.js";import{u as ie}from"./react-hook-form-9f4fcfa9.js";import{o as ne}from"./yup-c41d85d2.js";import{c as ae,a as R}from"./yup-342a5df4.js";import{A as C}from"./AddButton-51d1b2cd.js";import{b as ce,a as le,u as me,L as j,I as A,a6 as de,E as ue,d as pe,M as fe}from"./index-46de5032.js";import{M as ge}from"./index-d0de8b06.js";import{_ as he}from"./qr-scanner-cf010ec4.js";import{getCorrectValueTypeFormat as xe}from"./MkdListTableV2-bda31faf.js";import{G as be,M as we}from"./index-a9c1e6e9.js";import{A as _e}from"./index-a807e4ab.js";import"./moment-a9aaa855.js";import{R as je}from"./RecipientGroupMembers2-32b4726a.js";import{u as ye}from"./useGroups-d4ba52a9.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./index-3283c9b7.js";import"./ExportButton-eb4cf1f9.js";import"./index.esm-54e24cf9.js";import"./react-icons-36ae72b7.js";import"./MkdInput-a0090fba.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";import"./lucide-react-0b94883e.js";import"./index-af54e68d.js";const ke=n.lazy(()=>he(()=>import("./NoRecipients-e0047323.js"),["assets/NoRecipients-e0047323.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-46de5032.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-d88d3663.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css"])),Se=[{header:"ID",accessor:"id",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Group Name",accessor:"group_name",join:"group",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Members",accessor:"members",isSorted:!0,thumbnail:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Action",accessor:""}],hr=()=>{var P;const c=n.useRef(null),$=n.useRef(null),L=n.useRef(null),u=n.useRef(null),T=n.useRef(null),p=n.useRef(null),x=btoa("group:group_id"),b=btoa("group:members"),{groups:G}=ye();console.log(G);const{sdk:N}=ce(),{globalDispatch:y,authDispatch:F,authState:Ee,getManyById:O,showToast:k,tokenExpireError:V}=le(),[g,q]=se.useState([{accessor:"group_id",operator:"cs",value:"",uid:x},{accessor:"members",operator:"cs",value:"",uid:b}]),[s,w]=n.useState({filter:[],refresh:!1,modal:null,showModal:!1,selectedItems:[],loading:!1}),[z,v]=n.useState(!1),B=n.useMemo(()=>JSON.stringify(g),[g]),I=te();oe();const{profile:o}=me(),D=ae({group_id:R(),members:R()}),{register:Ne,reset:K,setValue:_,watch:U,formState:{errors:ve}}=ie({resolver:ne(D)}),{group_id:H,members:J}=U(),h=n.useCallback((e,t,l)=>{q(i=>i.map(a=>(a==null?void 0:a.uid)===l?{...a,[e]:t}:a))},[g,B]),X=n.useCallback(e=>{let t=[];return new Set(e.map(i=>i==null?void 0:i.accessor)).forEach(i=>{const a=e.filter(m=>m.accessor===i);if((a==null?void 0:a.length)>0){const m=a.filter(d=>d==null?void 0:d.value);if(m.length>1)m.forEach(d=>{const{accessor:f,operator:E,value:ee}=d,re=`goodbadugly_recipient_group.${f},${E},${ee}`;t.push(re)});else if(m.length===1){const{accessor:d,operator:f,value:E}=m[0];t.push(`goodbadugly_recipient_group.${d},${f},${xe(E,f)}`)}}}),t},[g]);async function Q(){const e=new fe;try{await e.exportCSVGroup()}catch(t){console.error("Export error:",t),k("Failed to export data",5e3,"error")}}const W=()=>{I("/member/add-recipient_group")},S=n.useCallback((e,t,l=[])=>{w(i=>({...i,selectedItems:l,showModal:t,modal:t?e:null}))},[]);async function Y(e){if(e){v(!0);try{N.setTable("recipient_group"),await N.callRestAPI({id:e},"DELETE"),k(y,"Delete successful"),c!=null&&c.current&&c.current.click()}catch(t){console.error("Delete error:",t),V(F,t.message),t.message!=="TOKEN_EXPIRED"&&k(y,t.message,5e3,"error")}finally{v(!1),S("delete",!1,[])}}}const M=n.useCallback((e={clear:!1})=>{var l,i;e!=null&&e.clear&&(h("value","",x),h("value","",b),u!=null&&u.current&&((l=u==null?void 0:u.current)==null||l.click()),p!=null&&p.current&&((i=p==null?void 0:p.current)==null||i.click()));const t=e!=null&&e.clear?[]:X(g);w(a=>({...a,filter:[`goodbadugly_recipient_group.user_id,eq,${o==null?void 0:o.id}`,...t],refresh:!0}))},[g]),Z=async(e,t,l)=>{const i=e==null?void 0:e.map(async m=>{var f;const d=await O("user",(f=m==null?void 0:m.members)==null?void 0:f.split(","));return{...m,members:r.jsx(je,{members:d==null?void 0:d.data})}});return await Promise.all(i)};return n.useEffect(()=>{y({type:"SETPATH",payload:{path:"recipient_group"}}),o!=null&&o.id&&w(e=>({...e,refresh:!1,filter:[`goodbadugly_recipient_group.user_id,eq,${o==null?void 0:o.id}`]}))},[o==null?void 0:o.id]),n.useEffect(()=>{var e;s!=null&&s.refresh&&(c!=null&&c.current)&&((e=c==null?void 0:c.current)==null||e.click())},[s==null?void 0:s.refresh]),r.jsxs(n.Fragment,{children:[r.jsxs("div",{className:" space-y-[1rem] rounded  bg-brown-main-bg p-5 px-5 md:px-8",children:[r.jsxs("div",{className:"my-[16px] flex w-full items-center justify-between gap-5",children:[r.jsx("h4",{className:"font-iowan text-[20px] font-bold md:text-[2rem] md:leading-[2.486rem]",children:"Recipient Groups"}),r.jsx("div",{className:"flex w-fit items-center justify-start gap-5",children:r.jsx(j,{children:r.jsx(C,{showPlus:!1,className:"!rounded-[.125rem] !p-[10px] !px-[10px]",onClick:()=>W(),children:"New Group"})})})]}),r.jsxs("div",{className:"flex w-full items-end justify-between gap-5",children:[r.jsxs("div",{className:"flex w-full flex-col gap-5 sm:flex-row sm:items-end md:w-[55%]",children:[r.jsxs("div",{className:"sm:grow=[0.5] flex grid-cols-[repeat(auto-fill,minmax(14rem,1fr))] flex-wrap items-end gap-2 sm:grid",children:[r.jsx("div",{className:"!grow",children:r.jsx(be,{onSelect:(e,t=!1)=>{var l,i;t?(h("value","",x),_("group_id","")):(h("value",(l=e==null?void 0:e.group)==null?void 0:l.id,x),_("group_id",(i=e==null?void 0:e.group)==null?void 0:i.id))},value:H,refreshRef:L,clearRef:u})}),r.jsx("div",{className:"!grow",children:r.jsx(j,{children:r.jsx(we,{onSelect:(e,t=!1)=>{t?(h("value","",b),_("member_id","")):(h("value",e==null?void 0:e.member_id,b),_("member_id",e==null?void 0:e.member_id))},value:J,refreshRef:T,clearRef:p})})})]}),r.jsxs("div",{className:"flex items-end gap-3",children:[r.jsx(A,{type:"submit",className:"mb-[-5px] flex !h-[2.25rem] !w-fit !min-w-fit !max-w-fit items-center justify-center whitespace-nowrap !rounded-[0.125rem] !border  bg-[#1f1d1a] !py-[0.5rem] px-2 !text-[1rem] tracking-wide text-white outline-none focus:outline-none md:px-5",color:"black",disabled:!(o!=null&&o.id),onClick:()=>{M()},children:"Search"}),r.jsx(C,{onClick:()=>{K(),M({clear:!0})},showPlus:!1,className:"!w-fit !min-w-fit !max-w-fit !border-0 !bg-transparent !p-0 !font-inter !text-[1rem] !font-[600] !leading-[1.21rem] !tracking-wide !text-black !underline !shadow-none md:mb-[-10px]",children:"Clear"})]})]}),r.jsxs(A,{type:"button",className:"flex !h-[2.25rem]  !w-fit !min-w-fit items-center justify-center whitespace-nowrap !rounded-[0.125rem] !border border-black bg-transparent !py-[0.5rem] px-2 !text-[1rem] tracking-wide text-black outline-none focus:outline-none md:px-5",color:"black",onClick:()=>{Q()},children:[r.jsx(de,{})," Export"]})]}),r.jsx("div",{className:"h-[.125rem] w-full border-[.125rem] border-black bg-black "}),o!=null&&o.id&&((P=s==null?void 0:s.filter)==null?void 0:P.length)>0?r.jsx(ge,{showSearch:!1,useDefaultColumns:!0,defaultColumns:[...Se],noDataComponent:{use:!0,component:r.jsx(j,{children:r.jsx(ke,{})})},onReady:()=>{w(e=>({...e,refresh:!1}))},processes:[Z],hasFilter:!1,tableRole:"admin",actionId:"id",table:"recipient_group",join:["user","group"],defaultFilter:s==null?void 0:s.filter,actions:{view:{show:!1,action:null,multiple:!1},select:{show:!1,action:null,multiple:!1},update:{show:!0,action:e=>{e!=null&&e[0]&&I(`/member/edit-recipient_group/${e[0]}`,{state:e[0]})},multiple:!1,children:"Edit",showChildren:!0,icon:r.jsx(ue,{}),locations:["dropdown"],onClick:e=>{e.preventDefault(),e.stopPropagation()}},remove:{show:!0,action:e=>{e!=null&&e[0]&&S("delete",!0,e)},multiple:!1,children:"Delete",icon:r.jsx(pe,{fill:"#292D32"}),locations:["dropdown"],onClick:e=>{e.preventDefault(),e.stopPropagation()}},view_all:{show:!1,type:"static",action:()=>{},children:r.jsx(r.Fragment,{children:"View All"}),className:"!gap-2 !bg-transparent !text-black !underline !shadow-none !border-0"},add:{show:!1,multiple:!0,children:"+ Add"},export:{show:!1,action:null,multiple:!0}},defaultPageSize:20,showPagination:!0,maxHeight:"md:grid-rows-[inherit] grid-rows-[inherit]",actionPostion:["dropdown"],refreshRef:c,updateRef:$}):null]}),r.jsx(j,{children:r.jsx(_e,{mode:"delete",action:"delete",table:"recipient_group",inputConfirmation:!1,title:"Delete Recipient Group",data:{id:s==null?void 0:s.selectedItems[0]},onSuccess:()=>{Y(s==null?void 0:s.selectedItems[0])},onClose:()=>S("delete",!1,[]),isOpen:(s==null?void 0:s.showModal)&&["delete"].includes(s==null?void 0:s.modal),loading:z})})]})};export{hr as default};
