import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as n,R as K,u as te,b as ae,L as Q}from"./vendor-4cdf2bd1.js";import{u as le}from"./react-hook-form-9f4fcfa9.js";import{o as ne}from"./yup-c41d85d2.js";import{c as oe,a as T}from"./yup-342a5df4.js";import{M as ie,A as me,G as re,L as ce,aa as de,I as xe,s as F}from"./index-46de5032.js";import{P as fe}from"./popup-94fb9e6f.js";import{l as pe}from"./logo5-2e16f0f2.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./MoonLoader-6f2b5db4.js";let i=new ie;const Ke=()=>{var N,y;const[J,u]=n.useState(!1);n.useState();const[z,V]=n.useState([]),[W,X]=n.useState(),[g,H]=n.useState(!1),Z=oe({email:T().email().required(),password:T().required()}).required(),{dispatch:q}=K.useContext(me),{dispatch:w}=K.useContext(re),[m,r]=n.useState(!1),[c,_]=n.useState(!1),$=te(),d=new URLSearchParams($.search).get("redirect_uri");console.log(d);const x=ae(),{register:b,handleSubmit:ee,setError:f,formState:{errors:o}}=le({resolver:ne(Z)});n.useEffect(()=>{localStorage.removeItem("admin_token")},[]);const A=async(t,a)=>{var C,v,S,L,M,R,k,I,G,O,U,B,E,Y,D;X(t||a);const p=t.email?t.email:(C=a==null?void 0:a.data)==null?void 0:C.email,se=t.password?t.password:(v=a==null?void 0:a.data)==null?void 0:v.password;try{r(!0);const s=await i.login(p,se,"member",a.company_id,g);if(s.error){if(r(!1),s.validation){const l=Object.keys(s.validation);for(let h=0;h<l.length;h++){const P=l[h];f(P,{type:"manual",message:s.validation[P]})}}}else if(s.two_factor_enabled){localStorage.setItem("2FA",s.two_factor_enabled);let l=btoa(JSON.stringify(JSON.stringify(s)));x(`/member/2FA/?t=${l}`)}else{q({type:"LOGIN",payload:s}),i.setTable("user");const l=await i.callRawAPI(`/v3/api/custom/goodbadugly/get-profile/${s.user_id}`,{},"GET");F(w,"Succesfully Logged In",4e3,"success"),await i.callRawAPI("/v3/api/custom/goodbadugly/user/update-login",[],"GET"),(S=l==null?void 0:l.data)!=null&&S.is_onboarded?x(d??"/member/dashboard"):x(d??"/member/get-started")}}catch(s){if(r(!1),F(w,(M=(L=s==null?void 0:s.response)==null?void 0:L.data)!=null&&M.message?(k=(R=s==null?void 0:s.response)==null?void 0:R.data)==null?void 0:k.message:s==null?void 0:s.message,4e3,"error"),s.message==="Select an account id to login to."){const l=JSON.parse(sessionStorage.getItem("selectCompanyAccount"));u(!0),V(l.accounts)}(s==null?void 0:s.message)=="Invalid Password"?f("password",{type:"manual",message:(G=(I=s==null?void 0:s.response)==null?void 0:I.data)!=null&&G.message?(U=(O=s==null?void 0:s.response)==null?void 0:O.data)==null?void 0:U.message:s==null?void 0:s.message}):f("email",{type:"manual",message:(E=(B=s==null?void 0:s.response)==null?void 0:B.data)!=null&&E.message?(D=(Y=s==null?void 0:s.response)==null?void 0:Y.data)==null?void 0:D.message:s==null?void 0:s.message})}},j=async t=>{let a="member";const p=await i.oauthLoginApi(t,a);window.open(p,"_self")};return e.jsxs("main",{className:"bg-cover] h-screen  min-h-screen bg-brown-main-bg bg-no-repeat",children:[e.jsx("div",{className:"sticky right-0 top-0 z-[9] flex h-[4.5rem] w-full flex-row items-center justify-between bg-[#1f1d1a] px-8 md:hidden",children:e.jsx("img",{src:pe,alt:"logo",className:"h-10 w-[180px]"})}),e.jsxs("div",{className:"flex items-center max-h-full md:h-full md:min-h-full",children:[e.jsxs("div",{className:"relative hidden h-full max-h-full min-h-full flex-col items-center justify-center bg-black px-5 md:flex md:w-1/2 md:px-[6.9375rem]",children:[e.jsx("div",{className:"my-2 text-xl font-semibold text-[#262626]",children:e.jsx(ce,{children:e.jsx(de,{className:"!h-[3.25rem] !w-[23.8519rem]"})})}),e.jsx("div",{className:"flex flex-col gap-2 justify-center items-center mb-3",children:e.jsxs("span",{className:"whitespace-nowrap text-center font-iowan text-[24px] font-semibold text-[#FFF0E5] sm:text-[2.5rem]",children:["Welcome to Your ",e.jsx("br",{})," Update Center"]})}),e.jsxs("div",{className:"absolute inset-x-0 bottom-[3.75rem] m-auto flex h-[1.1875rem] max-h-[1.1875rem] min-h-[1.1875rem] w-full  justify-center gap-[1.5rem] md:flex-row",children:[e.jsxs("a",{target:"_blank",className:"text-center font-inter text-[1rem] font-[500] leading-[1.21rem] text-[#A5A5A3]",href:"https://updatestack.com/privacy-policy",children:[" ","Privacy Policy"]}),e.jsx("hr",{className:"h-[.125rem] w-full border-[.125rem] border-[#A5A5A3] bg-[#A5A5A3] md:h-full md:w-[0.125rem]"}),e.jsx("a",{target:"_blank",className:"text-center font-inter text-[1rem] font-[500] leading-[1.21rem] text-[#A5A5A3]",href:"https://updatestack.com/terms-of-use",children:"Terms of Service"})]})]}),e.jsxs("div",{className:"md: h flex max-h-full w-full flex-col items-center justify-start px-6 pt-10 md:h-full md:min-h-full md:w-1/2 md:justify-center md:px-[3rem] lg:px-[6.9375rem]",children:[e.jsxs("div",{className:"flex w-full flex-col items-center justify-center gap-[1rem] md:grow",children:[e.jsxs("form",{className:"w-full min-w-full max-w-full space-y-[1rem] 2xl:space-y-[2rem]",onSubmit:ee(A),children:[e.jsx("h2",{className:"font-iowan text-[2rem] font-[700] leading-[3.25rem] text-[#1F1D1A]",children:"Sign in"}),e.jsxs("div",{className:" mt-[.875rem] flex flex-col space-y-1 text-sm ",children:[e.jsx("label",{htmlFor:"",className:"font-iowan text-[16px] font-[700]",children:"Email"}),e.jsx("input",{className:"h-[44px] border-[2px] border-[#1f1d1a] bg-transparent  px-3 py-2 text-sm font-normal text-[#1f1d1a] outline-none focus:border-[#1f1d1a] focus:shadow-none focus:outline-none",type:"text",placeholder:"<EMAIL>",...b("email")}),e.jsx("p",{className:"mb-[12px] text-xs italic text-red-500",children:(N=o==null?void 0:o.email)==null?void 0:N.message})]}),e.jsxs("div",{className:"flex flex-col space-y-1 text-sm",children:[e.jsx("label",{htmlFor:"",className:"font-iowan text-[16px] font-[700]",children:"Password"}),e.jsxs("div",{className:"flex h-[44px] items-center rounded-sm border-[2px] border-[#1f1d1a] bg-transparent px-2 py-1 text-[#1f1d1a]",children:[e.jsx("input",{className:"focus-visible::outline-none w-[95%] border-none bg-transparent p-1 text-sm font-normal text-[#1f1d1a] shadow-[0] outline-none focus:border-none focus:shadow-none focus:outline-none",type:c?"text":"password",placeholder:"********",...b("password"),style:{boxShadow:"0 0 transparent"}}),e.jsx("span",{className:"w-[5%] cursor-pointer pr-6",onClick:()=>_(!c),children:c?e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.99998 3.33337C13.5326 3.33335 16.9489 5.50937 19.0735 9.61715L19.2715 10L19.0735 10.3828C16.9489 14.4906 13.5326 16.6667 10 16.6667C6.46737 16.6667 3.05113 14.4907 0.926472 10.3829L0.728455 10.0001L0.926472 9.61724C3.05113 5.50946 6.46736 3.3334 9.99998 3.33337ZM7.08333 10C7.08333 8.38921 8.38917 7.08337 10 7.08337C11.6108 7.08337 12.9167 8.38921 12.9167 10C12.9167 11.6109 11.6108 12.9167 10 12.9167C8.38917 12.9167 7.08333 11.6109 7.08333 10Z",fill:"#A8A8A8"})}):e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",children:[e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.28033 2.21967C2.98744 1.92678 2.51256 1.92678 2.21967 2.21967C1.92678 2.51256 1.92678 2.98744 2.21967 3.28033L5.38733 6.44799C4.04329 7.533 2.8302 8.97021 1.81768 10.7471C1.37472 11.5245 1.37667 12.4782 1.81881 13.2539C3.74678 16.6364 6.40456 18.789 9.29444 19.6169C12.0009 20.3923 14.8469 19.9857 17.3701 18.4308L20.7197 21.7803C21.0126 22.0732 21.4874 22.0732 21.7803 21.7803C22.0732 21.4874 22.0732 21.0126 21.7803 20.7197L3.28033 2.21967ZM14.2475 15.3082L13.1559 14.2166C12.81 14.3975 12.4167 14.4995 11.9991 14.4995C10.6184 14.4995 9.49911 13.3802 9.49911 11.9995C9.49911 11.5819 9.60116 11.1886 9.78207 10.8427L8.69048 9.75114C8.25449 10.3917 7.99911 11.1662 7.99911 11.9995C7.99911 14.2087 9.78998 15.9995 11.9991 15.9995C12.8324 15.9995 13.6069 15.7441 14.2475 15.3082Z",fill:"#A8A8A8"}),e.jsx("path",{d:"M19.7234 16.5416C20.5189 15.7335 21.2556 14.7869 21.9145 13.7052C22.5512 12.66 22.5512 11.34 21.9145 10.2948C19.3961 6.16075 15.7432 4.00003 11.9999 4C10.6454 3.99999 9.30281 4.28286 8.02148 4.83974L19.7234 16.5416Z",fill:"#A8A8A8"})]})})]}),e.jsx("p",{className:"text-xs italic text-red-500",children:(y=o==null?void 0:o.password)==null?void 0:y.message}),e.jsxs("div",{className:"flex justify-between mt-12 text-sm",children:[e.jsxs("div",{className:"flex items-center text-[#1f1d1a]",children:[e.jsx("input",{className:"mr-2 h-[15px] w-[15px] border-[2px] bg-brown-main-bg",type:"checkbox",id:"rememberMe",checked:g,onChange:t=>H(t.target.checked)}),e.jsx("label",{htmlFor:"rememberMe",className:"text-[14px] font-[600] text-[#1f1d1a] sm:text-[16px]",children:"Remember me"})]}),e.jsx(Q,{to:"/member/forgot",className:"text-[14px] font-[600] text-[#1F1D1A] underline sm:text-[16px]",children:"Forgot password"})]})]}),e.jsx(xe,{type:"submit",className:"my-12 mb-6 flex h-[44px] w-full items-center justify-center rounded-sm bg-[#1f1d1a] py-2 tracking-wide text-white outline-none focus:outline-none",loading:m,disabled:m,children:e.jsx("span",{className:"capitalize",children:m?"Logging In":"Log In"})})]}),e.jsxs("div",{className:"flex flex-row items-center mt-4 w-full",children:[e.jsx("hr",{className:"w-full border-[1px] border-[#1f1d1a]"}),e.jsxs("span",{className:"mx-2 w-full max-w-[100px] whitespace-nowrap  text-[16px] font-[500]",children:[" ","Or login with"]}),e.jsx("hr",{className:"w-full border-[1px] border-[#1f1d1a]"})]}),e.jsxs("div",{className:"oauth mt-2 flex w-full flex-col gap-[1.5rem] text-[#344054] ",children:[e.jsxs("button",{onClick:()=>j("google"),className:"my-2 flex h-[2.75rem] min-w-[70%] cursor-pointer items-center justify-center gap-3 rounded-sm border-2  border-[#1f1d1a] px-4",children:[e.jsx("img",{src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAALpSURBVHgBtVbNTxNBFH8zuy3QoN0YJMEQs8QQP05LAsbEg4uRxMSD4AeaeLB6xEPhpIkm4MF4IsG/oODF4Edajgahy8UDxbAcjDEc2IORCIlUhVK6u/OcKbVpaZdWxN+lkzd9v9+b9968WQK7YEnXlYPSxm0GqCMQjZtUYScASUSw+NJwGU40GXOGFwfxIg7IqX6KGEYABSqCWBmKPc2TCbOiwEpXhwaMRAFQhb+Ei/i4aXpuyFNAkBMG8eqiLoVIG2N2Z5NhWiUCyxfPqLLtznuTYxKQWIRk869wT60SuYD8ZyHZrGzk3NGkCP3r6Cy0GGYyH5CuqRL1DXKhkBd5/gRrfa0h+7MSKQ0aRhqnEwOwC1YvtOuO41jlyPMCzpRvKT3boKbeNRdsYOzw1FwP/COoPSnriKjWdKxCsO8j0GAmm0/HdQZgHyADhXM8FdtqnPzArUVIv280gsOWVc5BH9xUoWrUJkWRi7pBiAQufRmF4fIukt+N8Hh0qAYsNUoBSztHRtmCfQASVCn8Z1BCiLXT6DJbg32CzPhFKpwXv9AHkY3jOoA5Uc6B53+Mn90o2SBi0mKo2MS5RZvyVVwYFp0g3P95GpbdQNJJuy3mnVgSqsT5JxuRnQKMQYj6uhyDr5Pjm8fg3o+zsMwCQlqR66RIteT6082S6LNw7BlJ/EpX22ufp1r1DEiF2yeOXDupfH396W0lcopMZKCoG/llNYzB4LN8+tvHr8zz3JYUl48MPkHJ0OyNN2NFxJFuZb1W7pfSp8J1K3cV6jQU+aHk1+IP/At5Ae3FTVWm9ny5e5FT4uMasi8WL7RKcs+nALUboO5bGKStozl2GJl+VD+w7VaAjpfXNRTHxb09OP61Hqj53m3GH9a35cUL/5DofWU6zNfGI7RgD9g6FI1hxu4stJV99LVotyJnaJjXZAiqAPI6Aa/Thx118hTIC/G6UMjolJLL2Y+AXBMgr4coPmc2CMVYojc648XxG0ZrPRAMMnAhAAAAAElFTkSuQmCC",className:"h-[18px] w-[18px]"}),e.jsx("span",{className:"text-[1rem] font-[600]",children:"Sign in With Google"})]}),e.jsxs("button",{onClick:()=>j("microsoft"),className:"oauth hidden h-[2.75rem] items-center  justify-center gap-2 border-2 border-[#1f1d1a]",children:[e.jsx("img",{src:"https://companieslogo.com/img/orig/MSFT-a203b22d.png?t=**********",className:"h-[1.125rem] w-[1.125rem]"}),e.jsx("span",{className:"text-[1rem] font-[600]",children:"Sign in With Microsoft"})]})]})]}),e.jsxs("div",{className:"flex items-center pb-10 mt-7 text-sm md:mt-0 lg:text-base",children:[e.jsxs("span",{className:"mr-1 font-medium text-[#1f1d1a]",children:["Don't have account?"," "]})," ",e.jsx(Q,{to:"/member/sign-up",className:"font-bold text-[#1f1d1a] underline",children:"Sign up here"})]})]})]}),e.jsx(fe,{open:J,setOpen:u,accounts:z,loading:m,login:A,data:W})]})};export{Ke as default};
