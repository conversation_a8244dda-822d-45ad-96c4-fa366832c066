import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{R as D,r as n}from"./vendor-4cdf2bd1.js";import{u as V}from"./react-hook-form-9f4fcfa9.js";import{o as ee}from"./yup-c41d85d2.js";import{c as te,a as se}from"./yup-342a5df4.js";import{M as ae,G as W,I as B,t as K,s as M}from"./index-46de5032.js";import oe from"./ModalPrompt-3db7e669.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";let y=new ae;const ke=()=>{var G;const I=te({email:se().email().required()}).required(),{dispatch:d}=D.useContext(W),[f,F]=n.useState(""),[b,T]=D.useState({}),[R,$]=n.useState(!1),[P,a]=n.useState(!1),[g,m]=n.useState(!1),[Z,c]=n.useState(!1),[A,j]=n.useState("");n.useState("");const[u,p]=n.useState(!1),[h,N]=n.useState("Profile"),[s,O]=n.useState({}),{dispatch:J}=D.useContext(W),{register:_,handleSubmit:v,setError:E,setValue:U,formState:{errors:L}}=V({resolver:ee(I)});async function H(){try{const t=await y.getProfile();O(t),U("email",t==null?void 0:t.email),U("first_name",t==null?void 0:t.first_name),U("last_name",t==null?void 0:t.last_name),F(t==null?void 0:t.email),j(t==null?void 0:t.photo)}catch(t){console.log("Error",t),K(d,t.response.data.message?t.response.data.message:t.message)}}const C=async t=>{var S,w;O(t);try{if(p(!0),b&&b.photo&&((S=b.photo)!=null&&S.file)){let i=new FormData;i.append("file",(w=b.photo)==null?void 0:w.file);let l=await y.uploadImage(i);console.log("uploadResult"),console.log(l),t.photo=l.url,M(d,"Profile Photo Updated",1e3)}const o=await y.updateProfile({first_name:t.first_name||(s==null?void 0:s.first_name),last_name:t.last_name||(s==null?void 0:s.last_name),photo:t.photo||A});if(!o.error)M(d,"Profile Updated",4e3),x();else{if(o.validation){const i=Object.keys(o.validation);for(let l=0;l<i.length;l++){const r=i[l];E(r,{type:"manual",message:o.validation[r]})}}x()}if(f!==t.email){const i=await y.updateEmail(t.email);if(!i.error)M(d,"Email Updated",1e3);else if(i.validation){const l=Object.keys(i.validation);for(let r=0;r<l.length;r++){const k=l[r];E(k,{type:"manual",message:i.validation[k]})}}x()}if(t.password.length>0){const i=await y.updatePassword(t.password);if(!i.error)M(d,"Password Updated",2e3);else if(i.validation){const l=Object.keys(i.validation);for(let r=0;r<l.length;r++){const k=l[r];E(k,{type:"manual",message:i.validation[k]})}}}await H(),p(!1)}catch(o){p(!1),console.log("Error",o),E("email",{type:"manual",message:o.response.data.message?o.response.data.message:o.message}),K(d,o.response.data.message?o.response.data.message:o.message)}};D.useEffect(()=>{J({type:"SETPATH",payload:{path:"profile"}}),H()},[]);const Q=()=>{a(!0)},X=()=>{m(!0)},q=()=>{c(!0)},x=()=>{$(!1),a(!1),m(!1),c(!1)},Y=async()=>{try{p(!0);const t=await y.updateProfile({first_name:s==null?void 0:s.first_name,last_name:s==null?void 0:s.last_name,photo:""});if(!t.error)M(d,"Profile Picture Deleted",1e3);else if(t.validation){const S=Object.keys(t.validation);for(let w=0;w<S.length;w++){const o=S[w];E(o,{type:"manual",message:t.validation[o]})}}await H(),p(!1),x()}catch(t){p(!1),console.log("Error",t)}};return e.jsxs("div",{className:"mt-6 w-10/12 rounded-md border",children:[e.jsx("div",{className:"flex items-center border-b border-b-[#E0E0E0] px-8 py-3 text-[#8D8D8D]",children:e.jsxs("div",{className:"flex items-center space-x-6",children:[e.jsx("div",{className:`cursor-pointer rounded-lg px-3 py-1 ${h==="Profile"?"bg-[#f4f4f4] text-[#1f1d1a]":""} `,onClick:()=>N("Profile"),children:"Profile"}),e.jsx("div",{className:`cursor-pointer rounded-lg px-3 py-1 ${h==="Security"?"bg-[#f4f4f4] text-[#1f1d1a]":""} `,onClick:()=>N("Security"),children:"Security"})]})}),e.jsxs("main",{children:[h==="Profile"&&e.jsx("div",{className:"rounded bg-brown-main-bg",children:e.jsx("form",{onSubmit:v(C),children:e.jsx("div",{className:"mx-10 max-w-lg pt-10",children:e.jsxs("div",{className:"mb-6 flex items-center justify-between text-left",children:[e.jsxs("div",{className:"flex items-center gap-x-2",children:[e.jsx("p",{className:"mr-28 text-base font-medium text-gray-600",children:"Email"}),e.jsx("p",{className:"text-base font-medium text-gray-900",children:f})]}),e.jsx("p",{className:"cursor-pointer text-base font-semibold text-indigo-600",onClick:q,children:"Edit"})]})})})}),h==="Security"&&e.jsx("div",{className:"rounded bg-brown-main-bg px-10 py-6",children:e.jsx("form",{onSubmit:v(C),className:"max-w-lg",children:e.jsxs("div",{className:"",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold capitalize text-gray-700",children:"Password"}),e.jsx("input",{..._("password"),name:"password",className:"focus:shadow-outline w-[100px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 leading-tight text-[#1d1f1a] shadow   focus:outline-none sm:w-[180px]",id:"password",type:"password",placeholder:"******************"}),e.jsx("p",{className:"text-xs italic text-red-500",children:(G=L.password)==null?void 0:G.message})]}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsx(B,{className:"focus:shadow-outline rounded bg-indigo-600 px-4 py-2 font-bold text-white hover:bg-indigo-600 focus:outline-none disabled:cursor-not-allowed",type:"submit",loading:u,disabled:u,children:"Update"})})]})})}),R&&e.jsx(oe,{actionHandler:Y,closeModalFunction:x,title:"Are you sure ? ",message:"Are you sure you want to delete profile picture ? ",acceptText:"DELETE",rejectText:"CANCEL"}),P&&e.jsx(z,{title:"Edit information",label:"First Name",buttonName:"Save and close",isOpen:Q,onClose:x,handleSubmit:v,onSubmit:C,register:_,id:"first_name",submitLoading:u,errors:L}),g&&e.jsx(z,{title:"Edit information",label:"Last Name",buttonName:"Save and close",isOpen:X,onClose:x,handleSubmit:v,onSubmit:C,register:_,id:"last_name",submitLoading:u,errors:L}),Z&&e.jsx(z,{title:"Change Email",label:"Email",buttonName:"Submit",isOpen:q,onClose:x,handleSubmit:v,onSubmit:C,register:_,id:"email",submitLoading:u,errors:L,defaultValues:s})]})]})},z=I=>{var h,N;const{title:d,label:f,buttonName:F,isOpen:b,onClose:T,handleSubmit:R,onSubmit:$,register:P,id:a,submitLoading:g,errors:m,defaultValues:Z}=I,[c,A]=n.useState(!1),[j,u]=n.useState({email:""}),p=s=>O=>{s==="email"&&u({...j,[s]:O.target.value})};return e.jsx("div",{className:"fixed inset-0 z-10 overflow-y-auto",children:e.jsx("div",{className:`fixed inset-0 z-10 overflow-y-auto ${b?"block":"hidden"} `,children:e.jsxs("div",{className:"flex min-h-screen items-end justify-center px-4 pb-20 pt-4 text-center sm:block sm:p-0",children:[e.jsx("div",{className:"fixed inset-0 transition-opacity",children:e.jsx("div",{className:"absolute inset-0 bg-gray-500 opacity-75"})}),e.jsx("span",{className:"hidden sm:inline-block sm:h-screen sm:align-middle","aria-hidden":"true",children:"​"}),e.jsxs("div",{className:"inline-block transform overflow-hidden rounded-lg bg-brown-main-bg px-4 pb-4 pt-5 text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-md sm:p-6 sm:align-middle",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"text-lg font-semibold leading-6 text-gray-900",children:d}),e.jsx("button",{className:"text-gray-500 hover:text-gray-700 focus:outline-none",onClick:T,children:e.jsx("svg",{className:"h-6 w-6",fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M6 18L18 6M6 6l12 12"})})})]}),e.jsxs("form",{onSubmit:R($),className:"max-w-lg",children:[c===!0&&e.jsxs("div",{className:"mt-3 flex",children:[e.jsx("div",{className:"mr-2",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.0003 1.66663C5.39795 1.66663 1.66699 5.39759 1.66699 9.99996C1.66699 14.6023 5.39795 18.3333 10.0003 18.3333C14.6027 18.3333 18.3337 14.6023 18.3337 9.99996C18.3337 5.39759 14.6027 1.66663 10.0003 1.66663ZM8.33366 9.16663C8.33366 8.82145 8.61348 8.54163 8.95866 8.54163H10.0003C10.3455 8.54163 10.6253 8.82145 10.6253 9.16663L10.6253 13.5416C10.6253 13.8868 10.3455 14.1666 10.0003 14.1666C9.65515 14.1666 9.37533 13.8868 9.37533 13.5416L9.37532 9.79163H8.95866C8.61348 9.79163 8.33366 9.5118 8.33366 9.16663ZM10.0003 6.04163C9.65515 6.04163 9.37533 6.32145 9.37533 6.66663C9.37533 7.0118 9.65515 7.29163 10.0003 7.29163C10.3455 7.29163 10.6253 7.0118 10.6253 6.66663C10.6253 6.32145 10.3455 6.04163 10.0003 6.04163Z",fill:"#4F46E5"})})}),e.jsxs("div",{children:[e.jsx("p",{className:"mb-1 text-sm font-medium text-gray-600",children:"We've send an email to:"}),e.jsx("p",{className:"mb-2 text-sm font-semibold text-gray-900",children:j==null?void 0:j.email}),e.jsx("p",{className:"mb-2 text-sm font-medium text-gray-600",children:"In order to complete the email update click the confirmation link."}),e.jsx("p",{className:"mb-2 text-sm font-medium text-gray-600",children:"(the link expires in 24 hours)"})]})]}),c===!1&&(a==="first_name"||a==="last_name")&&e.jsxs("div",{className:"mt-3",children:[e.jsx("label",{htmlFor:"firstName",className:"mb-1 block text-sm font-medium text-gray-700",children:f}),e.jsx("input",{className:"focus:shadow-outline w-[100px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 leading-tight text-[#1d1f1a] shadow   focus:outline-none sm:w-[180px]",id:a,type:"text",placeholder:`Enter ${f} `,name:a,...P(a)}),e.jsx("p",{className:"text-xs italic text-red-500",children:(h=m==null?void 0:m.id)==null?void 0:h.message})]}),c===!1&&a==="email"&&e.jsxs("div",{className:"mt-3",children:[e.jsx("label",{htmlFor:"firstName",className:"mb-1 block text-sm font-medium text-gray-700",children:f}),e.jsx("input",{className:"focus:shadow-outline w-[100px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 leading-tight text-[#1d1f1a] shadow   focus:outline-none sm:w-[180px]",id:a,type:"text",placeholder:`Enter ${f}`,name:a,...P(a),onChange:p("email")}),e.jsx("p",{className:"text-xs italic text-red-500",children:(N=m==null?void 0:m.id)==null?void 0:N.message})]}),e.jsxs("div",{className:"mt-4 flex justify-between",children:[e.jsx("button",{className:"mr-2 w-full rounded-md border border-black/60 px-4 py-2 text-gray-700 ",onClick:T,children:"Cancel"}),(a==="first_name"||a==="last_name"||c===!0)&&e.jsx(B,{className:"focus:shadow-outline w-full rounded-md bg-indigo-500 px-4 py-2 font-bold text-white hover:bg-indigo-600 focus:outline-none disabled:cursor-not-allowed",type:"submit",loading:g,disabled:g,children:F}),a==="email"&&!c&&e.jsx(B,{className:"focus:shadow-outline w-full rounded-md bg-indigo-500 px-4 py-2 font-bold text-white hover:bg-indigo-600 focus:outline-none disabled:cursor-not-allowed",type:"submit",loading:g,disabled:g,onClick:()=>A(!0),children:"Submit"})]})]})]})]})})})};export{z as EditInfoModal,ke as default};
