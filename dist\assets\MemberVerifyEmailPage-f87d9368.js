import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{A as x,G as h,M as g,x as y}from"./index-46de5032.js";import{u as b}from"./useLocalStorage-53cfe2d8.js";import{h as k,r as a,b as v,L as j}from"./vendor-4cdf2bd1.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";function O(){const[l]=k(),[s,n]=a.useState(!0),[r,m]=a.useState(""),{dispatch:c}=a.useContext(x),{dispatch:d}=a.useContext(h),u=v(),{setLocalStorage:o}=b(["token","user","role","step"]);async function f(){n(!0);try{const t=await new g().callRawAPI(`/v2/api/lambda/verify-email?token=${l.get("token")}`);if(m(""),c({type:"LOGIN",payload:t}),!(t!=null&&t.error)){o("verified",!0),o("token",t==null?void 0:t.token);const i=await y(d,c,"user",t==null?void 0:t.user_id,{step:2},!1);i!=null&&i.error||o("step",2),u("/member/get-started",{state:{first_login:!0}})}}catch(p){m(p.message)}n(!1)}return a.useEffect(()=>{f()},[]),e.jsxs("div",{className:"flex min-h-screen w-full justify-center",children:[e.jsxs("div",{className:"mx-auto mt-32 max-w-lg text-center",children:[" ",e.jsx("h1",{className:"mb-3 text-3xl font-semibold",children:s?"Verifying...":r?e.jsx("p",{className:"text-red-500 empty:hidden",children:r}):"Account verification complete!"})," ",e.jsxs("p",{children:[s||r?"":"Your account has been verified, you can now "," ",e.jsxs(j,{to:"/member/login",className:`font-semibold text-primary-black underline ${s||r?"hidden":""}`,children:[" ","Login"]})]})," "]})," "]})}export{O as default};
