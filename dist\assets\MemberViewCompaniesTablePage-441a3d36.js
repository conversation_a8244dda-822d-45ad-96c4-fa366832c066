import{j as s}from"./@nextui-org/listbox-0f38ca19.js";import{R as m,i as n}from"./vendor-4cdf2bd1.js";import"./yup-342a5df4.js";import{M as x,G as t,t as p,f as h}from"./index-46de5032.js";import"./MkdInput-a0090fba.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";let l=new x;const H=()=>{m.useContext(t);const{dispatch:o}=m.useContext(t),[e,c]=m.useState({}),[d,i]=m.useState(!0),r=n();return m.useEffect(function(){(async function(){try{i(!0),l.setTable("companies");const a=await l.callRestAPI({id:Number(r==null?void 0:r.id),join:""},"GET");a.error||(c(a.model),i(!1))}catch(a){i(!1),console.log("error",a),p(o,a.message)}})()},[]),s.jsx("div",{className:" mx-auto rounded  p-5 shadow-md",children:d?s.jsx(h,{}):s.jsxs(s.Fragment,{children:[s.jsx("h4",{className:"text-[16px] font-medium md:text-xl",children:"View Companies"}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Logo"}),s.jsx("div",{className:"flex-1",children:s.jsx("img",{src:e==null?void 0:e.logo,alt:"Logo",className:"h-[100px] w-[150px] rounded"})})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Email"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.email})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Address"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.address})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Name"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.name})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Phone"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.phone})]})})]})})};export{H as default};
