import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r,u as a}from"./vendor-4cdf2bd1.js";import{_ as o}from"./qr-scanner-cf010ec4.js";import{T as t}from"./index-0d5645ff.js";import{L as i,S as m}from"./index-46de5032.js";const b=r.lazy(()=>o(()=>import("./SubMenus-6f7e6175.js"),["assets/SubMenus-6f7e6175.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-46de5032.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-d88d3663.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/react-spinners-b860a5a3.js"])),n=r.lazy(()=>o(()=>import("./MemberHeader-09052c94.js"),["assets/MemberHeader-09052c94.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-46de5032.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-d88d3663.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/index-59df21c1.js","assets/index.esm-54e24cf9.js","assets/react-icons-36ae72b7.js","assets/useRecentEngagements-74872108.js","assets/react-responsive-19663c38.js","assets/@popperjs/core-f3391c26.js","assets/index-0d5645ff.js"])),p=({children:s})=>{const{pathname:l}=a();return l.includes("updates")&&l.includes("preview")?s:e.jsxs("div",{id:"startup_wrapper",className:"block h-full max-h-full min-h-full w-full max-w-full bg-brown-main-bg md:flex",children:[e.jsx(n,{}),e.jsxs("div",{className:"grid h-full w-full grid-cols-1 grid-rows-[auto_1fr]",children:[e.jsx(i,{children:e.jsx(t,{})}),e.jsx(r.Suspense,{fallback:e.jsx("div",{className:"flex h-screen w-full items-center justify-center",children:e.jsx(m,{size:40,color:"#1f1d1a"})}),children:e.jsx("div",{className:"scrollbar-hide w-full overflow-y-auto overflow-x-hidden sm:pl-[80px] lg:pl-0",children:s})})]})]})},u=r.memo(p),j=Object.freeze(Object.defineProperty({__proto__:null,default:u},Symbol.toStringTag,{value:"Module"}));export{j as M,b as S};
