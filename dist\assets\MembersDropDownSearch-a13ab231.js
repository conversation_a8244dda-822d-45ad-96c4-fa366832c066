import{j as p}from"./@nextui-org/listbox-0f38ca19.js";import{S as y}from"./index-0087bc92.js";import{u as D}from"./index-46de5032.js";import{u as M}from"./useCompanyMember-f698e8a0.js";import{r as c}from"./vendor-4cdf2bd1.js";import"./@nextui-org/theme-345a09ed.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";function A({onSelect:u,value:d,refreshRef:f,clearRef:h}){var i,n;const{profile:t}=D(),{companyMember:{myMembers:o},loading:e,getMyCompanyMembers:l}=M({filter:[`company_id,eq,${(n=(i=t==null?void 0:t.companies)==null?void 0:i[0])==null?void 0:n.id}`]}),s=c.useRef(null),x=async()=>{var a,r;await l({filter:[`company_id,eq,${(r=(a=t==null?void 0:t.companies)==null?void 0:a[0])==null?void 0:r.id}`]})};return c.useEffect(()=>{t!=null&&t.id&&x()},[t==null?void 0:t.id]),p.jsx("div",{ref:s,children:p.jsx(y,{className:"",value:d,useExternalData:!0,display:"name",label:"Members:",onSelect:u,showSearchIcon:!0,uniqueKey:"member_id",placeholder:"Search Members",inputClassName:"!h-[2.5rem] !max-h-[2.5rem] !min-h-[2.5rem]",externalDataOptions:o==null?void 0:o.map(a=>{var r,m;return{...a,name:`${(r=a==null?void 0:a.user)==null?void 0:r.first_name} ${(m=a==null?void 0:a.user)==null?void 0:m.last_name}`}}),externalDataLoading:e==null?void 0:e.myMembers,refreshRef:f,clearRef:h,containerRef:s})})}export{A as default};
