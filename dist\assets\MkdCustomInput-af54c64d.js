import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{R as y,r as I}from"./vendor-4cdf2bd1.js";import{f as F,o as M}from"./index-46de5032.js";import{_ as z}from"./react-toggle-6478c5c4.js";const k=y.forwardRef(({label:c,required:v=!1,type:n="text",name:t="",disabled:a=!1,loading:N=!1,mapping:m=null,options:f=[],placeholder:u,className:d,errors:o=null,...l},C)=>{var g,x,p,b,h,w,$,j;const r=I.useId();return e.jsx(e.Fragment,{children:e.jsxs("div",{className:"relative grow",children:[["radio","checkbox","color","toggle"].includes(n)?null:e.jsx(e.Fragment,{children:c&&e.jsxs("label",{className:"mb-2 block font-iowan text-[1rem] font-[700] capitalize text-gray-700 ",htmlFor:r,children:[c,v&&e.jsx("sup",{className:"z-[99999] text-[.825rem] text-red-600",children:"*"})]})}),N?e.jsx(F,{count:1,counts:[2],className:"!h-[3rem] !max-h-[3rem] !min-h-[3rem]  w-full appearance-none !gap-0 overflow-hidden rounded-sm border-[.125rem] border-[#1f1d1a] bg-brown-main-bg !p-0 px-3 py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none"}):n==="textarea"?e.jsx(e.Fragment,{children:e.jsx("textarea",{className:`focus:shadow-outline  w-full  appearance-none rounded-sm border-[.125rem] border-[#1f1d1a] bg-brown-main-bg px-3 py-2 text-sm font-normal leading-tight  text-[#1f1d1a] shadow focus:outline-none ${d} ${o&&o[t]&&((g=o[t])!=null&&g.message)?"!border-red-500":"border-soft-200"} ${a?"appearance-none bg-gray-200":""}`,disabled:a,id:r,name:t,placeholder:u,...l})}):["radio","checkbox","color","toggle"].includes(n)?e.jsxs("div",{className:"flex h-[1.875rem] items-center gap-2 pb-1 pt-3",children:[["toggle"].includes(n)?e.jsx(z,{className:`toggle_class w-full appearance-none rounded-sm border-[.125rem] border-[#1f1d1a] bg-brown-main-bg px-3 py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none ${d} ${o&&o[t]&&((x=o[t])!=null&&x.message)?"!border-red-500":"border-soft-200"}`,disabled:a,icons:!1,name:t,...l}):e.jsx("input",{autoComplete:"off","aria-autocomplete":"off",ref:C,disabled:a,type:n,id:r,name:t,placeholder:u,...l,className:`focus:shadow-outline bg-text-black h-4 w-4 cursor-pointer appearance-none border-[.125rem] px-2 py-2  text-[.8125rem] text-sm font-normal leading-tight text-black accent-black  shadow focus:outline-none focus:ring-0 sm:!text-base ${d} ${o&&o[t]&&((p=o[t])!=null&&p.message)?"!border-red-500":"border-soft-200"} ${n==="color"?"min-h-[3.125rem] min-w-[6.25rem]":""} ${a?"appearance-none bg-gray-200":""} ${["radio"].includes(n)?"rounded-full ":"rounded-md"}`}),e.jsx("label",{className:"mb-2  block h-full  cursor-pointer font-iowan text-[1rem] font-bold capitalize text-gray-700 ",htmlFor:r,children:c})]}):["dropdown","select"].includes(n)?e.jsxs("select",{type:n,id:r,name:t,disabled:a,placeholder:u,className:`focus:shadow-outline h-[3rem] w-full appearance-none rounded-sm border-[.125rem] border-[#1f1d1a] bg-brown-main-bg p-[.625rem] px-3 py-2 text-sm font-normal  leading-tight  text-[#1f1d1a]  shadow focus:outline-none  focus:ring-0  ${d} ${o&&o[t]&&((b=o[t])!=null&&b.message)?"!border-red-500":"border-soft-200"} ${a?"appearance-none bg-gray-200":""}`,...l,children:[e.jsx("option",{}),f.map((s,i)=>e.jsx("option",{value:s,children:s},i+1))]}):n==="mapping"?e.jsx(e.Fragment,{children:m?e.jsxs("select",{id:r,name:t,disabled:a,placeholder:u,className:`focus:shadow-outline  h-[3rem]  w-full appearance-none rounded-sm border-[.125rem] border-[#1f1d1a] bg-brown-main-bg p-[.625rem] px-3 py-2 text-sm font-normal leading-tight  text-[#1f1d1a] shadow focus:outline-none focus:ring-0  ${d} ${o&&o[t]&&((h=o[t])!=null&&h.message)?"!border-red-500":"border-soft-200"} ${a?"appearance-none bg-gray-200":""}`,...l,children:[e.jsx("option",{}),f.map((s,i)=>e.jsx("option",{value:s,children:m[s]},i+1))]}):"Please Pass the mapping e.g {key:value}"}):["number","decimal"].includes(n)?e.jsx("input",{autoComplete:"off","aria-autocomplete":"off",className:`focus:shadow-outline  h-[3rem]  w-full appearance-none rounded-sm border-[.125rem] border-[#1f1d1a] bg-brown-main-bg p-[.625rem] px-3 py-2 text-sm font-normal leading-tight text-[#1f1d1a] shadow focus:outline-none focus:ring-0 ${d} ${o&&o[t]&&((w=o[t])!=null&&w.message)?"!border-red-500":"border-soft-200"} ${a?"appearance-none bg-gray-200":""}`,type:n,id:r,name:t,disabled:a,placeholder:u,step:"0.01",min:"0.00",onInput:s=>{const i=s.target.value;i&&!/^\d+(\.\d{0,2})?$/.test(i)&&(s.target.value=i.slice(0,-1))},...l}):e.jsx("input",{autoComplete:"off","aria-autocomplete":"off",type:n,id:r,name:t,disabled:a,placeholder:u,className:`focus:shadow-outline  h-[3rem]  w-full appearance-none rounded-sm border-[.125rem] border-[#1f1d1a] bg-brown-main-bg p-[.625rem] px-3 py-2 text-sm font-normal leading-tight text-[#1f1d1a] shadow focus:outline-none focus:ring-0 ${d} ${o&&o[t]&&(($=o[t])!=null&&$.message)?"!border-red-500":"border-soft-200"} ${a?"appearance-none bg-gray-200":""}`,...l}),o&&o[t]&&e.jsx("p",{className:"text-field-error absolute inset-x-0 top-[90%] m-auto mt-2 text-[.8rem] italic text-red-500",children:M((j=o[t])==null?void 0:j.message,{casetype:"capitalize",separator:" "})})]})})});k.displayName="MkdCustomInput";const S=k;export{S as M};
