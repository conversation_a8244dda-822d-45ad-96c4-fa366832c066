import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as d}from"./vendor-4cdf2bd1.js";import{o as j}from"./index-46de5032.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";let n=null;const M=({type:o="text",label:u,className:c,placeholder:s="Search",options:x=[],disabled:i=!1,setValue:f,value:l,onReady:h,timer:g=1e3})=>{const a=d.useId(),[m,b]=d.useState("");function p(t){const r=t.target.value;f(r),b(r),n&&clearTimeout(n),n=setTimeout(()=>{r.length&&h(r)},g)}return e.jsx(e.Fragment,{children:e.jsxs("form",{children:[e.jsx("label",{className:"mb-2 block cursor-pointer text-sm font-semibold text-[#1f1d1a]",htmlFor:a,children:j(u,{casetype:"capitalize",separator:"space"})}),o==="dropdown"||o==="select"?e.jsxs("select",{type:o,id:a,disabled:i,placeholder:s,onChange:t=>p(t),value:l||m,className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${c}`,children:[e.jsx("option",{}),x.map((t,r)=>e.jsx("option",{value:t,children:t},r+1))]}):e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:e.jsx("svg",{className:"h-4 w-4 text-gray-500 dark:text-gray-400","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 20 20",children:e.jsx("path",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"})})}),e.jsx("input",{type:o,id:a,disabled:i,placeholder:s,onChange:t=>p(t),value:l||m,className:"block w-full rounded-lg border border-blue-600  bg-brown-main-bg p-4 pl-10 text-sm text-[#1f1d1a] placeholder-[#1f1d1a] focus:border-blue-500 focus:ring-blue-500 dark:text-gray-400 dark:placeholder-gray-400"})]})]})})};export{M as default};
