import{j as s}from"./@nextui-org/listbox-0f38ca19.js";import{r as i}from"./vendor-4cdf2bd1.js";import{_ as d}from"./qr-scanner-cf010ec4.js";import F from"./MkdPopover-dd21a7e9.js";import{h as S}from"./moment-a9aaa855.js";import"./@nextui-org/theme-345a09ed.js";import"./react-tooltip-7630c8e3.js";import"./@mantine/core-691d33c8.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";const I=i.lazy(()=>d(()=>import("./CurrencyCell-b6517447.js"),["assets/CurrencyCell-b6517447.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js"])),y=i.lazy(()=>d(()=>import("./DefaultCell-8eee0f91.js"),["assets/DefaultCell-8eee0f91.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js"])),J=i.lazy(()=>d(()=>import("./EditableStatusCell-6b4f4203.js"),["assets/EditableStatusCell-6b4f4203.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js"])),q=i.lazy(()=>d(()=>import("./EditableTextCell-43f41b7d.js"),["assets/EditableTextCell-43f41b7d.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js"])),G=i.lazy(()=>d(()=>import("./FileCell-31477a7c.js"),["assets/FileCell-31477a7c.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js"])),K=i.lazy(()=>d(()=>import("./ImageCell-180814e7.js"),["assets/ImageCell-180814e7.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-46de5032.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-d88d3663.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/index-af54e68d.js","assets/index.esm-6fcccbfe.js","assets/react-icons-36ae72b7.js"])),Q=i.lazy(()=>d(()=>import("./JoinCell-19a5e102.js"),["assets/JoinCell-19a5e102.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js"])),U=i.lazy(()=>d(()=>import("./ListCell-266b1b5e.js"),["assets/ListCell-266b1b5e.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-d0de8b06.js","assets/qr-scanner-cf010ec4.js"])),W=i.lazy(()=>d(()=>import("./NoteCell-66e7aa39.js"),["assets/NoteCell-66e7aa39.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-46de5032.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-d88d3663.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css"])),$=i.lazy(()=>d(()=>import("./StatusCell-bb2a272a.js"),["assets/StatusCell-bb2a272a.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/MkdPopover-dd21a7e9.js","assets/react-tooltip-7630c8e3.js","assets/@mantine/core-691d33c8.js","assets/@uppy/dashboard-51133bb7.js","assets/@fullcalendar/core-085b11ae.js","assets/core-b9802b0d.css","assets/@uppy/core-a4ba4b97.js","assets/@uppy/aws-s3-a6b02742.js","assets/@craftjs/core-a2cdaeb4.js","assets/@uppy/compressor-4bcbc734.js","assets/MkdListTableV2-e3b0c442.css"])),H=i.lazy(()=>d(()=>import("./TruncatedCell-f27359b3.js"),["assets/TruncatedCell-f27359b3.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-46de5032.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-d88d3663.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css"])),Z={damaged:{text:"damaged",details:"The item is damaged and needs attention."},"place order":{text:"Place Order",details:"The order has been placed and is awaiting processing."},verified:{text:"Verified",details:"The address has been verified against the database."},unverified:{text:"unverified",details:"The address validation failed during pre-validation."},warning:{icon:s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:s.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 13C11.45 13 11 12.55 11 12V8C11 7.45 11.45 7 12 7C12.55 7 13 7.45 13 8V12C13 12.55 12.55 13 12 13ZM13 17H11V15H13V17Z",fill:"#F59E0B"})}),text:"warning",details:"The address validation succeeded, but it should be double-checked."},error:{icon:s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:s.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.4902 2.84406C11.1661 1.69 12.8343 1.69 13.5103 2.84406L22.0156 17.3654C22.699 18.5321 21.8576 19.9999 20.5056 19.9999H3.49483C2.14281 19.9999 1.30147 18.5321 1.98479 17.3654L10.4902 2.84406ZM12 9C12.4142 9 12.75 9.33579 12.75 9.75V13.25C12.75 13.6642 12.4142 14 12 14C11.5858 14 11.25 13.6642 11.25 13.25V9.75C11.25 9.33579 11.5858 9 12 9ZM13 15.75C13 16.3023 12.5523 16.75 12 16.75C11.4477 16.75 11 16.3023 11 15.75C11 15.1977 11.4477 14.75 12 14.75C12.5523 14.75 13 15.1977 13 15.75Z",fill:"#DC2626"})}),text:"error",details:"The address validation failed with no certainty."},submitted:{text:"submitted",details:"The data has been submitted."},processing:{text:"Processing",details:"The data is currently being processed."},label_created:{text:"Label Created",details:"A shipping label has been created for the order."},pending:{text:"pending",details:"The process is pending and awaiting further action."},completed:{text:"completed",details:"The process has been completed successfully."},cancelled:{text:"cancelled",details:"The process has been cancelled."},voided:{text:"Voided",details:"The order has been voided."},deleted:{text:"deleted",details:"The data has been deleted."},false:{text:"false",details:"The status is false."},active:{text:"Active",details:"The status is active."},inactive:{text:"Inactive",details:"The status is inactive."},save:{text:"",details:"The status is saved."},no_save:{text:"",details:"The status is not saved."},closed:{text:"Closed",details:"The status is closed."},open:{text:"Open",details:"The status is open."},true:{text:"true",details:"The status is true."},"not held":{text:"Not Held",details:"The inventory is not held."},held:{text:"Held",details:"The inventory is held."},approved:{text:"approved",details:"The status is approved."},charged:{text:"Charge Added",details:"A charge has been added."}},X=({value:r,mappings:e})=>{var f,x,l,v,j,n;const a=["object"].includes(typeof(e==null?void 0:e[r]))&&!Array.isArray(e==null?void 0:e[r])?Z[(x=(f=e==null?void 0:e[r])==null?void 0:f.text)==null?void 0:x.toLowerCase()]:["string","number"].includes(typeof(e==null?void 0:e[r]))?Z[(l=e==null?void 0:e[r])==null?void 0:l.toLowerCase()]:null,_=["object"].includes(typeof(e==null?void 0:e[r]))&&!Array.isArray(e==null?void 0:e[r])?(j=(v=e==null?void 0:e[r])==null?void 0:v.text)==null?void 0:j.toLowerCase():["string","number"].includes(typeof(e==null?void 0:e[r]))?(n=e==null?void 0:e[r])==null?void 0:n.toLowerCase():null;return s.jsx(i.Fragment,{children:a!=null&&a.text||_?s.jsx(F,{display:s.jsxs("span",{className:"flex w-fit items-center justify-normal gap-[.3125rem]  p-[.3125rem] capitalize",children:[_=="incoming"?s.jsx("img",{src:"/assets/incoming.svg"}):_=="outgoing"?s.jsx("img",{src:"/assets/outgoing.svg"}):s.jsx("span",{className:"cursor-pointer",children:a==null?void 0:a.icon}),s.jsx("span",{className:"max-w-[9.375rem]  whitespace-nowrap  font-inter text-[.875rem] font-[400] leading-[1.125rem] text-black ",children:(a==null?void 0:a.text)??_})]}),place:"left",backgroundColor:"#000",openOnClick:!1,children:s.jsx("div",{className:"flex max-w-[9.375rem] items-center gap-2",children:s.jsx("span",{className:"w-full whitespace-normal break-words font-inter text-[.875rem] font-[400] leading-[1.125rem] text-white",children:(a==null?void 0:a.details)??(a==null?void 0:a.text)??_})})}):null})},u=i.memo(X),o=r=>{const e={year:"numeric",month:"long",day:"numeric"},a=new Date(r),[_,f]=r.split("T"),[x,l]=[a.getHours(),a.getMinutes()];return f&&(x||l)&&(e.hour="2-digit",e.minute="2-digit"),new Date(r).toLocaleDateString(void 0,e)},C=({column:r,tableRole:e,handleTableCellChange:a,allowEditing:_,onPopoverStateChange:f,row:x,currentTableData:l,expandRow:v,showNote:j,actions:n,columnIndex:w})=>{var b;const t=i.useMemo(()=>r,[r]),h=i.useMemo(()=>x,[x]),Y=i.useMemo(()=>l,[l]),B=()=>{var p,E,g,L,D,O,V,A,M,P,R,k,N;const c=h[t==null?void 0:t.accessor];return(((p=t==null?void 0:t.accessor)==null?void 0:p.indexOf("image"))>-1||((E=t==null?void 0:t.accessor)==null?void 0:E.indexOf("photo"))>-1)&&(t!=null&&t.selected_column)&&c?s.jsx(K,{src:c,onPopoverStateChange:f}):(((g=t==null?void 0:t.accessor)==null?void 0:g.indexOf("pdf"))>-1||((L=t==null?void 0:t.accessor)==null?void 0:L.indexOf("doc"))>-1||((D=t==null?void 0:t.accessor)==null?void 0:D.indexOf("file"))>-1||((O=t==null?void 0:t.accessor)==null?void 0:O.indexOf("video"))>-1||["attached_files","attached_file"].includes(t==null?void 0:t.accessor))&&(t!=null&&t.selected_column)&&c?s.jsx(G,{value:c}):t!=null&&t.join&&(t!=null&&t.selected_column)&&!(t!=null&&t.date)?s.jsx(Q,{value:(V=h[t==null?void 0:t.join])==null?void 0:V[t==null?void 0:t.accessor]}):t!=null&&t.join&&(t!=null&&t.selected_column)&&(t!=null&&t.date)?s.jsx(y,{value:((A=h[t==null?void 0:t.join])==null?void 0:A.member_status)===0||((M=h[t==null?void 0:t.join])==null?void 0:M.member_status)===2?"":(P=h[t==null?void 0:t.join])!=null&&P[t==null?void 0:t.accessor]?s.jsx("span",{className:"font-iowan-regular text-base",children:S((R=h[t==null?void 0:t.join])==null?void 0:R[t==null?void 0:t.accessor]).format("MM/DD/YYYY hh:mma")}):""}):t!=null&&t.mappingExist&&!["admin"].includes(e)&&(t!=null&&t.selected_column)?s.jsx($,{value:c,mappings:t==null?void 0:t.mappings}):t!=null&&t.mappingExist&&["admin"].includes(e)&&(t!=null&&t.selected_column)&&(t==null?void 0:t.type)!="version2"?s.jsx($,{value:c,mappings:t==null?void 0:t.mappings}):t!=null&&t.mappingExist&&["admin"].includes(e)&&(t!=null&&t.selected_column)&&((N=(k=t==null?void 0:t.mappings)==null?void 0:k[c])==null?void 0:N.type)=="version2"?s.jsx(u,{value:c,mappings:t==null?void 0:t.mappings}):t!=null&&t.mappingExist&&_&&["admin"].includes(e)&&(t!=null&&t.selected_column)?s.jsx(J,{value:c,mappings:t==null?void 0:t.mappings,onChange:T=>a(T.target.value,t==null?void 0:t.accessor,h.id)}):!(t!=null&&t.mappingExist)&&(t==null?void 0:t.accessor)!=="id"&&(t==null?void 0:t.accessor)!=="create_at"&&(t==null?void 0:t.accessor)!=="update_at"&&(t==null?void 0:t.accessor)!=="user_id"&&(t==null?void 0:t.accessor)!==""&&_&&(t!=null&&t.selected_column)?s.jsx(q,{value:c,onChange:T=>a(T.target.value,t==null?void 0:t.accessor,h.id)}):t!=null&&t.truncate&&(t!=null&&t.selected_column)?s.jsx(H,{value:c,length:50}):t!=null&&t.replace&&(t!=null&&t.selected_column)?s.jsx(H,{value:c,length:30}):t!=null&&t.list&&(t!=null&&t.selected_column)?s.jsx(U,{column:t,data:c,expandRow:v,currentTableData:Y}):t!=null&&t.isCurrency&&(t!=null&&t.selected_column)?s.jsx(I,{currency:t==null?void 0:t.currency,value:c}):["notes","note"].includes(t==null?void 0:t.accessor)&&(t!=null&&t.selected_column)&&c?s.jsx(W,{value:c,showNote:j}):["update_at","create_at"].includes(t==null?void 0:t.accessor)&&(t!=null&&t.selected_column)?s.jsx(y,{value:o(c)}):(t==null?void 0:t.accessor)!==""&&(t!=null&&t.selected_column)&&!(t!=null&&t.date)?s.jsx(y,{value:c}):null};return[0,"0"].includes(w)&&[(b=n==null?void 0:n.select)==null?void 0:b.show].includes(!1),s.jsx("td",{style:{background:(x==null?void 0:x.bg)??""},className:`${w==0?" font-iowan text-[15px] font-semibold":"pl-6"} ${(r==null?void 0:r.type)=="dashboard",""} ${w==1?"font-iowan-regular":""} !w-[auto] !min-w-[6.25rem]  !max-w-[auto] whitespace-nowrap border-b   pr-6 capitalize ${(r==null?void 0:r.type)=="fund_manager"&&w!=0?"font-iowan-regular":""}  ${(r==null?void 0:r.type)=="font-iowan"?"font-iowan-regular":""}  ${(r==null?void 0:r.type)=="teams"?"!min-w-[2rem] !max-w-[3rem] !pr-0":""}`,children:B()})},ft=i.memo(C);export{ft as default};
