import{j as s}from"./@nextui-org/listbox-0f38ca19.js";import"./vendor-4cdf2bd1.js";import{b as E}from"./index.esm-25e0e799.js";import{L as f,bp as F,bs as L,E as M,l as z,d as T,bo as B}from"./index-46de5032.js";import{M as k}from"./index-af54e68d.js";import{D as j}from"./index-b05aa8a0.js";import{a as O}from"./MkdListTableBindOperations-38051783.js";import"./@nextui-org/theme-345a09ed.js";import"./react-icons-36ae72b7.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const D=({action:e,row:p,actionId:r,key:d})=>{var h;if(e!=null&&e.bind)switch((h=e==null?void 0:e.bind)==null?void 0:h.action){case"hide":if(!O(e,p))return s.jsx(f,{children:s.jsx(j,{name:(e==null?void 0:e.children)??d,className:"hover:!bg-white-100 !w-[11rem] !min-w-[11rem] !max-w-[11rem] !bg-brown-main-bg",icon:e==null?void 0:e.icon,onClick:()=>{e!=null&&e.action&&(e==null||e.action([p[r]]))}})})}if(!(e!=null&&e.bind))return s.jsx(f,{children:s.jsx(j,{name:(e==null?void 0:e.children)??d,className:"hover:!bg-white-100 !w-[11rem] !min-w-[11rem] !max-w-[11rem] bg-brown-main-bg",icon:e==null?void 0:e.icon,onClick:()=>{e!=null&&e.action&&(e==null||e.action([p[r]]))}})})},K=({action:e,actionKey:p,row:r,actionId:d})=>s.jsx(f,{children:s.jsx(k,{display:s.jsxs("span",{className:"flex  w-full cursor-pointer items-center justify-between gap-3 px-2 capitalize text-[#262626] hover:bg-[#F4F4F4]",children:[s.jsxs("span",{className:"flex grow gap-3",children:[e==null?void 0:e.icon,(e==null?void 0:e.children)??p]}),s.jsx(F,{})]}),className:"w-full",tooltipClasses:"!rounded-[.5rem] w-full !min-w-fit !w-fit !max-w-fit !px-0 !right-[3.25rem] !border bg-white",place:"left-start",backgroundColor:"#fff",children:e!=null&&e.options&&Object.keys(e==null?void 0:e.options).length?Object.keys(e==null?void 0:e.options).map((h,u)=>s.jsx(D,{action:e==null?void 0:e.options[h],actionId:d,row:r},u)):null})}),P=(e,p)=>{var r;return e!=null&&e.bind&&["hide"].includes((r=e==null?void 0:e.bind)==null?void 0:r.action)?!O(e,p):!0},_=(e,p,r)=>p.mappingExist?p.mappingAction[r[e]]:e,se=({row:e,columns:p,actions:r,actionId:d="id",setDeleteId:h,onPopoverStateChange:u=null})=>{var g,i,N,v;const R=p==null?void 0:p.find(m=>["status","verify","receipt_status"].includes(m==null?void 0:m.accessor)),A=Object.keys(e).find(m=>["status","verify","receipt_status"].includes(m));return s.jsx(s.Fragment,{children:Object.keys(r).filter(m=>{var l,b,w,x,C,y;return((l=r[m])==null?void 0:l.show)&&((b=r[m])==null?void 0:b.locations)&&((x=(w=r[m])==null?void 0:w.locations)==null?void 0:x.length)&&((y=(C=r[m])==null?void 0:C.locations)==null?void 0:y.includes("dropdown"))&&P(r[m],e)}).length?s.jsx("div",{className:"items center z-3 relative flex h-fit w-fit",children:s.jsx(f,{children:s.jsxs(k,{display:s.jsx(L,{className:"h-[1.5rem] w-[1.5rem] rotate-90 cursor-pointer",stroke:"#1F1D1A"}),tooltipClasses:"!rounded-[.125rem] !min-w-fit !w-fit !max-w-fit !px-0 !right-[3.25rem] !border !border-black bg-white",place:"left-end",onPopoverStateChange:u,classNameArrow:"!border-b !border-r !border-primary-black",children:[((g=r==null?void 0:r.edit)==null?void 0:g.show)&&s.jsx(f,{children:s.jsx(j,{className:"!w-[11rem] !min-w-[11rem] !max-w-[11rem] !bg-brown-main-bg",icon:s.jsx(M,{}),name:"Edit",onClick:()=>{var m,l;(m=r==null?void 0:r.edit)!=null&&m.action&&((l=r==null?void 0:r.edit)==null||l.action([e[d]]))}})}),((i=r==null?void 0:r.view)==null?void 0:i.show)&&s.jsx(f,{children:s.jsx(j,{className:"!w-[11rem] !min-w-[11rem] !max-w-[11rem] !bg-brown-main-bg",icon:s.jsx(E,{className:"text-gray-400"}),name:"View",onClick:()=>{var m,l;(m=r==null?void 0:r.view)!=null&&m.action&&((l=r==null?void 0:r.view)==null||l.action([e[d]]))}})}),((N=r==null?void 0:r.status)==null?void 0:N.show)&&s.jsx(f,{children:s.jsx(j,{className:"!w-[11rem] !min-w-[11rem] !max-w-[11rem] !bg-brown-main-bg",icon:s.jsx(z,{}),name:_(A,R,e),onClick:()=>{var m,l;(m=r==null?void 0:r.status)!=null&&m.action&&((l=r==null?void 0:r.status)==null||l.action([e[d]]))}})}),((v=r==null?void 0:r.delete)==null?void 0:v.show)&&s.jsx(f,{children:s.jsx(j,{className:"!w-[11rem] !min-w-[11rem] !max-w-[11rem] !bg-brown-main-bg",icon:s.jsx(T,{}),name:"Delete",onClick:()=>{var m,l,b;(m=r[key])!=null&&m.action?(l=r[key])!=null&&l.action&&((b=r[key])==null||b.action([e[d]])):h&&h(e[d])}})}),Object.keys(r).filter(m=>{var l,b,w,x;return((l=r[m])==null?void 0:l.show)&&((b=r[m])==null?void 0:b.locations)&&((x=(w=r[m])==null?void 0:w.locations)==null?void 0:x.includes("dropdown"))}).map((m,l)=>{var b,w,x;if((b=r[m])!=null&&b.type&&[B.DROPDOWN].includes((w=r[m])==null?void 0:w.type))return s.jsx(K,{row:e,actionKey:m,actionId:d,action:r[m]},l);if(!((x=r[m])!=null&&x.type))return s.jsx(D,{row:e,actionId:d,action:r[m]},l)})]})})}):null})};export{se as default};
