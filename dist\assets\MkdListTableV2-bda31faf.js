import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{r as p,R as w}from"./vendor-4cdf2bd1.js";import{u as Ao}from"./react-hook-form-9f4fcfa9.js";import{o as Lo}from"./yup-c41d85d2.js";import{c as Oo}from"./yup-342a5df4.js";import{T as Mo,M as zo,A as qo,G as Ro,u as To,n as Bo,t as ps,a7 as Ko,g as Uo,h as zs,a8 as qs,w as Go,L as Rs,o as Fo}from"./index-46de5032.js";import{P as Jo}from"./index-3283c9b7.js";import{a as Qo,T as Wo,b as Xo,O as Yo}from"./index-d0de8b06.js";import{A as Ts}from"./AddButton-51d1b2cd.js";import{E as Zo}from"./ExportButton-eb4cf1f9.js";import{a as Vo}from"./index.esm-54e24cf9.js";import{M as Ho}from"./MkdInput-a0090fba.js";import{T as Io}from"./lucide-react-0b94883e.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./react-icons-36ae72b7.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";const xo=(b,T)=>typeof b=="string"&&["eq"].includes(T)?`'${b}'`:b,ts=(b,T)=>typeof T=="number"&&["cs"].includes(b)?"eq":b,Do=({defaultColumns:b=[],excludeColumns:T=[],columnModel:_=null,processes:k=[],actions:i={view:{show:!0,multiple:!0,action:null},edit:{show:!0,multiple:!0,action:null},delete:{show:!0,multiple:!0,action:null},select:{show:!0,multiple:!0,action:null},action:{show:!1,multiple:!1,action:null,showChildren:!0,children:"+ Add",type:"",className:"",locations:[],icon:null},add:{show:!0,multiple:!0,action:null,showChildren:!0,children:"+ Add",type:"",className:""},export:{show:!0,multiple:!0,action:null,showText:!1,className:""}},updateRef:hs=null,onUpdateCurrentTableData:gs=null,actionPostion:D=["dropdown"],actionId:Bs="id",tableRole:Ks="admin",table:C="user",tableTitle:G="",tableSchema:ao=[],hasFilter:Q=!0,schemaFields:se=[],showPagination:Us=!0,defaultFilter:ws=[],refreshRef:Cs=null,allowEditing:Gs=!1,allowSortColumns:Fs=!0,showSearch:Js=!0,topClasses:Qs="",join:W=[],filterDisplays:Ws=[],resetFilters:X=null,defaultPageSize:Xs=500,searchFilter:A=[],onReady:L=null,maxHeight:a=null,rawFilter:Ys=[],externalData:o={page:1,data:[],limit:0,pages:0,total:0,use:!1,loading:!1,canNextPage:!1,canPreviousPage:!1,fetch:(ss,O,R)=>{},search:(ss,O,R,$)=>{}},noDataComponent:Zs={use:!1,component:t.jsx(t.Fragment,{})},useImage:Vs=!0,canChangeLimit:Hs=!0,selectedItemsRef:Ss=null,useDefaultColumns:ys=!1,showYScrollbar:Is=!0,showXScrollbar:xs=!0,showScrollbar:Ds=!0,isActivityTable:as=!1})=>{var vs,Ps,Es,_s,ks,As,Ls;const ss=p.useMemo(()=>new Mo,[]),O=p.useMemo(()=>new zo,[]),R=p.useRef(null),{dispatch:$}=w.useContext(qo),{dispatch:M,state:{columModel:os}}=w.useContext(Ro);w.useState("");const[F,J]=w.useState([]),[l,Y]=w.useState(Xs),[P,es]=w.useState(0),[oe,is]=w.useState(0),[r,Z]=w.useState(1),[so,cs]=w.useState(!1),[oo,us]=w.useState(!1),[eo,rs]=w.useState(!1),[io,fs]=w.useState(!1),[ls,ns]=w.useState([]),[co,Ns]=w.useState([]),[j,V]=w.useState([]),[h,bs]=w.useState(""),[B,K]=w.useState(!1);w.useState(!1);const[uo,S]=w.useState(!1),[ee,ro]=w.useState(!1),[fo,lo]=w.useState("name"),[c,z]=w.useState({views:[],data:null,columns:[],columnId:0,columnsReady:!1}),[no,mo]=w.useState(!1),{profile:E,getProfile:po}=To(),U=p.useMemo(()=>ls,[ls]),to=Oo({}),{register:ie,handleSubmit:ho,setError:ce,reset:js,formState:{errors:ue}}=Ao({resolver:Lo(to)}),ds=p.useCallback(()=>{let s=[];return new Set(U.map(e=>e==null?void 0:e.accessor)).forEach(e=>{const f=U.filter(m=>m.accessor===e);if((f==null?void 0:f.length)>0){const m=f.filter(g=>g==null?void 0:g.value);if(m.length>1)m.forEach(g=>{const{accessor:n,operator:d,value:N}=g,q=`goodbadugly_${C}.${n},${ts(d==="cs"||d==="eq"?"o"+d:d,N)},${N}`;s.push(q)});else if(m.length===1){const{accessor:g,operator:n,value:d}=m[0];s.push(`goodbadugly_${C}.${g},${ts(n,d)},${xo(d,n)}`)}}}),s},[U,C]),v=p.useCallback(async(s={limit:l,page:1})=>{const u=ds();try{const e=`/v3/api/custom/goodbadugly/generic/search/${C}?limit=${s==null?void 0:s.limit}&page=${s==null?void 0:s.page}`;S(!0);const f=await Bo(M,$,{endpoint:e,method:"POST",payload:{search:h,columns:c==null?void 0:c.columns,filter:A,tree_filter:u}},"tableSearchData",!1);if(!(f!=null&&f.error)){V([]);const{data:m,total:g,limit:n,num_pages:d,page:N}=f;let q=m;if(k!=null&&k.length)for(const I of k)["function"].includes(typeof I)&&(q=await I(q,c==null?void 0:c.columns,u));J(()=>q),Y(Number(n)),es(d??P),Z(Number(N)),is(Number(g)),cs(Number(N)>1),us(Number(N)+1<=d?Number(d):P),S(!1),L&&L(q)}S(!1)}catch{S(!1),L&&L([])}},[C,l,h,c,A,M,$,P,U]),y=p.useCallback(async(s,u,e)=>{R.current&&R.current.abort();const f=new AbortController;R.current=f;const m=f.signal,n=[...ds(),...ws,...e==null?void 0:e.filterConditions];try{S(!0);const d=await ss.getPaginate(C,{size:u,page:s,...W&&W.length?{join:W}:null,...e!=null&&e.order?{order:e==null?void 0:e.order,direction:e==null?void 0:e.direction}:null,filter:n!=null&&n.length?n:void 0},m);V([]);const{list:N,total:q,limit:I,num_pages:Os,page:ms}=d;let x=N;if(k!=null&&k.length)for(const Ms of k)["function"].includes(typeof Ms)&&(x=await Ms(x,c==null?void 0:c.columns,n));J(()=>x),Y(I),es(Os),Z(ms),is(q),cs(ms>1),us(ms+1<=Os),S(!1),L&&L(x)}catch(d){d.name==="AbortError"?console.log("Fetch aborted"):(S(!1),console.error("ERROR",d),ps($,d.message)),L&&L([])}finally{z(d=>({...d,columnsReady:!1})),R.current===f&&(console.info("abortControllerRef.current null"),R.current=null)}},[C,W,ws,$,c,ds,U]),go=p.useCallback(s=>{c!=null&&c.columns[s].isSorted?c.columns[s].isSortedDesc=!(c!=null&&c.columns[s].isSortedDesc):(c==null||c.columns.forEach(u=>{u.isSorted=!1,u.isSortedDesc=!1}),c.columns[s].isSorted=!0),async function(){h?h&&v({limit:l,page:r}):o!=null&&o.use?(S(!0),o==null||o.fetch(r,l,{filterConditions:[],order:c==null?void 0:c.columns[s].accessor,direction:c!=null&&c.columns[s].isSortedDesc?"desc":"asc"})):await y(r,l,{filterConditions:[],order:c==null?void 0:c.columns[s].accessor,direction:c!=null&&c.columns[s].isSortedDesc?"desc":"asc"})}()},[c,r,l,co,y]),wo=p.useCallback(s=>{(async function(){Y(s),h?h&&v({limit:s,page:r}):(await y(r,s,{filterConditions:[]}),K(!1))})()},[B,h,r,y,v]),Co=p.useCallback(s=>{const u={uid:Ko(),accessor:s,operator:"cs",value:""};ns(e=>[...e,u])},[]),So=p.useCallback((s,u,e)=>{ns(f=>f.map(m=>(m==null?void 0:m.uid)===e?{...m,[s]:u}:m)),s==="value"&&ro(!0)},[]),yo=p.useCallback(()=>{(async function(){h?h&&(o!=null&&o.use?o==null||o.search(h,columns,A,{limit:l,page:r-1>0?r-1:r}):v({limit:l,page:r-1>0?r-1:r})):(o!=null&&o.use?(S(!0),o==null||o.fetch(r-1>0?r-1:r,l)):await y(r-1>0?r-1:r,l,{filterConditions:[]}),K(!1))})()},[B,h,r,l,y,v]),No=p.useCallback(s=>{(async function(){Z(s),h?h&&(o!=null&&o.use?o==null||o.search(h,columns,A,{limit:l,page:s}):v({limit:l,page:s})):(o!=null&&o.use?(S(!0),o==null||o.fetch(s,l)):await y(s,l,{filterConditions:[]}),K(!1))})()},[B,h,l,y,v]),bo=p.useCallback(()=>{(async function(){h?h&&(o!=null&&o.use?o==null||o.search(h,columns,A,{limit:l,page:r+1<=P?r+1:r}):v({limit:l,page:r+1<=P?r+1:r})):(o!=null&&o.use?(S(!0),o==null||o.fetch(r+1<=P?r+1:r,l)):await y(r+1<=P?r+1:r,l,{filterConditions:[]}),K(!1))})()},[B,h,r,P,l,y,v]);p.useCallback((s,u,e)=>{const f=u==="eq"&&isNaN(e)?`${e}`:e,m=`${s},${u},${f}`.toLowerCase();Ns(g=>[...g.filter(d=>!d.includes(s)),m]),bs(e)},[]);const jo=p.useCallback(async s=>{const u=async e=>{try{fs(!0),O.setTable(C);const f=await O.callRestAPI({id:e},"DELETE");f!=null&&f.error||(J(m=>m.filter(g=>Number(g.id)!==Number(e))),fs(!1),rs(!1))}catch(f){throw fs(!1),rs(!1),ps($,f==null?void 0:f.message),new Error(f)}};if(Array.isArray(s))for(const e of s)await u(e);else typeof s=="number"&&await u(s)},[C,$]),$o=p.useCallback(async()=>{try{O.setTable(C);const s={search:Uo(h),columns:c==null?void 0:c.columns,exclude_columns:T,filter:A,raw_filter:Ys};await O.customExportCSV(s)}catch(s){throw new Error(s)}},[C,h,c,T,A]),vo=p.useCallback(async s=>{var u,e,f;s==null||s.preventDefault(),[(u=s==null?void 0:s.code)==null?void 0:u.toLowerCase(),(e=s==null?void 0:s.key)==null?void 0:e.toLowerCase()].includes("enter")?h?h&&(o!=null&&o.use?o==null||o.search(h,columns,A):v({limit:l,page:r})):(o!=null&&o.use?(S(!0),o==null||o.fetch(r,l,{filterConditions:[]})):await y(r,l,{filterConditions:[]}),K(!1)):(bs((f=s==null?void 0:s.target)==null?void 0:f.value),B||K(!0))},[B,h,r,l,y,v]);p.useCallback(async()=>{js(),await y(r,l)},[js,y,r,l]);const Po=p.useCallback(()=>{h?h&&(o!=null&&o.use?(S(!0),o==null||o.search(r,l,{filterConditions:[]})):v({limit:l,page:r})):o!=null&&o.use?(S(!0),o==null||o.fetch(r,l,{filterConditions:[]})):y(r,l,{filterConditions:[]})},[U,C,y,r,l]),$s=p.useCallback(async(s,u,e)=>{try{O.setTable(C),await O.callRestAPI({id:s,[u]:e},"PUT")}catch(f){console.log("ERROR",f),ps($,f.message)}},[C,$]),Eo=p.useCallback(async(s,u,e,f)=>{let m;u=isNaN(Number.parseInt(u))?u:Number.parseInt(u);try{clearTimeout(m),m=setTimeout(async()=>{await $s(s,f,u)},200),J(g=>g.map((n,d)=>d===e?{...n,[f]:u}:n))}catch(g){console.error(g)}},[$s]),H=p.useCallback((s,u=[])=>{if(!s)return z(f=>({...f,columns:[...b],columnsReady:!0,views:u}));const e=s!=null&&s.columns?JSON.parse(s==null?void 0:s.columns):[];z(f=>({...f,data:s,views:u,columnId:u!=null&&u.length?s==null?void 0:s.column_id:s==null?void 0:s.id,columnsReady:!0,columns:e!=null&&e.length?e:b}))},[b,c]),_o=p.useCallback(async()=>{var u,e,f,m,g;z(n=>({...n,columnsReady:!1})),zs(M,!0,"columModel");const s=await qs(M,$,"column_views",[..._?[`model,eq,'${_}'`]:[`model,eq,'${C}'`],`user_id,eq,${E==null?void 0:E.id}`]);if(!(s!=null&&s.error)&&((u=s==null?void 0:s.data)!=null&&u.length)){const n=s==null?void 0:s.data.find(d=>d==null?void 0:d.current_view);H(n,(e=s==null?void 0:s.data)==null?void 0:e.reverse())}else{const n=await qs(M,$,"column",[..._?[`model,eq,'${_}'`]:[`model,eq,'${C}'`],"user_id,eq,0"]);if(!(n!=null&&n.error)&&((f=n==null?void 0:n.data)!=null&&f.length)){const d={name:"default",default_view:!0,current_view:!0,user_id:E==null?void 0:E.id,model:_||C,column_id:(m=n==null?void 0:n.data[0])==null?void 0:m.id,columns:(g=n==null?void 0:n.data[0])==null?void 0:g.columns},N=await Go(M,$,"column_views",d,!1);H({...d,id:N==null?void 0:N.data},[{...d,id:N==null?void 0:N.data}])}else H(null,[])}zs(M,!1,"columModel")},[_,C,E==null?void 0:E.id,M,$,H,z]),ko=p.useCallback(s=>{Y(s==null?void 0:s.limit),es(s==null?void 0:s.pages),Z(s==null?void 0:s.page),is(s==null?void 0:s.total),cs((s==null?void 0:s.page)>1),us((s==null?void 0:s.page)+1<=(s==null?void 0:s.pages))},[]);return w.useEffect(()=>{var s;(s=i==null?void 0:i.select)!=null&&s.action&&i.select.action(j)},[j==null?void 0:j.length]),p.useEffect(()=>{const s=c==null?void 0:c.columns.find(u=>u==null?void 0:u.searchable);s&&lo(s==null?void 0:s.accessor)},[]),w.useEffect(()=>{if(ys)return z(s=>({...s,columns:[...b],columnsReady:!0,views:[]}));_o()},[_,b==null?void 0:b.length,ys]),p.useEffect(()=>{c!=null&&c.columnsReady&&(o!=null&&o.use?(S(!0),o==null||o.fetch(r,l,{filterConditions:[]})):y(r,l,{filterConditions:[]}))},[c==null?void 0:c.columnsReady,o==null?void 0:o.use]),p.useEffect(()=>{X&&(o!=null&&o.use?(S(!0),o==null||o.fetch(1,l,{filterConditions:X})):y(1,l,{filterConditions:X}))},[X]),p.useEffect(()=>{po()},[]),t.jsxs("div",{className:`relative grid !h-[auto] !max-h-full !min-h-[auto] w-full min-w-full max-w-full items-start gap-2 ${a||"grid-rows-[auto_auto_auto]"}`,children:[Ss&&t.jsx("button",{type:"button",ref:Ss,onClick:()=>{j!=null&&j.length&&V([])},className:"hidden"}),hs&&t.jsx("button",{type:"button",ref:hs,onClick:()=>{gs&&gs(s=>{J(()=>s==null?void 0:s.data),ko(s)}),S(!1)},className:"hidden"}),Cs&&t.jsx("button",{type:"button",ref:Cs,onClick:()=>{o!=null&&o.use?(S(!0),o==null||o.fetch(r,l,{filterConditions:[]})):y(1,l,{filterConditions:[]})},className:"hidden"}),t.jsxs("div",{className:`flex w-full justify-between ${G&&Q?"flex-col gap-3":"h-fit items-center"} ${Qs}`,children:[t.jsx("h4",{className:"flex items-center font-inter text-[1rem] font-bold capitalize leading-[1.5rem] tracking-[-0.011em]",children:G||""}),t.jsxs("div",{className:`flex h-fit flex-col md:flex-row ${Q?"w-full":"w-fit"} items-start justify-between gap-2 text-center md:items-center`,children:[Q?t.jsx(Qo,{table:C,columnModel:_||C,onSubmit:Po,columnData:c,searchField:fo,handleSubmit:ho,setColumnData:z,onColumnClick:Co,filterDisplays:Ws,setOptionValue:So,selectedOptions:ls,setSelectedOptions:ns,setFilterConditions:Ns}):null,t.jsxs("div",{className:`flex h-full w-full justify-between gap-2 self-end md:w-fit md:flex-row md:justify-end ${!G&&!Q?"w-full":""}`,children:[Object.keys(i).map((s,u)=>{var e,f;if(i[s].show&&i[s].hasOwnProperty("type")&&["toggle"].includes(i[s].type))return t.jsx(Ho,{type:"toggle",onChange:m=>{var g,n,d;(g=i[s])!=null&&g.action&&((d=i[s])==null||d.action((n=m==null?void 0:m.target)==null?void 0:n.checked))},label:((e=i[s])==null?void 0:e.children)??s,value:(f=i[s])==null?void 0:f.value},u)}),Js?t.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-black px-2 py-1 focus-within:border-gray-400",children:[t.jsx(Vo,{className:"text-xl text-black"}),t.jsx("input",{type:"text",placeholder:"Search",className:"w-full border-none p-0 placeholder:text-left focus:outline-none",style:{boxShadow:"0 0 transparent"},onKeyDown:s=>{s.key==="Enter"&&s.preventDefault()},onKeyUp:s=>vo(s)})]}):null,j!=null&&j.length&&D.includes("ontop")?t.jsx(Rs,{children:t.jsx(Wo,{actions:i,selectedItems:j})}):null,t.jsxs("div",{className:"flex w-[auto] items-center justify-end gap-2 self-end",children:[Object.keys(i).map((s,u)=>{var e,f,m,g,n;if(i[s].show&&i[s].hasOwnProperty("type")&&["static"].includes(i[s].type))return t.jsxs(Ts,{onClick:()=>{var d,N;(d=i[s])!=null&&d.action&&((N=i[s])==null||N.action())},title:((e=i[s])==null?void 0:e.title)??s,showPlus:!1,className:`!h-[2.5rem] ${(f=i[s])==null?void 0:f.className}`,loading:((m=i[s])==null?void 0:m.loading)??!1,disabled:((g=i[s])==null?void 0:g.disabled)??!1,icon:((n=i[s])==null?void 0:n.icon)??null,children:[s==="delete"?t.jsx(Io,{}):null,i[s].children?i[s].children:t.jsx(t.Fragment,{children:Fo(s==="delete"?"Remove":s,{casetype:"capitalize",separator:" "})})]},u)}),((vs=i==null?void 0:i.export)==null?void 0:vs.show)&&t.jsx(Zo,{showText:(Ps=i==null?void 0:i.export)==null?void 0:Ps.showText,onClick:$o,className:`mx-1 !h-[2.5rem] ${(Es=i==null?void 0:i.export)==null?void 0:Es.className}`}),((_s=i==null?void 0:i.add)==null?void 0:_s.show)&&t.jsx(Ts,{onClick:()=>{var s,u;(s=i==null?void 0:i.add)!=null&&s.action&&((u=i==null?void 0:i.add)==null||u.action())},showChildren:(ks=i==null?void 0:i.add)==null?void 0:ks.showChildren,className:`!h-[2.5rem] ${(As=i==null?void 0:i.add)==null?void 0:As.className}`,children:(Ls=i==null?void 0:i.add)==null?void 0:Ls.children})]})]})]})]}),t.jsx(Xo,{table:C,onSort:go,actions:i,actionId:Bs,useImage:Vs,tableRole:Ks,maxHeight:a,tableTitle:G,setLoading:S,deleteItem:jo,columnData:c,popoverShown:no,allowEditing:Gs,setColumnData:z,actionPostion:D,deleteLoading:io,selectedItems:j,showScrollbar:Ds,showYScrollbar:Is,showXScrollbar:xs,showDeleteModal:eo,noDataComponent:Zs,allowSortColumns:Fs,currentTableData:F,setSelectedItems:V,onPopoverStateChange:mo,setShowDeleteModal:rs,handleTableCellChange:Eo,loading:uo||(os==null?void 0:os.loading)||(o==null?void 0:o.loading),isActivityTable:as}),j!=null&&j.length&&D.includes("overlay")?t.jsx(Rs,{children:t.jsx(Yo,{actions:i,selectedItems:j,currentTableData:F})}):null,Us&&(F!=null&&F.length)?t.jsx("div",{className:"mt-4 w-full",children:t.jsx(Jo,{currentPage:r,pageCount:P,pageSize:l,startSize:10,canPreviousPage:so,canNextPage:oo,updatePageSize:wo,previousPage:yo,nextPage:bo,multiplier:10,updateCurrentPage:No,canChangeLimit:Hs})}):null]})},Je=p.memo(Do);export{Je as default,ts as getCorrectOperator,xo as getCorrectValueTypeFormat};
