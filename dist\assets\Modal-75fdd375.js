import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as o}from"./vendor-4cdf2bd1.js";import{b as y}from"./index.esm-6fcccbfe.js";import{o as g}from"./index-46de5032.js";import"./@nextui-org/theme-345a09ed.js";import"./react-icons-36ae72b7.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const v=({onClose:u,modalContentRef:r,panelClassName:d})=>{const n=o.useRef(null),c=o.useRef(!1),f=a=>{n.current=a.touches[0].clientY,c.current=!0},l=a=>{if(!c.current||!n.current||!r.current)return;const t=a.touches[0].clientY-n.current;t>0&&(r.current.style.transform=`translateY(${t}px)`,r.current.style.transition="none")},p=a=>{if(!c.current||!n.current||!r.current)return;const t=a.changedTouches[0].clientY-n.current;r.current.style.transition="transform 0.3s ease-out",t>100?(r.current.style.transform="translateY(100%)",setTimeout(()=>{r.current&&(r.current.style.transform=""),u()},300)):r.current.style.transform="",c.current=!1,n.current=null};return e.jsx("div",{className:"sticky top-0 z-10 pt-2 w-full md:hidden",onClick:u,children:e.jsx("div",{onTouchStart:f,onTouchMove:l,onTouchEnd:p,className:`mx-auto mb-4 h-[6px] w-[60px] cursor-pointer rounded-full bg-[#F2DFCE] ${d}`})})},w=({title:u,isOpen:r,zIndex:d,children:n,page:c="",modalHeader:f,modalCloseClick:l,panelHeader:p=!1,modalHeaderClassName:a="",disableCancel:h=!1,classes:t={modal:"h-full",modalDialog:"h-[90%]",modalContent:"min-W-FULL",panelClassName:""},clickOutToClose:x=!1})=>{const b=o.useRef(null),m=o.useRef(null);return o.useEffect(()=>{m.current&&(m.current.style.transform="")},[r]),o.useEffect(()=>{const s=i=>{x&&m.current&&!m.current.contains(i.target)&&l()};return r&&x&&document.addEventListener("mousedown",s),()=>{document.removeEventListener("mousedown",s)}},[r,x,l]),o.useEffect(()=>{const s=document.querySelectorAll("body, .scrollable-container");return r?s.forEach(i=>{i.style.overflow="hidden"}):s.forEach(i=>{i.style.overflow="auto"}),()=>{s.forEach(i=>{i.style.overflow="auto"})}},[r]),e.jsx("div",{ref:b,style:{zIndex:d??999999999999,backgroundColor:"rgba(0, 0, 0, 0.5)"},className:`fixed bottom-0 left-0 right-0 top-0 flex w-full items-center justify-center bg-[#00000099] p-[1.5rem] backdrop-blur-sm   ${r?"opacity-100":"pointer-events-none opacity-0"} ${t==null?void 0:t.modal}`,children:e.jsxs("div",{ref:m,className:`relative border border-primary-black ${c==="ManagePermissionAddRole"?"w-fit":"w-[80%]"} overflow-hidden rounded-lg bg-brown-main-bg shadow transition-transform duration-300 md:pb-5 ${r?"translate-y-0":"translate-y-full"} ${t==null?void 0:t.modalDialog}`,children:[p&&e.jsx(v,{onClose:l,modalContentRef:m,panelClassName:t==null?void 0:t.panelClassName}),f&&e.jsxs("div",{style:{zIndex:1},className:`flex sticky top-0 justify-between px-5 py-5 border-b bg-brown-main-bg ${a}`,children:[e.jsx("div",{className:"text-center font-iowan text-[1.25rem] font-[700] capitalize leading-[1.5rem] tracking-[-1.5%]",children:["string"].includes(typeof u)?g(u,{casetype:"capitalize",separator:" "}):u}),h?null:e.jsx("button",{type:"button",className:"cursor-pointer modal-close",onClick:l,children:e.jsx(y,{className:"text-xl"})})]}),e.jsx("div",{className:`mt-4 px-5 ${t==null?void 0:t.modalContent}`,children:n})]})})},B=o.memo(w);export{B as Modal};
