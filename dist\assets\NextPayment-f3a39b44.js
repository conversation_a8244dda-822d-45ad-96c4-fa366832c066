import{j as r}from"./@nextui-org/listbox-0f38ca19.js";import{r as b}from"./vendor-4cdf2bd1.js";import{a as g,u as w}from"./index-46de5032.js";import{u as N}from"./useDate-14dbf4c5.js";import{u as v}from"./useSubscription-58f7fe18.js";import{IntervalMap as y}from"./CurrentPlan-712f36c6.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./AddButton-51d1b2cd.js";import"./index-0d34eb6c.js";import"./index-dadd4882.js";const U=()=>{var i,o,n,m,p,l,c,a,x,u,f;const{globalState:t,setGlobalState:d}=g(),{data:e,loading:q,getSubscription:h}=v(),{convertDate:j}=N(),{profile:s}=w();return b.useEffect(()=>{s!=null&&s.id&&(h({filter:[`user_id,eq,${s==null?void 0:s.id}`,"cancelled,eq,0","status,eq,'active'"],join:[]}),t!=null&&t.refreshSubscription&&d("refreshSubscription",!1))},[s==null?void 0:s.id,t==null?void 0:t.refreshSubscription]),r.jsxs("div",{className:"grid h-[19.9375rem] max-h-[19.9375rem] min-h-[19.9375rem] w-full grid-cols-1 grid-rows-12 flex-col justify-between gap-[.75rem] rounded-[.625rem] md:h-full md:max-h-full md:min-h-full md:w-1/2",children:[r.jsx("div",{className:"row-span-2 font-iowan text-[20px] font-[700] leading-[24.86px]",children:"Next Payment"}),r.jsxs("div",{className:"row-span-6 space-y-[.75rem]  ",children:[r.jsxs("span",{className:"",children:[r.jsx("span",{className:"font-iowan text-[3.25rem] font-bold leading-[4.04rem] ",children:e!=null&&e.subscription?(n=(((o=(i=e==null?void 0:e.object)==null?void 0:i.plan)==null?void 0:o.amount)/100).toFixed(2))==null?void 0:n.split(".")[0]:"$0"}),r.jsx("span",{className:"font-iowan text-[3.25rem] font-bold leading-[4.04rem] ",children:e!=null&&e.subscription?r.jsxs(r.Fragment,{children:[".",(l=(((p=(m=e==null?void 0:e.object)==null?void 0:m.plan)==null?void 0:p.amount)/100).toFixed(2))==null?void 0:l.split(".")[1],(a=(c=e==null?void 0:e.object)==null?void 0:c.plan)!=null&&a.interval?r.jsxs("span",{className:"font-iowan text-[16px] font-bold leading-[64.64px] ",children:[" ","/",y.get((u=(x=e==null?void 0:e.object)==null?void 0:x.plan)==null?void 0:u.interval)]}):r.jsx("span",{className:"font-iowan text-[16px] font-bold leading-[64.64px] ",children:"/mo"})]}):".00"})]}),r.jsx("div",{className:"font-Inter text-[1rem] font-normal leading-[1.5rem] text-gray-600",children:e!=null&&e.subscription?j(((f=e==null?void 0:e.object)==null?void 0:f.current_period_end)*1e3):null})]})]})};export{U as default};
