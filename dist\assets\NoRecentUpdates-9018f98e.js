import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{a as N,u as v,L as l,l as k,I as U}from"./index-46de5032.js";import{M as S}from"./index-af54e68d.js";import{u as C}from"./useSubscription-58f7fe18.js";import{r as o,b as I}from"./vendor-4cdf2bd1.js";import{M as P}from"./index-dadd4882.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const _={free_trial:1,pro:5,"pro yearly":5,business:10,"business yearly":10,enterprise:1/0},Z=()=>{const{setGlobalState:a,globalState:s}=N(),{profile:r}=v(),[h,i]=o.useState(!1),[j,c]=o.useState(!1);I();const{loading:b,data:e,processRegisteredDate:y,getSubscription:g,getCustomerSubscription:d,getSentUpdates:w}=C();return o.useEffect(()=>{r!=null&&r.id&&(d(),g({filter:[`user_id,eq,${r==null?void 0:r.id}`]}),y(r==null?void 0:r.create_at),w(r),s!=null&&s.refreshSubscription&&a("refreshSubscription",!1))},[r==null?void 0:r.id,s==null?void 0:s.refreshSubscription]),o.useEffect(()=>{var m,p,f,u;if(e){if(!(e!=null&&e.subscription)&&!(e!=null&&e.trial_expired)){if((e==null?void 0:e.sentUpdates)>=1){i(!0);return}i(!1);return}if(e!=null&&e.trial_expired&&!(e!=null&&e.subscription)){i(!0);return}const n=(u=(f=(p=(m=e==null?void 0:e.object)==null?void 0:m.plan)==null?void 0:p.nickname)==null?void 0:f.split(" ")[0])==null?void 0:u.trim(),x=_[n]||(n!=null&&n.includes("enterprise")?1/0:0);(e==null?void 0:e.sentUpdates)>=x&&x!==1/0?i(!0):i(!1)}},[e]),t.jsxs(t.Fragment,{children:[t.jsxs("div",{className:"flex h-full w-full items-center justify-center",children:[" ",t.jsxs("div",{className:"flex flex-col items-center justify-center gap-[1.5rem] text-center",children:[t.jsxs("div",{className:"flex w-full flex-col items-center justify-center text-center",children:[t.jsx("div",{className:"no-updates-icon",children:t.jsx(l,{children:t.jsx(k,{fill:"black",className:"!h-[3.75rem] !w-[3.75rem]"})})}),t.jsx("br",{}),t.jsx("div",{className:"font-iowan text-[20px] font-[700] md:text-[1.5rem] md:leading-[1.865rem]  ",children:"You Have No Updates"}),t.jsx("br",{}),t.jsx("p",{className:"font-inter text-[1rem] font-[400] leading-[1.5rem]",children:"Keep your team/stakeholders up to date with"}),t.jsx("p",{className:"font-inter text-[1rem] font-[400] leading-[1.5rem]",children:"company updates"})]}),t.jsx(l,{children:h?t.jsx(S,{display:t.jsx("button",{onClick:()=>{c(!0)},className:"flex h-[2.75rem] w-fit items-center justify-center rounded-[.0625rem] bg-[#1f1d1a] px-5 py-2 font-iowan tracking-wide text-white opacity-50",children:e!=null&&e.subscription?"Upgrade Plan":"Subscribe"}),openOnClick:!1,backgroundColor:"#1f1d1a",place:"top",children:t.jsx("div",{className:"px-1 py-3 text-white sm:p-3",children:t.jsx("p",{className:"text-[12px]",children:e!=null&&e.subscription?"You have reached your monthly update limit for your current plan.":e!=null&&e.trial_expired?"Please Upgrade your account to create an update!":"You have used your free update. Please subscribe to send more updates!"})})}):t.jsx(U,{type:"button",className:"flex h-[2.75rem] w-fit items-center justify-center rounded-[.0625rem] bg-[#1f1d1a] px-5 py-2 font-iowan tracking-wide text-white",color:"black",onClick:()=>{a("triggerUpdate",!0)},children:"Create Update"})})]})]}),t.jsx(l,{children:t.jsx(P,{isOpen:j,onClose:()=>{c(!1)},currentPlan:e==null?void 0:e.subscription,onSuccess:()=>{d()}})})]})};export{Z as default};
