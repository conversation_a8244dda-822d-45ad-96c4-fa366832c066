import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{R as l,r as s}from"./vendor-4cdf2bd1.js";import{M as o,G as m,A as d,p as n,I as c}from"./index-46de5032.js";import{A as x}from"./AddButton-51d1b2cd.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const G=({onClose:e,isOpen:a,note:r})=>{new o;const{state:{updateModel:i,currentState:p},dispatch:h}=l.useContext(m);return l.useContext(d),s.useState([]),t.jsx(n,{isOpen:a,modalCloseClick:()=>e&&e(),title:"Note",modalHeader:!0,classes:{modalDialog:"!grid grid-rows-[auto_85%] !w-full !px-0 md:!w-[35.375rem] md:min-h-[50%] md:h-[50%] md:max-h-[50%] max-h-[50%] min-h-[50%]",modalContent:"!z-10 !px-0 overflow-hidden !pt-0",modal:"h-full"},children:t.jsx("div",{className:"h-full min-h-full",children:a?t.jsx(t.Fragment,{children:t.jsxs("div",{className:"relative mx-auto grid h-full max-h-full min-h-full w-full grow grid-cols-1 grid-rows-[90%_10%] rounded text-start !font-inter leading-snug tracking-wide",children:[t.jsx("div",{className:"w-full gap-5 overflow-y-auto pb-10 ",children:t.jsx("div",{className:"w-full space-y-5 px-5",children:t.jsx("div",{className:"text-justify font-inter text-lg font-medium leading-snug tracking-wider",children:r})})}),t.jsxs("div",{className:"relative flex gap-5 px-5",children:[t.jsx("div",{className:"grow"}),t.jsxs("div",{className:"flex h-fit w-[15.6875rem] items-center gap-[.75rem]",children:[t.jsx(x,{onClick:()=>e(),disabled:i==null?void 0:i.loading,className:"!hidden !grow !border-none !bg-soft-200 !text-sub-500",children:"Cancel"}),t.jsx(c,{type:"submit",loading:i==null?void 0:i.loading,disabled:i==null?void 0:i.loading,className:"!hidden !grow px-4 py-2 font-bold capitalize text-white",children:"Update and Close"})]})]})]})}):null})})};export{G as NoteModal,G as default};
