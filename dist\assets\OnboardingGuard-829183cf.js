import{j as o}from"./@nextui-org/listbox-0f38ca19.js";import{r as n,b as w,u as y}from"./vendor-4cdf2bd1.js";import{u as S,a as j,T as E,S as P,M as T}from"./index-46de5032.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const J=({children:p})=>{const[f,s]=n.useState(!0),[g,a]=n.useState(!1),b=w(),i=y(),{profile:e}=S({isPublic:!1}),{showToast:h}=j(),x=new E;return console.log("trekek",e),n.useEffect(()=>{(async()=>{var c,d,m,u,l;if(["/member/get-started","/member/billing","/member/login","/member/sign-up","/member/forgot"].some(t=>i.pathname.includes(t))){a(!0),s(!1);return}if(e!=null&&e.id)try{const t=new T;t.setTable("user");const r=await t.callRawAPI(`/v3/api/custom/goodbadugly/get-profile/${e.id}`,{},"GET");if((c=r==null?void 0:r.data)!=null&&c.oauth&&((d=r==null?void 0:r.data)==null?void 0:d.step)===1)try{await x.update("user",e.id,{step:3})}catch(k){console.error("Error updating step:",k)}if(!((m=r==null?void 0:r.data)!=null&&m.is_onboarded)){b("/member/get-started"),s(!1);return}a(!0)}catch(t){console.error("Error checking onboarding status:",t),h(((l=(u=t==null?void 0:t.response)==null?void 0:u.data)==null?void 0:l.message)??t.message,4e3,"error"),a(!0)}finally{s(!1)}else{if(!e)return;a(!0),s(!1)}})()},[e==null?void 0:e.id,i.pathname]),f?o.jsx("div",{className:"flex h-screen w-full items-center justify-center",children:o.jsx(P,{size:40,color:"#1f1d1a"})}):g?p:o.jsx(LazyLoad,{brand:!0})};export{J as default};
