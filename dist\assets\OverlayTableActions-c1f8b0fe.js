import{j as s}from"./@nextui-org/listbox-0f38ca19.js";import"./vendor-4cdf2bd1.js";import{bo as N,L as m,bp as R,d as g,o as b}from"./index-46de5032.js";import{A as j}from"./AddButton-51d1b2cd.js";import{M as C}from"./index-af54e68d.js";import{D as v}from"./index-b05aa8a0.js";import{p as F}from"./MkdListTableBindOperations-38051783.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const Y=({actions:r,selectedItems:e,currentTableData:d})=>{var h;return s.jsxs("div",{className:"fixed inset-x-0 bottom-5 z-[999999] m-auto flex !h-[3.25rem] !max-h-[3.25rem] w-fit items-center justify-start gap-2 rounded-[.875rem] bg-black px-[.75rem] pb-[.5rem] pt-[.5rem]",children:[s.jsxs("div",{className:"font-inter text-white",children:["Selected: ",e.length]}),(h=Object.keys(r).filter(l=>{var p,u,x,f;return((p=r[l])==null?void 0:p.show)&&((u=r[l])==null?void 0:u.locations)&&((f=(x=r[l])==null?void 0:x.locations)==null?void 0:f.includes("overlay"))}))!=null&&h.length?s.jsx(s.Fragment,{children:Object.keys(r).filter(l=>{var p,u,x,f;return((p=r[l])==null?void 0:p.show)&&((u=r[l])==null?void 0:u.locations)&&((f=(x=r[l])==null?void 0:x.locations)==null?void 0:f.includes("overlay"))}).map((l,p)=>{var u,x,f;if((u=r[l])!=null&&u.type&&[N.DROPDOWN].includes((x=r[l])==null?void 0:x.type))return s.jsx(O,{actionKey:l,action:r[l],selectedItems:e,currentTableData:d},p);if(!((f=r[l])!=null&&f.type))return s.jsx(B,{actionKey:l,action:r[l],selectedItems:e,currentTableData:d},p)}).filter(Boolean)}):null]})},O=({action:r,actionKey:e,selectedItems:d,currentTableData:h})=>(h.filter(l=>d.includes(l==null?void 0:l.id)),s.jsx(m,{children:s.jsx(C,{display:s.jsxs("span",{className:"hover:text[#262626] !border-white-200 !bg-white-100 relative flex h-[3rem] w-full cursor-pointer items-center justify-between gap-2 overflow-hidden rounded-[.625rem] border border-primary bg-primary px-2  py-2 font-inter text-sm font-medium capitalize  leading-loose tracking-wide  text-white hover:bg-[#F4F4F4]",children:[s.jsxs("span",{className:"flex grow items-center justify-start gap-3 text-white",children:[r==null?void 0:r.icon,(r==null?void 0:r.children)??e]}),s.jsx(R,{className:"-rotate-90"})]}),zIndex:999,className:"w-full",tooltipClasses:"!rounded-[.5rem] !text-white w-full !min-w-[11rem] !px-0 !right-[3.25rem] !border",place:"top-start",backgroundColor:"#18181B",children:r!=null&&r.options&&Object.keys(r==null?void 0:r.options).length?Object.keys(r==null?void 0:r.options).map((l,p)=>s.jsx(A,{action:r==null?void 0:r.options[l],selectedItems:d,currentTableData:h},p)):null})})),A=({action:r,selectedItems:e,currentTableData:d})=>{var l;const h=d.filter(p=>e.includes(p==null?void 0:p.id));if(r!=null&&r.bind)switch((l=r==null?void 0:r.bind)==null?void 0:l.action){case"hide":if(!F(r,h))return s.jsx(m,{children:s.jsx(v,{name:(r==null?void 0:r.children)??key,className:"hover:!bg-white-100 !text-white ",icon:r==null?void 0:r.icon,onClick:()=>{r!=null&&r.action&&(r==null||r.action(e))}})})}if(!(r!=null&&r.bind))return s.jsx(m,{children:s.jsx(v,{name:(r==null?void 0:r.children)??key,className:"hover:!bg-white-100 !text-white ",icon:r==null?void 0:r.icon,onClick:()=>{r!=null&&r.action&&(r==null||r.action(e))}})})},B=({selectedItems:r,action:e,actionKey:d,currentTableData:h})=>{const l=h.filter(p=>r.includes(p==null?void 0:p.id));if(r&&(r==null?void 0:r.length)===1&&!(e!=null&&e.multiple))return s.jsx(w,{action:e,actionKey:d,selectedItems:r,rows:l});if(r&&(r==null?void 0:r.length)>=1&&(e!=null&&e.multiple))if(e!=null&&e.multipleFrom){if(!!(e!=null&&e.multipleFrom&&(r==null?void 0:r.length)>=(e==null?void 0:e.multipleFrom)))return s.jsx(w,{action:e,actionKey:d,selectedItems:r,rows:l})}else return s.jsx(w,{action:e,actionKey:d,selectedItems:r,rows:l})},w=({action:r,actionKey:e,selectedItems:d,rows:h})=>{var l;if(r!=null&&r.bind)switch((l=r==null?void 0:r.bind)==null?void 0:l.action){case"hide":if(!F(r,h))return s.jsx(m,{children:s.jsxs(j,{showPlus:!1,loading:(r==null?void 0:r.loading)??!1,disabled:(r==null?void 0:r.disabled)??!1,icon:(r==null?void 0:r.icon)??null,className:`!border-white-200 !bg-white-100 flex cursor-pointer gap-2 px-2 py-2 text-lg font-medium  leading-loose tracking-wide !text-white ${e==="view"?"text-blue-500":e==="delete"?"!text-red-500":"text-[#292829fd]"} hover:underline`,onClick:()=>{r!=null&&r.action&&r.action(d)},children:[e==="delete"?s.jsx(g,{}):null,r.children?r.children:s.jsx(s.Fragment,{children:b(e==="delete"?"Remove":e,{casetype:"capitalize",separator:" "})})]})})}if(!(r!=null&&r.bind))return s.jsx(m,{children:s.jsxs(j,{showPlus:!1,loading:(r==null?void 0:r.loading)??!1,disabled:(r==null?void 0:r.disabled)??!1,icon:(r==null?void 0:r.icon)??null,className:`!border-white-200 !bg-white-100 flex cursor-pointer gap-2 px-2 py-2 text-lg font-medium  leading-loose tracking-wide !text-white ${e==="view"?"text-blue-500":e==="delete"?"!text-red-500":"text-[#292829fd]"} hover:underline`,onClick:()=>{r!=null&&r.action&&r.action(d)},children:[e==="delete"?s.jsx(g,{}):null,r.children?r.children:s.jsx(s.Fragment,{children:b(e==="delete"?"Remove":e,{casetype:"capitalize",separator:" "})})]})})};export{Y as default};
