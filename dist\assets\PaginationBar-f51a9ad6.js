import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{$ as d,L as y}from"./index-46de5032.js";import{r as u}from"./vendor-4cdf2bd1.js";import{L as v}from"./index-3283c9b7.js";import{M as $}from"./index-af54e68d.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const V=({currentPage:i,pageCount:t,pageSize:l,canPreviousPage:a,canNextPage:n,updatePageSize:c,previousPage:f,nextPage:b,startSize:x=10,multiplier:h=10,updateCurrentPage:m,canChangeLimit:w=!0,dataTotal:j})=>{const[r,p]=u.useState(!1);return console.log(t,l,j),e.jsxs("div",{className:" flex h-fit w-full  items-center justify-between gap-[1.5rem]  pl-2 md:flex-row",children:[e.jsxs("div",{className:"flex w-fit items-center justify-between gap-[1.5rem] ",children:[e.jsx("div",{className:"block md:block",children:e.jsxs("span",{children:["Page"," ",e.jsxs("strong",{children:[+i," of ",t]})," "]})}),e.jsx("div",{className:"",children:e.jsx(v,{pageSize:l,multiplier:h,startSize:x,updatePageSize:c,canChangeLimit:w})})]}),e.jsxs("div",{className:"flex h-[2.5rem] grow items-center justify-end gap-[.375rem]  ",children:[e.jsx("button",{type:"button",onClick:f,disabled:!a,className:"flex h-[2rem] w-[2rem] items-center justify-center rounded",children:e.jsx(d,{className:"h-[.875rem] -rotate-90",fill:"black",stroke:"black"})}),r?e.jsx("button",{type:"button",className:"h-[2rem] w-[2rem] rounded border shadow-md",onClick:()=>p(!1),children:"..."}):null,e.jsx("div",{className:"flex w-fit min-w-fit max-w-fit gap-3 overflow-x-auto",children:t!==void 0&&Array.from({length:Number(t)}).map((A,k)=>{const s=k+1;if(!r&&t<=5||!r&&t<=7)return e.jsx("button",{type:"button",disabled:s===i,className:`h-[2rem] w-[2rem] rounded  shadow-md ${i===s?"font-semibold text-black":""}`,onClick:()=>m(s),children:s},s);if(!r&&t>5&&s<=5)return e.jsx("button",{type:"button",disabled:s===i,className:`h-[2rem] w-[2rem] rounded  shadow-md ${i===s?"font-semibold text-black":""}`,onClick:()=>m(s),children:s},s);if(t>5&&t>=8&&s>5&&s<7&&!r)return e.jsx(y,{children:e.jsx($,{display:e.jsx("button",{type:"button",disabled:s===i,className:` h-[2rem] w-fit min-w-[2rem] max-w-fit rounded border px-2 shadow-md ${i===s?"font-semibold text-black":""}`,children:"..."},s),backgroundColor:"#FFF0E5",tooltipClasses:"items-center flex  flex-col gap-2 h-[31.25rem] min-h-[31.25rem] max-h-[31.25rem] w-fit min-w-fit max-w-fit overflow-auto",children:t!==void 0&&Array.from({length:Number(t)}).map((F,N)=>{const o=N+1;if(o>5)return e.jsx("button",{type:"button",disabled:o===i,className:`!m-auto flex h-[2rem] w-auto min-w-[2rem] max-w-fit items-center justify-center rounded border p-2 leading-[1.5rem] shadow-md ${i===o?"font-semibold text-black":""}`,onClick:()=>m(o),children:o},o)})})});if(!r&&t>5&&t>=8&&s===7)return e.jsx("button",{type:"button",disabled:t===i,className:`h-[2rem] w-[2rem] rounded  shadow-md ${i===t?"font-semibold text-black":""}`,onClick:()=>m(t),children:t},s)})}),e.jsx("button",{type:"button",onClick:b,disabled:!n,className:"flex h-[2rem] w-[2rem] items-center justify-center rounded",children:e.jsx(d,{className:"h-[.875rem] rotate-90",fill:"black",stroke:"black"})})]})]})};export{V as default};
