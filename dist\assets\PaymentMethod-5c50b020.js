import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{b as O,a as P,O as _,L as u,E as z,d as K,b8 as R,b9 as q}from"./index-46de5032.js";import{C as y}from"./index-ae32885b.js";import{r as l}from"./vendor-4cdf2bd1.js";import{A as B}from"./index-a807e4ab.js";import{E as G}from"./lucide-react-0b94883e.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const me=()=>{var w,N,E,M,k,F,L,S,A;const[e,b]=l.useState({modal:null,showModal:!1,card:null}),[f,x]=l.useState({card:!1,deleteCard:!1}),{sdk:C}=O(),{showToast:c,tokenExpireError:v}=P(),m=(n,o,a=[])=>{b(d=>({...d,modal:n,showModal:o,ids:a}))};async function h(){var n,o,a,d,p;try{x(i=>({...i,card:!0}));const{data:s,error:r,message:g}=await C.getCustomerStripeCards();if(console.log(s),r&&c(g,5e3),!s)return;const j=(n=s==null?void 0:s.data)==null?void 0:n.find(i=>{var I;return!!((I=i==null?void 0:i.customer)!=null&&I.default_source)});b(i=>({...i,card:j}))}catch(s){console.error(s);const r=(a=(o=s==null?void 0:s.response)==null?void 0:o.data)!=null&&a.message?(p=(d=s==null?void 0:s.response)==null?void 0:d.data)==null?void 0:p.message:s==null?void 0:s.message;c(r,5e3),v(r)}finally{x(s=>({...s,card:!1}))}}const T=async()=>{var n,o,a,d,p;try{x(j=>({...j,deleteCard:!0}));const s=(n=e==null?void 0:e.card)==null?void 0:n.id;if(!s){c("No card found to delete",5e3,"error");return}const{error:r,message:g}=await C.deleteCustomerStripeCard(s);if(c(g||"Card deleted successfully",5e3,r?"error":"success"),r){console.error(r);return}m(null,!1),h()}catch(s){console.error(s);const r=(a=(o=s==null?void 0:s.response)==null?void 0:o.data)!=null&&a.message?(p=(d=s==null?void 0:s.response)==null?void 0:d.data)==null?void 0:p.message:s==null?void 0:s.message;c(r,5e3,"error"),v(r)}finally{x(s=>({...s,deleteCard:!1}))}};return l.useEffect(()=>{h()},[]),t.jsxs(l.Fragment,{children:[t.jsxs("div",{className:"relative flex h-[9.1875rem] max-h-[9.1875rem] min-h-[9.1875rem] flex-col justify-between gap-[.75rem] rounded-[.25rem] border border-[#1F1D1A] bg-[#FFF0E5] p-4",children:[f!=null&&f.card?t.jsx(_,{color:"black",loading:!0}):null,t.jsx("button",{type:"button",className:"peer absolute right-3 top-2 rounded-full p-1 transition-colors hover:bg-black/5",children:t.jsx(G,{className:"h-5 w-5 text-gray-600"})}),t.jsxs("div",{className:"absolute right-3 top-[-5000%] flex gap-2 rounded-[.25rem] border border-black bg-brown-main-bg p-2 opacity-0 shadow-lg transition-all hover:top-12 hover:opacity-100 focus:top-12 focus:opacity-100 peer-focus:top-12 peer-focus:opacity-100 peer-focus-visible:top-12 peer-focus-visible:opacity-100",children:[t.jsx("button",{onClick:()=>m("edit",!0),className:"rounded-lg p-2 transition-colors hover:bg-gray-100",title:"Edit",children:t.jsx(u,{children:t.jsx(z,{})})}),((w=e==null?void 0:e.card)==null?void 0:w.id)&&t.jsx("button",{onClick:()=>m("delete",!0),className:"rounded-lg p-2 transition-colors hover:bg-gray-100",title:"Delete",children:t.jsx(u,{children:t.jsx(K,{})})})]}),t.jsx("label",{className:"block font-iowan text-[1.25rem] font-[700] leading-[1.5537rem] text-[#1F1D1A]",children:"Payment Method"}),e!=null&&e.card?t.jsxs(l.Fragment,{children:[t.jsx("div",{className:"space-y-[.75rem]  ",children:t.jsxs("div",{className:"flex items-center gap-2 font-iowan  text-[32px] font-[700] leading-[39.78px]",children:[t.jsx(u,{children:t.jsx(y,{name:(E=(N=e==null?void 0:e.card)==null?void 0:N.brand)==null?void 0:E.toLowerCase()})}),(M=e==null?void 0:e.card)==null?void 0:M.last4]})}),t.jsx("span",{className:" font-Inter text-[1rem] font-normal leading-[1.5rem] text-gray-600",children:R((k=e==null?void 0:e.card)==null?void 0:k.exp_month,(F=e==null?void 0:e.card)==null?void 0:F.exp_year)})]}):t.jsxs(l.Fragment,{children:[t.jsx("div",{className:"space-y-[.75rem]  ",children:t.jsxs("div",{className:"flex items-center gap-2 font-iowan  text-[32px] font-[700] leading-[39.78px]",children:[t.jsx(u,{children:t.jsx(y,{name:"visa"})}),"****"]})}),t.jsx("span",{className:" font-Inter text-[1rem] font-normal leading-[1.5rem] text-gray-600"})]})]}),t.jsx(q,{isOpen:(e==null?void 0:e.showModal)&&["edit"].includes(e==null?void 0:e.modal),onClose:()=>m(null,!1),onSuccess:()=>{m(null,!1),h()}}),t.jsx(u,{children:t.jsx(B,{title:"Delete Payment Method",mode:"manual",action:"Delete",multiple:!1,onSuccess:T,inputConfirmation:!1,onClose:()=>m(null,!1),customMessage:t.jsxs("div",{className:"space-y-3",children:[t.jsx("p",{children:"Are you sure you want to delete this payment method?"}),(e==null?void 0:e.card)&&t.jsxs("div",{className:"flex items-center gap-2 font-medium",children:[t.jsx(y,{name:(S=(L=e==null?void 0:e.card)==null?void 0:L.brand)==null?void 0:S.toLowerCase()}),t.jsxs("span",{children:["Card ending in ",(A=e==null?void 0:e.card)==null?void 0:A.last4]})]}),t.jsx("p",{className:"text-sm text-red-500",children:"This action cannot be undone."})]}),isOpen:(e==null?void 0:e.showModal)&&["delete"].includes(e==null?void 0:e.modal)})})]})};export{me as default};
