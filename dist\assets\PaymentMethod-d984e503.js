import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as p,b as O}from"./vendor-4cdf2bd1.js";import{b as T,a as F,u as z,O as G,L as d,E as K,b8 as $}from"./index-46de5032.js";import{C as M}from"./index-ae32885b.js";import{u as q}from"./useSubscription-58f7fe18.js";import{A as B}from"./index-a807e4ab.js";import{M as H}from"./MkdInput-a0090fba.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";const J=new Map([["at_period_end","At Period End"],["lifetime","Instantly"]]),ge=()=>{var b,v,w,C,N,_;const{sdk:E}=T(),{globalState:o,setGlobalState:h,RequestItems:l,setLoading:j,showToast:y,tokenExpireError:k}=F(),[r,g]=p.useState({modal:null,selectedIds:[],showModal:!1,cancel_type:null}),{loading:u,data:s,getSubscription:L,getCardData:A}=q(),{profile:i}=z(),P=O(),f=(c,t,m=[])=>{g(a=>({...a,[c]:t,showModal:t,modal:t?c:null,selectedIds:m}))},I=async()=>{var c,t,m,a,x;if(!(r!=null&&r.cancel_type))return y("Please Select one cancel option",5e3,"error");try{j(l==null?void 0:l.deleteModel,!0);const n=await E.cancelStripeSubscription((c=s==null?void 0:s.subscription)==null?void 0:c.id,{cancel_type:r==null?void 0:r.cancel_type});n!=null&&n.error||h("refreshSubscription",!0)}catch(n){const S=(m=(t=n==null?void 0:n.response)==null?void 0:t.data)!=null&&m.message?(x=(a=n==null?void 0:n.response)==null?void 0:a.data)==null?void 0:x.message:n==null?void 0:n.message;y(S,5e3,"error"),k(S)}finally{j(l==null?void 0:l.deleteModel,!1),f("cancel_subscription",!1)}};return p.useEffect(()=>{i!=null&&i.id&&(A(),L({filter:[`user_id,eq,${i==null?void 0:i.id}`,"cancelled,eq,0","status,eq,'active'"],join:[]}),o!=null&&o.refreshSubscription&&h("refreshSubscription",!1))},[i==null?void 0:i.id,o==null?void 0:o.refreshSubscription]),e.jsxs(p.Fragment,{children:[e.jsxs("div",{className:"relative grid h-[19.9375rem] max-h-[19.9375rem] min-h-[19.9375rem] w-full grid-cols-1 grid-rows-12 flex-col justify-between gap-[.75rem] rounded-[.625rem] md:h-full md:max-h-full md:min-h-full md:w-1/2",children:[u!=null&&u.card?e.jsx(G,{color:"black",loading:!0}):null,e.jsxs("div",{className:"row-span-2 flex items-center gap-3 whitespace-nowrap font-iowan text-[20px] font-[700] leading-[24.86px]",children:["Payment Method"," ",e.jsx(d,{children:e.jsx(K,{className:"cursor-pointer",onClick:()=>{P("/member/billing/payment-details")}})})]}),e.jsx("div",{className:"row-span-6 space-y-[.75rem]  ",children:s!=null&&s.card?e.jsxs(p.Fragment,{children:[e.jsx("div",{className:"space-y-[.75rem]  ",children:e.jsxs("div",{className:"flex items-center gap-2 font-iowan  text-[32px] font-[700] leading-[39.78px]",children:[e.jsx(d,{children:e.jsx(M,{name:(v=(b=s==null?void 0:s.card)==null?void 0:b.brand)==null?void 0:v.toLowerCase()})}),(w=s==null?void 0:s.card)==null?void 0:w.last4]})}),e.jsx("span",{className:" font-Inter text-[1rem] font-normal leading-[1.5rem] text-gray-600",children:$((C=s==null?void 0:s.card)==null?void 0:C.exp_month,(N=s==null?void 0:s.card)==null?void 0:N.exp_year)})]}):e.jsxs(p.Fragment,{children:[e.jsx("div",{className:"space-y-[.75rem]  ",children:e.jsxs("div",{className:"flex items-center gap-2 font-iowan  text-[32px] font-[700] leading-[39.78px]",children:[e.jsx(d,{children:e.jsx(M,{name:"visa"})}),"****"]})}),e.jsx("span",{className:" font-Inter text-[1rem] font-normal leading-[1.5rem] text-gray-600",children:"00/00"})]})}),e.jsx("div",{className:"flex row-span-4 items-end",children:s!=null&&s.subscription?e.jsx("button",{onClick:()=>f("cancel_subscription",!0),className:"mb-[7px] bg-transparent text-red-600 underline",children:"Cancel Subscription"}):null})]}),e.jsx(d,{children:e.jsx(B,{title:"Cancel Subscription",mode:"manual",action:"Ok",multiple:!1,onSuccess:I,inputConfirmation:!1,onClose:()=>f("cancel_subscription",!1),customMessage:e.jsxs("div",{className:"space-y-5",children:[e.jsx("div",{children:"Are you sure you want to cancel your subscription?"}),e.jsxs("div",{className:"flex gap-5 justify-between items-center",children:[e.jsx("span",{children:"Cancel:"}),(_=Array.from(J.entries()))==null?void 0:_.map(([c,t],m)=>e.jsx("div",{className:"",children:e.jsx(d,{children:e.jsx(H,{type:"radio",name:"cancel_type",value:c,label:t,onChange:a=>{g(x=>{var n;return{...x,cancel_type:(n=a==null?void 0:a.target)==null?void 0:n.value}})}})})},m))]})]}),isOpen:(r==null?void 0:r.showModal)&&["cancel_subscription"].includes(r==null?void 0:r.modal)})})]})};export{ge as default};
