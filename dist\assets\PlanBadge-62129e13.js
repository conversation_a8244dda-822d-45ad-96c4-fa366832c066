import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{bM as e,bN as p,bO as n,bP as m}from"./index-46de5032.js";import"./vendor-4cdf2bd1.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const P=({name:r})=>{const s={free:e,enterprise:p,business:n,pro:m},i={free:"#E4FFA7",pro:"#C6DCF3",business:"#D2C7F2",enterprise:"#F6D6A2"},o=r?s[r]:e;return t.jsx("div",{style:{backgroundColor:i[r]},className:"flex h-10 w-10 items-center justify-center rounded-full border border-[#1F1D1A] ",children:o&&t.jsx(o,{})})};export{P as default};
