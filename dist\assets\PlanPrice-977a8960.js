import{j as n}from"./@nextui-org/listbox-0f38ca19.js";import"./vendor-4cdf2bd1.js";import{bS as r}from"./index-46de5032.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const d={free:{monthly:0,annual:0},pro:{monthly:29.99,annual:23.99},business:{monthly:49.99,annual:39.99},enterprise:{monthly:89.99,annual:71.99}},c=s=>{var l;const i=(l=s==null?void 0:s.stripe_price[0])==null?void 0:l.amount;if(!i)return d[s==null?void 0:s.name];const e=i-i*20/100;return{monthly:i,annual:e}},z=({name:s,plan:i,currency:e,focusYearly:l,exchangeRates:o})=>{var a,m,p,x;const t=c(i);return console.log(["free"].includes(s),e,"currency"),n.jsxs("div",{className:"flex items-center gap-[8px]",children:[n.jsxs("span",{className:`monthly-price flex items-end ${["free"].includes(s)||!l?"text-primary-black":"!text-[#797570] !line-through"} `,children:[n.jsxs("span",{className:"flex items-end font-iowan text-[40px] font-[700] leading-[49.73px]",children:[n.jsx("span",{className:"currency",children:e}),["free"].includes(s)?n.jsx("span",{children:"0"}):n.jsx("span",{children:(a=r(t==null?void 0:t.monthly,e,o))==null?void 0:a.split(".")[0]})]}),["free"].includes(s)?n.jsx("span",{className:"py-2 font-iowan text-[16px] font-[700] leading-[19.89px]",children:".00"}):n.jsxs("span",{className:"py-2 font-iowan text-[16px] font-[700] leading-[19.89px]",children:[".",(m=r(t==null?void 0:t.monthly,e,o))==null?void 0:m.split(".")[1]]})]}),["free"].includes(s)?null:n.jsxs("span",{className:`yearly-price flex items-end  ${l?"text-primary-black":"hidden !text-[#797570] !line-through"}`,children:[n.jsxs("span",{className:"flex items-end font-iowan text-[40px] font-[700] leading-[49.73px]",children:[n.jsx("span",{className:"currency",children:e}),n.jsx("span",{children:(p=r(t==null?void 0:t.annual,e,o))==null?void 0:p.split(".")[0]})]}),n.jsxs("span",{className:"flex py-2 font-iowan text-[16px] font-[700] leading-[19.89px]",children:[".",(x=r(t==null?void 0:t.annual,e,o))==null?void 0:x.split(".")[1],"/mo"]})]})]})};export{z as default};
