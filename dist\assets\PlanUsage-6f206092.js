import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{a as d,u as a}from"./index-46de5032.js";import{u as h}from"./useSubscription-58f7fe18.js";import{r as l}from"./vendor-4cdf2bd1.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const x={free_trial:1,pro:5,"pro yearly":5,business:10,"business yearly":10,enterprise:1/0},R=({name:i})=>{var p;if(console.log(i,"limitt"),["enterprise","enterprise yearly"].includes(i))return null;d();const{profile:t}=a(),{getSentUpdates:c,data:s,getCustomerSubscription:m,getSubscription:u}=h();console.log(s,"limitt"),l.useEffect(()=>{t!=null&&t.id&&(c(t),t!=null&&t.id&&(m(),u({filter:[`user_id,eq,${t==null?void 0:t.id}`,"cancelled,eq,0","status,eq,'active'","cancelled_at,is"],order:"id,desc",limit:1})))},[t==null?void 0:t.id]);const r=s==null?void 0:s.subscription,n=!(s!=null&&s.subscription)&&!(s!=null&&s.trial_expired),o=r?x[(p=i==null?void 0:i.split(" ")[0])==null?void 0:p.trim()]||0:n?1:0;return console.log(i,"limitt",o,r,s),e.jsx(l.Fragment,{children:e.jsxs("div",{className:"flex items-center gap-2",children:[(s==null?void 0:s.sentUpdates)>0?e.jsxs("span",{children:[s==null?void 0:s.sentUpdates,"/",o]}):null,(s==null?void 0:s.sentUpdates)>0?e.jsxs("span",{children:["update",(s==null?void 0:s.sentUpdates)>1?"s":""," sent this month"]}):e.jsx("span",{children:r?"No update sent this month":n?"1 free update available":"No update sent this month"})]})})};export{R as default};
