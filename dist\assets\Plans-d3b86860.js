import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{r as s}from"./vendor-4cdf2bd1.js";import{M as n,G as D,A as E,O as v}from"./index-46de5032.js";import{u as N}from"./useRecentSubscription-8c0698f8.js";import"./moment-a9aaa855.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@stripe/stripe-js-6b714a86.js";import"./index-6edcbb0d.js";import"./index-23a711b5.js";import{C as T,N as U,P as k}from"./index-dadd4882.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";new n;new n;function ae({updateStep:F=null,trial:R=null}){N();const[A,p]=s.useState(),[B,f]=s.useState(),{dispatch:h}=s.useContext(D);s.useContext(E),s.useState(!1);const[l,c]=s.useState(!1),[a,M]=s.useState(!0),[i,Y]=s.useState("USD"),[m,$]=s.useState({USD:1,EUR:1}),u=[29.99,59.99,89.99],x={USD:"$",EUR:"€",GBP:"£",JPY:"¥"};function g(e,r){return(e*m[r]).toFixed(2)}function S(e){let r=e;return a&&(r=e-e/100*20),g(r,i)}function y(){const e=document.querySelectorAll(".price-elem");if(e.length<u.length){console.error("Not enough container elements to display all prices.");return}u.forEach((r,o)=>{const d=e[o],j=S(r),[C,P]=String(j).split(".");d.firstElementChild.innerHTML=`${x[i]}${C}`,d.lastElementChild.innerHTML=`.${P}/mo`})}async function b(){c(!0);try{const r=await new n().getStripePrices("limit=100","&page=1");p(r.list)}catch(e){console.error(e)}finally{c(!1)}}async function w(){var e;try{const o=await new n().getCustomerStripeSubscription();f(o==null?void 0:o.customer),h({type:"SET_SUB_STATUS",payload:(e=o==null?void 0:o.customer)==null?void 0:e.subId})}catch(r){console.error(r)}}return s.useEffect(()=>{b(),w()},[]),s.useEffect(()=>{y()},[i,a,m]),t.jsx(t.Fragment,{children:t.jsxs("section",{className:"relative h-full max-h-full min-h-full w-full min-w-full max-w-full space-y-[1.5rem] overflow-y-auto px-6 pb-5 sm:pb-0 ",children:[l&&t.jsx(v,{loading:l,color:"#1f1d1a",size:10}),t.jsxs("div",{className:"h-[3.8125rem] w-full space-y-[.75rem] bg-[#FFF0E5] md:w-fit",children:[t.jsx("div",{className:"text-left font-iowan text-[1.5rem] font-bold leading-[1.865rem]",children:"Your Plan"}),t.jsx("div",{className:"text-left font-Inter text-[1rem] font-normal leading-[1.21rem]",children:"Your billings information"})]}),t.jsxs("div",{className:"grid w-full grid-cols-1 grid-rows-1 gap-4 md:grid-cols-12",children:[t.jsx(T,{}),t.jsxs("div",{className:"col-span-12 flex h-fit flex-col items-center gap-[2.5rem] rounded-[.25rem] border border-primary-black p-[2.5rem] md:col-span-8 md:h-[19.9375rem] md:max-h-[19.9375rem] md:min-h-[19.9375rem] md:flex-row",children:[t.jsx(U,{}),t.jsx("hr",{className:"h-[.125rem] w-full border-[.125rem] border-[#DDCCBD] bg-[#DDCCBD] md:h-full md:w-[0.125rem]"}),t.jsx(k,{})]})]})]})})}export{ae as default};
