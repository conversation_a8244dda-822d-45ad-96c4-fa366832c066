import{j as c}from"./@nextui-org/listbox-0f38ca19.js";import{u as l,r as n,i as f}from"./vendor-4cdf2bd1.js";import{a as g,L as b,O as x}from"./index-46de5032.js";import{U as w}from"./index-a613c3fd.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const C=()=>{const{custom:m,setGlobalState:u}=g(),i=l();n.useEffect(()=>{const t=i.pathname.includes("/public/view/");u("isPublicView",t),console.log("Setting public view state:",t,i.pathname)},[i.pathname]);const[e,o]=n.useState({loading:!0,access:"denied"}),{public_link_id:s,update_id:r}=f(),d=async()=>{try{o(a=>({...a,loading:!0}));const t=await m({endpoint:`/v3/api/custom/goodbadugly/public/update/access/${r}/${s}`,methood:"GET"});t!=null&&t.error||o(a=>{var p;return{...a,access:(p=t==null?void 0:t.data)==null?void 0:p.access}})}catch(t){console.log("error >>",t)}finally{o(t=>({...t,loading:!1}))}};return n.useEffect(()=>{s&&r&&d()},[s,r]),c.jsxs(b,{children:[e!=null&&e.loading?c.jsx(x,{loading:!0}):null,!(e!=null&&e.loading)&&["granted"].includes(e==null?void 0:e.access)&&c.jsx(w,{isPublic:!0})]})};export{C as default};
