import{j as r}from"./@nextui-org/listbox-0f38ca19.js";import{r as o}from"./vendor-4cdf2bd1.js";import{_ as i}from"./qr-scanner-cf010ec4.js";import{L as t}from"./index-6edcbb0d.js";import{L as s}from"./index-46de5032.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const m=o.lazy(()=>i(()=>import("./PublicHeader-792636f2.js"),["assets/PublicHeader-792636f2.js","assets/vendor-4cdf2bd1.js","assets/index-46de5032.js","assets/@nextui-org/listbox-0f38ca19.js","assets/@nextui-org/theme-345a09ed.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-d88d3663.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css"])),p=({children:e})=>r.jsxs("div",{className:"flex w-full flex-col",children:[r.jsx(s,{children:r.jsx(m,{})}),r.jsx("div",{className:"h-screen min-h-screen grow bg-brown-main-bg",children:r.jsx(o.Suspense,{fallback:r.jsx("div",{className:"flex h-screen w-full items-center justify-center",children:r.jsx(t,{size:40,color:"#2CC9D5"})}),children:e})})]}),y=o.memo(p);export{y as default};
