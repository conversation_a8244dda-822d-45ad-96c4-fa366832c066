import{j as i}from"./@nextui-org/listbox-0f38ca19.js";import"./vendor-4cdf2bd1.js";import{M as x}from"./index-af54e68d.js";import{u as h,a9 as p}from"./index-46de5032.js";import"./@nextui-org/theme-345a09ed.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const A=({members:l=[],title:s=""})=>{const{profile:o}=h(),d=l==null?void 0:l.slice(0,3),n=(l==null?void 0:l.length)>3?(l==null?void 0:l.length)-3:0,c=t=>(t==null?void 0:t.id)===(o==null?void 0:o.id);return i.jsx("div",{style:{display:"flex",alignItems:"center",position:"relative"},children:l!=null&&l.length?i.jsx(x,{tooltipClasses:"!left-[-42px] md:left-[0px] !absolute ",display:i.jsxs("div",{className:` relative h-[24px] ${(l==null?void 0:l.length)>3?"h w-[calc(100%+5.5rem)]":"p w-[calc(100%+5rem)]"} `,children:[n>0&&i.jsx("div",{style:{right:"0px",zIndex:10},className:"absolute flex h-[24px] w-[24px] items-center justify-center overflow-hidden rounded-full border border-white bg-primary-black text-white",children:i.jsxs("span",{className:"text-xs font-medium",children:["+",n]})}),d.map((t,a)=>i.jsx("div",{style:{right:`${(a+1)*16}px`,zIndex:9-a},className:"absolute h-[24px] w-[24px] overflow-hidden rounded-full border border-white bg-white",children:t!=null&&t.photo?i.jsx("img",{src:t==null?void 0:t.photo,alt:`${t==null?void 0:t.first_name} ${t==null?void 0:t.last_name}`,className:"h-full w-full object-cover"}):i.jsx(p,{className:"h-full w-full"})},a))]}),place:"top",openOnClick:!1,backgroundColor:"#1f1d1a",show:!!(l!=null&&l.length),children:i.jsxs("div",{className:"flex w-auto flex-col gap-2 font-Inter md:w-auto",children:[i.jsx("div",{className:`text-white ${s.length>0?"":"hidden"}`,children:s}),l==null?void 0:l.map((t,a)=>i.jsxs("div",{className:"flex items-center gap-2 text-white",children:[i.jsx("div",{className:"h-[24px] w-[24px] overflow-hidden rounded-full border border-white/20",children:t!=null&&t.photo?i.jsx("img",{src:t==null?void 0:t.photo,alt:`${t==null?void 0:t.first_name} ${t==null?void 0:t.last_name}`,className:"h-full w-full object-cover"}):i.jsx(p,{className:"h-full w-full"})}),i.jsx("span",{className:"line-clamp-1 max-w-[150px] overflow-ellipsis whitespace-nowrap text-sm md:text-base",children:c(t)?"Me":`${t==null?void 0:t.first_name} ${t==null?void 0:t.last_name}`})]},a))]})}):i.jsx("span",{className:"pl-[40px] md:pl-[40px]",children:"-"})})};export{A as default};
