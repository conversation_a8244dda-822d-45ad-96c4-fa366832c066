import{j as s}from"./@nextui-org/listbox-0f38ca19.js";import"./vendor-4cdf2bd1.js";import{M as p}from"./index-af54e68d.js";import{u as h,a9 as c}from"./index-46de5032.js";const v=({members:t=[],title:n=""})=>{const{profile:o}=h(),i=t==null?void 0:t.slice(0,3),d=(t==null?void 0:t.length)>3?(t==null?void 0:t.length)-3:0,x=l=>(l==null?void 0:l.id)===(o==null?void 0:o.id);return s.jsx("div",{style:{display:"flex",alignItems:"center",position:"relative"},children:t!=null&&t.length?s.jsx(p,{tooltipClasses:"!left-[-130px] !absolute ",display:s.jsxs("div",{className:"relative h-[24px] w-[calc(100%+5rem)]",children:[i.map((l,a)=>s.jsx("div",{style:{left:`${a*16}px`,zIndex:i.length-a},className:"absolute h-[24px] w-[24px] overflow-hidden rounded-full border border-white bg-white",children:l!=null&&l.photo?s.jsx("img",{src:l==null?void 0:l.photo,alt:`${l==null?void 0:l.first_name} ${l==null?void 0:l.last_name}`,className:"h-full w-full object-cover"}):s.jsx(c,{className:"h-full w-full"})},a)),d>0&&s.jsx("div",{style:{left:`${i.length*16}px`,zIndex:0},className:"absolute flex h-[24px] w-[24px] items-center justify-center overflow-hidden rounded-full border border-white bg-primary-black text-white",children:s.jsxs("span",{className:"text-xs font-medium",children:["+",d]})})]}),place:"top",openOnClick:!1,backgroundColor:"#1f1d1a",show:!!(t!=null&&t.length),children:s.jsxs("div",{className:"flex w-[220px] flex-col gap-2 font-Inter md:w-[250px]",children:[s.jsx("div",{className:`text-white ${n?"":"hidden"}`,children:n}),t==null?void 0:t.map((l,a)=>s.jsxs("div",{className:"flex w-full items-center justify-between gap-3 text-white",children:[s.jsxs("div",{className:"flex items-center justify-between gap-2",children:[s.jsx("div",{className:"h-[24px] w-[24px] overflow-hidden rounded-full border border-white/20",children:l!=null&&l.photo?s.jsx("img",{src:l==null?void 0:l.photo,alt:`${l==null?void 0:l.first_name} ${l==null?void 0:l.last_name}`,className:"h-full w-full object-cover"}):s.jsx(c,{className:"h-full w-full"})}),s.jsx("span",{className:"line-clamp-1 max-w-[90px] overflow-ellipsis whitespace-nowrap text-sm md:text-base",children:x(l)?"Me":`${l==null?void 0:l.first_name} ${l==null?void 0:l.last_name}`})]}),(l==null?void 0:l.role)&&s.jsx("span",{className:"whitespace-nowrap rounded px-2 py-0.5 text-sm font-semibold md:text-base",style:{color:l.roleColor},children:l.role})]},a))]})}):"-"})};export{v as R};
