import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{A as d}from"./index-2d79ce9c.js";import{au as f,a as j,O as u,L as x}from"./index-46de5032.js";import{r as s}from"./vendor-4cdf2bd1.js";import"./@nextui-org/theme-345a09ed.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const D=({id:m,memberId:n,onClose:l,onSuccess:e})=>{const{handleInvitation:c}=f(),{showToast:i}=j(),[r,a]=s.useState(!1),p=async()=>{try{a(!0),await c(m,"reject",n),i("Invitation declined successfully"),e==null||e()}catch(o){console.error("Error rejecting invitation:",o),err.message!=="TOKEN_EXPIRED"&&i(o.message||"Failed to decline invitation",5e3,"error")}finally{a(!1)}};return t.jsxs(s.Fragment,{children:[r&&t.jsx(u,{}),t.jsx("div",{className:"relative h-fit max-h-fit min-h-[6.25rem] w-[25rem] min-w-[25rem] ",children:t.jsx(x,{children:t.jsx(d,{customMessage:t.jsx(t.Fragment,{children:"Reject Team Invitation?"}),onClose:l,onSuccess:p,action:"Reject",mode:"manual",multiple:!1,inputConfirmation:!1,className:"action-confirmation-modal",disabled:r})})})]})};export{D as default};
