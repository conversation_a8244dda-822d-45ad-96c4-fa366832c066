import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{A as d}from"./index-2d79ce9c.js";import{au as f,a as v,O as x,L as h}from"./index-46de5032.js";import{r as s}from"./vendor-4cdf2bd1.js";import"./@nextui-org/theme-345a09ed.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const D=({id:n,memberId:o,onClose:l,onSuccess:r})=>{const{handleInvitation:p}=f(),{showToast:m}=v(),[t,a]=s.useState(!1);console.log(o,"memberId");const c=async()=>{try{a(!0),await p(n,"remove",o),m("Member removed successfully"),r==null||r()}catch(i){console.error("Error removing member:",i),err.message!=="TOKEN_EXPIRED"&&m(i.message||"Failed to remove member",5e3,"error")}finally{a(!1)}};return e.jsxs(s.Fragment,{children:[t&&e.jsx(x,{}),e.jsx("div",{className:"relative h-fit max-h-fit min-h-[6.25rem] w-[25rem] min-w-[25rem] ",children:e.jsx(h,{children:e.jsx(d,{customMessage:e.jsx(e.Fragment,{children:"Remove Team Member?"}),onClose:l,onSuccess:c,action:"Remove",mode:"manual",multiple:!1,inputConfirmation:!1,className:"action-confirmation-modal",disabled:t})})})]})};export{D as default};
