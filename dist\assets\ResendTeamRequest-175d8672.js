import{j as r}from"./@nextui-org/listbox-0f38ca19.js";import{A as p}from"./index-2d79ce9c.js";import{O as f,L as u}from"./index-46de5032.js";import{u as c}from"./useCompanyMember-f698e8a0.js";import{r as s}from"./vendor-4cdf2bd1.js";import"./@nextui-org/theme-345a09ed.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const z=({id:t,onClose:a,onSuccess:n})=>{var i,o;const{companyMember:{single:e},getCompanyMember:l,loading:m}=c({filter:[`member_id,eq,${t}`]});return s.useEffect(()=>{l(t)},[t]),r.jsx(s.Fragment,{children:r.jsxs("div",{className:"relative h-fit max-h-fit min-h-[6.25rem] w-[25rem] min-w-[25rem] ",children:[m!=null&&m.single?r.jsx(f,{loading:!0}):null,e?r.jsx(u,{children:r.jsx(p,{customMessage:r.jsxs(r.Fragment,{children:["Resend Invite to"," ",r.jsxs("b",{children:[(i=e==null?void 0:e.user)==null?void 0:i.first_name," ",(o=e==null?void 0:e.user)==null?void 0:o.last_name]})]}),onClose:a,onSuccess:n,action:"Resend",mode:"manual",multiple:!1,inputConfirmation:!1,className:"action-confirmation-modal"})}):null]})})};export{z as default};
