import{j as o}from"./@nextui-org/listbox-0f38ca19.js";import"./vendor-4cdf2bd1.js";import{L as t,p,b5 as s}from"./index-46de5032.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const z=({isOpen:r=!1,title:a="change route",onClose:m,onSuccess:e,options:i=[{name:"",route:""}],modalClasses:l={modalDialog:"max-h-[90%] min-h-[12rem] overflow-y-auto !w-full md:!w-[29.0625rem]",modal:"h-full"},customMessage:d=""})=>o.jsx(t,{children:o.jsx(p,{isOpen:r,modalCloseClick:m,title:a,modalHeader:!0,classes:l,children:r&&o.jsx(t,{children:o.jsx(s,{onClose:m,onSuccess:e,options:i})})})});export{z as RouteChangeModal,z as default};
