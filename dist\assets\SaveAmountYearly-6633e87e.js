import{j as m}from"./@nextui-org/listbox-0f38ca19.js";import"./vendor-4cdf2bd1.js";import{bS as n}from"./index-46de5032.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const p=t=>{var i;const r=(i=t==null?void 0:t.stripe_price[0])==null?void 0:i.amount;if(!r)return 0;const o=r*12*20/100;return Math.round(o)},E=({name:t,plan:r,currency:e,exchangeRates:o})=>{var s;const i=p(r);return m.jsx("div",{class:"save-amount-text flex h-[29px] w-fit min-w-[167px] max-w-fit items-center justify-center gap-2 rounded-full border border-[#6A930A] bg-[#E4FFA7] px-3 py-1.5",children:m.jsxs("span",{class:"font-bold text-[#6A930A]",children:[e,(s=n(i,e,o))==null?void 0:s.split(".")[0]," savings annually"]})})};export{E as default};
