import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{r as a,R as re}from"./vendor-4cdf2bd1.js";import{G as je,A as ye,f as oe,o as te,q as ke}from"./index-46de5032.js";import"./MkdInput-a0090fba.js";import"./react-quill-a78e6fc7.js";import{v as P}from"./uuid-82fce61c.js";import{c as Ne}from"./@popperjs/core-f3391c26.js";import{c as se}from"./lucide-react-0b94883e.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";const Se=N=>N?`max-h-[${N}]`:"max-h-[18.75rem]",Ae=({onSelect:N,showBorder:ve=!0,display:j="display",value:h,uniqueKey:x,selector:Ce=null,disabled:A=!1,placeholder:le="- search -",label:W="Select",maxHeight:ae="18.75rem",height:Ve="fit-content",selectedOptions:J=[],table:F="",errors:I,name:Z,className:D="w-[23rem]",inputClassName:H="",join:v=[],filter:Q=[],mode:$e="static",useExternalData:C=!1,externalDataLoading:ne=!1,externalDataOptions:w=[],onReady:U=null,refreshRef:ce=null,clearRef:fe=null,showSearchIcon:ue=!1,required:me=!1,dataRetrievalState:T=null,displaySeparator:y="",customOptions:V=[],onPopoverStateChange:X=null,popoverShown:Y,containerRef:g})=>{var p;const $=a.useRef(null);a.useRef(null),a.useRef(null);const{dispatch:ie,state:de}=re.useContext(je),{dispatch:he}=re.useContext(ye),G=de[T??F],[u,M]=a.useState([]),ge=P({namespace:"SearchableDropdown"});P({namespace:"SearchableDropdown"}),P({namespace:"SearchableDropdown"});const[d,o]=a.useState(!1),[L,q]=a.useState(""),[E,B]=a.useState(null),[b,S]=a.useState([]);a.useState(!1);const[Le,K]=a.useState(null),_=a.useMemo(()=>Q,[Q]),be=a.useMemo(()=>JSON.stringify(w),[w]),R=async()=>{var r;const e=await ke(ie,he,F,{..._!=null&&_.length?{filter:_}:null,...v&&(v!=null&&v.length)?{join:v}:null},T&&T);if(!e.error){if(F==="user"){const f=(r=e==null?void 0:e.data)==null?void 0:r.map(m=>m!=null&&m.first_name||m!=null&&m.last_name?{...m,full_name:te(`${m.first_name} ${m.last_name}`,{casetype:"capitalize",separator:" "})}:m);M(()=>[...f]),S(()=>[...f])}else M(()=>[...e==null?void 0:e.data]),S(()=>[...e==null?void 0:e.data]);if(h){const f=e==null?void 0:e.data.find(m=>m[x]==h);f&&B(()=>f)}U&&U(e==null?void 0:e.data)}},k=(e,r,f)=>{if(typeof r=="string")return String(e[r]);if(typeof r=="object")if(Array.isArray(r)){if(r.some(l=>typeof l!="string"))return String(e[Object.keys(e)[0]]);const z=r.map(l=>e[l]);return String(z.length?z.join(` ${y} `):z.join(" "))}else{if(![1,2].includes(Object.keys(r).length)||Object.keys(r).some(s=>!["and","or"].includes(s)))return String(e[Object.keys(e)[0]]);const l=r.and,i=r.or;if(l&&i){if(typeof l=="string"&&typeof i=="string"){const s=e[l],n=e[i];return String(s||n||e[Object.keys(e)[0]])}if(Array.isArray(l)&&Array.isArray(i)){if(l.some(c=>!e[c])){const c=i.map(ee=>{if(e[ee])return e[ee]}).filter(Boolean);return String(c.length?c.length?c.join(` ${y} `):c.join(" "):e[Object.keys(e)[0]])}const n=l.map(c=>{if(e[c])return e[c]}).filter(Boolean);return String(n.length?n.join(` ${y} `):n.join(" "))}if(Array.isArray(l)&&typeof i=="string"){if(l.some(c=>!e[c])){const c=e[i];return String(c||e[Object.keys(e)[0]])}const n=l.map(c=>{if(e[c])return e[c]}).filter(Boolean);return String(n.length?n.join(` ${y} `):n.join(" "))}if(Array.isArray(i)&&typeof l=="string"){const s=e[l];if(s)return String(s);const n=i.map(c=>{if(e[c])return e[c]}).filter(Boolean);return String(n.length?n.length?n.join(` ${y} `):n.join(" "):e[Object.keys(e)[0]])}}else if(l&&!i){if(typeof l=="string"){const s=e[l];return String(s||e[Object.keys(e)[0]])}if(Array.isArray(l)){const s=l.map(n=>{if(e[n])return e[n]}).filter(Boolean);return String(s.length?s.length?s.join(` ${y} `):s.join(" "):e[Object.keys(e)[0]])}}else if(!l&&i){if(typeof i=="string"){const s=e[i];return String(s||e[Object.keys(e)[0]])}if(Array.isArray(i)){const s=i.map(n=>{if(e[n])return e[n]}).filter(Boolean);return String(s.length?s.length?s.join(` ${y} `):s.join(" "):e[Object.keys(e)[0]])}}}},O=(e,r=!1,f=!0)=>{if(!(f&&!N)){if(r)return B(null),N(null,!0),o(!1);B(e),f&&N(e),L&&q(""),u.length>b&&S(u),d&&o(!1)}};a.useCallback(e=>{if(x)return!!J.find(f=>f[x]===e[x])},[u,b,J]);const xe=a.useCallback(()=>{if(d||L)return L;if(E)return k(E,j);if(h&&u&&(u!=null&&u.length)){const e=u==null?void 0:u.find(r=>r[x]===Number(h));return e?k(e,j):""}else return""},[d,h,L,O,u]),we=a.useCallback(e=>{if(q(e),e){const r=u.filter(f=>k(f,j).toLowerCase().includes(e.toLowerCase()));S(r)}else S(u)},[L]);return a.useEffect(()=>{if(d&&(g!=null&&g.current)&&$.current){const e=Ne(g.current,$.current,{placement:"bottom-start",modifiers:[{name:"flip",options:{fallbackPlacements:["top-start"]}},{name:"preventOverflow",options:{boundary:"viewport",padding:8}},{name:"offset",options:{offset:[0,4]}}]});return K(e),()=>{e.destroy(),K(null)}}},[d,g]),a.useEffect(()=>{const e=r=>{const f=(g==null?void 0:g.current)&&!g.current.contains(r.target),m=$.current&&!$.current.contains(r.target);f&&m&&o(!1)};return d&&document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[d,g]),a.useEffect(()=>{!C&&!(u!=null&&u.length)&&R()},[C]),a.useEffect(()=>{C&&(M(()=>[...w]),S(()=>[...w]))},[C,be]),a.useEffect(()=>{X&&!Y&&d&&X(!0)},[Y,d]),t.jsxs(t.Fragment,{children:[t.jsx("button",{ref:ce,type:"button",hidden:!0,onClick:()=>{if(C){if(b!=null&&b.length){const e=b.find(r=>r[x]===h);e&&O(e,!1,!1)}else if(w!=null&&w.length){const e=w.find(r=>r[x]===h);e&&O(e,!1,!1)}}else R()}}),t.jsx("button",{ref:fe,type:"button",hidden:!0,onClick:()=>{B(null),o(!1)}}),t.jsxs("div",{className:`relative ${D}`,children:[W&&t.jsxs("label",{className:"mb-2 block  font-iowan text-[1rem] font-bold text-[#1f1d1a]",children:[W,me&&t.jsx("sup",{className:"text-[.825rem] text-red-600",children:"*"})]}),ne||G!=null&&G.loading?t.jsx(oe,{count:1,counts:[2],className:`!h-[3rem] !max-h-[3rem] !min-h-[3rem] !gap-0 overflow-hidden rounded-[.125rem] !bg-[#ebebeb] !p-0 ${D} ${H}`}):t.jsxs(t.Fragment,{children:[t.jsxs("div",{className:`flex h-[3rem] w-full items-center justify-normal rounded-[.125rem] border border-primary-black pl-3 shadow ${H} ${A?"bg-gray-200":"bg-brown-main-bg"}`,children:[ue&&!A&&t.jsx("div",{className:"!w-4 ",children:t.jsx("svg",{className:"h-4 w-4 text-gray-500 dark:text-gray-400","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 20 20",children:t.jsx("path",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"})})}),t.jsx("div",{className:"grow",children:t.jsx("input",{type:"text",disabled:A,placeholder:le,id:ge,className:`${A?"bg-gray-200":"bg-brown-main-bg"} showListButton h-full w-full appearance-none rounded-[.125rem] border-0 px-3 py-2  leading-tight text-black placeholder:text-[#1fd1a] focus:outline-none focus:outline-0 focus:ring-0`,value:xe(),onFocus:()=>{d||o(!0)},onChange:e=>{var r,f;return we((f=(r=e.target.value)==null?void 0:r.trimStart())==null?void 0:f.toLowerCase())},onKeyDown:e=>{e.key==="Enter"&&e.preventDefault()}})}),!A&&t.jsxs("div",{onClick:()=>{o(!d)},className:"mr-3 flex cursor-pointer flex-col items-center justify-center",children:[t.jsx(se,{className:"h-4 w-5 p-0",stroke:"#1f1d1a"}),t.jsx(se,{className:"h-4 w-5 rotate-180 p-0",stroke:"#1f1d1a"})]})]}),d&&t.jsx("div",{ref:$,className:`fixed z-[999] w-[calc(309-15%)] overflow-y-auto rounded-md border border-primary-black bg-brown-main-bg py-3 text-sm shadow-lg md:w-[309px] ${Se(ae)}`,children:t.jsxs("div",{className:"",children:[t.jsx("div",{className:`flex h-[2.8rem] min-h-[2.8rem] cursor-pointer items-center justify-start gap-5 truncate px-3 py-2 text-sm font-normal capitalize hover:bg-primary-black/40 hover:text-white 
                    ${!E&&!h?"bg-primary-black text-white":"text-primary-black"}`,onClick:()=>O(null,!0),children:"None"}),V.length&&V.find(e=>e==null?void 0:e.show)?V==null?void 0:V.map((e,r)=>{if(e!=null&&e.show)return t.jsxs("div",{title:e!=null&&e.children&&typeof(e==null?void 0:e.children)=="string"?e==null?void 0:e.children:e!=null&&e.icon&&typeof(e==null?void 0:e.icon)=="string"?e==null?void 0:e.icon:null,className:"flex h-[2.8rem] min-h-[2.8rem] cursor-pointer items-center justify-start gap-3 truncate px-3 py-2 text-sm font-normal capitalize text-primary-black hover:bg-primary-black/40 hover:text-white ",onClick:()=>(e==null?void 0:e.action)&&(e==null?void 0:e.action()),children:[e!=null&&e.icon?e.icon:null,e!=null&&e.children?e.children:null]},r)}):null,b.length?b==null?void 0:b.map((e,r)=>(e==null?void 0:e.searchableType)==="section"?t.jsx("div",{disabled:!0,className:"flex h-[2.8rem] min-h-[2.8rem] w-full items-center justify-start gap-5 truncate bg-black px-3 py-2 text-sm font-bold capitalize text-white",children:e==null?void 0:e.display},r):t.jsx("button",{type:"button",title:e&&k(e,j),className:`flex h-[2.8rem] min-h-[2.8rem] w-full cursor-pointer items-center justify-start gap-5 truncate px-3 py-2 text-sm font-normal capitalize hover:bg-primary-black/40 hover:text-white  ${E&&(h&&h===e[x]||k(e,j)===k(E,j))?"bg-primary-black text-white":"text-primary-black"} `,onClick:()=>O(e),children:k(e,j)},r)):null]})}),I&&I[Z]&&t.jsx("p",{className:"text-field-error absolute inset-x-0 top-[90%] m-auto mt-2 text-[.8rem] italic text-red-500",children:te((p=I[Z])==null?void 0:p.message,{casetype:"capitalize",separator:" "})})]})]})]})},ar=a.memo(Ae);export{ar as default};
