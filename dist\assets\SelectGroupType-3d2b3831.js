import{j as s}from"./@nextui-org/listbox-0f38ca19.js";import{r as o}from"./vendor-4cdf2bd1.js";import{c as H}from"./react-hook-form-9f4fcfa9.js";import{G as J,A as Y,M as z,T as ee,t as B,s as v}from"./index-46de5032.js";import{a as te,C as se}from"./CreateGroupModal-9562fe27.js";import{X as ae}from"./XMarkIcon-6ed09631.js";import{W as f,t as oe}from"./@headlessui/react-cdd9213e.js";function ne({title:u,titleId:l,...p},g){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:g,"aria-labelledby":l},p),u?o.createElement("title",{id:l},u):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 12.75 6 6 9-13.5"}))}const re=o.forwardRef(ne),ie=re;function le({title:u,titleId:l,...p},g){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:g,"aria-labelledby":l},p),u?o.createElement("title",{id:l},u):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"}))}const ce=o.forwardRef(le),ue=ce;function we({control:u,name:l,setValue:p,allowedRoles:g,label:X="Group name",setGroupName:E=null,errors:m,onReady:C,isEditMode:de=!1,onGroupChange:G=null}){var O,U;const h=o.useRef(null),[c,I]=o.useState([]),[x,j]=o.useState(""),{dispatch:b}=o.useContext(J),{dispatch:S,state:P}=o.useContext(Y);o.useState(!1);const[q,R]=o.useState(!0),[$,_]=o.useState(null),[D,w]=o.useState(""),[L,T]=o.useState(!1),y=H({control:u,name:l}),F=x===""?c:c.filter(e=>e.group_name.toLowerCase().replace(/\s+/g,"").includes(x.toLowerCase().replace(/\s+/g,"")));async function V(e,a){if(!a.trim())return v(b,"Group name cannot be empty",5e3,"error"),!1;T(!0);try{return await new z().callRawAPI(`/v4/api/records/group/${e}`,{group_name:a.trim()},"PUT"),I(n=>n.map(d=>d.id===e?{...d,group_name:a.trim()}:d)),v(b,"Group name updated successfully",5e3,"success"),!0}catch(i){return B(S,i.message),i.message!=="TOKEN_EXPIRED"&&v(b,i.message,5e3,"error"),!1}finally{T(!1)}}function Q(e,a){a.stopPropagation(),_(e.id),w(e.group_name)}async function M(e){e.stopPropagation(),await V($,D)&&(_(null),w(""))}function A(e){e.stopPropagation(),_(null),w("")}async function K(){R(!0);try{const e=new z,[a,i]=await Promise.all([e.callRawAPI(`/v4/api/records/group?filter=user_id,in,'NULL',${P.user}`),(async()=>await new ee().getPaginate("recipient_group",{join:["user","group"],filter:[`goodbadugly_recipient_group.user_id,eq,${P.user}`],size:1e3,page:1,order:"id",direction:"desc"}))()]),n=a.list||[],d=i.list||[];console.log("All Groups:",n.map(t=>({id:t.id,name:t.group_name}))),console.log("Recipient Groups (TreeSDK):",d.map(t=>{var r;return{id:t.id,group_id:t.group_id,group_name:(r=t.group)==null?void 0:r.group_name,members:t.members}}));const W={};d.forEach(t=>{if(t.group&&t.group.id){const r=t.members?t.members.split(",").map(N=>N.trim()):[];W[t.group.id]={recipient_group_id:t.id,members:r,group_name:t.group.group_name},console.log(`Recipient group mapping: ${t.group.group_name} (ID: ${t.group.id}) -> members: [${r.join(", ")}]`)}});const k=n.map(t=>{const r=W[t.id];return{id:t.id,group_name:t.group_name,members:r?r.members:[],hasMembers:!!r,recipient_group_id:r?r.recipient_group_id:null}}),Z=new Set(n.map(t=>t.id));d.forEach(t=>{if(t.group&&!Z.has(t.group.id)){const r=t.members?t.members.split(",").map(N=>N.trim()):[];k.push({id:t.group.id,group_name:t.group.group_name,members:r,hasMembers:!0,recipient_group_id:t.id,isFromRecipientAPI:!0}),console.log(`Added missing group from recipient API: ${t.group.group_name} (ID: ${t.group.id})`)}}),console.log("Final Merged Groups:",k.map(t=>{var r;return{id:t.id,name:t.group_name,hasMembers:t.hasMembers,memberCount:((r=t.members)==null?void 0:r.length)||0,members:t.members}})),I(k)}catch(e){B(S,e.message),e.message!=="TOKEN_EXPIRED"&&v(b,e.message,5e3,"error")}R(!1)}return o.useEffect(()=>{K()},[]),o.useEffect(()=>{C&&C()},[c==null?void 0:c.length]),o.useEffect(()=>{var e;E&&E(((e=c.find(a=>a.id==y))==null?void 0:e.group_name)??"")},[y]),s.jsxs(s.Fragment,{children:[s.jsx(f,{value:x||y,onChange:e=>{console.log("SelectGroupType onChange called with value:",e),p(e);const a=c.find(i=>i.id==e);console.log("Selected group data:",a),G?(console.log("Calling onGroupChange with value:",e,"and members:",a==null?void 0:a.members),G(e,(a==null?void 0:a.members)||[])):console.log("No onGroupChange callback provided")},children:s.jsxs("div",{className:"relative z-50 w-full",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("label",{className:"mb-1 block font-iowan  text-[16px] font-semibold capitalize text-[#1f1d1a]",children:X}),s.jsx("div",{className:"",children:s.jsx("button",{className:"font-medium",type:"button",onClick:()=>{var e;(e=h==null?void 0:h.current)==null||e.click()},children:"+ New Group"})})]}),s.jsxs("div",{className:"w-full",children:[s.jsxs(f.Button,{className:"relative w-full cursor-default rounded-[2px] text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 ",children:[s.jsx("div",{className:"w-full",children:s.jsx(f.Input,{className:`focus:shadow-outline  h-[2.6rem] w-full appearance-none rounded-[2px] border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal leading-tight text-[#1f1d1a]    placeholder:text-[14px] placeholder:text-[#1f1d1a] focus:outline-none md:h-[44px] ${(O=m==null?void 0:m[l])!=null&&O.message?"border-red-500":""}`,defaultValue:y,placeholder:"Type to search",displayValue:e=>{const a=c.find(i=>i.id==e);return(a==null?void 0:a.group_name)??""},onChange:e=>{j(e.target.value),e.target.value===""&&j("")},onBlur:()=>j(""),name:l,autoComplete:"off"})}),s.jsx("div",{className:"absolute inset-y-0 right-3 flex items-center",children:s.jsx(te,{className:"h-5 w-5 text-[#1f1d1a]","aria-hidden":"true"})})]}),s.jsx("p",{className:"text-field-error italic text-red-500",children:(U=m==null?void 0:m[l])==null?void 0:U.message})]}),s.jsx(oe,{as:o.Fragment,leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:s.jsx(f.Options,{className:"custom-overflow absolute z-30 mt-1 max-h-[18.75rem]  w-full overflow-y-auto rounded-md border bg-brown-main-bg py-1 pt-6 text-base shadow-lg ring-1 ring-[#1f1d1a]/5 focus:outline-none",children:q||F.length===0&&x!==""?s.jsx("div",{className:"relative cursor-default select-none bg-brown-main-bg px-2 py-2 pl-10 text-gray-700",children:"Nothing found."}):F.map(e=>s.jsx(f.Option,{className:({active:a})=>`relative cursor-default select-none py-2 pl-10 pr-12 ${a?"bg-primary-black text-white":"text-gray-900"}`,value:e.id,children:({selected:a,active:i})=>s.jsx(s.Fragment,{children:$===e.id?s.jsxs("div",{className:"flex items-center gap-2",onClick:n=>n.stopPropagation(),children:[s.jsx("input",{type:"text",value:D,onChange:n=>w(n.target.value),className:"flex-1 rounded border border-gray-300 px-2 py-1 text-sm text-black",autoFocus:!0,onKeyDown:n=>{n.key==="Enter"?M(n):n.key==="Escape"&&A(n)}}),s.jsx("button",{onClick:M,disabled:L,className:"p-1 text-green-600 hover:text-green-800 disabled:opacity-50",title:"Save",children:s.jsx(ie,{className:"h-4 w-4"})}),s.jsx("button",{onClick:A,disabled:L,className:"p-1 text-red-600 hover:text-red-800 disabled:opacity-50",title:"Cancel",children:s.jsx(ae,{className:"h-4 w-4"})})]}):s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("span",{className:"block truncate font-medium",children:e.group_name}),s.jsx("button",{onClick:n=>Q(e,n),className:`p-1 transition-all ${i?"text-white opacity-100 hover:text-gray-200":"text-gray-500 opacity-0 hover:text-gray-700 hover:opacity-100"}`,title:"Edit group name",children:s.jsx(ue,{className:"h-4 w-4"})})]})})},e.id))})})]})}),s.jsx(se,{type:"base",buttonRef:h,afterCreate:e=>{K(),p(e)}})]})}export{we as S};
