import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{b as A,r as n,u as R}from"./vendor-4cdf2bd1.js";import{A as L,G as Y,u as D,T as F,M as U,t as V,s as H,a1 as l,aY as $,aZ as B,a_ as K,a$ as q,b0 as G,b1 as X,b2 as z,b3 as W,b4 as Z}from"./index-46de5032.js";import{h as S}from"./moment-a9aaa855.js";import{P as J}from"./index-8a8a991b.js";import{b as Q}from"./index.esm-6fcccbfe.js";import{B as ee}from"./react-spinners-b860a5a3.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./react-icons-36ae72b7.js";const te=["Investor","Marketing","Product","Finance","Team/HR","Operations"],r={Investor:"Investor",Marketing:"Marketing",Product:"Product",Finance:"Finance",TeamHR:"Team/HR",Operations:"Operations"},h=[{id:1,title:"Standard",img:"standard",url:"",template:l,type:r.Investor,preview:"/assets/techchrunch-preview2.svg"},{id:2,img:"techstars",title:"Techstars",url:"(https://www.techstars.com)",preview:"/assets/techchrunch-preview2.svg",template:$,type:r.Investor},{id:3,title:"Kima Ventures",url:"(https://www.kimaventures.com)",img:"kima",template:B,type:r.Investor,preview:"/assets/techchrunch-preview2.svg"},{id:4,title:"Y Combinator",img:"yc-combinator",url:"(https://www.ycombinator.com)",template:K,type:r.Investor,preview:"/assets/techchrunch-preview2.svg"},{id:5,title:"Underscore VC",img:"underscore",url:"(https://underscore.vc)",template:q,type:r.Investor,preview:"/assets/techchrunch-preview2.svg"},{id:6,title:"Founder Institute",img:"founder",url:"(https://www.fi.co)",template:G,type:r.Investor,preview:"/assets/techchrunch-preview2.svg"},{id:7,title:"Emergence ",url:"(https://www.emcap.com)",img:"emergence",template:X,type:r.Investor,preview:"/assets/techchrunch-preview2.svg"},{id:8,title:"NFX Capital",img:"nfx",url:"(https://www.nfx.com)",template:z,type:r.Investor,preview:"/assets/techchrunch-preview2.svg"},{id:9,title:"Hackernoon",url:"(https://hackernoon.com)",img:"hacker",template:W,type:r.Investor,preview:"/assets/techchrunch-preview2.svg"},{id:10,img:"techcrunch",title:"Techcrunch",url:"(https://techcrunch.com)",template:Z,type:r.Investor,preview:"/assets/techchrunch-preview2.svg"},{id:11,title:"Standard",img:"standard",url:"",template:l,type:r.Marketing,preview:"/assets/techchrunch-preview2.svg"},{id:12,title:"Standard",img:"standard",url:"",categories:[{id:121,title:"Standard",img:"standard",type:"Product",template:l,preview:"/assets/techchrunch-preview2.svg"},{id:122,title:"Standard",img:"standard",type:"Engineering",template:l,preview:"/assets/techchrunch-preview2.svg"},{id:123,title:"Standard",img:"standard",type:"Design",template:l,preview:"/assets/techchrunch-preview2.svg"}],template:l,type:r.Product,preview:"/assets/techchrunch-preview2.svg"},{id:13,title:"Standard",img:"standard",url:"",template:l,type:r.Finance,preview:"/assets/techchrunch-preview2.svg"},{id:14,title:"Standard",img:"standard",url:"",template:l,type:r.TeamHR,preview:"/assets/techchrunch-preview2.svg"},{id:15,title:"Standard",img:"standard",url:"",template:l,type:r.Operations,preview:"/assets/techchrunch-preview2.svg"}],Se=()=>{const w=A(),{dispatch:y,state:u}=n.useContext(L),{dispatch:f}=n.useContext(Y),[T,E]=n.useState(!1),I=R(),[p,b]=n.useState("Investor"),[g,_]=n.useState({isOpen:!1,imageUrl:"",header:""}),[se,P]=n.useState({});D();const v=new URLSearchParams(I.search).get("id");n.useEffect(()=>{if(v){const e=h.find(s=>s.id==v);x(e.template,e.id,e.title)}},[v]),n.useEffect(()=>{const e=localStorage.getItem("template-tab")||"Investor";b(e)},[]),n.useEffect(()=>{const e={},s=a=>new Promise((i,o)=>{const d=new Image;d.src=a,d.onload=()=>{e[a]=d,i(a)},d.onerror=o});(async()=>{try{const a=h.map(i=>i.preview?s(i.preview):i.categories?i.categories.map(o=>s(o.preview)):null).flat().filter(Boolean);await Promise.all(a),P(e)}catch(a){console.error("Error preloading images:",a)}})()},[]);function j(e){return p==="Investor"?`${e} UpdateStack Investor Update Template`:`${e} UpdateStack ${p} Update Template`}function N(e,s){_({isOpen:!0,imageUrl:e,header:s})}function k(){_({isOpen:!1,imageUrl:"",header:""})}async function x(e,s,c){E(s);try{const a=new F,i=localStorage.getItem("requested_update_id");if(i){const o=await a.update("updates",i,{name:"Update Title",template_name:c,user_id:u.user,mrr:0,arr:0,cash:0,burnrate:0,date:S().format("MMM D, YYYY"),public_link_enabled:0,private_link_open:1,company_id:u.company.id});for(let d=0;d<e.length;d++){const m=e[d];await a.create("notes",o.data,{type:m,status:0})}w(`/member/edit-updates/${i}?autofocus=true`,{state:{templateTitle:c}})}else{const o=await a.create("updates",{name:"Update Title",template_name:c,user_id:u.user,mrr:0,arr:0,cash:0,burnrate:0,date:S().format("MMM D, YYYY"),public_link_enabled:0,private_link_open:1,company_id:u.company.id});await new U().callRawAPI("/v3/api/custom/goodbadugly/activities/draft",{update_id:o.data},"POST");for(let m=0;m<e.length;m++){const C=e[m];await a.create("notes",{update_id:o.data,type:C,status:0})}w(`/member/edit-updates/${o.data}?autofocus=true`,{state:{templateTitle:c}})}}catch(a){V(y,a.message),a.message!=="TOKEN_EXPIRED"&&H(f,a.message,5e3,"error")}finally{E(!1),localStorage.removeItem("requested_update_id")}}n.useEffect(()=>{f({type:"SETPATH",payload:{path:"templates"}})},[]);const O=({tab:e,isSelected:s,onClick:c})=>t.jsx("button",{className:` border-b-[6px] px-4 py-2 pb-3 text-[18px] text-lg font-semibold transition-all duration-200 ease-out ${s?"border-b-[6px] border-b-[#1F1D1A]":"font-Ionwan-regular border-b-transparent"}`,onClick:()=>c(e),children:e}),M=({template:e,onSelect:s,creating:c})=>t.jsxs("div",{className:"flex h-[209px] max-w-[100%] flex-col justify-between rounded-[4px] border border-[#1f1D1A] bg-[#F2DFCE] p-6 sm:max-w-[388px] ",children:[t.jsxs("div",{className:"items-center",children:[t.jsx("img",{src:`/assets/${e.img}.png`,className:"mb-4",alt:""}),t.jsx("h3",{className:"text-[18px] font-semibold",children:j(e.title)})]}),t.jsxs("div",{className:"flex justify-between mt-4",children:[t.jsx("button",{className:"flex gap-3 items-center",onClick:()=>N(e.preview,e.title),children:t.jsx("span",{className:"text-sm font-medium font-Inter",children:"Preview"})}),t.jsx("button",{className:"flex gap-3 items-center",onClick:()=>s(e),children:c===e.id?t.jsxs(t.Fragment,{children:[t.jsx("span",{className:"hidden text-sm font-medium font-Inter sm:block",children:"Creating"}),t.jsx(ee,{size:12})]}):t.jsxs(t.Fragment,{children:[t.jsx("span",{className:"text-sm font-medium font-Inter",children:"Select"}),t.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[t.jsx("path",{d:"M2.5 10L17.5 10",stroke:"black",strokeWidth:"1.66667",strokeLinecap:"round",strokeLinejoin:"round"}),t.jsx("path",{d:"M11.668 15.8335L17.5013 10.0002L11.668 4.16683",stroke:"black",strokeWidth:"1.66667",strokeLinecap:"round",strokeLinejoin:"round"})]})]})})]})]});return t.jsxs(t.Fragment,{children:[t.jsxs("div",{className:"flex flex-col p-5 pt-8 space-y-4 w-full md:px-8",children:[t.jsx("span",{className:"text-[24px] font-semibold sm:text-lgheader",children:"Select A Template"}),t.jsxs("div",{className:"w-full md:mx-auto",children:[t.jsx("div",{id:"tabContainer",className:"scrollbar-hide mb-6 flex w-full overflow-x-auto  border-b-[1px] border-b-[#1f1d1a]/20 md:overflow-hidden",children:te.map(e=>t.jsx(O,{tab:e,isSelected:e===p,onClick:s=>{b(s),localStorage.setItem("template-tab",s)}},e))}),h.map(e=>{if([r.Product].includes(e==null?void 0:e.type)&&[p].includes(e==null?void 0:e.type))return t.jsx(J,{template:e,openPreviewModal:N,onSelect:s=>x(s.template,s.id,s.title),creating:T},e.id)}),t.jsx("div",{className:"grid grid-cols-1 justify-center gap-4 sm:grid-cols-2 md:grid-cols-3 md:justify-normal 2xl:gap-[80px]",children:h.map(e=>{if(![r.Product].includes(e==null?void 0:e.type)&&[p].includes(e==null?void 0:e.type))return t.jsx(M,{template:e,onSelect:()=>x(e.template,e.id,e.title),creating:T},e.id)})})]})]}),g.isOpen&&t.jsx("div",{className:"pt-80px fixed inset-0 z-[10000] mx-auto flex max-h-[100vh] w-[100%] items-center justify-center bg-black bg-opacity-50",children:t.jsx("div",{className:"flex h-[100%] items-center justify-center",children:t.jsxs("div",{className:"h-[90%] w-full overflow-y-auto rounded-lg border border-primary-black bg-brown-main-bg  md:max-w-[860px]",children:[t.jsxs("div",{className:"flex justify-between items-center p-6 mb-4",children:[t.jsxs("div",{className:"flex gap-2 items-center",children:[t.jsx("span",{className:"font-iowan text-[20px] font-medium",children:"Template Preview:"}),t.jsxs("h2",{className:"font-iowan text-[20px] font-semibold",children:[g.header," Investor Update"]})]}),t.jsx("button",{onClick:k,className:"text-xl",children:t.jsx(Q,{className:"text-xl"})})]}),t.jsx("div",{className:"bg-[#F2DFCE] px-[60px] py-[40px]",children:t.jsx("img",{src:g.imageUrl,alt:"Template Preview",className:"object-cover w-full h-auto"})})]})})})]})};export{Se as default};
