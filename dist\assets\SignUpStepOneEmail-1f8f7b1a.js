import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as A,b as C,h as L,L as T}from"./vendor-4cdf2bd1.js";import{c as M,a as B,b as k}from"./yup-342a5df4.js";import{ax as s,G as v,u as P,I as K,_ as U,s as h,M as g}from"./index-46de5032.js";import{u as G}from"./react-hook-form-9f4fcfa9.js";import{M as H}from"./index-bdf26615.js";import{o as I}from"./yup-c41d85d2.js";import{M as w}from"./MkdInput-a0090fba.js";import{l as D}from"./logo5-2e16f0f2.js";import{u as V}from"./useLocalStorage-53cfe2d8.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";const Y={[s.MEMBER]:"Signing Up"},Q={[s.MEMBER]:"Sign Up"},F={[s.COLLABORATOR]:[{step:1,name:"Email"},{step:2,name:"Account"},{step:3,name:"Company"}],[s.STAKEHOLDER]:[{step:1,name:"Email"},{step:2,name:"Account"},{step:3,name:"Company"}],[s.MEMBER]:[{step:1,name:"Email"},{step:2,name:"Account"},{step:3,name:"Company"}],[s.INVESTOR]:[{step:1,name:"Email"},{step:2,name:"Account"},{step:3,name:"Company"}]},Re=({role:t="member",updateStep:c=null})=>{const{dispatch:l}=A.useContext(v),y=C(),[r]=L(),{localStorageData:i}=V(["step"]),{updateProfile:j}=P({isPublic:!0}),m=r==null?void 0:r.get("company_email"),b=M({email:B().email().required("This field is required"),accept_terms:k().required("This field is required")}),{register:d,handleSubmit:N,setError:R,watch:S,setValue:p,formState:{errors:u,isSubmitting:o}}=G({resolver:I(b),defaultValues:{email:"",accept_terms:!1}}),{accept_terms:E}=S(),O=async n=>{try{if([s.COLLABORATOR,s.STAKEHOLDER].includes(t))return j({step:2}),c&&c(),0;if(!E){h(l,"Agree to the Terms of Service and Privacy Policy",5e3,"error");return}const x=await new g().callRawAPI("/v3/api/custom/goodbadugly/users/register-email",{email:n.email,role:t},"POST");y("/get-verified")}catch(a){console.log("Error",a),h(l,a.message,5e3,"error"),R("email",{type:"manual",message:a.message})}},f=async n=>{try{const x=await new g().oauthLoginApi(n,t);window.open(x,"_self")}catch{}};return A.useEffect(()=>{m&&[s.COLLABORATOR,s.STAKEHOLDER].includes(t)&&(p("email",m),p("accept_terms",!0))},[m,t]),e.jsxs("div",{className:"md:w-[60%] md:px-6",children:[e.jsx("div",{className:"sticky right-0 top-0 z-[9] flex h-[4.5rem] w-full flex-row items-center justify-between bg-[#1f1d1a] px-8 md:hidden",children:e.jsx("img",{src:D,alt:"logo",className:"h-10 w-[180px]"})}),e.jsxs("div",{className:"flex flex-col px-4 py-8 w-full md:px-0",children:[e.jsxs("div",{className:"w-full space-y-[2.25rem] md:mt-0 md:space-y-[6.25rem] ",children:[e.jsx(H,{steps:F[t],currentStep:i==null?void 0:i.step,onClick:()=>{},className:""}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-iowan text-[1.5rem] font-[700] sm:text-[2.5rem]",children:"Create an account"}),e.jsx("p",{className:"font-medium text-black",children:"Be more productive by sending automated updates free for 30 days!"})]})]}),e.jsxs("form",{className:"space-y-5",onSubmit:N(O),children:[e.jsx("div",{className:"mt-4",children:e.jsx(w,{type:"email",name:"email",label:[s.COLLABORATOR,s.STAKEHOLDER].includes(t)?"Company Email":"Email",errors:u,register:d,placeholder:"Enter email",disabled:[s.COLLABORATOR,s.STAKEHOLDER].includes(t),required:!0})}),e.jsxs("div",{className:"flex items-center justify-start gap-3  py-2 font-semibold text-[#1f1d1a]",children:[e.jsx("div",{className:"",children:e.jsx(w,{name:"accept_terms",register:d,required:!0,errors:u,type:"checkbox",disabled:[s.COLLABORATOR,s.STAKEHOLDER].includes(t)})}),e.jsxs("span",{className:"",children:["By creating an account you are agreeing to the"," ",e.jsx("a",{target:"_blank",className:"font-bold text-[#1f1d1a] underline",href:"https://updatestack.com/terms-of-use",children:"Terms of Service"})," ","and",e.jsxs("a",{target:"_blank",className:"font-bold text-[#1f1d1a] underline",href:"https://updatestack.com/privacy-policy",children:[" ","Privacy Policy"]})]})]}),e.jsx(K,{type:"submit",color:"white",className:"my-4 flex h-[2.75rem] w-full items-center justify-center rounded-sm bg-[#1f1d1a] py-2 tracking-wide !text-white outline-none focus:outline-none",loading:o,disabled:o,children:e.jsx("span",{className:"capitalize",children:o?Y[t]:Q[t]})})]}),e.jsxs("div",{className:" my-3 flex min-w-[70%] flex-row items-center justify-between gap-2 md:my-3 md:mt-6",children:[e.jsx("hr",{className:"w-full grow border-[.0625rem] border-[#1f1d1a] md:w-auto"}),e.jsx("span",{className:"600 w-full whitespace-nowrap text-[1rem] font-[500] md:w-auto",children:"Or SignUp with"}),e.jsx("hr",{className:"w-full grow border-[.0625rem] border-[#1f1d1a] md:w-auto"})]}),e.jsxs("div",{className:"oauth mt-4 flex min-w-[70%] flex-col  gap-4 text-[#344054] md:grow",children:[e.jsxs("button",{onClick:()=>f("google"),className:" flex h-[2.75rem] min-w-[70%] cursor-pointer items-center justify-center gap-3 rounded-sm border-2  border-[#1f1d1a] px-4",children:[e.jsx("img",{src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAALpSURBVHgBtVbNTxNBFH8zuy3QoN0YJMEQs8QQP05LAsbEg4uRxMSD4AeaeLB6xEPhpIkm4MF4IsG/oODF4Edajgahy8UDxbAcjDEc2IORCIlUhVK6u/OcKbVpaZdWxN+lkzd9v9+b9968WQK7YEnXlYPSxm0GqCMQjZtUYScASUSw+NJwGU40GXOGFwfxIg7IqX6KGEYABSqCWBmKPc2TCbOiwEpXhwaMRAFQhb+Ei/i4aXpuyFNAkBMG8eqiLoVIG2N2Z5NhWiUCyxfPqLLtznuTYxKQWIRk869wT60SuYD8ZyHZrGzk3NGkCP3r6Cy0GGYyH5CuqRL1DXKhkBd5/gRrfa0h+7MSKQ0aRhqnEwOwC1YvtOuO41jlyPMCzpRvKT3boKbeNRdsYOzw1FwP/COoPSnriKjWdKxCsO8j0GAmm0/HdQZgHyADhXM8FdtqnPzArUVIv280gsOWVc5BH9xUoWrUJkWRi7pBiAQufRmF4fIukt+N8Hh0qAYsNUoBSztHRtmCfQASVCn8Z1BCiLXT6DJbg32CzPhFKpwXv9AHkY3jOoA5Uc6B53+Mn90o2SBi0mKo2MS5RZvyVVwYFp0g3P95GpbdQNJJuy3mnVgSqsT5JxuRnQKMQYj6uhyDr5Pjm8fg3o+zsMwCQlqR66RIteT6082S6LNw7BlJ/EpX22ufp1r1DEiF2yeOXDupfH396W0lcopMZKCoG/llNYzB4LN8+tvHr8zz3JYUl48MPkHJ0OyNN2NFxJFuZb1W7pfSp8J1K3cV6jQU+aHk1+IP/At5Ae3FTVWm9ny5e5FT4uMasi8WL7RKcs+nALUboO5bGKStozl2GJl+VD+w7VaAjpfXNRTHxb09OP61Hqj53m3GH9a35cUL/5DofWU6zNfGI7RgD9g6FI1hxu4stJV99LVotyJnaJjXZAiqAPI6Aa/Thx118hTIC/G6UMjolJLL2Y+AXBMgr4coPmc2CMVYojc648XxG0ZrPRAMMnAhAAAAAElFTkSuQmCC",className:"h-[1.125rem] w-[1.125rem]"}),e.jsx("span",{className:"text-[1rem] font-[600]",children:"Sign up With Google"})]}),e.jsxs("button",{onClick:()=>f("microsoft"),className:"oauth flex hidden h-[2.75rem] items-center  justify-center gap-2 border-2 border-[#1f1d1a]",children:[e.jsx("img",{src:"https://companieslogo.com/img/orig/MSFT-a203b22d.png?t=**********",className:"h-[1.125rem] w-[1.125rem]"}),e.jsx("span",{className:"text-[1rem] font-[600]",children:"Sign up With Microsoft"})]})]}),e.jsxs("div",{className:"flex flex-row justify-center items-center mt-7 text-sm lg:text-base",children:[e.jsxs("span",{className:"mr-1 font-medium text-[#1f1d1a]",children:["Already have an account?"," "]})," ",e.jsx(T,{to:`/${U[t]}/login`,className:"font-bold text-[#1f1d1a] underline",children:"Sign in"})]})]})]})};export{F as STEPS,Re as default};
