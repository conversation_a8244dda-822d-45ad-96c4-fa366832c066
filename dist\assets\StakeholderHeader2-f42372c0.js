import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as n,b as p,L as m,j as h}from"./vendor-4cdf2bd1.js";import{d as f}from"./index.esm-6fcccbfe.js";import{G as u,A as w}from"./index-46de5032.js";import{d as g}from"./index.esm-54e24cf9.js";import{U as j,A as b}from"./UserIcon-63b89263.js";import{B as C}from"./BriefcaseIcon-6beed5a1.js";import{o as l}from"./@headlessui/react-cdd9213e.js";import"./@nextui-org/theme-345a09ed.js";import"./react-icons-36ae72b7.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const q=()=>{var o;const{state:t,dispatch:x}=n.useContext(u),{state:r,dispatch:d}=n.useContext(w),i=p(),c=[{to:"/stakeholder/dashboard",text:"Dashboard",icon:e.jsx(f,{className:"text-xl text-[#A8A8A8]"}),value:"dashboard"},{to:"/stakeholder/update_requests",text:"  Updates",icon:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M13.492 1.66666H6.50866C3.47533 1.66666 1.66699 3.475 1.66699 6.50833V13.4833C1.66699 16.525 3.47533 18.3333 6.50866 18.3333H13.4837C16.517 18.3333 18.3253 16.525 18.3253 13.4917V6.50833C18.3337 3.475 16.5253 1.66666 13.492 1.66666ZM4.37533 9.90833C4.40033 8.44166 4.98366 7.05833 6.01699 6.025C7.08366 4.95833 8.50033 4.375 10.0003 4.375C11.5003 4.375 12.917 4.95833 13.9753 6.025C14.0003 6.05 14.0253 6.08333 14.0503 6.11666V5.4C14.0503 5.05833 14.3337 4.775 14.6753 4.775C15.017 4.775 15.3003 5.05833 15.3003 5.4V7.60833C15.3003 7.95 15.017 8.23333 14.6753 8.23333H12.467C12.1253 8.23333 11.842 7.95 11.842 7.60833C11.842 7.26666 12.1253 6.98333 12.467 6.98333H13.1587C13.1337 6.95833 13.117 6.93333 13.092 6.90833C12.267 6.08333 11.167 5.625 10.0003 5.625C8.83366 5.625 7.73366 6.08333 6.90866 6.90833C6.10033 7.71666 5.65033 8.79166 5.63366 9.93333C5.62533 10.2667 5.34199 10.5417 5.00033 10.5417H4.99199C4.65033 10.5417 4.37533 10.25 4.37533 9.90833ZM13.9753 13.975C12.917 15.0333 11.5003 15.625 10.0003 15.625C8.50033 15.625 7.08366 15.0417 6.02533 13.975C6.00033 13.95 5.97533 13.9167 5.95033 13.8833V14.5917C5.95033 14.9333 5.66699 15.2167 5.32533 15.2167C4.98366 15.2167 4.70033 14.9333 4.70033 14.5917V12.3833C4.70033 12.0417 4.98366 11.7583 5.32533 11.7583H7.53366C7.87533 11.7583 8.15866 12.0417 8.15866 12.3833C8.15866 12.725 7.87533 13.0083 7.53366 13.0083H6.84199C6.86699 13.0333 6.88366 13.0583 6.90866 13.0833C7.73366 13.9083 8.83366 14.3667 10.0003 14.3667C11.167 14.3667 12.267 13.9083 13.092 13.0833C13.9087 12.2667 14.367 11.175 14.367 10.0083C14.367 9.66666 14.6503 9.38333 14.992 9.38333C15.3337 9.38333 15.617 9.66666 15.617 10.0083C15.617 11.5167 15.0337 12.925 13.9753 13.975Z",fill:"#A8A8A8","fill-opacity":`${t.path==="updates","1"}`})}),value:"update_requests"},{to:"/stakeholder/companies",text:"  Companies",icon:e.jsx(g,{className:"text-xl text-[#A8A8A8]"}),value:"companies"},{to:"/stakeholder/engagement",text:" Engagements",icon:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M15.3913 14.0251L15.7163 16.6584C15.7997 17.3501 15.058 17.8334 14.4663 17.4751L11.583 15.7584C11.383 15.6417 11.333 15.3917 11.4413 15.1917C11.858 14.4251 12.083 13.5584 12.083 12.6917C12.083 9.64175 9.46633 7.15842 6.24967 7.15842C5.59133 7.15842 4.94967 7.25841 4.34967 7.45842C4.04133 7.55841 3.74133 7.27508 3.81633 6.95842C4.57467 3.92508 7.49133 1.66675 10.9747 1.66675C15.0413 1.66675 18.333 4.74175 18.333 8.53341C18.333 10.7834 17.1747 12.7751 15.3913 14.0251Z",fill:"#A8A8A8","fill-opacity":`${t.path==="engagements"?"1":"0.8"}`}),e.jsx("path",{d:"M10.8327 12.6917C10.8327 13.6833 10.466 14.6 9.84935 15.325C9.02435 16.325 7.71602 16.9667 6.24935 16.9667L4.07435 18.2583C3.70768 18.4833 3.24102 18.175 3.29102 17.75L3.49935 16.1083C2.38268 15.3333 1.66602 14.0917 1.66602 12.6917C1.66602 11.225 2.44935 9.93333 3.64935 9.16666C4.39102 8.68333 5.28268 8.40833 6.24935 8.40833C8.78268 8.40833 10.8327 10.325 10.8327 12.6917Z",fill:"#A8A8A8","fill-opacity":`${t.path==="engagements"?"1":"0.8"}`})]}),value:"engagements"}];let a=s=>{x({type:"OPEN_SIDEBAR",payload:{isOpen:s}})};return e.jsx("div",{className:`z-[10] flex flex-col border border-[#E0E0E0] bg-[#1F1D1A] text-[#A8A8A8] transition-all ${t.isOpen?"absolute left-0 top-0 h-full w-[220px] sm:min-w-[16rem] sm:max-w-[16rem] lg:relative":"relative h-full w-[4.1rem] bg-[#1f1d1a] text-white sm:min-w-[5rem] sm:max-w-[4.2rem]"} `,children:e.jsxs("div",{className:"flex relative flex-col min-w-full max-w-full h-screen min-h-full max-h-full overflow-y-clip",children:[e.jsxs("div",{className:`text-[#393939] ${t.isOpen?"flex w-full":"flex items-center justify-center"} `,children:[!t.isOpen&&e.jsx("div",{className:" absolute -right-3 top-7 z-[20] cursor-pointer",children:e.jsx("div",{onClick:()=>a(!t.isOpen),children:e.jsx("span",{children:e.jsxs("svg",{width:"25",height:"25",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"19",height:"19",rx:"9.5",fill:"#1F1D1A"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"19",height:"19",rx:"9.5",stroke:"#FFF0E5"}),e.jsx("path",{d:"M8.66602 6.66667L11.9993 10L8.66602 13.3333",stroke:"#FFF0E5","stroke-width":"1.33333","stroke-linecap":"round","stroke-linejoin":"round"})]})})})}),e.jsx("div",{}),e.jsxs("div",{className:"flex flex-row justify-between items-center px-5 my-3 w-full text-xl font-bold",children:[e.jsx(m,{to:"",className:"block text-xl font-extrabold text-center text-white",children:e.jsxs("div",{className:"flex gap-2 items-center",children:[!t.isOpen&&e.jsxs("svg",{className:"ml-[-10px] mt-[13px]",width:"39",height:"24",viewBox:"0 0 39 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M33.8839 2.36687V17.8149H5.04853V5.44459H10.8325H19.4661H31.2166V2.36537L2.40017 2.35202V2.36863H1.93164V20.9075H37.0008V2.36863L33.8839 2.36687Z",fill:"#FEF1E5"}),e.jsx("path",{d:"M7.71582 12.6606V15.7531H31.2168V7.50627H19.4663H10.8327H7.71582V10.5989H28.1V12.6606H7.71582Z",fill:"#FEF1E5"})]}),t.isOpen&&e.jsxs("span",{children:["update",e.jsx("span",{className:"font-normal",children:"stack"})]})]})}),t.isOpen&&e.jsx("div",{className:"cursor-pointer",children:e.jsx("div",{onClick:()=>a(!t.isOpen),children:e.jsx("span",{children:e.jsxs("svg",{width:"30",height:"30",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"19",height:"19",rx:"9.5",fill:"#1F1D1A"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"19",height:"19",rx:"9.5",stroke:"#FFF0E5"}),e.jsx("path",{d:"M11.334 6.66667L8.00065 10L11.334 13.3333",stroke:"#FFF0E5","stroke-width":"1.33333","stroke-linecap":"round","stroke-linejoin":"round"})]})})})})]})]}),e.jsx("div",{className:"flex-1 mt-6 w-auto h-fit",children:e.jsx("div",{className:"w-auto sidebar-list2",children:e.jsx("ul",{className:"flex flex-wrap gap-2 px-2 text-sm",children:c.map(s=>e.jsx("li",{className:"block w-full list-none",children:e.jsx(h,{to:s.to,className:`${t.path==s.value?"active-nav font-bold":""} `,children:e.jsxs("div",{className:"flex gap-3 items-center",children:[s.icon,t.isOpen&&e.jsx("span",{children:s.text})]})})},s.value))})})}),e.jsx("div",{className:"flex gap-3 justify-end pb-5 space-y-10",children:e.jsxs(l,{as:"div",className:"relative w-full space-y-3 border-t border-t-white/10 px-2 pt-4 text-[#262626]",children:[e.jsxs(l.Button,{className:"flex w-full items-center justify-center gap-[5px] sm:gap-6",children:[e.jsx("img",{className:`${t.isOpen?"h-10 w-10":"h-5 w-5 xl:h-6 xl:w-6"} rounded-[50%] object-cover`,src:((o=r.profile)==null?void 0:o.photo)??"/default.png",alt:""}),t.isOpen?e.jsx(e.Fragment,{children:e.jsxs("div",{className:"text-left text-white",children:[e.jsxs("p",{className:"w-32 text-sm font-medium truncate",children:[r.profile.first_name," ",r.profile.last_name]}),e.jsx("p",{className:"mt-1 w-[150px] truncate text-xs",children:r.profile.email})]})}):null]}),e.jsx(l.Items,{className:` absolute ${t.isOpen?"-bottom-1 -right-full md:-right-[170px]":"-bottom-1 -right-[170px]"}  mb-8 w-[160px] origin-top-right divide-y divide-gray-100 rounded-md border border-[#1f1d1a] bg-brown-main-bg font-bold text-[#1f1d1a] shadow-lg ring-1 ring-[#1f1d1a] ring-opacity-5 focus:outline-none`,children:e.jsxs("div",{className:"px-1 py-1",children:[e.jsx(l.Item,{children:({active:s})=>e.jsxs("button",{className:`group flex w-full items-center px-3 py-3 text-sm ${s?"border-b border-b-black/20":"border-b border-b-transparent"}`,onClick:()=>{i("/stakeholder/account")},children:[e.jsx(j,{className:"mr-2 w-5 h-5"}),"Account"]})}),e.jsx(l.Item,{children:({active:s})=>e.jsxs("button",{className:`group flex w-full items-center px-3 py-3 text-sm ${s?"border-b border-b-black/20":"border-b border-b-transparent"}`,onClick:()=>{i("/stakeholder/startup-profile")},children:[e.jsx(C,{className:"mr-2 w-5 h-5"}),"Company"]})}),e.jsx(l.Item,{children:({active:s})=>e.jsxs("button",{className:`group flex w-full items-center px-3 py-3 text-sm ${s?"border-b border-b-black/20":"border-b border-b-transparent"}`,onClick:()=>{d({type:"LOGOUT"}),i("/stakeholder/login")},children:[e.jsx(b,{className:"mr-2 w-5 h-5 rotate-90"}),"Logout"]})})]})})]})})]})})};export{q as StartupHeader,q as default};
