import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{R as F,r as q,b as $,L as A}from"./vendor-4cdf2bd1.js";import{u as B}from"./react-hook-form-9f4fcfa9.js";import{o as I}from"./yup-c41d85d2.js";import{c as M,a as r,e as O}from"./yup-342a5df4.js";import{A as T,I as z,M as D,s as K,t as U}from"./index-46de5032.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const pe=()=>{var c,m,p;const{dispatch:i}=F.useContext(T),[l,t]=q.useState(!1),k=window.location.search,P=new URLSearchParams(k).get("token"),E=M({code:r().required(),password:r().required(),confirmPassword:r().oneOf([O("password"),null],"Passwords must match")}).required(),R=$(),{register:a,handleSubmit:S,setError:d,formState:{errors:o}}=B({resolver:I(E)}),C=async x=>{var f,u,h,w,g,b,j,y;let L=new D;try{t(!0);const s=await L.reset(P,x.code,x.password);if(!s.error)K(i,"Password Reset"),setTimeout(()=>{R("/stakeholder/login")},2e3);else if(s.validation){const N=Object.keys(s.validation);for(let n=0;n<N.length;n++){const v=N[n];d(v,{type:"manual",message:s.validation[v]})}}t(!1)}catch(s){t(!1),console.log("Error",s),d("code",{type:"manual",message:(u=(f=s==null?void 0:s.response)==null?void 0:f.data)!=null&&u.message?(w=(h=s==null?void 0:s.response)==null?void 0:h.data)==null?void 0:w.message:s==null?void 0:s.message}),U(i,(b=(g=s==null?void 0:s.response)==null?void 0:g.data)!=null&&b.message?(y=(j=s==null?void 0:s.response)==null?void 0:j.data)==null?void 0:y.message:s==null?void 0:s.message)}};return e.jsx("main",{className:"min-h-screen bg-brown-main-bg",children:e.jsx("div",{className:"flex min-h-full flex-col items-center justify-center",children:e.jsxs("div",{className:"my-12 mt-3 flex w-[300px] flex-col items-center rounded-lg p-4 sm:w-[520px] lg:max-w-[700px]",children:[e.jsx("div",{className:"my-2 text-xl font-semibold text-[#262626]",children:e.jsx("svg",{onClick:()=>window.location.href="https://updatestack.com",width:"294",height:"40",viewBox:"0 0 294 40",fill:"none",xmlns:"http://www.w3.org/2000/svg"})}),e.jsxs("div",{className:"mb-3 flex flex-col items-center justify-center gap-2",children:[e.jsx("span",{className:"whitespace-nowrap font-iowan text-[24px] font-semibold sm:text-[40px]",children:"Reset Your Password"}),e.jsx("span",{className:"text-center text-[14px] font-[500] lg:text-base",children:"Enter the code sent to your email and your new password"})]}),e.jsxs("form",{className:"w-[350px] max-w-[96%] space-y-3 md:min-w-[70%] md:space-y-6",onSubmit:S(C),children:[e.jsxs("div",{className:"mt-[14px] flex flex-col space-y-2 text-sm",children:[e.jsx("label",{htmlFor:"code",className:"font-iowan text-[16px] font-[700]",children:"Code"}),e.jsx("input",{className:"h-[44px] rounded-sm border-[2px] border-[#1f1d1a] bg-transparent px-3 py-2 text-[#1f1d1a] outline-none focus:border-[#1f1d1a] focus:shadow-none focus:outline-none",type:"text",placeholder:"Enter code",...a("code")}),e.jsx("p",{className:"text-xs italic text-red-500",children:(c=o.code)==null?void 0:c.message})]}),e.jsxs("div",{className:"flex flex-col space-y-2 text-sm",children:[e.jsx("label",{htmlFor:"password",className:"font-iowan text-[16px] font-[700]",children:"New Password"}),e.jsx("input",{className:"h-[44px] rounded-sm border-[2px] border-[#1f1d1a] bg-transparent px-3 py-2 text-[#1f1d1a] outline-none focus:border-[#1f1d1a] focus:shadow-none focus:outline-none",type:"password",placeholder:"Enter new password",...a("password")}),e.jsx("p",{className:"text-xs italic text-red-500",children:(m=o.password)==null?void 0:m.message})]}),e.jsxs("div",{className:"flex flex-col space-y-2 text-sm",children:[e.jsx("label",{htmlFor:"confirmPassword",className:"font-iowan text-[16px] font-[700]",children:"Confirm New Password"}),e.jsx("input",{className:"h-[44px] rounded-sm border-[2px] border-[#1f1d1a] bg-transparent px-3 py-2 text-[#1f1d1a] outline-none focus:border-[#1f1d1a] focus:shadow-none focus:outline-none",type:"password",placeholder:"Confirm new password",...a("confirmPassword")}),e.jsx("p",{className:"text-xs italic text-red-500",children:(p=o.confirmPassword)==null?void 0:p.message})]}),e.jsx(z,{type:"submit",className:"my-12 flex h-[44px] w-full items-center justify-center rounded-sm bg-[#1f1d1a] py-2 tracking-wide text-white outline-none focus:outline-none",loading:l,disabled:l,children:e.jsx("span",{className:"capitalize",children:"Reset Password"})})]}),e.jsxs("div",{className:"mt-4 flex items-center text-sm",children:[e.jsxs("span",{className:"mr-1 font-medium text-[#1f1d1a]",children:["Remember your password?"," "]})," ",e.jsx(A,{to:"/stakeholder/login",className:"font-bold text-[#1f1d1a] underline",children:"Login here"})]})]})})})};export{pe as default};
