import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{b as S,r as a}from"./vendor-4cdf2bd1.js";import{c as v,a as p}from"./yup-342a5df4.js";import{u as k}from"./react-hook-form-9f4fcfa9.js";import{M as E}from"./index-bdf26615.js";import{a as q,u as P,I}from"./index-46de5032.js";import{o as M}from"./yup-c41d85d2.js";import{M as f}from"./MkdInput-a0090fba.js";import{u as T}from"./useLocalStorage-53cfe2d8.js";import{l as L}from"./logo5-2e16f0f2.js";import{STEPS as C}from"./SignUpStepOneEmail-1f8f7b1a.js";import{M as F}from"./index-36531d2b.js";import"./@nextui-org/theme-345a09ed.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";const ge=({updateStep:u=null})=>{const{globalState:{completeRegistration:o},custom:x,showToast:h}=q();S();const[m,z]=a.useState("member"),[i,O]=a.useState(3),{localStorageData:l,setLocalStorage:b}=T(["step"]),{profile:s,updateProfile:w}=P({isPublic:!0}),g=v({first_name:p().required("This field is required"),last_name:p().required("This field is required"),password:p().required("This field is required")}),{register:n,handleSubmit:j,setError:N,watch:U,setValue:c,formState:{errors:d,isSubmitting:V}}=k({resolver:M(g),defaultValues:{first_name:"",last_name:"",password:""}}),y=async r=>{try{const t=await x({endpoint:"/v3/api/custom/goodbadugly/users/complete-registration",method:"POST",payload:{first_name:r==null?void 0:r.first_name,last_name:r==null?void 0:r.last_name,password:r==null?void 0:r.password,role:m}},"completeRegistration");t!=null&&t.error||(w({step:i}),b("step",i),u&&u(i))}catch(t){console.error("Error",t),h(t.message,5e3,"error"),N("password",{type:"manual",message:t.message})}};return a.useEffect(()=>{c("password","")},[]),a.useEffect(()=>{s!=null&&s.id&&(c("first_name",s==null?void 0:s.first_name),c("last_name",s==null?void 0:s.last_name))},[s==null?void 0:s.id]),console.log("step two role >>",m),e.jsxs("div",{className:"w-full md:w-[60%] md:px-6",children:[e.jsx("div",{className:"sticky right-0 top-0 z-[9] flex h-[4.5rem] w-full flex-row items-center justify-between bg-[#1f1d1a] px-8 md:hidden",children:e.jsx("img",{src:L,alt:"logo",className:"h-10 w-[180px]"})}),e.jsxs("div",{className:"flex flex-col px-4 py-8 w-full md:px-0",children:[e.jsxs("div",{className:" w-full space-y-[40px] md:space-y-[6.25rem] ",children:[e.jsx(E,{steps:C[m],currentStep:l==null?void 0:l.step,onClick:()=>{},className:""}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-iowan text-[1.5rem] font-[700] sm:text-[2.5rem]",children:"Account Info"}),e.jsx("p",{className:"mb-3 font-normal text-black",children:"Update your account Info"})]})]}),e.jsxs("form",{className:"space-y-8",onSubmit:j(y),children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4 mt-4",children:[e.jsx("div",{className:"",children:e.jsx(f,{type:"text",name:"first_name",label:"First Name",errors:d,register:n,placeholder:"Enter First Name",required:!0,className:"!rounded-[.125rem] !border !border-black placeholder:text-[12px]"})}),e.jsx("div",{className:"",children:e.jsx(f,{type:"text",name:"last_name",label:"Last Name",errors:d,register:n,placeholder:"Enter Last Name",required:!0,className:"!rounded-[.125rem] !border !border-black placeholder:text-[12px]"})})]}),e.jsx("div",{className:"",children:e.jsx(F,{type:"password",name:"password",label:"Password",errors:d,register:n,placeholder:"Enter Password",className:"!rounded-[.125rem] !border !border-black placeholder:text-[12px]",required:!0})}),e.jsx(I,{type:"submit",className:"my-4 flex h-[2.75rem] w-full items-center justify-center rounded-sm bg-[#1f1d1a] py-2 tracking-wide text-white outline-none focus:outline-none",loading:o==null?void 0:o.loading,disabled:o==null?void 0:o.loading,children:e.jsx("span",{className:"capitalize",children:"Continue"})})]})]})]})};export{ge as default};
