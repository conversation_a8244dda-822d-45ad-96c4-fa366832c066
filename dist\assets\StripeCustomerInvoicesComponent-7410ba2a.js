import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as k,R as i}from"./vendor-4cdf2bd1.js";import"./yup-342a5df4.js";import"./@stripe/react-stripe-js-5f217abb.js";import{M as L,A as T,G,s as g,t as M}from"./index-46de5032.js";import{_ as O}from"./qr-scanner-cf010ec4.js";import{R as W}from"./tableWrapper-ca490fb1.js";import w from"./Loader-24da96b3.js";import{C as $}from"./ClipboardDocumentIcon-f03b0627.js";import"./@nextui-org/theme-345a09ed.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./index.esm-7add6cfb.js";import"./react-icons-36ae72b7.js";import"./index-23a711b5.js";const B=k.lazy(()=>O(()=>import("./StripePaginationBar-d32b1a3a.js"),["assets/StripePaginationBar-d32b1a3a.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index.esm-bb52b9ca.js","assets/react-icons-36ae72b7.js"])),j=[{header:"Status",accessor:"status"},{header:"Currency",accessor:"currency"},{header:"Amount due",accessor:"amount_due",type:"currency"},{header:"Amount paid",accessor:"amount_paid",type:"currency"},{header:"Amount remaining",accessor:"amount_remaining",type:"currency"},{header:"Created at",accessor:"created",type:"timestamp"}],xe=()=>{var x,f,h;const y=new L,{dispatch:b,state:F}=i.useContext(T),{dispatch:l}=i.useContext(G),[c,v]=i.useState(null),[t,N]=i.useState({}),[m,u]=i.useState(10),[S,C]=i.useState(!1),[P,_]=i.useState(!1),[p,d]=i.useState(!0);function I(r){(async function(){u(r),await n({limit:r})})()}function R(){(async function(){await n({limit:m,before:t==null?void 0:t.data[0].id})})()}function E(){(async function(){await n({limit:m,after:t==null?void 0:t.data[(t==null?void 0:t.data.length)-1].id})})()}async function n(r){var o,a;try{d(!0);const{list:s,limit:D,error:A,message:z}=await y.getCustomerStripeInvoices(r);if(console.log(s),A&&g(l,z,5e3),!s)return;c||v(((o=s==null?void 0:s.data[0])==null?void 0:o.id)??""),N(s),u(+D),C(c&&c!==((a=s.data[0])==null?void 0:a.id)),_(s.has_more),d(!1)}catch(s){console.error("ERROR",s),g(l,s.message,5e3),M(b,s.message),d(!1)}}return i.useEffect(()=>{n({})},[]),p?e.jsx(w,{}):e.jsx("div",{className:"overflow-hidden p-5 px-5 pt-8 rounded bg-brown-main-bg sm:px-8",children:e.jsxs("div",{className:"bg-brown-main-bg",children:[e.jsxs("div",{className:"p-5 px-0 w-full rounded",children:[e.jsx("div",{className:"flex justify-between mb-6 w-full text-center",children:e.jsx("div",{className:"flex flex-row justify-between w-full",children:e.jsx("div",{className:"text-left text-[16px] font-[600] sm:text-[20px]",children:"Invoices"})})}),e.jsx("div",{className:`${p?"":"custom-overflow overflow-x-auto"}`,children:p?e.jsx("div",{className:"flex justify-center items-center py-5 max-w-full max-h-fit min-h-fit min-w-fit",children:e.jsx(w,{size:50})}):e.jsx(W,{children:e.jsxs("table",{className:"min-w-full divide-y divide-[#1f1d1a]/10",children:[e.jsx("thead",{className:"",children:e.jsx("tr",{children:j.map((r,o)=>e.jsx("th",{scope:"col",className:"font  whitespace-nowrap border-b-[#1f1d1a]/10  px-4 text-left font-[700] md:border-0 md:border-b-[3px] md:border-dashed md:px-6 md:py-3",children:r.header},o))})}),e.jsx("tbody",{className:"font-iowan-regular  divide-y divide-[#1f1d1a]/10",children:(x=t==null?void 0:t.data)==null?void 0:x.map((r,o)=>e.jsx("tr",{children:j.map((a,s)=>a.accessor==""?e.jsx("td",{className:"px-6 py-4 whitespace-nowrap"},s):a.mapping?e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.mapping[r[a.accessor]]},s):a.type==="timestamp"?e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:new Date(r[a.accessor]*1e3).toLocaleString("en-US")},s):a.type==="currency"?e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap",children:["$",Number(r[a.accessor]/100).toFixed(2)]},s):e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:r[a.accessor]},s))},o))})]})})})]}),((f=t==null?void 0:t.data)==null?void 0:f.length)==0?e.jsxs("div",{className:"mb-[20px] mt-24 flex flex-col items-center",children:[e.jsx($,{className:"w-8 h-8 text-gray-700",strokeWidth:2}),e.jsx("p",{className:"mt-4 text-base font-medium text-center",children:"No Invoices"})]}):null,((h=t==null?void 0:t.data)==null?void 0:h.length)>0&&e.jsx("div",{className:"p-5 bg-brown-main-bg",children:e.jsx(B,{pageSize:m,canPreviousPage:S,canNextPage:P,updatePageSize:I,previousPage:R,nextPage:E})})]})})};export{xe as default};
