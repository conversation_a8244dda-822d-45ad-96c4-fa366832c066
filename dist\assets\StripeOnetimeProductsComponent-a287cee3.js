import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{R as c,r as j}from"./vendor-4cdf2bd1.js";import{M as w,A as N,G as S,s as o,t as m}from"./index-46de5032.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const q=()=>{const r=new w,{dispatch:l,state:v}=c.useContext(N),{dispatch:s}=c.useContext(S),[d,p]=j.useState([]),u=async({priceId:e,priceCurrency:n,priceStripeId:x,priceName:g,productName:_,quantity:f=1})=>{let b={success_url:`${r.fe_baseurl}/user/checkout?success=true&session_id={CHECKOUT_SESSION_ID}`,cancel_url:`${r.fe_baseurl}/user/checkout?success=false&session_id={CHECKOUT_SESSION_ID}`,mode:"payment",payment_method_types:["card"],shipping_address_collection:{allowed_countries:["CA","US"]},shipping_options:[{shipping_rate_data:{type:"fixed_amount",display_name:"Shipping fees",fixed_amount:{currency:n,amount:500}}}],locale:"en",line_items:[{price:x,quantity:f}],phone_number_collection:{enabled:!1},payment_intent_data:{metadata:{app_price_id:e,app_product_name:g,app_highlevel_product_name:_,is_order:"true"}}};try{const{error:a,model:i,message:y}=await r.initCheckoutSession(b);if(a){o(s,y);return}i!=null&&i.url&&(location.href=i.url)}catch(a){console.error("Error",a),o(s,a.message,7500),m(l,a.code)}},h=async()=>{try{const e=await r.getStripePrices({limit:"all"},{type:"one_time"});if(e.error){o(s,e.message,7500);return}p(e.list)}catch(e){o(s,e.message,7500),m(l,e.code)}};return c.useEffect(()=>{s({type:"SETPATH",payload:{path:"billing"}}),h()},[]),t.jsxs("div",{className:"my-4 rounded bg-brown-main-bg p-5 shadow-lg",children:[t.jsx("h2",{className:"mb-3 mt-0 text-xl font-medium leading-tight text-[#1f1d1a]",children:"Products"}),t.jsx("div",{className:"container flex flex-wrap py-5",children:d.map((e,n)=>t.jsx("div",{className:"mx-1 flex justify-center border",children:t.jsxs("div",{className:"max-w-sm rounded-lg bg-brown-main-bg shadow-lg",children:[t.jsx("div",{className:"",children:t.jsx("img",{className:"mx-auto max-h-60 rounded-t-lg object-cover",src:"https://cdn.shopify.com/s/files/1/0533/2089/files/placeholder-images-product-1_large.png?format=jpg&quality=90&v=1530129292",alt:""})}),t.jsxs("div",{className:"p-6 text-center",children:[t.jsx("h3",{className:"mb-2 text-xl font-medium text-gray-900",children:e.product_name}),t.jsx("h5",{className:"mb-2 text-xl font-medium text-slate-500",children:e.name}),t.jsxs("p",{className:"mb-4 text-base text-gray-700",children:["$",+e.amount]}),t.jsx("button",{onClick:()=>u({priceId:e.id,priceName:e.name,productName:e.product_name,priceStripeId:e.stripe_id,priceCurrency:e.currency}),type:"button",className:" inline-block rounded bg-blue-600 px-6 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-blue-700 hover:shadow-lg focus:bg-blue-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-blue-800 active:shadow-lg",children:"Buy"})]})]})},n))})]})};export{q as default};
