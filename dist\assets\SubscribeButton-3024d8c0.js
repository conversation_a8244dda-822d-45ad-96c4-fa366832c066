import{j as c}from"./@nextui-org/listbox-0f38ca19.js";import{u as Q,I as W,bT as X}from"./index-46de5032.js";import{u as P}from"./useSubscription-58f7fe18.js";import{r as g}from"./vendor-4cdf2bd1.js";import{A as D}from"./index-a807e4ab.js";import ee from"./EditPaymentMethodModal-1a7612af.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const se={free:"#1f1d1a",pro:"#fff0e5",business:"#fff0e5",enterprise:"#fff0e5"},t={free:0,"pro monthly":1,"business monthly":2,"enterprise monthly":3,"pro yearly":4,"business yearly":5,"enterprise yearly":6,pro:1,business:2,enterprise:3},je=({plan:C,name:m,currency:L,onSuccess:A,currentPlan:i,focusYearly:h,allPlans:j=[]})=>{var v,T,a,O,V;const{loading:_,data:n,handleSubscription:$,handleUpgradeSubscription:F,updateData:H,getCardData:N}=P(),[e,y]=g.useState({modal:null,showModal:!1,currentPrice:null,selectedPrice:null,action:"subscribe",pendingAction:null}),[b,S]=g.useState(""),[k,d]=g.useState(""),[z,M]=g.useState(!1),{profile:x}=Q(),E=(s,o)=>{if(!s||!s.trim())return{isValid:!0,error:null};const r=s.trim().toUpperCase();return["3_MONTH_DISCOUNT"].includes(r)&&o?{isValid:!1,error:"This coupon code is only allowed for monthly plans. Please select a monthly plan to use this coupon."}:{isValid:!0,error:null}},w=(s,o)=>{y(r=>({...r,modal:s,showModal:o})),!o&&s==="subscribe"&&(S(""),d(""))},R=async()=>{var r,f;d(""),M(!0);const o=E(b,h);if(!o.isValid){d(o.error),M(!1);return}if(["subscribe"].includes(e==null?void 0:e.action))try{const u=b.trim()||null;await $((r=e==null?void 0:e.selectedPrice)==null?void 0:r.id,u),w("subscribe",!1),S(""),d("")}catch(u){console.log(u),u.message&&u.message.toLowerCase().includes("coupon")?d("Invalid coupon code. Please check and try again."):d("An error occurred. Please try again.");return}finally{M(!1)}if(["upgrade","downgrade"].includes(e==null?void 0:e.action))try{const u=b.trim()||null;await F((f=e==null?void 0:e.selectedPrice)==null?void 0:f.id,u),w("subscribe",!1),S(""),d("")}catch(u){console.log(u),u.message&&u.message.toLowerCase().includes("coupon")?d("Invalid coupon code. Please check and try again."):d("An error occurred. Please try again.");return}finally{M(!1)}},Y=()=>{var r,f;const s=t==null?void 0:t[(r=e==null?void 0:e.currentPrice)==null?void 0:r.name],o=t==null?void 0:t[(f=e==null?void 0:e.selectedPrice)==null?void 0:f.name];return s>o?"downgrade":"upgrade"},Z=()=>{var s;if(i!=null&&i.price_id){if(String(i==null?void 0:i.price_id)===String((s=e==null?void 0:e.selectedPrice)==null?void 0:s.id))return"Current Plan";{if(m==="free")return"Free Plan";const o=Y();return`${X(o)} Plan`}}else return["free"].includes(m)?"Current Plan":"Subscribe"},q=()=>{if(!(n!=null&&n.card)||n!=null&&n.card&&(n!=null&&n.cardExpired)){y(s=>({...s,pendingAction:"subscribe",modal:"add_card",showModal:!0})),console.log("Setting pendingAction to subscribe");return}w("subscribe",!0)};return g.useEffect(()=>{(n==null?void 0:n.subscriptionStatus)==="success"&&A&&(H({subscriptionStatus:null}),A())},[n==null?void 0:n.subscriptionStatus]),g.useEffect(()=>{x!=null&&x.id&&N()},[x==null?void 0:x.id]),g.useEffect(()=>{const s=j==null?void 0:j.flatMap(r=>r==null?void 0:r.stripe_price),o=s==null?void 0:s.find(r=>String(r==null?void 0:r.id)===String(i==null?void 0:i.price_id));console.log("SubscribeButton Debug:",{currentPlanPriceId:i==null?void 0:i.price_id,foundCurrentPrice:o,allPricesCount:s==null?void 0:s.length,planName:m}),y(r=>({...r,currentPrice:o}))},[i==null?void 0:i.price_id,j,m]),g.useEffect(()=>{var u,U,B,I;if(!h){const p=(u=C==null?void 0:C.stripe_price)==null?void 0:u.find(l=>(l==null?void 0:l.name)==`${m} monthly`||(l==null?void 0:l.name)==m),G=t==null?void 0:t[(U=e==null?void 0:e.currentPrice)==null?void 0:U.name],J=t==null?void 0:t[p==null?void 0:p.name],K=i!=null&&i.price_id?G>J?"downgrade":"upgrade":"subscribe";return y(l=>({...l,action:K,selectedPrice:p}))}const s=(B=C==null?void 0:C.stripe_price)==null?void 0:B.find(p=>(p==null?void 0:p.name)==`${m} yearly`),o=t==null?void 0:t[(I=e==null?void 0:e.currentPrice)==null?void 0:I.name],r=t==null?void 0:t[s==null?void 0:s.name],f=i!=null&&i.price_id?o>r?"downgrade":"upgrade":"subscribe";y(p=>({...p,action:f,selectedPrice:s}))},[(v=e==null?void 0:e.selectedPrice)==null?void 0:v.id,(T=e==null?void 0:e.currentPrice)==null?void 0:T.id,h]),g.useEffect(()=>{if(b.trim()){const o=E(b,h);o.isValid?d(""):d(o.error)}},[h,b]),c.jsxs("div",{children:[c.jsxs(W,{type:"button",loading:!1,disabled:["free"].includes(m)||(_==null?void 0:_.card)||String(i==null?void 0:i.price_id)===String((a=e==null?void 0:e.selectedPrice)==null?void 0:a.id),onClick:q,className:`!flex !h-[3.5rem] !w-full !items-center !justify-center !gap-2 !rounded-[.125rem] !border !border-primary-black !p-3 font-iowan !text-[1rem] !font-[700] !leading-[1.2431rem] !tracking-wide  ${["free"].includes(m)?"!bg-[#fff0e5] !text-primary-black":"!bg-primary-black !text-white"}`,children:[Z(),["free"].includes(m)?null:c.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"min-h-6 min-w-6",children:c.jsx("path",{d:"M14.707 5.63585L20.364 11.2929C20.5515 11.4804 20.6568 11.7347 20.6568 11.9999C20.6568 12.265 20.5515 12.5193 20.364 12.7069L14.707 18.3639C14.5184 18.546 14.2658 18.6468 14.0036 18.6445C13.7414 18.6423 13.4906 18.5371 13.3052 18.3517C13.1198 18.1663 13.0146 17.9155 13.0123 17.6533C13.01 17.3911 13.1108 17.1385 13.293 16.9499L17.243 12.9999H4C3.73478 12.9999 3.48043 12.8945 3.29289 12.707C3.10536 12.5194 3 12.2651 3 11.9999C3 11.7346 3.10536 11.4803 3.29289 11.2927C3.48043 11.1052 3.73478 10.9999 4 10.9999H17.243L13.293 7.04985C13.1975 6.95761 13.1213 6.84726 13.0689 6.72526C13.0165 6.60325 12.9889 6.47204 12.9877 6.33926C12.9866 6.20648 13.0119 6.0748 13.0622 5.9519C13.1125 5.829 13.1867 5.71735 13.2806 5.62346C13.3745 5.52957 13.4861 5.45531 13.609 5.40503C13.7319 5.35475 13.8636 5.32945 13.9964 5.3306C14.1292 5.33176 14.2604 5.35934 14.3824 5.41175C14.5044 5.46416 14.6148 5.54034 14.707 5.63585Z",fill:se[m]})})]}),c.jsx(ee,{isOpen:(e==null?void 0:e.showModal)&&["add_card"].includes(e==null?void 0:e.modal),onClose:()=>w(null,!1),onSuccess:()=>{console.log("EditPaymentMethodModal onSuccess called, pendingAction:",e==null?void 0:e.pendingAction),(e==null?void 0:e.pendingAction)==="subscribe"?(console.log("Proceeding to subscribe modal after card added"),setTimeout(()=>{y(s=>({...s,pendingAction:null,modal:"subscribe",showModal:!0}))},300)):w(null,!1),N()},title:"Add a Card"}),c.jsx(D,{title:e==null?void 0:e.action,mode:"manual",action:e==null?void 0:e.action,multiple:!1,onSuccess:()=>{R()},inputConfirmation:!1,customLoading:z,onClose:()=>{w("subscribe",!1),S(""),d("")},customMessage:c.jsxs("div",{className:"space-y-5",children:[c.jsxs("div",{children:["Plan: ",(O=e==null?void 0:e.selectedPrice)==null?void 0:O.name]}),c.jsx("div",{className:"flex gap-5 justify-between items-center",children:c.jsxs("span",{children:["Price ",L,(V=e==null?void 0:e.selectedPrice)==null?void 0:V.amount,b.trim()&&c.jsxs("span",{className:"ml-2 text-sm text-green-600",children:["(Coupon: ",b.trim(),")"]})]})}),c.jsxs("div",{className:"space-y-2",children:[c.jsx("label",{className:"block text-sm font-medium text-black font-iowan",children:"Coupon Code (Optional)"}),c.jsx("input",{type:"text",value:b,onChange:s=>{const o=s.target.value;if(S(o),!o.trim()){d("");return}const f=E(o,h);f.isValid?d(""):d(f.error)},placeholder:"Enter coupon code (e.g., vip-access-100)",className:"px-3 py-2 w-full bg-transparent rounded-md border border-black shadow-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"}),k&&c.jsx("p",{className:"text-sm text-red-600",children:k})]})]}),isOpen:(e==null?void 0:e.showModal)&&["subscribe"].includes(e==null?void 0:e.modal)})]})};export{je as default};
