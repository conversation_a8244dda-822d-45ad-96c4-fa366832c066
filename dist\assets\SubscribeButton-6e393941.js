import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{A,G as O,I as L,M as W,t as G,s as b}from"./index-46de5032.js";import{b as Z,r as i,i as q}from"./vendor-4cdf2bd1.js";import{h as J}from"./moment-a9aaa855.js";import{E as V,u as y,a as P,C as Y}from"./@stripe/react-stripe-js-5f217abb.js";import{t as f,S as j}from"./@headlessui/react-cdd9213e.js";import{l as ee}from"./@stripe/stripe-js-6b714a86.js";import"./index-6edcbb0d.js";import{L as te}from"./index-23a711b5.js";import{X as se}from"./XMarkIcon-cfb26fe7.js";const ie=({open:c,setOpen:t})=>(Z(),i.useContext(A),i.use<PERSON>ontext(O),e.jsx(f,{appear:!0,show:c,as:i.Fragment,children:e.jsxs(j,{as:"div",className:"relative z-[7000]",onClose:()=>t(!0),children:[e.jsx(f.Child,{as:i.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(f.Child,{as:i.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(j.Panel,{className:"w-full max-w-[500px] transform space-y-10 overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-sm shadow-xl transition-all",children:[e.jsx("h5",{className:"mt-6 text-center",children:"Successful!!!"}),e.jsx(L,{onClick:()=>logout(),className:"disabled:bg-disabledblack mt-7 w-full rounded-lg bg-primary-700 py-2 text-center text-lg font-semibold text-white transition-colors duration-100",children:"Ok"})]})})})})]})})),M=new W,oe=({refetch:c,updateStep:t,planID:s,planAmount:u,buttonTitle:w,activeSub:o,subscribe:n,trial:r=null})=>{const d=ee("pk_test_51Ll5ukBgOlWo0lDUrBhA2W7EX2MwUH9AR5Y3KQoujf7PTQagZAJylWP1UOFbtH4UwxoufZbInwehQppWAq53kmNC00UIKSmebO");return e.jsx(V,{stripe:d,children:e.jsx(ae,{refetch:c,updateStep:t,planAmount:u,buttonTitle:w,planID:s,activeSub:o,subscribe:n,trial:r})})},z=oe;function ae({refetch:c=()=>{},updateStep:t=null,planID:s,planAmount:u,buttonTitle:w,activeSub:o,subscribe:n,trial:r=null}){const[d,m]=i.useState(!1),[k,p]=i.useState(!1),{dispatch:_}=i.useContext(A),{dispatch:l}=i.useContext(O),[C,N]=i.useState(!1),[B,F]=i.useState(""),[S,T]=i.useState(""),[I,D]=i.useState(!1);i.useState("");const U=y(),$=P(),R=()=>{m(!0)},H=async v=>{try{const a=await M.callRawAPI("/v3/api/custom/goodbadugly/payment-intent/create",{amount:u},"POST");F(a==null?void 0:a.client_secret),T(a.payment_intent)}catch(a){console.log(a),G(_,a.message)}},K=async v=>{N(!0),v.preventDefault();try{const a=await U.createToken($.getElement(Y));if(a.error)return b(l,a.error||"Something went wrong");const Q={cardToken:a.token.id};let X,g;if(o!=null&&o.subId?r||await n(s):(X=await M.createStripeCustomer(Q),r||(g=await M.createStripeSubscription({planId:s}))),t&&t(),c(),N(!1),!a.error)b(l,"Card added successfully");else if(a.validation){const h=Object.keys(a.validation);for(let x=0;x<h.length;x++){const E=h[x];b(l,a.validation[E],3e3)}}if(console.log(g),!(g!=null&&g.error))b(l,"Plan subscribed successfully");else if(a.validation){const h=Object.keys(a.validation);for(let x=0;x<h.length;x++){const E=h[x];b(l,a.validation[E],3e3)}}}catch{}};return i.useEffect(()=>{d===!0&&H()},[d]),e.jsxs(e.Fragment,{children:[!d&&e.jsx("button",{onClick:()=>R(),disabled:o===s,className:`flex h-[70px] flex-row items-center justify-center  gap-2 rounded-lg border border-[#1f1d1a]/30 py-2 text-center font-iowan text-[15px] font-medium  font-semibold ${o===s&&"!bg-green-700 !text-white"}`,type:"button",children:e.jsx("div",{className:"flex flex-col gap-2",children:e.jsx("span",{children:r?"Enter Card Details":`${u} ${w}`})})}),d&&e.jsx("div",{className:"tems-center flex justify-center  ",children:e.jsxs("form",{className:"w-full max-w-md transform overflow-hidden border-0 bg-brown-main-bg p-6 text-left align-middle text-sm transition-all",onSubmit:v=>K(v),children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:r?"Card Details":"Confirm card payment"})}),e.jsx(Y,{className:"mb-3 rounded p-4 shadow-inner",options:{hidePostalCode:!0,style:{base:{backgroundColor:"",fontSize:"14px",lineHeight:"20px"}}},onReady:()=>D(!0)}),!I&&e.jsx("div",{className:"flex w-full justify-center",children:e.jsx(te,{})}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a]/30 py-2 text-center font-iowan font-medium",type:"button",onClick:()=>m(!1),children:"Cancel"}),e.jsx(L,{type:"submit",loading:C,disabled:C,className:"disabled:bg-disabledblack rounded-lg bg-primary-700 py-2 text-center font-semibold text-white transition-colors duration-100",children:r})]})]})}),e.jsx(ie,{open:k,setOpen:p})]})}new W;function ge({refetch:c=()=>{},monthlyPlan:t,yearlyPlan:s,subscribe:u,loading:w,activeSub:o,recentSub:n,isYearly:r,updateStep:d=null,trial:m=null}){const[k,p]=i.useState(!1),[_,l]=i.useState(!1);i.useContext(A),i.useContext(O),q();const C=()=>{p(!0)};function N(D){return` ${J(D).format("D, MMMM, YYYY")}`}const B=o&&o.subId&&o.planId,F=o&&o.planId===(t==null?void 0:t.id),S=o&&o.planId===(s==null?void 0:s.id),T=B?F||S?"Current Plan":"Change Plan":"Subscribe",I=n.price_id==(t==null?void 0:t.id)||n.price_id==(s==null?void 0:s.id)?N(n==null?void 0:n.plan_end_date):"Cancelled Plan";return e.jsxs(e.Fragment,{children:[n.cancelled===1?e.jsx("button",{className:`mx-auto mt-6 w-fit cursor-pointer ${n.price_id==(t==null?void 0:t.id)||o==(s==null?void 0:s.id)?"border-[5px] border-red-500 bg-brown-main-bg font-bold text-[#1f1d1a]":"bg-primary text-white"}  leading-normalshadow-md w-full max-w-[170px] rounded bg-primary px-4 py-1.5 text-xs font-medium uppercase transition duration-150 ease-in-out  hover:text-white focus:outline-none`,children:I}):e.jsx("button",{className:`mx-auto mt-6 w-fit cursor-pointer ${o===(t==null?void 0:t.id)||o===(s==null?void 0:s.id),"bg-[#1f1d1a] text-white"} hover:text-whi  rounded px-8 py-3 text-xs font-medium capitalize  leading-normal shadow-md transition duration-150 ease-in-out hover:bg-[#1f1d1a]/90 focus:outline-none`,onClick:()=>{C()},children:m??T}),e.jsx(f,{appear:!0,show:k,as:i.Fragment,children:e.jsxs(j,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>{l(!1),p(!1)},children:[e.jsx(f.Child,{as:i.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"overflow-y-auto fixed inset-0",children:e.jsx("div",{className:"flex justify-center items-center p-4 min-h-full text-center",children:e.jsx(f.Child,{as:i.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(j.Panel,{className:"w-full max-w-[500px] transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-sm shadow-xl transition-all",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(j.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:m?"Card Details":"Upgrade Plan"}),e.jsx("button",{onClick:()=>{l(!1),p(!1)},type:"button",children:e.jsx(se,{className:"w-6 h-6"})})]}),e.jsx("div",{className:"grid grid-cols-1 gap-4 mt-6",children:r?e.jsx(z,{refetch:c,updateStep:d,planAmount:s==null?void 0:s.amount,buttonTitle:"Billed Anually",planID:s==null?void 0:s.id,activeSub:o,subscribe:u,trial:m}):e.jsx(z,{planAmount:t==null?void 0:t.amount,buttonTitle:"Billed Monthly",planID:t==null?void 0:t.id,activeSub:o,refetch:c,updateStep:d,subscribe:u,trial:m})})]})})})})]})})]})}export{ge as S};
