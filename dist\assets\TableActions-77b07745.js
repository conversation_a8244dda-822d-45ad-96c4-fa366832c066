import{j as p}from"./@nextui-org/listbox-0f38ca19.js";import"./vendor-4cdf2bd1.js";import{o as e}from"./index-46de5032.js";import{A as g}from"./AddButton-51d1b2cd.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const S=({actions:i,selectedItems:l})=>p.jsx("div",{className:"flex gap-2",children:Object.keys(i).map(r=>i[r].show).includes(!0)?p.jsx(p.Fragment,{children:Object.keys(i).map(r=>{var a,t,n,d,u,m,f,x;if(i[r].show&&!["static"].includes(i[r].type)&&!["select","add","export"].includes(r)){if(l&&(l==null?void 0:l.length)===1&&!((a=i[r])!=null&&a.multiple))return p.jsx(g,{showPlus:!1,loading:((t=i[r])==null?void 0:t.loading)??!1,disabled:((n=i[r])==null?void 0:n.disabled)??!1,icon:((d=i[r])==null?void 0:d.icon)??null,className:`!h-[2.5rem] cursor-pointer px-2 py-2 text-lg  font-medium leading-loose tracking-wide ${r==="view"?"text-blue-500":r==="delete"?"text-red-500":"text-[#292829fd]"} hover:underline`,onClick:()=>{var o;(o=i[r])!=null&&o.action&&i[r].action(l)},children:e(r,{casetype:"capitalize",separator:" "})},r);if(l&&(l==null?void 0:l.length)>=1&&((u=i[r])!=null&&u.multiple))return p.jsx(g,{showPlus:!1,loading:((m=i[r])==null?void 0:m.loading)??!1,disabled:((f=i[r])==null?void 0:f.disabled)??!1,icon:((x=i[r])==null?void 0:x.icon)??null,className:`!h-[2.5rem] cursor-pointer px-2 py-2 text-lg  font-medium leading-loose tracking-wide ${r==="view"?"text-blue-500":r==="delete"?"text-red-500":"text-[#292829fd]"} hover:underline`,onClick:()=>{var o;(o=i[r])!=null&&o.action&&i[r].action(l)},children:e(r,{casetype:"capitalize",separator:" "})},r)}}).filter(Boolean)}):null});export{S as default};
