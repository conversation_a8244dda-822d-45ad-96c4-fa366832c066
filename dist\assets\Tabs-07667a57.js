import{j as o}from"./@nextui-org/listbox-0f38ca19.js";import{r as a}from"./vendor-4cdf2bd1.js";import{o as p}from"./index-46de5032.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const c=({tab:e,setView:l,view:i,viewsMap:t})=>{var r,m,s,n;return o.jsxs("div",{onClick:()=>l(e),className:`flex h-full w-fit min-w-[6.8125rem] cursor-pointer items-center justify-center gap-1  py-2 font-iowan text-[18px] capitalize md:text-[20px] ${i===((r=t[e])==null?void 0:r.value)?" border-b-[6px] border-black text-black":"font-iowan-regular font-normal "}`,children:[p(e,{casetype:"capitalize",separator:" "}),(m=t[e])!=null&&m.hasCount?o.jsx("div",{className:`flex h-[1.25rem] w-[1.25rem] items-center justify-center rounded-full font-inter text-[.75rem] font-[600] leading-[1rem] ${i===((s=t[e])==null?void 0:s.value)?"bg-black text-white":"bg-weak-100 text-sub-500"}`,children:(n=t[e])==null?void 0:n.count}):null]})},z=({tabs:e=[],setView:l,view:i,viewsMap:t})=>(a.useRef(),a.useRef([]),100/e.length,o.jsx("div",{className:"scrollbar-hide shadow-soft-200 flex !h-[3rem] w-full max-w-full overflow-x-auto border-b-2 border-[#1f1d1a]/10 shadow-sm md:overflow-x-clip ",children:o.jsx("div",{className:"flex h-full shrink-0 items-center justify-between gap-[1.5rem]",children:e.map((r,m)=>o.jsx(c,{tab:r,view:i,type:"tab",viewsMap:t,setView:s=>{l(s)}},r))})}));export{z as default};
