import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{a as p,L as a,p as c,a0 as f,T as h}from"./index-46de5032.js";import"./vendor-4cdf2bd1.js";import{R as w,A as b,a as g,b as u}from"./Teams-487b9fbe.js";import{h as x}from"./moment-a9aaa855.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./yup-c41d85d2.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-hook-form-9f4fcfa9.js";import"./yup-342a5df4.js";import"./index-d0de8b06.js";import"./MkdInput-a0090fba.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";import"./AddButton-51d1b2cd.js";import"./MkdListTableV2-bda31faf.js";import"./index-3283c9b7.js";import"./ExportButton-eb4cf1f9.js";import"./index.esm-54e24cf9.js";import"./react-icons-36ae72b7.js";import"./lucide-react-0b94883e.js";import"./ClipboardDocumentIcon-f03b0627.js";const j={add:"Add Team Member",resend:"Resend Invite",accept:"Accept Invite",reject:"Reject Invite",delete:"Remove Team Member"},s={add:{modalDialog:"h-fit max-h-fit min-h-fit w-full min-w-full md:!w-fit !max-w-full md:!min-w-fit !gap-0 ",modalContent:"!bg-brown-main-bg !z-10 !mt-0 overflow-hidden !py-0"},resend:{modalDialog:"h-fit max-h-fit min-h-fit md:!w-fit !w-full",modalContent:"!bg-brown-main-bg !z-10 !mt-0 overflow-hidden !py-0"},accept:{modalDialog:"h-fit max-h-fit min-h-fit md:!w-fit !w-full",modalContent:"!bg-brown-main-bg !z-10 !mt-0 overflow-hidden !py-0"},reject:{modalDialog:"h-fit max-h-fit min-h-fit md:!w-fit !w-full",modalContent:"!bg-brown-main-bg !z-10 !mt-0 overflow-hidden !py-0"},delete:{modalDialog:"h-fit max-h-fit min-h-fit md:!w-fit !w-full",modalContent:"!bg-brown-main-bg !z-10 !mt-0 overflow-hidden !py-0"}},v=async(e,i,m,o)=>{console.log(e,o);const r=new h;try{console.log("id >>",e,"resending"),await r.update("company_invites",e,{invited_at:x().format("YYYY-MM-DD HH:mm:ss")}),i("Invite resent successfully"),m()}catch(n){console.error("Error resending invitation:",n),i("Failed to resend invitation",5e3,"error")}},ne=({ids:e,isOpen:i,onClose:m,modal:o="",onSuccess:r})=>{var l,d;const{showToast:n}=p();return console.log(i,"isopen",o,e,"memberid"),t.jsx(a,{children:t.jsxs(c,{modalHeader:!0,title:j[o],isOpen:i,modalCloseClick:m,classes:{modalDialog:(l=s[o])==null?void 0:l.modalDialog,modalContent:(d=s[o])==null?void 0:d.modalContent,modal:"h-full"},children:[i&&["add"].includes(o)&&t.jsx(a,{children:t.jsx(f,{onSuccess:r})}),i&&["resend"].includes(o)&&t.jsx(a,{children:t.jsx(w,{id:e[1],onSuccess:()=>{v(e[0],n,r,e[1])},onClose:m})}),i&&["accept"].includes(o)&&t.jsx(a,{children:t.jsx(b,{id:e[0],memberId:e[1],onSuccess:()=>{r()},onClose:m})}),i&&["reject"].includes(o)&&t.jsx(a,{children:t.jsx(g,{id:e[0],memberId:e[1],onSuccess:()=>{r()},onClose:m})}),i&&["delete"].includes(o)&&t.jsx(a,{children:t.jsx(u,{id:e[0],memberId:e[1],onSuccess:r,onClose:m})})]})})};export{ne as default};
