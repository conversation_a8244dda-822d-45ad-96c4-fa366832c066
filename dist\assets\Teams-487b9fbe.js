import{j as s}from"./@nextui-org/listbox-0f38ca19.js";import{o as je}from"./yup-c41d85d2.js";import{b as ke,a as Ne,u as Ae,M as V,au as Ce,I as z,a6 as Pe,L as ie,d as Ie,av as Le}from"./index-46de5032.js";import{r as o,h as Re,R as Me,b as Oe}from"./vendor-4cdf2bd1.js";import{u as $e}from"./react-hook-form-9f4fcfa9.js";import{c as Te,a as U}from"./yup-342a5df4.js";import{M as Fe}from"./index-d0de8b06.js";import{M as q}from"./MkdInput-a0090fba.js";import{A as Ve}from"./AddButton-51d1b2cd.js";import{getCorrectOperator as Q,getCorrectValueTypeFormat as ze}from"./MkdListTableV2-bda31faf.js";import{_ as j}from"./qr-scanner-cf010ec4.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import{C as Ue}from"./ClipboardDocumentIcon-f03b0627.js";const qe=o.lazy(()=>j(()=>import("./TeamModal-e1778085.js"),["assets/TeamModal-e1778085.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-46de5032.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-d88d3663.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/yup-c41d85d2.js","assets/@hookform/resolvers-fad2f3d1.js","assets/react-hook-form-9f4fcfa9.js","assets/yup-342a5df4.js","assets/index-d0de8b06.js","assets/MkdInput-a0090fba.js","assets/react-toggle-6478c5c4.js","assets/@uppy/dashboard-51133bb7.js","assets/@fullcalendar/core-085b11ae.js","assets/core-b9802b0d.css","assets/@uppy/core-a4ba4b97.js","assets/@uppy/aws-s3-a6b02742.js","assets/@craftjs/core-a2cdaeb4.js","assets/@uppy/compressor-4bcbc734.js","assets/MkdInput-5e6afe8d.css","assets/AddButton-51d1b2cd.js","assets/MkdListTableV2-bda31faf.js","assets/index-3283c9b7.js","assets/ExportButton-eb4cf1f9.js","assets/index.esm-54e24cf9.js","assets/react-icons-36ae72b7.js","assets/lucide-react-0b94883e.js","assets/MkdListTableV2-e3b0c442.css","assets/ClipboardDocumentIcon-f03b0627.js"])),lt=o.lazy(()=>j(()=>import("./RemoveTeamMember-fc217a4b.js"),["assets/RemoveTeamMember-fc217a4b.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-2d79ce9c.js","assets/qr-scanner-cf010ec4.js","assets/index-46de5032.js","assets/react-confirm-alert-1abd021a.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-d88d3663.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css"])),ct=o.lazy(()=>j(()=>import("./AcceptTeamRequest-513be316.js"),["assets/AcceptTeamRequest-513be316.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-2d79ce9c.js","assets/qr-scanner-cf010ec4.js","assets/index-46de5032.js","assets/react-confirm-alert-1abd021a.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-d88d3663.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css"])),dt=o.lazy(()=>j(()=>import("./RejectTeamRequest-365550bc.js"),["assets/RejectTeamRequest-365550bc.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-2d79ce9c.js","assets/qr-scanner-cf010ec4.js","assets/index-46de5032.js","assets/react-confirm-alert-1abd021a.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-d88d3663.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css"])),ut=o.lazy(()=>j(()=>import("./ResendTeamRequest-175d8672.js"),["assets/ResendTeamRequest-175d8672.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-2d79ce9c.js","assets/qr-scanner-cf010ec4.js","assets/index-46de5032.js","assets/react-confirm-alert-1abd021a.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-d88d3663.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/useCompanyMember-f698e8a0.js"])),Qe=k=>{let N=[];return new Set(k.map(n=>n==null?void 0:n.accessor)).forEach(n=>{const i=k.filter(a=>a.accessor===n);if((i==null?void 0:i.length)>0){const a=i.filter(d=>d==null?void 0:d.value);if(a.length>1)a.forEach(d=>{const{accessor:y,operator:f,value:A}=d,B=`goodbadugly_user.${y},${f==="cs"||f==="eq"?Q("o"+f,A):Q(f,A)},${A}`;N.push(B)});else if(a.length===1){const{accessor:d,operator:y,value:f}=a[0];N.push(`goodbadugly_user.${d},${Q(y,f)},${ze(f,y)}`)}}}),N},Be=()=>{const k=[{header:"First Name",accessor:"first_name",join:"user",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,type:"teams"},{header:"Last Name",accessor:"last_name",join:"user",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,type:"teams"},{header:"Email",accessor:"email",join:"user",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,type:"font-iowan"},{header:"Last Login",accessor:"last_login_at",join:"user",isSorted:!0,isSortedDesc:!1,mappingExist:!1,date:!0,mappings:{},selected_column:!0},{size:"reduce",header:"Invitations",accessor:"is_incoming",isSorted:!0,isSortedDesc:!1,mappingExist:!0,mappingType2:!0,type:"version2",mappings:{false:{text:"Outgoing",bg:"#F6A13C",color:"black",type:"version2"},true:{text:"Incoming",bg:"#9DD321",color:"black",type:"version2"},null:{text:"",bg:"transparent",color:"transparent",type:"version2"}},selected_column:!0},{size:"reduce",header:"Status",accessor:"member_status",isSorted:!0,isSortedDesc:!1,mappingExist:!0,mappings:{1:{text:"Active",bg:"#9DD321",color:"black"},0:{text:"Request",bg:"#F6A13C",color:"black"},2:{text:"Pending",bg:"#F6A13C",color:"black"}},selected_column:!0},{header:"Action",accessor:""}],{sdk:N,tdk:le,operations:n}=ke(),i=o.useRef(null),a=o.useRef(null),d=o.useRef(null),{authState:y,showToast:f,tokenExpireError:A,getSingle:B,globalState:R,setGlobalState:ce}=Ne();o.useState(""),o.useState(!1);const[C,Ge]=Re();o.useState("");const[G,J]=o.useState(!1),[_,de]=o.useState({modal:null,showModal:!1,seletedItems:[]}),K=btoa("user:email"),H=btoa("user:first_name"),W=btoa("user:last_name"),S=C.get("trigger"),v=C.get("update_id"),[h,ue]=Me.useState([{accessor:"email",operator:"cs",value:"",uid:K},{accessor:"last_name",operator:"cs",value:"",uid:W},{accessor:"first_name",operator:"cs",value:"",uid:H}]);o.useState(parseInt(C.get("page")||"1"));const[g,M]=o.useState({page:1,data:[],limit:0,pages:0,total:0,use:!0,reload:!1,canNextPage:!1,canPreviousPage:!1});o.useState(parseInt(C.get("limit")||"30"));const me=o.useMemo(()=>JSON.stringify(h),[h]),X=Oe(),{profile:u,updateProfile:Je}=Ae({isPublic:!1}),pe=Te({last_name:U().nullable(),first_name:U().nullable(),email:U().nullable().email("Invalid email")});o.useState([]);const{register:O,handleSubmit:fe,setError:Ke,reset:ge,watch:_e,formState:{errors:$,isSubmitting:He}}=$e({resolver:je(pe),defaultValues:{email:"",last_name:"",first_name:""}}),{email:Y,first_name:Z,last_name:D}=_e(),T=o.useCallback((t,e,r)=>{ue(l=>l.map(c=>(c==null?void 0:c.uid)===r?{...c,[t]:e}:c))},[h,me]),he=o.useCallback(async(t,e,r)=>{var ee,te;d.current&&d.current.abort();const l=new AbortController;d.current=l,l.signal;const P=[...Qe(h),...r==null?void 0:r.filterConditions,`company_id,eq,${(te=(ee=u==null?void 0:u.companies)==null?void 0:ee[0])==null?void 0:te.id}`];try{const w=new V,I=new URLSearchParams({page:t,size:e});P!=null&&P.length&&I.append("filter",JSON.stringify(P)),r!=null&&r.order&&(I.append("order",r.order),I.append("diresction",r.direction));const Se=await w.callRawAPI(`/v3/api/custom/goodbadugly/company-members-with-invites?${I.toString()}`,{},"GET"),{list:Ee,pagination:b}=Se.response,F=Ee.map(m=>{var ne;const p=m.invitation,se=u==null?void 0:u.id;let E=null,re=null,oe=null,ae=null;p&&(re=p.id,oe=p.invited_at,ae=p.status,p.invite_type==="inviter"?E=!1:p.invite_type==="invitee"?E=!0:p.inviter_id===se?E=!1:E=!0);const L={...m,is_active:m.member_status===1,is_incoming:E,invite_id:re,invited_at:oe,invite_status:ae,member_status_text:m.member_status_text,has_reverse_connection:m.has_reverse_connection};return console.log(`Transformed member ${(ne=m.user)==null?void 0:ne.first_name}:`,{member_status:L.member_status,is_incoming:L.is_incoming,invite_status:L.invite_status,invite_type:p==null?void 0:p.invite_type,inviter_id:p==null?void 0:p.inviter_id,currentUserId:se}),L});console.log("Transformed data with invitation details:",F),F.forEach(m=>{console.log(`Member ${m.user.first_name}: member_status=${m.member_status}, is_incoming=${m.is_incoming}, invite_id=${m.invite_id}`)}),M(m=>({...m,data:F,page:b.page,limit:b.size,total:b.total,pages:b.totalPages,canPreviousPage:b.page>1,canNextPage:b.page<b.totalPages}))}catch(w){w.name==="AbortError"?console.log("Fetch aborted"):console.error("ERROR",w)}finally{M(w=>({...w,reload:!0})),d.current===l&&(console.info("abortControllerRef.current null"),d.current=null)}},[h,u]),xe=t=>{var e;(e=i==null?void 0:i.current)==null||e.click()},{fetchInvitations:be}=Ce(),x=(t,e,r=[])=>{console.log(r,"ids"),de(l=>({...l,seletedItems:r,showModal:e,modal:e?t:null}))},ve=(t=null)=>{var e;(e=i==null?void 0:i.current)==null||e.click(),be(!1),x(null,!1),ce("memberChange",!(R!=null&&R.memberChange)),["add"].includes(S)&&v&&t&&we(t),["add"].includes(S)&&v&&X(`/member/edit-updates/${v}`)},we=async t=>{try{const r=await new V().callRawAPI("/v3/api/custom/goodbadugly/updates/recipient/group",{update_id:v,groups:[{id:t.user_id||t.id,group_name:t.first_name&&t.last_name?`${t.first_name} ${t.last_name}`:t.email,type:2,recipient_member:[{user_id:t.user_id||t.id}]}]},"POST");r!=null&&r.error||f("Team member automatically added as recipient",5e3,"success")}catch(e){console.error("Error auto-adding member as recipient:",e)}};o.useEffect(()=>{var t;a!=null&&a.current&&(g!=null&&g.reload)&&(M(e=>({...e,reload:!1})),(t=a==null?void 0:a.current)==null||t.click())},[g==null?void 0:g.reload,a==null?void 0:a.current]),o.useEffect(()=>{T("value",Y,K)},[Y]),o.useEffect(()=>{T("value",Z,H)},[Z]),o.useEffect(()=>{T("value",D,W)},[D]),o.useEffect(()=>{["add"].includes(S)&&x("add",!0)},[S]),o.useEffect(()=>{var e;(h==null?void 0:h.every(r=>(r==null?void 0:r.value)==""))&&G&&((e=i==null?void 0:i.current)==null||e.click(),J(!1))},[G,h]);const ye=async()=>{const t=new V;try{await t.exportCSVTeam()}catch(e){console.error("Export error:",e),f("Failed to export data",5e3,"error")}};return s.jsxs(o.Fragment,{children:[s.jsxs("div",{className:"  grid h-full max-h-full min-h-full w-full grid-rows-[auto_auto_auto_1fr] space-y-[1rem] rounded  bg-brown-main-bg p-5 px-5 md:px-8",children:[s.jsxs("div",{className:"my-[16px] flex w-full items-center justify-between gap-5",children:[s.jsx("h4",{className:" font-iowan text-[18px] font-semibold md:text-[24px]",children:"Team Members"}),s.jsxs("div",{className:"flex w-fit items-center justify-start gap-5",children:[s.jsxs(z,{type:"button",className:"flex !h-[2.25rem] w-fit items-center justify-center whitespace-nowrap !rounded-[0.125rem] !border border-black bg-transparent !py-[0.5rem] px-2 !text-[1rem] tracking-wide text-black outline-none focus:outline-none md:px-5",color:"black",onClick:()=>{ye()},children:[s.jsx(Pe,{})," Export"]}),s.jsx(z,{type:"button",className:"flex !h-[2.25rem] w-fit items-center justify-center whitespace-nowrap !rounded-[0.125rem] !border bg-[#1f1d1a] !py-[0.5rem] px-2 !text-[1rem] tracking-wide text-white outline-none focus:outline-none md:px-5",color:"black",onClick:()=>{x("add",!0)},children:"Add New"})]})]}),s.jsx("div",{className:"flex w-full items-center gap-5",children:s.jsxs("div",{className:"flex w-full flex-wrap items-end gap-2 md:w-[75%] md:flex-nowrap",children:[s.jsx("div",{children:s.jsx(q,{type:"text",errors:$,register:O,name:"first_name",label:"First Name",placeholder:"Search",className:"!h-[2.25rem] !rounded-[0.125rem] !border !py-[0.5rem]"})}),s.jsx("div",{children:s.jsx(q,{type:"text",errors:$,register:O,name:"last_name",label:"Last Name",placeholder:"Search",className:"!h-[2.25rem] !rounded-[0.125rem] !border !py-[0.5rem]"})}),s.jsx("div",{children:s.jsx(q,{type:"text",errors:$,register:O,name:"email",label:"Email",placeholder:"Search",className:"!h-[2.25rem] !rounded-[0.125rem] !border !py-[0.5rem]"})}),s.jsx(z,{type:"submit",className:"flex !h-[2.25rem] w-fit items-center justify-center whitespace-nowrap !rounded-[0.125rem] !border bg-[#1f1d1a]  !py-[0.5rem] px-2 !text-[1rem] tracking-wide text-white outline-none focus:outline-none md:px-5",color:"black",disabled:!(u!=null&&u.id),onClick:fe(xe,t=>{console.log("error >>",t)}),children:"Search"}),s.jsx(Ve,{onClick:()=>{ge(),J(!0)},showPlus:!1,className:"!w-fit !min-w-fit !max-w-fit !border-0 !bg-transparent !p-0 !font-inter !text-[1rem] !font-[600] !leading-[1.21rem] !tracking-wide !text-black !underline !shadow-none ",children:"Clear"})]})}),s.jsx("div",{className:"h-[.125rem] w-full border-[.125rem] border-black bg-black "}),u!=null&&u.id?s.jsx(Fe,{showSearch:!1,useDefaultColumns:!0,defaultColumns:[...k],noDataComponent:{use:!0,component:s.jsx(ie,{children:s.jsxs("div",{className:"flex flex-col items-center",children:[s.jsx(Ue,{className:"h-8 w-8 text-gray-700",strokeWidth:2}),s.jsx("p",{className:"mt-4 text-center text-base font-medium",children:"No Team Members added yet"})]})})},onUpdateCurrentTableData:t=>{t(g)},externalData:{...g,fetch:he},hasFilter:!1,tableRole:"admin",table:"order",actionId:"member_id",actions:{view:{show:!1,action:null,multiple:!1},select:{show:!1,action:null,multiple:!1},remove:{show:!0,action:t=>{const e=g.data.find(c=>(c==null?void 0:c.member_id)==t),r=e==null?void 0:e.invite_id,l=e==null?void 0:e.member_id;x("delete",!0,[r,l])},multiple:!0,children:"Remove",icon:s.jsx(Ie,{fill:"#292D32"}),locations:["dropdown"],bind:{column:["member_status","is_incoming","member_status"],action:"hide",logic:"and",operator:[n.NOT_EQUAL,n.EQUAL,n.NOT_EQUAL],ifValue:[1,!0,2]}},resend:{show:!0,action:t=>{const e=g.data.find(c=>(c==null?void 0:c.member_id)==t),r=e==null?void 0:e.invite_id,l=e==null?void 0:e.id;console.log("Resend action data:",{member_status:e.member_status,is_incoming:e.is_incoming,invite_status:e.invite_status,invitation:e.invitation}),x("resend",!0,[r,l])},multiple:!0,children:s.jsx("span",{className:"ml-[5px]",children:"Resend"}),showChildren:!0,icon:s.jsx(Le,{fill:"#292D32"}),locations:["dropdown"],bind:{column:["member_status","is_incoming","member_status"],action:"hide",logic:"or",operator:[n.NOT_EQUAL,n.NOT_EQUAL,n.EQUAL],ifValue:[2,!1,1]}},accept:{show:!0,action:t=>x("accept",!0,t),multiple:!0,children:({row:t,actionId:e})=>{console.log(t,e,"memberid");const r=t.invite_id;return console.log(r,"memberid"),s.jsxs("div",{className:"flex w-full gap-2",children:[s.jsx("button",{onClick:l=>{l.stopPropagation(),r&&x("reject",!0,[r,e])},className:"flex-1 rounded-sm border border-[#1f1d1a] px-4 py-1.5 text-sm font-medium hover:bg-gray-50 disabled:opacity-50",children:"Reject"}),s.jsx("button",{onClick:l=>{l.stopPropagation(),r&&x("accept",!0,[r,t.member_id])},className:"flex-1 rounded-sm bg-[#1f1d1a] px-4 py-1.5 text-sm font-medium text-white hover:bg-[#2a2724] disabled:opacity-50",children:"Accept"})]})},showChildren:!0,locations:["buttons"],bind:{column:["member_status","is_incoming","invite_id"],action:"hide",logic:"or",operator:[n.EQUAL,n.NOT_EQUAL,n.EQUAL],ifValue:[1,!0,null]}},view_all:{show:!1,type:"static",action:()=>{},children:s.jsx(s.Fragment,{children:"View All"}),className:"!gap-2 !bg-transparent !text-black !underline !shadow-none !border-0"},add:{show:!1,multiple:!0,children:"+ Add"},export:{show:!1,action:null,multiple:!0}},defaultPageSize:20,showPagination:!0,refreshRef:i,updateRef:a,maxHeight:"md:grid-rows-[inherit] grid-rows-[inherit]",actionPostion:["dropdown","buttons"]}):null]}),s.jsx(ie,{children:s.jsx(qe,{modal:_==null?void 0:_.modal,isOpen:_==null?void 0:_.showModal,onClose:()=>{x(null,!1),["add"].includes(S)&&v&&X(`/member/edit-updates/${v}`)},ids:_==null?void 0:_.seletedItems,onSuccess:ve})})]})},mt=Object.freeze(Object.defineProperty({__proto__:null,default:Be},Symbol.toStringTag,{value:"Module"}));export{ct as A,ut as R,mt as T,dt as a,lt as b};
