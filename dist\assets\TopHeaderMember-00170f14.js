import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as o,b as Re,i as Be,R as $e,u as Oe}from"./vendor-4cdf2bd1.js";import{U as He,c as ze}from"./index-0d5645ff.js";import{A as ae,G as Ue,M as D,s as Te,t as Ge,a as Qe,b as Ve,au as Ye,L as te,T as De}from"./index-46de5032.js";import"./lodash-82bd9112.js";import{M as Ke}from"./index.esm-6fcccbfe.js";import{_ as Ze}from"./MoonLoader-6f2b5db4.js";import{l as We}from"./logo5-2e16f0f2.js";import{h as Ae}from"./moment-a9aaa855.js";import{A as Xe}from"./index-a807e4ab.js";import{e as Je}from"./AuthAction-52ee0934.js";import{u as es}from"./useMentions-6b3c3c1d.js";import"./@nextui-org/theme-345a09ed.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./react-icons-36ae72b7.js";function ss({title:f,titleId:a,...c},u){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:u,"aria-labelledby":a},c),f?o.createElement("title",{id:a},f):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"}))}const ts=o.forwardRef(ss),Z=ts,as=()=>{var E,y,w;const{state:f,dispatch:a}=o.useContext(ae),c=Re();if(console.log(f,"bar"),!((E=f.impersonation)!=null&&E.active))return null;const u=()=>{Je(a),window.location.href="/admin/users",c("/admin/users")};return e.jsxs("div",{className:" z-50 flex items-center justify-between bg-[#1f1d1a] px-4 py-2 text-white lg:w-[600px]",children:[e.jsxs("div",{className:" hidden items-center gap-4 lg:flex",children:[e.jsx("div",{className:"flex items-center gap-2",children:e.jsxs("span",{className:"text-sm",children:["Admin: ",(y=f.impersonation.originalAdmin)==null?void 0:y.first_name," ",(w=f.impersonation.originalAdmin)==null?void 0:w.last_name]})}),e.jsx("div",{className:"h-4 w-px bg-gray-500"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("span",{className:"text-sm",children:["Viewing as: ",f.userDetails.firstName," ",f.userDetails.lastName]}),e.jsx("span",{className:"rounded bg-yellow-500 px-2 py-0.5 text-xs text-black",children:f.role})]})]}),e.jsx("button",{onClick:u,className:"rounded bg-white px-3 py-1 text-sm text-black hover:bg-gray-100",children:"Exit Impersonation"})]})};function rs({update:f,afterEdit:a}){const{dispatch:c,state:u}=o.useContext(ae),{dispatch:E}=o.useContext(Ue),[y,w]=o.useState(!1),[N,A]=o.useState("");Be();const[W,H]=o.useState(null);console.log(N,"profilek",u),o.useEffect(()=>{A(u.company.name||"Company Name")},[u.company.name]);async function $(g){w(!0);try{await new D().callRawAPI(`/v4/api/records/companies/${u.company.id}`,{name:g},"PUT"),c({type:"REFETCH_COMPANY"}),Te(E,"Company name saved")}catch(v){Ge(c,v.message),v.message!=="TOKEN_EXPIRED"&&Te(E,v.message,5e3,"error")}w(!1)}return e.jsx("div",{className:"flex flex-row items-center",children:y?e.jsx(Ze,{size:14}):e.jsxs(e.Fragment,{children:[e.jsx("input",{className:"no-box-shadow focus:shadow-outline appearance-none border-none bg-brown-main-bg bg-inherit p-0 text-3xl text-[18px] font-bold capitalize focus:outline-none",defaultValue:"Company name",value:N,onChange:g=>{A(g.target.value),W&&clearTimeout(W);const v=setTimeout(()=>$(g.target.value),2e3);H(v)},readOnly:y,style:{width:`${N?(N==null?void 0:N.length)+1:14}ch`}}),N?null:e.jsx(Ke,{size:16})]})})}const Fs=()=>{const{dispatch:f}=$e.useContext(Ue),{globalState:a,showToast:c,setGlobalState:u}=Qe();$e.useContext(ae);const[E,y]=o.useState(""),{isOpen:w,showBackButton:N}=a,A=Oe(),{tdk:W,sdk:H}=Ve(),[$,g]=o.useState(!1),[v,_]=o.useState(!1),[R,U]=o.useState(null),[j,M]=o.useState(""),[C,Me]=o.useState([]),[z,re]=o.useState(!1),[G,Q]=o.useState(null),[X,ne]=o.useState("member"),[T,oe]=o.useState([]),[le,ie]=o.useState(!1),[Ie,V]=o.useState(null),[I,de]=o.useState([]),[ns,ce]=o.useState(!1),{loading:os,mentions:Se,unreadCount:Y,fetchMentions:J,fetchUnreadCount:ee,markMentionAsSeen:ls,markAllMentionsAsSeen:is}=es(),[S,me]=o.useState([]),[ds,ue]=o.useState(!1),[F,he]=o.useState([]),[cs,pe]=o.useState(!1);console.log(G,"currentCollaboration",C,T);const{invitations:k,loading:K,handleInvitation:Fe,refetchInvitations:xe}=Ye();console.log(k,"invite");const P=async()=>{var t,r;console.log("Fetching collaboration requests"),re(!0);const s=new De;try{const n=await H.callRawAPI("/v3/api/custom/goodbadugly/team-updates",{},"GET");if(!n.error&&n.data){console.log("Team updates data received:",n.data);const i=Array.from(n.data.filter(p=>p.collaborator_status==2));console.log("Filtered collaboration updates:",i);const h=(await Promise.all(i.map(async p=>{var d;const m=await s.getList("update_collaborators",{filter:[`id,eq,${p==null?void 0:p.update_collaborator_id}`]}),x=(d=m==null?void 0:m.list)==null?void 0:d[0];return x?{...p,collaborator_details:x}:null}))).filter(Boolean);console.log("Processed collaborator data:",h),Me(h)}}catch(n){console.error("Error fetching collaboration requests:",n);const i=((r=(t=n==null?void 0:n.response)==null?void 0:t.data)==null?void 0:r.message)??n.message;c(i,5e3),tokenExpireError(i)}finally{re(!1)}},Pe=async(s,t)=>{var r,n,i,l;try{const h="/v3/api/custom/goodbadugly/collaborator/response",p=t==="accept"?1:2,m=C.find(d=>d.id===s);console.log("Handling collaboration action",{updateId:s,action:t,collaborator_id:(r=m==null?void 0:m.collaborator_details)==null?void 0:r.collaborator_id});const x=await H.callRawAPI(h,{collaborator_id:(n=m==null?void 0:m.collaborator_details)==null?void 0:n.collaborator_id,update_id:s,status:p},"POST");console.log("Collaboration action result:",x),u("collaborationChange",!(a!=null&&a.collaborationChange)),x.error?c(`Failed to ${t} collaboration request`,5e3):(c(`Collaboration request ${t}ed successfully`),await P(),await L(),t==="accept"&&setTimeout(()=>{q(`/member/edit-updates/${G}`)},100))}catch(h){console.error("Error handling collaboration:",h);const p=((l=(i=h==null?void 0:h.response)==null?void 0:i.data)==null?void 0:l.message)??h.message;c(p,5e3),tokenExpireError(p)}},L=async()=>{var s,t;console.log("Fetching update requests");try{ie(!0);const r=new D,n=new De,i=await r.callRawAPI("/v3/api/custom/goodbadugly/users/update-requests",void 0,"GET");if(console.log("Update requests data received:",i),!i.error&&((s=i.data)==null?void 0:s.length)>0){const l=[...new Set(i.data.map(d=>d.requesting_user_id))];console.log("Requesting user IDs:",l);const h=await n.getPaginate("user",{filter:[`id,in,${l.join(",")}`],page:1,limit:l.length});console.log("User details:",h);const p=h.list.reduce((d,b)=>(d[b.id]=b,d),{}),m={};for(const d of l){r.setTable("companies");const b=await r.callRawAPI(`/v4/api/records/companies?filter=user_id,eq,${d}`,void 0,"GET");(t=b==null?void 0:b.list)!=null&&t[0]&&(m[d]=b.list[0])}console.log("User company map:",m);const x=i.data.map(d=>({...d,requesting_user:p[d.requesting_user_id]||null,requesting_user_company:m[d.requesting_user_id]||null}));console.log("Updates with user details:",x),oe(x),u("requestedUpdates",x==null?void 0:x.length)}else console.log("No update requests found or error in response"),oe([]),u("requestedUpdates",0)}catch(r){console.error("Error fetching update requests:",r),c(r.message||"Error fetching update requests",5e3,"error")}finally{ie(!1)}},fe=async()=>{var s;try{ce(!0);const r=await new D().callRawAPI("/v3/api/custom/goodbadugly/user/unanswered-questions",[],"GET");!r.error&&((s=r.data)==null?void 0:s.length)>0?(de(r.data),u("unansweredQuestions",r.data.length)):(de([]),u("unansweredQuestions",0))}catch(t){console.error("Error fetching unanswered questions:",t),c(t.message||"Error fetching unanswered questions",5e3,"error")}finally{ce(!1)}},Le=async(s,t,r)=>{try{console.log("Handling update request action",{requestId:s,updateId:t,action:r});const i=await new D().callRawAPI("/v3/api/custom/goodbadugly/update-requests/acceptance",{request:r==="accept"?1:2,update_id:t},"POST");console.log("Update request action result:",i),u("updateRequestChange",!(a!=null&&a.updateRequestChange)),i.error?c(`Failed to ${r} update request`,5e3,"error"):(c(`Update request ${r}ed successfully`),await L(),await P(),await xe(),r==="accept"&&setTimeout(()=>{q(`/member/edit-updates/${t}?autofocus=true`)},100))}catch(n){console.error("Error handling update request:",n),c(n.message||`Error ${r}ing update request`,5e3,"error")}},ge=async()=>{var s;try{pe(!0);const r=await new D().callRawAPI("/v3/api/custom/goodbadugly/updates/cadence-notifications",[],"GET");!r.error&&((s=r.data)!=null&&s.notifications)?he(r.data.notifications):he([])}catch(t){console.error("Error fetching cadence notifications:",t),c(t.message||"Error fetching cadence notifications",5e3,"error")}finally{pe(!1)}},se=async()=>{try{ue(!0);const t=await new D().callRawAPI("/v3/api/custom/goodbadugly/notifications/reports",[],"GET");!t.error&&(t!=null&&t.notifications)?me(t==null?void 0:t.notifications):me([])}catch(s){console.error("Error fetching update shared notifications:",s),c(s.message||"Error fetching update shared notifications",5e3,"error")}finally{ue(!1)}};o.useEffect(()=>{P(),L(),fe(),J(),ee(),ge(),se();const s=setInterval(()=>{P(),L(),fe(),J(),ee(),ge(),se()},18e4);return()=>clearInterval(s)},[]);const B=s=>{const t=Ae(),r=Ae(s),n=t.diff(r,"seconds");if(n<60)return"just now";if(n<3600){const l=Math.floor(n/60);return`${l} ${l===1?"min":"mins"} ago`}if(n<86400){const l=Math.floor(n/3600);return`${l} ${l===1?"hour":"hours"} ago`}if(n<604800){const l=Math.floor(n/86400);return`${l} ${l===1?"day":"days"} ago`}if(n<2592e3){const l=Math.floor(n/604800);return`${l} ${l===1?"week":"weeks"} ago`}if(n<31536e3){const l=Math.floor(n/2592e3);return`${l} ${l===1?"month":"months"} ago`}const i=Math.floor(n/31536e3);return`${i} ${i===1?"year":"years"} ago`},we=(s,t)=>{U(s),M(t),u("memberChange",!(a!=null&&a.memberChange)),_(!0)};o.useEffect(()=>{xe()},[a==null?void 0:a.memberChange]),o.useEffect(()=>{console.log("collaborationChange triggered",a==null?void 0:a.collaborationChange),(async()=>{await P(),console.log("Collaboration requests fetched")})()},[a==null?void 0:a.collaborationChange]),o.useEffect(()=>{console.log("updateRequestChange triggered",a==null?void 0:a.updateRequestChange),(async()=>{await L(),console.log("Update requests fetched")})()},[a==null?void 0:a.updateRequestChange]),o.useEffect(()=>{console.log("mentionsChange triggered",a==null?void 0:a.mentionsChange),(async()=>{await J(),await ee(),console.log("Mentions refetched")})()},[a==null?void 0:a.mentionsChange]),o.useEffect(()=>{console.log("updateSharedNotificationChange triggered",a==null?void 0:a.updateSharedNotificationChange),(async()=>{await se(),console.log("Update shared notifications refetched")})()},[a==null?void 0:a.updateSharedNotificationChange]),o.useEffect(()=>{const s=t=>{t.target.closest(".notifications-container")||g(!1)};return document.addEventListener("mousedown",s),()=>document.removeEventListener("mousedown",s)},[]);let be=s=>{console.log(s,w),f({type:"OPEN_SIDEBAR",payload:{isOpen:s}})};const q=Re();o.useEffect(()=>{const s=A.pathname.split("/");s[1]!=="user"&&s[1]!=="admin"?y(s[1]):y(s[2])},[A]);const O=(()=>{const s=[];return F.forEach(t=>{s.push({...t,type:"cadence",timestamp:new Date(t.created_at||Date.now()),displayTime:t.urgency_status==="overdue"?`${Math.abs(t.days_until_due)} days overdue`:t.urgency_status==="due_today"?"Today":"Tomorrow"})}),k.forEach(t=>{s.push({...t,type:"invitation",timestamp:new Date((t==null?void 0:t.invited_at)||Date.now()),displayTime:t.timeAgo})}),C.forEach(t=>{var r,n;s.push({...t,type:"collaboration",timestamp:new Date(((r=t.collaborator_details)==null?void 0:r.update_at)||Date.now()),displayTime:B((n=t.collaborator_details)==null?void 0:n.update_at)})}),T.forEach(t=>{s.push({...t,type:"update_request",timestamp:new Date(t.update_at||Date.now()),displayTime:B(t.update_at)})}),I.forEach(t=>{s.push({...t,type:"question",timestamp:new Date(t.sent_at||Date.now()),displayTime:B(t.sent_at)})}),Se.forEach(t=>{s.push({...t,type:"mention",timestamp:new Date(t.created_at||Date.now()),displayTime:B(t.created_at)})}),S.forEach(t=>{s.push({...t,type:"shared_update",timestamp:new Date(t.created_at||Date.now()),displayTime:B(t.created_at)})}),s.sort((t,r)=>r.timestamp-t.timestamp)})();console.log(O,"notifications","invitations",k);const je=s=>{var t,r,n,i,l,h,p,m,x,d,b,ye,Ne,ve,_e,Ce,ke,qe;switch(s.type){case"cadence":return e.jsxs("div",{className:"mb-4 last:mb-0",children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("div",{className:"flex max-h-8 min-h-8 min-w-8 max-w-8 items-center justify-center overflow-hidden rounded-full bg-gray-200",children:e.jsx("div",{className:"flex h-full w-full items-center justify-center bg-[#1f1d1a]/5 text-sm font-medium",children:((t=s.name)==null?void 0:t[0])||"U"})}),e.jsxs("div",{className:"font-iowan-regular flex flex-grow items-center justify-between gap-6",children:[e.jsxs("p",{className:"font-iowan-regular whitespace-normal text-sm",children:[e.jsx("span",{className:"font-iowan font-semibold",children:s.name})," ",e.jsxs("span",{className:"font-iowan-regular text-base",children:[s.urgency_status==="overdue"?"is overdue":s.urgency_status==="due_today"?"is due today":"is due tomorrow"," ","(",s.cadence_text," update)"]})]}),e.jsx("p",{className:"whitespace-nowrap font-inter text-xs font-medium text-black",children:s.displayTime})]})]}),e.jsx("div",{className:"",children:e.jsx("button",{onClick:()=>{q(s.edit_link),g(!1)},className:`w-[92px] flex-1 rounded-sm ${s.urgency_status==="overdue"?"bg-[#ff4e4e]":"bg-[#1f1d1a]"} px-4 py-1.5 font-iowan text-sm font-medium text-white hover:opacity-90`,children:"Update Now"})})]},s.id);case"invitation":return e.jsxs("div",{className:"mb-4 last:mb-0",children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("div",{className:"h-8 max-h-8 min-h-8 w-8 min-w-8 max-w-8 overflow-hidden rounded-full bg-gray-200",children:s.photo?e.jsx("img",{src:s.photo,alt:s.inviter,className:"h-full w-full object-cover"}):e.jsx("div",{className:"flex h-full w-full items-center justify-center bg-[#1f1d1a]/5 text-sm font-medium",children:s.inviter.charAt(0)})}),e.jsxs("div",{className:"font-iowan-regular flex flex-grow items-center justify-between gap-6",children:[e.jsxs("p",{className:"font-iowan-regular whitespace-nowrap  text-[11px] sm:text-sm",children:[e.jsx("span",{className:"font-iowan font-semibold",children:s.inviter})," ",e.jsxs("span",{className:"font-iowan-regular text-base",children:["has invited you to"," "]}),e.jsx("span",{className:"font-iowan font-semibold",children:s.team}),e.jsx("span",{className:"ml-[5px] font-iowan font-semibold",children:"team"})]}),e.jsx("p",{className:"whitespace-nowrap font-inter text-xs font-medium text-black",children:s.displayTime})]})]}),e.jsxs("div",{className:"flex w-[50%] gap-2",children:[e.jsx("button",{onClick:()=>we(s.id,"reject"),disabled:K,className:"= w-[85px] flex-1 rounded-sm border border-[#1f1d1a] px-4 py-1.5 font-iowan text-sm font-medium disabled:opacity-50",children:"Reject"}),e.jsx("button",{onClick:()=>we(s.id,"accept"),disabled:K,className:"w-[85px] flex-1 rounded-sm bg-[#1f1d1a] px-4 py-1.5 font-iowan text-sm font-medium text-white hover:bg-[#2a2724] disabled:opacity-50",children:"Accept"})]})]},s.id);case"collaboration":return e.jsxs("div",{className:"mb-4 last:mb-0",children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("div",{className:"flex max-h-8 min-h-8 min-w-8 max-w-8 items-center justify-center overflow-hidden rounded-full bg-gray-200",children:(r=s.user)!=null&&r.photo?e.jsx("img",{src:s.user.photo,alt:`${s.user.first_name} ${s.user.last_name}`,className:"h-full w-full object-cover"}):e.jsx("div",{className:"flex h-full w-full items-center justify-center bg-[#1f1d1a]/5 text-sm font-medium",children:(i=(n=s.user)==null?void 0:n.first_name)==null?void 0:i.charAt(0)})}),e.jsxs("div",{className:"font-iowan-regular flex flex-grow items-center justify-between gap-6",children:[e.jsxs("p",{className:"font-iowan-regular whitespace-nowrap text-[11px] sm:text-sm",children:[e.jsx("span",{className:"font-iowan font-semibold",children:`${(l=s.user)==null?void 0:l.first_name} ${(h=s.user)==null?void 0:h.last_name}`})," ",e.jsx("span",{className:"font-iowan-regular text-base",children:"requested collaboration on"})," ",e.jsx("span",{className:"font-iowan font-semibold",children:s.name}),s.company_name&&e.jsxs(e.Fragment,{children:[e.jsxs("span",{className:"font-iowan-regular text-base",children:[" ","for"," "]}),e.jsx("span",{className:"font-iowan font-semibold",children:s.company_name})]})]}),e.jsx("p",{className:"whitespace-nowrap font-inter text-xs font-medium text-black",children:s.displayTime})]})]}),e.jsxs("div",{className:"flex w-[50%] gap-2",children:[e.jsx("button",{onClick:()=>{Q(s.id),M("reject"),_(!0)},disabled:z,className:"w-[85px] flex-1 rounded-sm border border-[#1f1d1a] px-4 py-1.5 font-iowan text-sm font-medium disabled:opacity-50",children:"Reject"}),e.jsx("button",{onClick:()=>{Q(s.id),M("accept"),_(!0)},disabled:z,className:"w-[85px] flex-1 rounded-sm bg-[#1f1d1a] px-4 py-1.5 font-iowan text-sm font-medium text-white hover:bg-[#2a2724] disabled:opacity-50",children:"Accept"})]})]},s.id);case"update_request":return e.jsxs("div",{className:"mb-4 last:mb-0",children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("div",{className:"flex max-h-8 min-h-8 min-w-8 max-w-8 items-center justify-center overflow-hidden rounded-full bg-gray-200",children:(p=s.requesting_user)!=null&&p.photo?e.jsx("img",{src:s.requesting_user.photo,alt:`${s.requesting_user.first_name} ${s.requesting_user.last_name}`,className:"h-full w-full object-cover"}):e.jsx("div",{className:"flex h-full w-full items-center justify-center bg-[#1f1d1a]/5 text-sm font-medium",children:((x=(m=s.requesting_user)==null?void 0:m.first_name)==null?void 0:x[0])||""})}),e.jsxs("div",{className:"font-iowan-regular flex flex-grow items-center justify-between gap-6",children:[e.jsxs("p",{className:"font-iowan-regular whitespace-normal text-sm",children:[e.jsx("span",{className:"font-iowan font-semibold",children:`${((d=s.requesting_user)==null?void 0:d.first_name)||""} ${((b=s.requesting_user)==null?void 0:b.last_name)||""}`})," ",s.requesting_user_company&&e.jsxs(e.Fragment,{children:[e.jsxs("span",{className:"font-iowan-regular text-base",children:["from"," "]}),e.jsx("span",{className:"font-iowan font-semibold",children:s.requesting_user_company.name})," "]}),e.jsx("span",{className:"font-iowan-regular text-base",children:"requested an update"})," "]}),e.jsx("p",{className:"whitespace-nowrap font-inter text-xs font-medium text-black",children:s.displayTime})]})]}),e.jsxs("div",{className:"flex w-[50%] gap-2",children:[e.jsx("button",{onClick:()=>{U(s.id),ne("update_request"),M("reject"),_(!0),V(s.update_id)},disabled:le,className:"w-[85px] flex-1 rounded-sm border border-[#1f1d1a] px-4 py-1.5 font-iowan text-sm font-medium disabled:opacity-50",children:"Reject"}),e.jsx("button",{onClick:()=>{U(s.id),ne("update_request"),M("accept"),_(!0),V(s.update_id)},disabled:le,className:"w-[85px] flex-1 rounded-sm bg-[#1f1d1a] px-4 py-1.5 font-iowan text-sm font-medium text-white hover:bg-[#2a2724] disabled:opacity-50",children:"Accept"})]})]},s.id);case"question":return e.jsxs("div",{className:"mb-4 last:mb-0",children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("div",{className:"flex max-h-8 min-h-8 min-w-8 max-w-8 items-center justify-center overflow-hidden rounded-full bg-gray-200",children:(ye=s.creator)!=null&&ye.photo?e.jsx("img",{src:s.creator.photo,alt:`${s.creator.first_name} ${s.creator.last_name}`,className:"h-full w-full object-cover"}):e.jsx("div",{className:"flex h-full w-full items-center justify-center bg-[#1f1d1a]/5 text-sm font-medium",children:((ve=(Ne=s.creator)==null?void 0:Ne.first_name)==null?void 0:ve[0])||""})}),e.jsxs("div",{className:"font-iowan-regular flex flex-grow items-center justify-between gap-6",children:[e.jsxs("p",{className:"font-iowan-regular whitespace-normal text-sm",children:[e.jsx("span",{className:"font-iowan font-semibold",children:`${((_e=s.creator)==null?void 0:_e.first_name)||""} ${((Ce=s.creator)==null?void 0:Ce.last_name)||""}`})," ",e.jsxs("span",{className:"font-iowan-regular text-base",children:["asked a question in"," "]}),e.jsx("span",{className:"font-iowan font-semibold",children:s.update_name})]}),e.jsx("p",{className:"whitespace-nowrap font-inter text-xs font-medium text-black",children:s.displayTime})]})]}),e.jsx("div",{className:"",children:e.jsx("button",{onClick:()=>{q(`/member/update/private/view/${s.update_id}?question=${s.id}`),g(!1)},className:"w-[92px] flex-1 rounded-sm bg-[#1f1d1a] px-4 py-1.5 font-iowan text-sm font-medium text-white hover:bg-[#2a2724]",children:"Respond"})})]},s.id);case"mention":return e.jsxs("div",{className:"mb-4 last:mb-0",children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("div",{className:"flex max-h-8 min-h-8 min-w-8 max-w-8 items-center justify-center overflow-hidden rounded-full bg-gray-200",children:s.mentioner_photo?e.jsx("img",{src:s.mentioner_photo,alt:`${s.mentioner_first_name} ${s.mentioner_last_name}`,className:"h-full w-full object-cover"}):e.jsx("div",{className:"flex h-full w-full items-center justify-center bg-[#1f1d1a]/5 text-sm font-medium",children:((ke=s.mentioner_first_name)==null?void 0:ke[0])||""})}),e.jsxs("div",{className:"font-iowan-regular flex flex-grow items-center justify-between gap-6",children:[e.jsxs("p",{className:"font-iowan-regular whitespace-normal text-sm",children:[e.jsx("span",{className:"font-iowan font-semibold",children:`${s.mentioner_first_name||""} ${s.mentioner_last_name||""}`})," ",e.jsxs("span",{className:"font-iowan-regular text-base",children:[s.mention_type==="reply"?"replied to you in":"mentioned you in"," "]}),e.jsx("span",{className:"font-iowan font-semibold",children:s.update_name})]}),e.jsx("p",{className:"whitespace-nowrap font-inter text-xs font-medium text-black",children:s.displayTime})]})]}),e.jsx("div",{className:"",children:e.jsx("button",{onClick:()=>{let Ee=`/member/update/private/view/${s.update_id}`;s.comment_id&&(Ee+=`#${btoa(`comment:${s.comment_id}`)}`),q(Ee),g(!1)},className:`w-[92px] flex-1 rounded-sm ${s.seen===0?"bg-[#1f1d1a] text-white hover:bg-[#2a2724]":"border border-[#1f1d1a] bg-transparent text-[#1f1d1a]"} px-4 py-1.5 font-iowan text-sm font-medium`,children:s.seen===0?"View":"Viewed"})})]},s.id);case"shared_update":return e.jsxs("div",{className:"mb-4 last:mb-0",children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("div",{className:"flex max-h-8 min-h-8 min-w-8 max-w-8 items-center justify-center overflow-hidden rounded-full bg-gray-200",children:s!=null&&s.sender_photo?e.jsx("img",{src:s==null?void 0:s.sender_photo,alt:`${s.sender_first_name} ${s.sender_last_name}`,className:"h-full w-full object-cover"}):e.jsx("div",{className:"flex h-full w-full items-center justify-center bg-[#1f1d1a]/5 text-sm font-medium",children:((qe=s.sender_first_name)==null?void 0:qe[0])||"U"})}),e.jsxs("div",{className:"font-iowan-regular flex flex-grow items-center justify-between gap-6",children:[e.jsxs("p",{className:"font-iowan-regular whitespace-normal text-sm",children:[e.jsx("span",{className:"font-iowan font-semibold",children:`${s.sender_first_name||""} ${s.sender_last_name||""}`})," ",e.jsx("span",{className:"font-iowan-regular text-base",children:"shared an update with you"})," ",s.company_name&&e.jsxs(e.Fragment,{children:[e.jsxs("span",{className:"font-iowan-regular text-base",children:["from"," "]}),e.jsx("span",{className:"font-iowan font-semibold",children:s.company_name})]})]}),e.jsx("p",{className:"whitespace-nowrap font-inter text-xs font-medium text-black",children:s.displayTime})]})]}),e.jsx("div",{className:"",children:e.jsx("button",{onClick:()=>{q(`/member/update/private/view/${s.update_id}`),g(!1)},className:`w-[92px] flex-1 rounded-sm ${s.seen===0?"bg-[#1f1d1a] text-white hover:bg-[#2a2724]":"border border-[#1f1d1a] bg-transparent text-[#1f1d1a]"} px-4 py-1.5 font-iowan text-sm font-medium`,children:s.seen===0?"View":"Viewed"})})]},s.id);default:return null}};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"sticky right-0 top-0 z-[9] flex h-[4.5rem] w-full flex-row items-center justify-between bg-[#1f1d1a] px-4 md:hidden md:px-8",children:[e.jsx("img",{onClick:()=>q("/member/dashboard"),src:We,alt:"logo",className:"h-10 w-[180px] cursor-pointer sm:invisible"}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:"notifications-container relative md:hidden",children:[e.jsxs("button",{className:"relative rounded-[50%] border border-[#E9DBD2] p-2",onClick:()=>g(!$),disabled:K||z,children:[e.jsx(Z,{className:"h-4 w-4 fill-brown-main-bg text-brown-main-bg"}),k.length+C.length+T.length+I.length+Y+F.length+S.length>0&&e.jsx("div",{className:"absolute -right-2 -top-[0.25rem] flex h-4 w-4 items-center justify-center rounded-full bg-[#F6A03C] text-xs text-[black]",children:e.jsx("span",{className:"text-xs",children:k.length+C.length+T.length+I.length+Y+F.length+S.length})})]}),$&&e.jsxs("div",{className:"custom-overscroll absolute right-[-42px] top-12 z-[1000] max-h-[252px] min-w-[300px] overflow-y-auto rounded-sm border border-[#1f1d1a]/10 bg-brown-main-bg p-4 shadow-lg sm:right-0 md:w-[445px]",children:[e.jsx("div",{className:"absolute -top-2 right-4 h-4 rotate-45 transform border-l border-t border-[#1f1d1a]/10 bg-brown-main-bg"}),O.length>0?e.jsx(e.Fragment,{children:O.map(s=>je(s))}):e.jsxs("div",{className:"flex flex-col items-center justify-center py-8",children:[e.jsx("div",{className:"mb-4 rounded-full bg-[#1f1d1a]/5 p-4",children:e.jsx(Z,{className:"h-8 w-8 text-[#1f1d1a]/40"})}),e.jsx("h3",{className:"mb-2 text-lg font-semibold text-[#1f1d1a]",children:"You have no notifications"}),e.jsxs("p",{className:"text-center text-sm text-[#1f1d1a]/60",children:["Recent activity within your account that requires you to take action",e.jsx("br",{}),"will be shown here"]})]})]})]}),w?e.jsx("svg",{onClick:()=>be(!w),className:"cursor-pointer",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M7 7L12 12M12 12L17 17M12 12L17 7M12 12L7 17",stroke:"#FCF1E6",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}):e.jsx("svg",{className:"cursor-pointer",width:"24",onClick:()=>be(!w),height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M21 12C21 12.1989 20.921 12.3897 20.7803 12.5303C20.6397 12.671 20.4489 12.75 20.25 12.75H3.75C3.55109 12.75 3.36032 12.671 3.21967 12.5303C3.07902 12.3897 3 12.1989 3 12C3 11.8011 3.07902 11.6103 3.21967 11.4697C3.36032 11.329 3.55109 11.25 3.75 11.25H20.25C20.4489 11.25 20.6397 11.329 20.7803 11.4697C20.921 11.6103 21 11.8011 21 12ZM3.75 6.75H20.25C20.4489 6.75 20.6397 6.67098 20.7803 6.53033C20.921 6.38968 21 6.19891 21 6C21 5.80109 20.921 5.61032 20.7803 5.46967C20.6397 5.32902 20.4489 5.25 20.25 5.25H3.75C3.55109 5.25 3.36032 5.32902 3.21967 5.46967C3.07902 5.61032 3 5.80109 3 6C3 6.19891 3.07902 6.38968 3.21967 6.53033C3.36032 6.67098 3.55109 6.75 3.75 6.75ZM20.25 17.25H3.75C3.55109 17.25 3.36032 17.329 3.21967 17.4697C3.07902 17.6103 3 17.8011 3 18C3 18.1989 3.07902 18.3897 3.21967 18.5303C3.36032 18.671 3.55109 18.75 3.75 18.75H20.25C20.4489 18.75 20.6397 18.671 20.7803 18.5303C20.921 18.3897 21 18.1989 21 18C21 17.8011 20.921 17.6103 20.7803 17.4697C20.6397 17.329 20.4489 17.25 20.25 17.25Z",fill:"#FEF1E5"})})]})]}),e.jsxs("div",{className:"sticky right-0 top-0 z-[9] hidden h-[4.5rem] w-full flex-row items-center justify-between border-b-[2px] border-b-[#1F1D1A] bg-brown-main-bg px-8  shadow-sm sm:h-[4.5rem] sm:max-h-[4.5rem]  sm:flex-row sm:px-6 sm:pl-[20px] md:flex md:pl-[100px] lg:pl-[20px] xl:pl-[20px]",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(rs,{}),e.jsx(te,{children:e.jsx(He,{})}),e.jsx(as,{})]}),e.jsxs("div",{className:"flex items-center gap-4 md:self-auto",children:[e.jsx("div",{className:"hidden md:block",children:e.jsx(te,{children:e.jsx(ze,{})})}),e.jsxs("div",{className:"notifications-container relative",children:[e.jsxs("button",{className:"relative rounded-[50%] border border-[#E9DBD2] p-2",onClick:()=>g(!$),disabled:K||z,children:[e.jsx(Z,{className:"h-6 w-6 fill-black text-[#1f1d1a]"}),k.length+C.length+T.length+I.length+Y+F.length+S.length>0&&e.jsx("div",{className:"absolute -right-2 -top-[0.25rem] flex h-5 w-5 items-center justify-center rounded-full bg-[#F6A03C] text-xs text-black",children:e.jsx("span",{className:"text-xs",children:k.length+C.length+T.length+I.length+Y+F.length+S.length})})]}),$&&e.jsxs("div",{className:"custom-overscroll absolute right-0 top-12 z-[1000] max-h-[252px] w-[420px] overflow-y-auto rounded-sm border border-[#1f1d1a]/10 bg-brown-main-bg p-4 shadow-lg sm:w-[460px]",children:[e.jsx("div",{className:"absolute -top-2 right-4 h-4 rotate-45 transform border-l border-t border-[#1f1d1a]/10 bg-brown-main-bg"}),O.length>0?e.jsx(e.Fragment,{children:O.map(s=>je(s))}):e.jsxs("div",{className:"flex flex-col items-center justify-center py-8",children:[e.jsx("div",{className:"mb-4 rounded-full bg-[#1f1d1a]/5 p-4",children:e.jsx(Z,{className:"h-8 w-8 text-[#1f1d1a]/40"})}),e.jsx("h3",{className:"mb-2 text-lg font-semibold text-[#1f1d1a]",children:"You have no notifications"}),e.jsxs("p",{className:"text-center text-sm text-[#1f1d1a]/60",children:["Recent activity within your account that requires you to take action",e.jsx("br",{}),"will be shown here"]})]})]})]})]})]}),e.jsx(te,{children:e.jsx(Xe,{mode:"manual",action:j,multiple:!1,inputConfirmation:!1,className:"action-confirmation-modal",isOpen:v,onClose:()=>{_(!1),U(null),Q(null),V(null)},onSuccess:async()=>{X==="update_request"?await Le(R,Ie,j):R?await Fe(R,j):G&&await Pe(G,j),_(!1),U(null),Q(null),V(null)},title:X==="update_request"?"Update Request":R?"Team Invitation":"Collaboration Request",customMessage:X==="update_request"?`Are you sure you want to ${j} this update request?${j==="accept"?" You will be redirected to the update page after accepting.":""}`:R?`This action will ${j} the team invitation.`:`This action will ${j} the collaboration request. ${j==="accept"?"You will be redirected to the update page after accepting.":""}`})})]})};export{Fs as default};
