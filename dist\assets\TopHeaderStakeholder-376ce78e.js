import{j as o}from"./@nextui-org/listbox-0f38ca19.js";import{R as s,r as a,u as p}from"./vendor-4cdf2bd1.js";import{G as n,A as c}from"./index-46de5032.js";import{B as l,E as u}from"./EditCompanyName-85298e9c.js";import{u as x}from"./useFundProfile-1f384383.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./lodash-82bd9112.js";import"./index.esm-6fcccbfe.js";import"./react-icons-36ae72b7.js";import"./MoonLoader-6f2b5db4.js";const q=()=>{const{state:i,dispatch:d}=s.useContext(n);s.useContext(c);const[f,r]=a.useState(""),{isOpen:h,showBackButton:m}=i,e=p();return x(),a.useEffect(()=>{const t=e.pathname.split("/");t[1]!=="user"&&t[1]!=="admin"?r(t[1]):r(t[2])},[e]),o.jsx("div",{className:"sticky right-0 top-0 z-[9] flex h-[72px] max-h-[72px] w-full items-center justify-between border-b-2  border-b-[#1d1f1a] bg-brown-main-bg px-5 py-4 shadow-sm sm:px-8",children:o.jsxs("div",{className:"flex items-center gap-3",children:[m&&o.jsx(l,{}),o.jsx(u,{})]})})};export{q as default};
