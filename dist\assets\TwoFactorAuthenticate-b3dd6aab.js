import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as s}from"./vendor-4cdf2bd1.js";import{M as p}from"./index-46de5032.js";import{t as h,S as f}from"./@headlessui/react-cdd9213e.js";const w=({option:l,setIsOpen:n,setActive2FA:m})=>{const[t,o]=s.useState(""),[r,x]=s.useState(""),c=new p,i=a=>{a.preventDefault(),c.enable2FA({token:t}).then(d=>{if(d!=null&&d.message)n(!1),m(u=>!u);else throw new Error("Incorrect")}).catch(d=>{alert(d.message)})};return s.useEffect(()=>{c.enable2FA().then(a=>{x(a)})},[]),e.jsx("div",{className:"mx-auto w-[100%] max-w-xs",children:l==="qr"?e.jsxs("div",{children:[e.jsx("h1",{className:"mb-5 text-center text-lg",children:"Scan QR Code to Continue Login"}),e.jsxs("form",{onSubmit:i,children:[e.jsx("input",{className:"h-[100] w-[70%]",required:!0,min:6,max:6,value:t,onChange:a=>o(a.target.value),type:"password",placeholder:"xxxxxxxx"}),e.jsx("button",{disabled:t.length<6,className:`${t.length<6?"bg-blue-300 ":"bg-blue-500 hover:bg-blue-700"} focus:shadow-outline  w-[30%] px-4 py-2 font-bold text-white  focus:outline-none disabled:cursor-not-allowed`,type:"submit",children:"Enable"})]}),r.qr_code?e.jsx("img",{src:r.qr_code,className:"mb-4 h-[300px] w-[100%] border bg-brown-main-bg px-8 pb-8 pt-6 shadow-lg"}):"",e.jsxs("p",{className:"text-center text-xs text-gray-500",children:["© ",new Date().getFullYear()," manaknightdigital inc. All rights reserved."]})]}):e.jsxs("div",{className:"py-[12px]",children:[e.jsx("h1",{className:"mb-5 text-center text-lg",children:"Enter code sent to your device"}),e.jsxs("form",{onSubmit:i,className:"flex items-center gap-3",children:[e.jsx("input",{className:"h-[100%] w-[70%] rounded border border-black bg-transparent",required:!0,min:6,max:6,value:t,onChange:a=>o(a.target.value),type:"password",placeholder:"xxxxxxxx"}),e.jsx("button",{disabled:t.length<6,className:`${t.length<6?"bg-black/50 ":"bg-black hover:bg-black"} focus:shadow-outline w-[30%] rounded px-4 py-2 font-iowan font-bold text-white  focus:outline-none disabled:cursor-not-allowed`,type:"submit",children:"Enable"})]})]})})},j=({isOpen:l,setIsOpen:n,setActive2FA:m})=>{const[t,o]=s.useState("qr"),[r,x]=s.useState(""),[c,i]=s.useState(!0),a=new p,d=()=>{t==="qr"?i(!1):a.enable2FA({type:"sms",phone:r}).then(b=>{b.error||i(g=>!g)})},u=()=>{n(!1),i(!0)};return e.jsx(h,{appear:!0,show:l,as:s.Fragment,children:e.jsxs(f,{as:"div",className:"relative z-[50]",onClose:()=>u(),children:[e.jsx(h.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"overflow-y-auto fixed inset-0",children:e.jsx("div",{className:"flex justify-center items-center p-4 min-h-full text-center",children:e.jsx(h.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(f.Panel,{className:"overflow-hidden p-6 w-full max-w-md text-sm text-left align-middle rounded-md shadow-xl transition-all transform bg-brown-main-bg",children:[e.jsx("h3",{className:"mb-6 text-xl font-semibold",children:"Enable 2FA"}),e.jsx("div",{className:"flex flex-col gap-6",children:c?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsxs("div",{className:"flex gap-3 items-center",children:[e.jsx("label",{className:"text-lg font-bold capitalize font-iowan",htmlFor:"2fa",children:"Select"}),e.jsxs("select",{value:t,onChange:b=>o(b.target.value),className:"h-[34px] w-[100px] rounded-md border border-[#1f1d1a] bg-transparent px-2 font-iowan text-sm",id:"2fa",name:"2fa",children:[e.jsx("option",{value:"qr",children:"QR Code"}),e.jsx("option",{value:"phone",children:"Phone"})]})]}),t==="phone"&&e.jsx("div",{children:e.jsx("input",{type:"text",value:r,className:"h-[34px] w-full rounded-md border border-[#1f1d1a] bg-transparent px-2 py-1 text-sm",onChange:b=>x(b.target.value),placeholder:"Enter your phone number"})})]}),e.jsxs("div",{className:"flex gap-4 justify-end items-center",children:[e.jsx("button",{className:"focus:shadow-outline h-[34px] w-[150px] rounded border border-black/40 bg-brown-main-bg px-6 py-2 font-bold text-[#1f1d1a] hover:bg-gray-100 focus:outline-none disabled:cursor-not-allowed",type:"button",onClick:()=>n(!1),children:"Cancel"}),e.jsx("button",{onClick:d,className:"focus:shadow-outline h-[34px] w-[150px] rounded bg-primary-black/90 px-6 py-2 font-bold text-white hover:bg-primary-black focus:outline-none disabled:cursor-not-allowed",type:"button",children:"Enable"})]})]}):e.jsx(w,{setIsOpen:n,option:t,setActive2FA:m})})]})})})})]})})},v=({active2FA:l,setActive2FA:n,loading:m})=>{const[t,o]=s.useState(!1),r=new p,x=()=>{r.disable2FA().then(i=>{n(!1)})},c=()=>{o(!0)};return console.log(l),e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mt-8",children:[e.jsx("div",{className:"mb-4 font-iowan text-[24px] font-[700] leading-[1.865rem]",children:"2FA"}),l===!0?e.jsx("div",{children:e.jsx("button",{onClick:x,className:"disabled:bg-disabledblack mt-6 block w-full rounded-lg bg-black px-5 py-3 text-center font-iowan text-lg font-semibold text-white transition-colors duration-100",type:"submit",children:"Disable 2FA"})}):l===!1&&e.jsxs("div",{children:[e.jsx("div",{}),e.jsx("p",{className:"mb-6 font-inter text-[16px] font-[400] leading-[1.21rem]",children:"You don’t have 2FA setup. Click the button below to enable it."}),e.jsx("button",{onClick:c,className:"disabled:bg-disabledblack block h-[2.6rem] w-[10rem] whitespace-nowrap rounded-[.125rem] bg-black px-3 py-1 text-center font-iowan text-sm font-semibold text-white transition-colors duration-100",type:"submit",children:"Enable 2FA"})]})]}),e.jsx(j,{isOpen:t,setIsOpen:o,setActive2FA:n})]})},C=v;export{C as T};
