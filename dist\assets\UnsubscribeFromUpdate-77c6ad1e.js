import{j as s}from"./@nextui-org/listbox-0f38ca19.js";import{M as n}from"./index-46de5032.js";import{i as u,r as e}from"./vendor-4cdf2bd1.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const R=()=>{const m=u(),[r,i]=e.useState(!0),[t,o]=e.useState("");async function c(){i(!0);try{const l=await new n().callRawAPI(`/v3/api/custom/goodbadugly/public/unsubscribe-from-updates/${m.id}`,{},"POST");o("")}catch(a){o(a.message)}i(!1)}return e.useEffect(()=>{c()},[]),s.jsx("div",{className:"flex min-h-screen w-full justify-center",children:s.jsxs("div",{className:"mx-auto mt-32 max-w-lg text-center",children:[" ",s.jsx("h1",{className:"mb-3 text-3xl font-semibold",children:r?"Processing...":t?s.jsx("p",{className:"text-red-500 empty:hidden",children:t}):"You have successfully unsubscribed from email notification"})," ",s.jsx("p",{children:r||t?"":"You have successfully unsubscribed from email notification "})]})})};export{R as default};
