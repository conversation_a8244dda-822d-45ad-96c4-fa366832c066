import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as a,R as j}from"./vendor-4cdf2bd1.js";import{a as F,A,u as M,M as W,bt as U,bu as $,bv as O}from"./index-46de5032.js";import{g as V}from"./react-audio-voice-recorder-a95781ec.js";import{b as q}from"./lucide-react-0b94883e.js";import{L as E,t as z}from"./@headlessui/react-cdd9213e.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const C=()=>{const f=a.useRef(!0),m=a.useRef(null),{showToast:s}=F(),[x,o]=a.useState({isSpeaking:!1,error:null,audioProgress:0,audioDuration:0});a.useEffect(()=>(typeof window<"u"&&window.speechSynthesis?m.current=window.speechSynthesis:(console.error("Speech synthesis not supported"),o(u=>({...u,error:"Speech synthesis not supported in this browser"}))),()=>{f.current=!1,l()}),[]);const l=a.useCallback(()=>{try{m.current&&m.current.cancel(),f.current&&o(u=>({...u,isSpeaking:!1,audioProgress:0,audioDuration:0,error:null}))}catch(u){console.error("Error stopping speech:",u)}},[]),b=a.useCallback(async u=>{try{if(!m.current)throw new Error("Speech synthesis not initialized");l(),o(r=>({...r,isSpeaking:!0,audioProgress:0,error:null}));const i=new SpeechSynthesisUtterance(u);i.rate=1,i.pitch=1,i.volume=1;const t=m.current.getVoices(),d=t.find(r=>r.lang.startsWith("en")&&r.name.includes("Female"))||t.find(r=>r.lang.startsWith("en"))||t[0];d&&(i.voice=d),i.onstart=()=>{f.current&&o(r=>({...r,isSpeaking:!0}))},i.onend=()=>{f.current&&o(r=>({...r,isSpeaking:!1,audioProgress:100}))},i.onerror=r=>{console.error("Speech synthesis error:",r),f.current&&o(y=>({...y,error:"Failed to play speech",isSpeaking:!1}))},m.current.speak(i)}catch(i){console.error("Speech Synthesis Error:",i),o(t=>({...t,error:i.message||"Failed to play speech",isSpeaking:!1})),err.message!=="TOKEN_EXPIRED"&&s(i.message||"Failed to play speech",5e3,"error")}},[l,s]);return{state:x,playAudioFromText:b,stopSpeaking:l}},K=()=>{var P,N;a.useContext(A);const{custom:f,showToast:m}=F(),{profile:s}=M(),x=(N=(P=s==null?void 0:s.companies)==null?void 0:P.find(n=>n==null?void 0:n.default_company))==null?void 0:N.name,{state:o,stopSpeaking:l,playAudioFromText:b}=C(),u=a.useRef(!0),[i,t]=a.useState({isListening:!1,isProcessing:!1,isSpeaking:!1,transcript:"",aiResponse:"",error:null,audioProgress:0,audioDuration:0}),[d,r]=a.useState(null),[y,w]=a.useState(!1),{startRecording:v,stopRecording:p,recordingBlob:h,isRecording:c}=V();a.useEffect(()=>{h&&(r(h),w(!0))},[h]),a.useEffect(()=>{!d||!y||L()},[d,y]);const L=async()=>{try{t(g=>({...g,isProcessing:!0}));let n=new FormData;const k=new File([d],"audio.wav",{type:"audio/wav"});n.append("file",k);const R=new W,S=await R.callTranscribe("/v3/api/custom/goodbadugly/ai/transcribe-audio",n,"POST");if(S!=null&&S.data){const g=await R.callRawAPI("/v3/api/custom/goodbadugly/integrations/ai/chat",{message:S.data,company:x||""},"POST");if(g&&!g.error){const{response:T}=g.data;t(B=>({...B,transcript:S.data,aiResponse:T,isProcessing:!1})),await b(T)}else throw new Error((g==null?void 0:g.error)||"Failed to get AI response")}}catch(n){console.error("Error processing voice:",n),t(k=>({...k,error:n.message||"Failed to process voice",isProcessing:!1})),m(n.message,5e3,"error")}finally{r(null),w(!1)}},I=a.useCallback(()=>{c?(p(),t(n=>({...n,isListening:!1,isProcessing:!0}))):(l(),t(n=>({...n,transcript:"",aiResponse:"",error:null,isListening:!0,isProcessing:!1})),v())},[c,l,v,p]),_=a.useCallback(()=>{t({isListening:!1,isProcessing:!1,isSpeaking:!1,transcript:"",aiResponse:"",error:null,audioProgress:0,audioDuration:0}),l(),c&&p()},[l,c,p]);a.useEffect(()=>{t(n=>({...n,isSpeaking:o.isSpeaking,error:o.error||n.error,audioProgress:o.audioProgress,audioDuration:o.audioDuration}))},[o]),a.useEffect(()=>()=>{u.current=!1,l(),c&&p()},[c,p]);const D=a.useCallback(()=>{c&&p(),t(n=>({...n,isListening:!1,isProcessing:!1,error:null}))},[c,p]);return{state:i,toggleListening:I,clearConversation:_,abortRequests:D,isRecording:c}},me=({onClose:f,onSuccess:m})=>{const{state:s,toggleListening:x,abortRequests:o}=K(),{playAudioFromText:l,stopSpeaking:b}=C(),{state:u,dispatch:i}=j.useContext(A),{profile:t}=u;console.log(t,"profile");const d=a.useRef(!1),r=a.useRef(null),[y,w]=j.useState(!1);console.log("Render cause - props:",{onClose:f,onSuccess:m}),console.log("Render cause - auth state:",u),console.log("Render cause - state:",s),j.useEffect(()=>{(async()=>{if(!d.current)try{const c=`Welcome, ${t==null?void 0:t.first_name}! I'm UpdateAI — your go-to knowledge base for everything about your company, product, and teams. Ask me anything, and I'll help you stay informed and up to speed. What's on your mind?`;r.current=setTimeout(()=>{d.current||(l(c).then(()=>{w(!0)}),d.current=!0)},500)}catch(c){console.error("Error playing welcome message:",c)}})()},[t==null?void 0:t.first_name]),j.useEffect(()=>()=>{b(),o(),r.current&&clearTimeout(r.current),d.current=!1},[]);const v=()=>{b(),d.current=!0,w(!1),r.current&&(clearTimeout(r.current),r.current=null),x()},p=()=>s.isListening?e.jsx(U,{className:"animate-pulse"}):s.isProcessing?e.jsx($,{className:"animate-pulse"}):e.jsxs("div",{className:"",children:[e.jsx(O,{className:"hidden md:block"}),e.jsx("img",{src:"/assets/microphone.svg",alt:"",className:"md:hidden"})]});return e.jsx(a.Fragment,{children:e.jsxs("div",{className:"overflow-y-auto pb-5 w-full bg-black text-brown-main-bg md:py-5 md:pb-10",children:[e.jsx("div",{className:"flex flex-col justify-center items-center w-full md:hidden",children:e.jsxs("div",{className:"flex gap-2 justify-start items-center md:hidden",children:[e.jsx("img",{src:"/updateai.svg",alt:"",className:"object-cover w-8 h-8"}),e.jsxs("span",{className:"flex justify-start items-center",children:["Update",e.jsx("span",{className:"!font-thin",children:"AI"})]})]})}),e.jsx("div",{className:"mt-[24px] text-center font-iowan text-[20px] font-[700] leading-[1.8rem] md:mt-0 md:text-left md:text-[1.5rem]",children:!s.isListening&&!s.isProcessing?`Hello ${t==null?void 0:t.first_name}, how can I help you today?`:""}),e.jsxs("div",{className:"relative mt-[2.5rem] flex w-full flex-col items-center justify-center gap-4 p-[10px] md:mt-[3.7rem]",children:[e.jsx(E,{className:"relative",children:({open:h})=>e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:v,disabled:s.isSpeaking||s.isProcessing,className:`relative h-[220px] max-h-[220px] min-h-[220px] min-w-[220px] max-w-[220px] rounded-full transition-all duration-300 md:h-[20rem] md:max-h-[20rem] md:min-h-[20rem] md:w-[20rem] md:min-w-[20rem] md:max-w-[20rem] 
                    ${s.isListening?"scale-110":"hover:scale-105"}
                    ${s.isSpeaking||s.isProcessing?"cursor-not-allowed opacity-50":"cursor-pointer"}`,children:e.jsx("div",{className:`absolute inset-0 rounded-full bg-gradient-to-r from-[#B1398B] via-[#FDBE16] to-[#22A386] p-[2px] \r
                    shadow-[0_0_10px_rgba(177,57,139,0.5),0_0_15px_rgba(253,190,22,0.6),0_0_20px_rgba(34,163,134,0.5)]\r
                    before:absolute before:inset-0 before:rounded-full before:bg-gradient-to-r before:from-[#B1398B] before:via-[#FDBE16] before:to-[#22A386] before:opacity-50 before:blur-md`,children:e.jsx("div",{className:"relative flex h-full w-full items-center justify-center rounded-full bg-brown-main-bg pb-[12px]",children:p()})})}),y&&!s.isListening&&!s.isProcessing&&!s.isSpeaking&&e.jsx(z,{show:!0,as:a.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",children:e.jsx(E.Panel,{static:!0,className:"absolute left-1/2 top-0 z-10 -mt-4 w-screen max-w-[200px] -translate-x-1/2 transform px-4 text-sm",children:e.jsxs("div",{className:"relative rounded-lg bg-[#1f1d1a] px-4 py-3 text-white shadow-lg ring-1 ring-[#1f1d1a]/5",children:[e.jsx("div",{className:"font-medium",children:"Tap the microphone to get started!"}),e.jsx("div",{className:"absolute left-1/2 top-full h-3 w-3 -translate-x-1/2 -translate-y-1/2 rotate-45 bg-[#1f1d1a]"})]})})})]})}),s.isListening&&e.jsx("div",{className:"absolute inset-0 left-[30px] top-[50px]  hidden h-8 w-8 items-center justify-center rounded-full md:left-[100px]",onClick:h=>{h.stopPropagation(),x()},children:e.jsx(q,{className:"h-8 text-red-500 w--8"})}),s.transcript&&e.jsx("div",{className:"text-center text-[1rem] font-medium text-brown-main-bg",children:s.transcript}),s.aiResponse&&e.jsx("div",{className:"text-center text-[1rem] font-medium text-brown-main-bg",children:s.aiResponse}),s.error&&e.jsx("div",{className:"text-center text-[1rem] font-medium text-red-500",children:s.error})]})]})})};export{me as default};
