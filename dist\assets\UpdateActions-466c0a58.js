import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{a as ge,u as be,bE as we,G as je,L as j,I as ye,p as de,s as k,w as ke,M as O,T as ve}from"./index-46de5032.js";import{r as m,b as Ne}from"./vendor-4cdf2bd1.js";import{h as $}from"./moment-a9aaa855.js";import{u as Ce}from"./useUpdate-d2d686a3.js";import{M as z}from"./index-af54e68d.js";import{A as Me,G as Pe}from"./index-f503aa1b.js";import{R as ce,b as Se,U as Ue}from"./index-3ff0ef21.js";import{u as _e}from"./useCompanyMember-f698e8a0.js";import{u as Ie}from"./useSubscription-58f7fe18.js";import{M as I}from"./index-dc002f62.js";import{D as Ae}from"./DocumentTextIcon-54b5e200.js";import{D as Le}from"./DocumentIcon-22c47322.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const ft=({onUpdateSuccess:q})=>{var V,J,Q,Z,D,ee,te,se,re,ne,le,ae,ie;const b=m.useRef(null),A=m.useRef(null),[Y,G]=m.useState(!1),[me,M]=m.useState(!1),[H,L]=m.useState(!1),[w,P]=m.useState(!1),[u,S]=m.useState(!1),[l,U]=m.useState(null),{authDispatch:ue,authState:T,globalState:v,setGlobalState:B,showToast:Te}=ge(),{triggerUpdate:F}=v;m.useState({startHerePrompt:!0,skipAwareness:!1});const x=Ne(),{profile:s,updateProfile:Ye}=be({isPublic:!1}),{loading:Ee,data:t,processRegisteredDate:Re,getSubscription:W,getCustomerSubscription:Oe,getSentUpdates:K}=Ie(),{companyMember:{myMembers:N},loading:E,getMyCompanyMembers:$e}=_e({filter:[`company_id,eq,${(J=(V=s==null?void 0:s.companies)==null?void 0:V[0])==null?void 0:J.id}`]}),{loading:C,updates:d,getUpdates:fe}=Ce();console.log(d);const r=((Q=v==null?void 0:v[we.createModel])==null?void 0:Q.loading)||!1;m.useEffect(()=>{!r&&l&&U(null)},[r]);const{dispatch:y}=m.useContext(je),i=v==null?void 0:v.subscriptionData,R={free_trial:1,pro:5,"pro yearly":5,business:10,"business yearly":10,enterprise:1/0};m.useEffect(()=>{s!=null&&s.id&&(S(!0),W({filter:[`user_id,eq,${s==null?void 0:s.id}`]}),K(s),fe())},[s==null?void 0:s.id]),m.useEffect(()=>{(async()=>{var c,o,p,f;if(t){if(!(t!=null&&t.subscription)&&!(t!=null&&t.trial_expired)){if((t==null?void 0:t.sentUpdates)>=1){P(!0),S(!1);return}P(!1),S(!1);return}if(t!=null&&t.trial_expired&&!(t!=null&&t.subscription)){P(!0),S(!1);return}const a=(f=(p=(o=(c=t==null?void 0:t.object)==null?void 0:c.plan)==null?void 0:o.nickname)==null?void 0:p.split(" ")[0])==null?void 0:f.trim(),g=R[a]||(a!=null&&a.includes("enterprise")?1/0:0);(t==null?void 0:t.sentUpdates)>=g&&g!==1/0?P(!0):P(!1),S(!1)}})()},[t]),m.useEffect(()=>{var n;F&&(s!=null&&s.id)&&((n=b==null?void 0:b.current)==null||n.click(),B("triggerUpdate",!1))},[s==null?void 0:s.id,F]),m.useEffect(()=>{!r&&l===null&&(s!=null&&s.id)&&(W({filter:[`user_id,eq,${s==null?void 0:s.id}`]}),K(s))},[r,l,s==null?void 0:s.id]),m.useEffect(()=>{var n,c,o;!E&&!(N!=null&&N.length)?(n=A==null?void 0:A.current)==null||n.click():!(C!=null&&C.list)&&!((c=d==null?void 0:d.list)!=null&&c.length)&&((o=b==null?void 0:b.current)==null||o.click())},[E,C==null?void 0:C.list]),console.log("UpdateAIButton render");async function pe(){var o,p,f,h;if(l!==null||r||u||w)return;if(t!=null&&t.trial_expired&&!(t!=null&&t.subscription)){k(y,"Please upgrade your account to create an update!",5e3,"error"),x("/member/billing?openManagePlan=true");return}const n=(h=(f=(p=(o=t==null?void 0:t.object)==null?void 0:o.plan)==null?void 0:p.nickname)==null?void 0:f.split(" ")[0])==null?void 0:h.trim(),c=R[n]||(n!=null&&n.includes("enterprise")?1/0:0);if((t==null?void 0:t.sentUpdates)>=c&&c!==1/0){k(y,"You have reached your monthly update limit for your current plan.",5e3,"error"),x("/member/billing?openManagePlan=true");return}try{U("blank");const a=await ke(y,ue,"updates",{name:"Update Title",user_id:T.user,mrr:0,arr:0,cash:0,burnrate:0,date:$().format("MMM D, YYYY"),public_link_enabled:0,private_link_open:1,company_id:T.company.id},!1);await new O().callRawAPI("/v3/api/custom/goodbadugly/activities/draft",{update_id:a.data},"POST"),a!=null&&a.error||x(`/member/edit-updates/${a.data}?autofocus=true`),M(!1)}catch(a){console.error(a),a.message!=="TOKEN_EXPIRED"&&k(y,a.message,5e3,"error"),U(null)}}async function X(){var o,p,f,h;if(l!==null||r||u||w)return;if(t!=null&&t.trial_expired&&!(t!=null&&t.subscription)){k(y,"Please upgrade your account to create an update!",5e3,"error"),x("/member/billing?openManagePlan=true");return}const n=(h=(f=(p=(o=t==null?void 0:t.object)==null?void 0:o.plan)==null?void 0:p.nickname)==null?void 0:f.split(" ")[0])==null?void 0:h.trim(),c=R[n]||(n!=null&&n.includes("enterprise")?1/0:0);if((t==null?void 0:t.sentUpdates)>=c&&c!==1/0){k(y,"You have reached your monthly update limit for your current plan.",5e3,"error"),x("/member/billing?openManagePlan=true");return}try{U("recent");const g=await new ve().create("updates",{name:`Update ${$().format("MMM D, YYYY")}`,user_id:T.user,mrr:0,arr:0,cash:0,burnrate:0,date:$().format("MMM D, YYYY"),public_link_enabled:0,private_link_open:1,company_id:T.company.id}),_=new O;await _.callRawAPI("/v3/api/custom/goodbadugly/activities/draft",{update_id:g.data},"POST");const oe=(await _.callRawAPI(`/v4/api/records/updates?filter=id,lt,${g.data}&page=1,1&order=id,desc`,void 0,"GET")).list[0];oe&&await xe(oe.id,g.data),M(!1),x(`/member/edit-updates/${g.data}?autofocus=true`)}catch(a){console.error(a),a.message!=="TOKEN_EXPIRED"&&k(y,a.message,5e3,"error"),U(null)}}async function xe(n,c){const o=new O,{list:p}=await o.callRawAPI(`/v4/api/records/notes?filter=update_id,eq,${n}&order=id,asc`,void 0,"GET");for(let f=0;f<p.length;f++){const h=p[f];await o.callRawAPI("/v4/api/records/notes",{update_id:c,content:h.content,type:h.type,status:0},"POST")}}const he=()=>{var n,c,o,p,f,h,a,g,_;if(!(i!=null&&i.subscription)){k(y,"You need to upgrade your plan to Enterprise or Business to use AI",5e3,"warning"),x("/member/billing?openManagePlan=true");return}if((o=(c=(n=i==null?void 0:i.object)==null?void 0:n.plan)==null?void 0:c.nickname)!=null&&o.toLowerCase().includes("pro")){k(y,"You need to upgrade your plan to Enterprise or Business to use AI",5e3,"warning"),x("/member/billing?openManagePlan=true");return}if((h=(f=(p=i==null?void 0:i.object)==null?void 0:p.plan)==null?void 0:f.nickname)!=null&&h.toLowerCase().includes("business")||(_=(g=(a=i==null?void 0:i.object)==null?void 0:a.plan)==null?void 0:g.nickname)!=null&&_.toLowerCase().includes("enterprise")){L(!0);return}x("/member/billing?openManagePlan=true")};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mb-[18px] hidden w-full flex-col flex-wrap items-center justify-between gap-5 md:flex md:flex-row",children:[e.jsxs("div",{className:"flex flex-row flex-wrap items-center justify-start gap-2 md:flex-nowrap md:gap-5",children:[!E&&!(N!=null&&N.length)?e.jsx(j,{children:e.jsx(z,{display:e.jsx(j,{children:e.jsx(ye,{buttonRef:A,type:"button",className:"add-member-button my-4 flex h-[2.75rem] w-fit items-center justify-center whitespace-nowrap rounded-[.0625rem] bg-[#1f1d1a] px-2 py-2 !text-[1rem] tracking-wide text-white outline-none focus:outline-none ",color:"black",onClick:()=>{},children:"Add Team Members"})}),onPopoverStateChange:n=>{var c,o;n||(c=d==null?void 0:d.list)!=null&&c.length||(o=b==null?void 0:b.current)==null||o.click()},className:"",tooltipClasses:"h-fit max-h-fit min-h-fit !border-[.125rem] !border-primary-black",openOnClick:!0,place:"bottom-start",classNameArrow:"!border-b !border-r !border-primary-black",children:e.jsx(j,{children:e.jsx(Me,{})})})}):null,e.jsx(z,{display:e.jsx(j,{children:e.jsx("button",{ref:b,type:"button",disabled:r||l!==null||u,className:`my-4 flex h-[2.75rem] w-fit items-center justify-center whitespace-nowrap rounded-[.0625rem] bg-[#1f1d1a] !px-6 py-2 font-iowan !text-[1rem] tracking-wide text-white outline-none focus:outline-none ${w&&!u&&!r?"opacity-90":""}`,onClick:()=>{if(w){x("/member/billing?openManagePlan=true");return}},children:r||u?e.jsx("div",{className:"flex items-center gap-2",children:e.jsx("span",{children:"Create Update"})}):w?t!=null&&t.subscription?"Upgrade Plan":t!=null&&t.trial_expired?"Subscribe":"Create Update":"Create Update"})}),tooltipClasses:"h-fit max-h-fit min-h-fit !border-[.125rem] !border-primary-black",openOnClick:!w&&!r&&l===null&&!u,place:"bottom-start",classNameArrow:"!border-b !border-r !border-primary-black",onPopoverStateChange:n=>{n||B("triggerUpdate",!1)},children:u?e.jsx("div",{className:"h-fit max-h-fit min-h-fit p-6",children:e.jsxs("div",{className:"flex items-center justify-center",children:[e.jsx(I,{size:20,color:"#000000",loading:!0,type:"beat"}),e.jsx("span",{className:"ml-2",children:"Checking update limits..."})]})}):w?e.jsxs("div",{className:"row-span-12 grid h-full max-h-full min-h-full w-full grid-cols-1 grid-rows-12 items-center justify-center p-5 md:w-[250px]",children:[e.jsx("div",{className:"row-span-8 text-center",children:e.jsx("p",{children:t!=null&&t.subscription?"You have reached your monthly update limit for your current plan.":t!=null&&t.trial_expired?"Please Upgrade your account to create an update!":"You have used your free update. Please subscribe to send more updates!"})}),e.jsx("div",{className:"row-span-4 w-full",children:e.jsx("button",{className:"flex w-full flex-col items-center justify-center gap-5 rounded-[.125rem] bg-primary-black px-4 py-2 font-iowan text-[1rem] font-[700] leading-5 text-white",onClick:()=>{x("/member/billing?openManagePlan=true")},children:t!=null&&t.subscription?"Upgrade Plan":"Subscribe"})})]}):e.jsx("div",{className:"h-fit max-h-fit min-h-fit",children:e.jsx(Pe,{showModifyRecentOption:((Z=d==null?void 0:d.list)==null?void 0:Z.length)>0,onModifyRecent:X,limitReached:w,limitChecking:u,subscriptionData:t})})}),e.jsx(j,{children:e.jsx(ce,{onSuccess:q})})]}),e.jsx(j,{children:e.jsx(Se,{})})]}),e.jsx("div",{className:"fixed bottom-4 right-4 z-50 md:hidden",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("button",{onClick:he,className:"flex h-[60px] min-h-[60px] w-[60px] min-w-[60px] items-center justify-center rounded-full bg-[#1f1d1a]",children:e.jsx(j,{children:e.jsx("img",{src:"/updateai.svg",alt:"",className:"h-8 w-8 object-cover"})})}),e.jsxs("div",{className:"relative",children:[e.jsx("button",{onClick:()=>G(!Y),className:"flex h-[60px] min-h-[60px] w-[60px] min-w-[60px] items-center justify-center rounded-full bg-[#1f1d1a]",children:Y?e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M18 6L6 18M6 6L18 18",stroke:"white",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}):e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M12 5V19M5 12H19",stroke:"white",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),Y&&e.jsxs("div",{className:"absolute bottom-full right-0 mb-2 flex min-w-[132px] flex-col gap-2",children:[w?e.jsx(z,{display:e.jsx("button",{onClick:()=>{x("/member/billing?openManagePlan=true")},className:`flex h-[40px] w-[140px] items-center justify-center rounded-[4px] bg-[#1f1d1a] font-iowan text-base font-bold text-white ${r?"":"opacity-50"}`,children:t!=null&&t.subscription?"Upgrade Plan":t!=null&&t.trial_expired?"Subscribe":"Upgrade Plan"}),openOnClick:!1,backgroundColor:"#1f1d1a",place:"top",children:e.jsx("div",{className:"p-3 text-white",children:e.jsx("p",{className:"text-sm",children:t!=null&&t.subscription?"You have reached your monthly update limit for your current plan.":t!=null&&t.trial_expired?"Please Upgrade your account to create an update!":"You have used your free update. Please subscribe to send more updates!"})})}):e.jsx("button",{onClick:()=>{!r&&l===null&&!u&&!w&&(G(!1),M(!0))},disabled:r||l!==null||u,className:`flex h-[40px] w-[140px] items-center justify-center rounded-[4px] bg-[#1f1d1a] font-iowan text-base font-bold text-white ${r||l!==null||u?"cursor-not-allowed":""}`,children:r||u?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{children:"Create Update"}),e.jsx(I,{size:10,color:"#ffffff",loading:!0,type:"beat",className:"!ml-2"})]}):"Create Update"}),e.jsx(j,{children:e.jsx(ce,{onSuccess:q})})]})]})]})}),(i==null?void 0:i.subscription)&&(((te=(ee=(D=i==null?void 0:i.object)==null?void 0:D.plan)==null?void 0:ee.nickname)==null?void 0:te.toLowerCase().includes("business"))||((ne=(re=(se=i==null?void 0:i.object)==null?void 0:se.plan)==null?void 0:re.nickname)==null?void 0:ne.toLowerCase().includes("enterprise")))&&e.jsx(j,{children:e.jsx(de,{clickOutToClose:!0,modalHeader:!1,panelHeader:!0,modalHeaderClassName:"!bg-black text-brown-main-bg",title:e.jsx(j,{children:e.jsxs("div",{className:"flex items-center justify-start gap-2",children:[e.jsx("img",{src:"/updateai.svg",alt:"",className:"h-8 w-8 object-cover"}),e.jsxs("span",{className:"flex items-center justify-start",children:[e.jsx("b",{children:"Update"}),e.jsx("span",{className:"!font-thin",children:"AI"})]})]})}),isOpen:H,modalCloseClick:()=>L(!1),classes:{modalDialog:"!bg-black min-h-fit max-h-fit w-full !max-h-[500px] md:!w-fit !max-w-full min-w-full md:!min-w-[595px] !min-h-[550px] md:!max-w-[595px] overflow-y-auto !gap-0 !m-0 !mt-auto !rounded-t-2xl",modalContent:"!bg-black !text-brown-main-bg !z-10 !mt-0 overflow-y-auto max-h-[500px] !pt-0",modal:"h-full items-end",modal:"!p-0",panelClassName:"!bg-[#565452]"},children:H?e.jsx("div",{children:e.jsx(Ue,{onClose:()=>L(!1),onSuccess:()=>{L(!1)}})}):null})}),e.jsx(de,{panelHeader:!0,clickOutToClose:!r&&!l,isOpen:me,modalCloseClick:()=>{!r&&!l&&M(!1)},classes:{modalDialog:"!bg-brown-main-bg h-fit min-h-fit max-h-fit w-full !max-w-full !min-w-full !rounded-t-xl !m-0 !mt-auto",modalContent:"!bg-brown-main-bg !z-10 !mt-0 overflow-y-auto !pt-0 !rounded-b-0",modal:"h-full items-end !p-0"},children:e.jsxs("div",{className:"h-fit max-h-fit min-h-fit",children:[u?e.jsxs("div",{className:"flex items-center justify-center p-8",children:[e.jsx(I,{size:20,color:"#000000",loading:!0,type:"beat"}),e.jsx("span",{className:"ml-2",children:"Checking update limits..."})]}):e.jsx("div",{className:`flex w-full items-start gap-4 py-4 ${((le=d==null?void 0:d.list)==null?void 0:le.length)>0?"hidden":""}`,children:e.jsxs("div",{children:[e.jsx("div",{className:"flex items-center gap-2",children:e.jsx("span",{className:"text-left font-iowan text-[18px] font-semibold md:text-[20px]",children:((ae=d==null?void 0:d.list)==null?void 0:ae.length)>0?"":"Start Here"})}),e.jsx("p",{className:"mt-1 text-[14px] font-medium md:text-[14px]",children:"Create an update by selecting:"})]})}),!u&&((ie=d==null?void 0:d.list)==null?void 0:ie.length)>0&&e.jsxs("button",{disabled:l!==null||r,onClick:()=>{l===null&&!r&&X()},className:"mt-6 flex h-fit max-h-fit min-h-fit w-full items-start justify-start gap-4 border-b border-b-[#1f1d1a]/10 bg-brown-main-bg px-1 py-4 hover:bg-brown-main-bg/50",children:[e.jsx("div",{className:"flex items-center justify-center gap-2 rounded-[.625rem] border border-[#1f1d1a] p-2",children:e.jsx("img",{src:"/assets/edit-2.svg",alt:"",className:"h-8 w-8"})}),e.jsxs("div",{className:"flex-grow",children:[e.jsx("p",{className:"text-left text-[14px] font-semibold md:text-xl",children:"Modify with Recent"}),e.jsx("p",{className:"md: mt-1 text-[12px] font-medium",children:"Start with most recent sent update"})]}),(l==="recent"||r)&&e.jsx("div",{className:"flex items-center",children:e.jsx(I,{size:10,color:"#000000",loading:!0,type:"beat",className:"ml-2"})})]}),!u&&e.jsxs(e.Fragment,{children:[e.jsxs("button",{disabled:l!==null||r,onClick:()=>{l===null&&!r&&(M(!1),x("/member/select-template"))},className:`flex w-full items-start gap-4 border-b border-b-[#1f1d1a]/10 px-1 py-4 hover:bg-brown-main-bg/50 ${l!==null||r?"cursor-not-allowed":""}`,children:[e.jsx("div",{className:"flex items-center justify-center gap-2 rounded-[.625rem] border border-[#1f1d1a] p-2",children:e.jsx(Ae,{className:"h-8 w-8 text-primary-black",strokeWidth:2})}),e.jsxs("div",{className:"flex-grow",children:[e.jsx("p",{className:"text-left text-[14px] font-semibold md:text-xl",children:"New Template Update"}),e.jsx("p",{className:"md: mt-1 text-[12px] font-medium",children:"Start an update using an existing update templates"})]})]}),e.jsxs("button",{disabled:l!==null||r,onClick:()=>{l===null&&!r&&pe()},className:"flex h-fit max-h-fit min-h-fit w-full items-start justify-start gap-4 border-b border-b-[#1f1d1a]/10 bg-brown-main-bg px-1 py-4 hover:bg-brown-main-bg/50",children:[e.jsx("div",{className:"flex items-center justify-center gap-2 rounded-[.625rem] border border-[#1f1d1a] p-2",children:e.jsx(Le,{className:"h-8 w-8 text-primary-black",strokeWidth:2})}),e.jsxs("div",{className:"flex-grow",children:[e.jsx("p",{className:"text-left text-[14px] font-semibold md:text-xl",children:"New Blank Update"}),e.jsx("p",{className:"md: mt-1 text-[12px] font-medium",children:"Start an update from scratch"})]}),(l==="blank"||r&&l==="blank")&&e.jsx("div",{className:"flex items-center",children:e.jsx(I,{size:10,color:"#000000",loading:!0,type:"beat",className:"ml-2"})})]})]})]})})]})};export{ft as default};
