import{j as n}from"./@nextui-org/listbox-0f38ca19.js";import{a as ce,M as me,u as fe,L as P,A as de,O as ge,f as xe,C as ne}from"./index-46de5032.js";import{M as he}from"./index-d0de8b06.js";import{R as Z,r as k,b as pe,L as ue}from"./vendor-4cdf2bd1.js";import{R as J}from"./index-a9c1e6e9.js";import{U as we,b as re}from"./index-abd77600.js";import{a as le}from"./index-8a8a991b.js";import te from"./MkdPopover-dd21a7e9.js";import{M as oe}from"./index-af54e68d.js";import{u as be}from"./useDate-14dbf4c5.js";import"./moment-a9aaa855.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./react-tooltip-7630c8e3.js";import"./@mantine/core-691d33c8.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";const je=(O={filter:[]})=>{const{authDispatch:T,globalDispatch:r,custom:h}=ce(),[d,C]=Z.useState({list:[],single:null}),[g,N]=Z.useState({list:!1,single:!1,update:!1,delete:!1,create:!1}),c=Z.useCallback((F={filter:[]})=>(async()=>{N(w=>({...w,list:!0}));try{const x=await new me().callRawAPI("/v3/api/custom/goodbadugly/new-activities",{},"GET");return x!=null&&x.error?null:(C($=>({...$,list:(x==null?void 0:x.data)||[]})),{data:(x==null?void 0:x.data)||[],pagination:(x==null?void 0:x.pagination)||{}})}catch(w){return console.error("Error fetching activities:",w),null}finally{N(w=>({...w,list:!1}))}})(),[T,r]);return{loading:g,activities:d,getActivities:c}},Ce=({isOpen:O,onClose:T,update:r,onAccept:h,onDecline:d,loading:C=!1})=>{var w,x,$,V,M;fe({isPublic:!1});const{convertDate:g}=be();if(!O)return null;const N=i=>{var q,Q,B;return(q=i==null?void 0:i.type)!=null&&q.includes("Collaboration Request")?"Collaboration Request":(Q=i==null?void 0:i.type)!=null&&Q.includes("Update Request")?"Update Request":(B=i==null?void 0:i.type)!=null&&B.includes("Update (In)")?"Update (In)":"Request"},c=i=>(i==null?void 0:i.status)===0||(i==null?void 0:i.status)===6?"Pending":(i==null?void 0:i.status)===1?"Accepted":(i==null?void 0:i.status)===2?"Rejected":(i==null?void 0:i.status)===7?"Received":"",F=i=>({0:"#F6A13C",1:"#9DD321",2:"#BCBBBA",6:"#F6A13C",7:"#CAB8FF"})[i]||"#BCBBBA";return n.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50",children:n.jsxs("div",{className:"relative w-full max-w-md rounded-md bg-brown-main-bg p-6 shadow-xl",children:[n.jsx("button",{onClick:T,className:"absolute right-4 top-4 text-gray-500 hover:text-gray-700",children:n.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:n.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),n.jsxs("div",{className:"flex flex-col items-center",children:[n.jsx(P,{children:n.jsx(oe,{display:n.jsx("p",{className:"w-full truncate text-center font-iowan text-[1.125rem] font-[700] capitalize leading-5 text-[#1f1d1a]",children:r==null?void 0:r.title}),openOnClick:!1,show:!!(r!=null&&r.company_name),backgroundColor:"#1f1d1a",place:"top",children:r!=null&&r.company_name?n.jsxs("p",{className:"mt-1 text-sm text-white",children:["(",r==null?void 0:r.company_name,")"]}):null})}),n.jsx("p",{className:"font-iowan-regular mt-3 text-[14px] font-normal text-[#1f1d1a]",children:g((r==null?void 0:r.activity_time)??(r==null?void 0:r.sent_at)??(r==null?void 0:r.date),{formatMatcher:"best fit",year:"numeric",month:"numeric",day:"numeric",timeZoneName:"short",timeZone:"America/Los_Angeles"}).replace(", "," - ")}),n.jsxs("div",{className:"mt-4 flex flex-col items-center justify-center gap-1",children:[n.jsx("span",{style:{backgroundColor:F(r==null?void 0:r.status)},className:"flex h-[1.5rem] w-full max-w-fit items-center justify-center whitespace-nowrap rounded-[100px] border-[.0625rem] border-[#1f1d1a] px-[10px] font-iowan text-xs",children:c(r)}),n.jsx(P,{children:n.jsx(oe,{display:n.jsx("p",{className:"font-iowan-regular text-black",children:N(r)}),openOnClick:!1,backgroundColor:"#1f1d1a",place:"top",children:n.jsx("p",{className:"mt-1 text-sm text-white",children:c(r)})})})]}),n.jsxs("div",{className:"mt-6 w-full",children:[n.jsx("p",{className:"mb-2 text-sm font-medium text-gray-700",children:"From:"}),n.jsxs("div",{className:"flex items-center gap-2",children:[n.jsx("div",{className:"flex h-8 w-8 items-center justify-center overflow-hidden rounded-full bg-gray-200",children:(w=r==null?void 0:r.from)!=null&&w.photo?n.jsx("img",{src:r.from.photo,alt:`${r.from.first_name} ${r.from.last_name}`,className:"h-full w-full object-cover"}):n.jsx("span",{className:"text-sm font-medium",children:(($=(x=r==null?void 0:r.from)==null?void 0:x.first_name)==null?void 0:$[0])||""})}),n.jsxs("span",{className:"font-medium",children:[(V=r==null?void 0:r.from)==null?void 0:V.first_name," ",(M=r==null?void 0:r.from)==null?void 0:M.last_name]})]})]}),n.jsx("div",{className:"mt-6 w-full",children:n.jsx(we,{onDecline:()=>d(r),onAccept:()=>h(r),update:r,loading:C})})]})]})})},p={UPDATE_IN:"Update (In)",UPDATE_OUT:"Update (Out)",REQUEST_IN:"Update Request (In)",REQUEST_OUT:"Update Request (Out)",COLLABORATOR_IN:"Collaboration Request (In)",COLLABORATOR_OUT:"Collaboration Request (Out)"},ie=[{header:"Title",accessor:"name",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,type:"dashboard"},{header:"Type",accessor:"type",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Status",accessor:"update_status",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Date",accessor:"date_created",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"To/From",accessor:"user_data",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0}],ae={0:9,1:10,2:7,3:0,4:1,5:2,6:5,9:-2,7:8},_e=O=>{const T=new Date,r=new Date(O),h=T-r,d=Math.floor(h/(1e3*60)),C=Math.floor(h/(1e3*60*60)),g=Math.floor(h/(1e3*60*60*24));return d<60?`${d} ${d===1?"minute":"minutes"} ago`:C<24?`${C} ${C===1?"hour":"hours"} ago`:`${g} ${g===1?"day":"days"} ago`},ss=({refreshRef:O})=>{var B,G;const[T,r]=k.useState(!1),h=k.useRef(null),[d,C]=k.useState({page:1,data:[],limit:0,pages:0,total:0,use:!0,reload:!1,canNextPage:!1,canPreviousPage:!1}),[g,N]=k.useState({updateSeenLoading:!1,showModal:!1,selectedUpdate:null,modalLoading:!1}),c=pe(),{getActivities:F}=je();ce(),k.useContext(de),console.log(g==null?void 0:g.updateSeenLoading);const w=e=>{N(m=>({...m,showModal:!0,selectedUpdate:e}))},x=()=>{N(e=>({...e,showModal:!1,selectedUpdate:null,modalLoading:!1}))},$=async e=>{var m,s,l,a;N(o=>({...o,modalLoading:!0})),(m=e==null?void 0:e.type)!=null&&m.includes(p.COLLABORATOR_IN)?c(`/member/updates?view=team_updates&availability=available&update_id=${(s=e==null?void 0:e.update)==null?void 0:s.id}`):(l=e==null?void 0:e.type)!=null&&l.includes(p.REQUEST_IN)&&c(`/member/updates?view=my_updates&availability=available&update_id=${(a=e==null?void 0:e.update)==null?void 0:a.id}`),x()},V=async e=>{var m,s,l,a;N(o=>({...o,modalLoading:!0})),(m=e==null?void 0:e.type)!=null&&m.includes(p.COLLABORATOR_IN)?c(`/member/updates?view=team_updates&availability=available&update_id=${(s=e==null?void 0:e.update)==null?void 0:s.id}&action=decline`):(l=e==null?void 0:e.type)!=null&&l.includes(p.REQUEST_IN)&&c(`/member/updates?view=my_updates&availability=available&update_id=${(a=e==null?void 0:e.update)==null?void 0:a.id}&action=decline`),x()},M=async e=>{var m,s,l,a,o,t,_,b,R,U,I,A,y,S,j,E,L,u,f,D,z,H,Y,K,W,X,v,ee,se;if(console.log(e,"upup"),!(e.status==10&&((m=e.type)!=null&&m.includes(p.UPDATE_IN)))&&!(e.status==10&&((s=e.type)!=null&&s.includes(p.REQUEST_OUT)))){if((l=e==null?void 0:e.type)!=null&&l.includes(p.UPDATE_IN)&&e.status===6){c(`/member/update/private/view/${(a=e==null?void 0:e.update)==null?void 0:a.id}?new=${e==null?void 0:e.id}`);return}if((o=e==null?void 0:e.type)!=null&&o.includes(p.UPDATE_OUT))if(e.status==6){c(`/member/edit-updates/${(t=e==null?void 0:e.update)==null?void 0:t.id}?new=${e==null?void 0:e.id}`);return}else if(e.status==3){console.log("update","hipp"),c(`/member/edit-updates/${(_=e==null?void 0:e.update)==null?void 0:_.id}`);return}else if(e.status==8){console.log("update","hipp"),c(`/member/edit-updates/${(b=e==null?void 0:e.update)==null?void 0:b.id}`);return}else{c(`/member/update/private/view/${(R=e==null?void 0:e.update)==null?void 0:R.id}`);return}else if(((e==null?void 0:e.status)==1||(e==null?void 0:e.status)==7)&&((U=e==null?void 0:e.type)!=null&&U.includes(p.REQUEST_OUT)||(I=e==null?void 0:e.type)!=null&&I.includes(p.REQUEST_IN)))if(console.log(e==null?void 0:e.isChild,"child",!((A=e.update)!=null&&A.sent_at)),!((y=e.update)!=null&&y.sent_at)&&((S=e==null?void 0:e.type)!=null&&S.includes(p.REQUEST_IN))){c(`/member/edit-updates/${(j=e==null?void 0:e.update)==null?void 0:j.id}`);return}else if((E=e.update)!=null&&E.sent_at){c(`/member/update/private/view/${(L=e==null?void 0:e.update)==null?void 0:L.id}`);return}else return;else if((u=e==null?void 0:e.type)!=null&&u.includes(p.REQUEST_IN)){if(e.status===0||e.status===6){w(e);return}if(e.status===1||e.status===7||e.status===8){c(`/member/edit-updates/${(f=e==null?void 0:e.update)==null?void 0:f.id}`);return}return}else if((D=e==null?void 0:e.type)!=null&&D.includes(p.COLLABORATOR_IN)){if(e.status===0||e.status===6){w(e);return}if(e.status===1||e.status===7){c(`/member/edit-updates/${(z=e==null?void 0:e.update)==null?void 0:z.id}`);return}return}if((H=e==null?void 0:e.type)!=null&&H.includes(p.COLLABORATOR_OUT))return c(`/member/edit-updates/${(Y=e==null?void 0:e.update)==null?void 0:Y.id}`);if((K=e==null?void 0:e.type)!=null&&K.includes(p.REQUEST_OUT)){if(e.status===6){c(`/member/update/private/view/${(W=e==null?void 0:e.update)==null?void 0:W.id}?new=${e==null?void 0:e.id}`);return}if(e.status===0){c(`/member/updates?view=team_updates&availability=available&update_id=${(X=e==null?void 0:e.update)==null?void 0:X.id}`);return}if(e.status===1||e.status===7){c(`/member/update/private/view/${(v=e==null?void 0:e.update)==null?void 0:v.id}`);return}return c(`/member/update/private/view/${(ee=e==null?void 0:e.update)==null?void 0:ee.id}`)}return c(`/member/update/private/view/${(se=e==null?void 0:e.update)==null?void 0:se.id}`)}},i=e=>{const m=(s,l=!1)=>{const a={...s},o=(s==null?void 0:s.type)||"";return a.mobile_data={...s,user_data:typeof o=="string"&&o.includes("(In)")?[s.from]:typeof o=="string"&&o.includes("(Out)")?s.to:s.recipients||[],recipient_data:typeof o=="string"&&o.includes("(In)")?[s.from]:typeof o=="string"&&o.includes("(Out)")?s.to:s.recipients||[]},a.isChild=l,ie.forEach(t=>{var _,b,R,U,I,A,y,S;switch(t==null?void 0:t.accessor){case"type":const j=l&&typeof o=="string"&&o.includes("Request (In)")?"Update (Out)":l&&typeof o=="string"&&o.includes("Request (Out)")?"Update (In)":o||"-",E=typeof j=="string"&&j.includes("(In)"),L=typeof j=="string"&&j.includes("(Out)"),u=typeof j=="string"?j.replace("(In)","(Incoming)").replace("(Out)","(Outgoing)"):"-";a[t==null?void 0:t.accessor]=n.jsx(te,{display:n.jsxs("span",{className:"flex w-fit flex-nowrap items-center justify-normal gap-[.3125rem] p-[.3125rem] capitalize",children:[n.jsx("span",{className:"text[13px] min-w-[max-content] max-w-[9.375rem] truncate whitespace-nowrap break-words font-inter font-[400] leading-[1.125rem] text-black",children:u.replace("(Incoming)","").replace("(Outgoing)","")}),E?n.jsx("img",{src:"/assets/incoming.svg",alt:"incoming"}):L?n.jsx("img",{src:"/assets/outgoing.svg",alt:"outgoing"}):null]}),place:"left",backgroundColor:"#000",openOnClick:!1,children:n.jsx("div",{className:"flex max-w-[9.375rem] items-center gap-2",children:n.jsx("span",{className:"w-full whitespace-normal break-words font-inter text-[.875rem] font-[400] leading-[1.125rem] text-white",children:u})})});break;case"name":a[t==null?void 0:t.accessor]=s.title?n.jsxs("div",{className:"flex min-w-[300px] items-center gap-2",children:[l&&n.jsx("svg",{className:"mr-2",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:n.jsx("path",{d:"M13.8538 11.3538L10.8538 14.3538C10.7838 14.4238 10.6947 14.4714 10.5977 14.4908C10.5006 14.5101 10.4 14.5002 10.3086 14.4623C10.2172 14.4244 10.1391 14.3603 10.0841 14.278C10.0292 14.1957 9.99992 14.0989 10 14V11.5H4.5C4.36739 11.5 4.24021 11.4473 4.14645 11.3536C4.05268 11.2598 4 11.1326 4 11V2C4 1.86739 4.05268 1.74021 4.14645 1.64645C4.24021 1.55268 4.36739 1.5 4.5 1.5C4.63261 1.5 4.75979 1.55268 4.85355 1.64645C4.94732 1.74021 5 1.86739 5 2V10.5H10V8C9.99992 7.90105 10.0292 7.80431 10.0841 7.72201C10.1391 7.63971 10.2172 7.57556 10.3086 7.53769C10.4 7.49981 10.5006 7.48991 10.5977 7.50924C10.6947 7.52856 10.7838 7.57624 10.8538 7.64625L13.8538 10.6462C13.9002 10.6927 13.9371 10.7478 13.9623 10.8085C13.9874 10.8692 14.0004 10.9343 14.0004 11C14.0004 11.0657 13.9874 11.1308 13.9623 11.1915C13.9371 11.2522 13.9002 11.3073 13.8538 11.3538Z",fill:"black"})}),n.jsx("button",{onClick:()=>M(s),className:"font-medium line-clamp-1 text-ellipsis font-iowan hover:underline",children:s.title})]}):"-";break;case"date_created":a[t==null?void 0:t.accessor]=n.jsxs("span",{className:"font-iowan-regular flex items-center gap-2 text-[14px]",children:[n.jsx(ne,{}),new Date(s.date).toLocaleDateString("en-US",{month:"2-digit",day:"2-digit",year:"2-digit"})]});break;case"update_status":let f;l?(console.log(s,"child"),s.status==10?f=12:(_=s.type)!=null&&_.includes("Request (Out)")?(b=s.update)!=null&&b.sent_at?s.status===6?f=5:f=8:f=9:(R=s.type)!=null&&R.includes("Request (In)")&&(s.status==8?f=11:f=(U=s==null?void 0:s.update)!=null&&U.sent_at?1:0)):(I=s.type)!=null&&I.includes("(In)")&&s.status==1||(A=s.type)!=null&&A.includes("(In)")&&s.status==8?f=8:(y=s.type)!=null&&y.includes("(Out)")&&s.status==8?f=11:s.status==10?f=12:f=ae[s.status]||0;const D=s.status==8?(S=s==null?void 0:s.metadata)==null?void 0:S.scheduled_date:null;a[t==null?void 0:t.accessor]=n.jsx(re,{status:f,scheduledDate:D});break;case"user_data":console.log(s.type.includes("Collaborator (Out)"),s==null?void 0:s.status);const z=s.isChild?Array.isArray(s.recipients)?s.recipients:[]:s.type.includes("Collaborator (In)")||s.type.includes("Request (In)")||s.type.includes("Update (In")?[s.from]:s.type.includes("Collaborator (Out)")&&(s==null?void 0:s.status)==9||s.type.includes("Request (Out)")?s.to:Array.isArray(s.recipients)?s.recipients.map(H=>JSON.parse(H)):[];a[t==null?void 0:t.accessor]=n.jsx("div",{children:n.jsx(P,{children:n.jsx(J,{members:z,title:n.jsx(n.Fragment,{})})})});break;default:a[t==null?void 0:t.accessor]=s[t==null?void 0:t.accessor]}}),a};return e==null?void 0:e.reduce((s,l)=>{var a,o,t,_;if(s.push(m(l)),(a=l==null?void 0:l.type)!=null&&a.includes("Request (Out)")&&((o=l==null?void 0:l.metadata)!=null&&o.is_child_available)&&(l.status===6||l.status===1)){const b={...l,title:l.title,sent_at:((t=l.metadata)==null?void 0:t.responded_at)||((_=l.metadata)==null?void 0:_.sent_at),type:l.type,is_child:!0};s.push(m(b,!0))}if(typeof(l==null?void 0:l.type)=="string"&&l.type.includes("Update Request")&&(l.status===1||l.status===7||l.status===8)){const b={...l,title:l.title,sent_at:l.sent_at,type:l.type};s.push(m(b,!0))}return s},[])},q=async()=>{console.log("fetchData"),r(!0);try{const e=await F();if(console.log("result",e),e!=null&&e.data){const m=i(e.data);C(s=>({...s,data:m,reload:!0,total:e.pagination.total,page:e.pagination.page,limit:e.pagination.limit,pages:e.pagination.pages}))}}catch(e){console.log("error",e)}finally{r(!1)}};k.useEffect(()=>{var e;h!=null&&h.current&&(d!=null&&d.reload)&&((e=h==null?void 0:h.current)==null||e.click(),C(m=>({...m,reload:!1})))},[d==null?void 0:d.reload]),k.useEffect(()=>{O&&(console.log("refreshRef","success"),O.current=()=>{h!=null&&h.current&&(console.log("refreshRef2","success"),q())})},[O]);const Q=(e,m)=>{var R,U,I,A,y,S,j,E,L;console.log("activity",e);const s=(e==null?void 0:e.mobile_data)||e,l=typeof s.type=="string"?s.type:"",a=u=>u?Array.isArray(u)?u.map(f=>{if(typeof f=="string")try{return JSON.parse(f)}catch(D){return console.error("Error parsing recipient:",D),f}return f}):[]:[];let o=[];e.isChild?o=Array.isArray(s.recipients)?s.recipients:[]:l.includes("Collaborator (In)")||l.includes("Request (In)")?o=[s.from]:l.includes("Collaborator (Out)")&&(e==null?void 0:e.status)==9||l.includes("Request (Out)")?o=a(s.to):s.recipient_data?o=a(s.recipient_data):s.recipients&&(o=a(s.recipients)),console.log("Parsed recipientData:",o),console.log("Activity type:",e.type);let t,_=typeof s.type=="string"?s.type:String(s.type||"");e.isChild?(R=s.type)!=null&&R.includes("Request (Out)")?(U=s.update)!=null&&U.sent_at?s.status===6?t=5:t=8:t=9:(I=s.type)!=null&&I.includes("Request (In)")&&(s.status==8?t=11:t=(A=s==null?void 0:s.update)!=null&&A.sent_at?1:0):_.includes("(In)")&&s.status==1||(y=s.type)!=null&&y.includes("(In)")&&s.status==8?t=8:(S=s.type)!=null&&S.includes("(Out)")&&s.status==8?t=11:e.status==10?t=12:t=ae[e.status]||0,console.log("status",e.type,t,(j=e==null?void 0:e.update)==null?void 0:j.sent_at);const b=s.status==8?(E=s==null?void 0:s.metadata)==null?void 0:E.scheduled_date:null;return b&&console.log("Scheduled date:",b),n.jsxs("div",{className:`flex flex-col gap-2 rounded-sm px-5 py-4 md:px-0 ${m%2!==0?"border-y border-y-[#E6DCD2] bg-[#F9EADF]":"bg-brown-main-bg"}`,children:[n.jsxs("div",{className:"flex justify-between items-center",children:[n.jsxs("div",{className:"flex items-center",children:[e.isChild&&n.jsx("svg",{className:"mr-2",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:n.jsx("path",{d:"M13.8538 11.3538L10.8538 14.3538C10.7838 14.4238 10.6947 14.4714 10.5977 14.4908C10.5006 14.5101 10.4 14.5002 10.3086 14.4623C10.2172 14.4244 10.1391 14.3603 10.0841 14.278C10.0292 14.1957 9.99992 14.0989 10 14V11.5H4.5C4.36739 11.5 4.24021 11.4473 4.14645 11.3536C4.05268 11.2598 4 11.1326 4 11V2C4 1.86739 4.05268 1.74021 4.14645 1.64645C4.24021 1.55268 4.36739 1.5 4.5 1.5C4.63261 1.5 4.75979 1.55268 4.85355 1.64645C4.94732 1.74021 5 1.86739 5 2V10.5H10V8C9.99992 7.90105 10.0292 7.80431 10.0841 7.72201C10.1391 7.63971 10.2172 7.57556 10.3086 7.53769C10.4 7.49981 10.5006 7.48991 10.5977 7.50924C10.6947 7.52856 10.7838 7.57624 10.8538 7.64625L13.8538 10.6462C13.9002 10.6927 13.9371 10.7478 13.9623 10.8085C13.9874 10.8692 14.0004 10.9343 14.0004 11C14.0004 11.0657 13.9874 11.1308 13.9623 11.1915C13.9371 11.2522 13.9002 11.3073 13.8538 11.3538Z",fill:"black"})}),n.jsx("p",{onClick:()=>M(s),className:"line-clamp-1 text-ellipsis font-iowan text-[16px] font-medium hover:underline",children:s.title&&s.title.length>15?`${s.title.substring(0,15)}...`:s.title||"Untitled"})]}),n.jsx(re,{status:t,scheduledDate:s.status==8?(L=s==null?void 0:s.metadata)==null?void 0:L.scheduled_date:null})]}),n.jsxs("div",{className:"flex justify-between items-center text-sm",children:[n.jsxs("span",{className:"font-iowan-regular flex items-center gap-2 text-[14px]",children:[n.jsx(ne,{className:"w-4 h-4"}),s.date?new Date(s.date).toLocaleDateString("en-US",{month:"2-digit",day:"2-digit",year:"2-digit"}):"N/A"]}),n.jsx("span",{className:"text-[#1f1d1a]",children:s.date?_e(s.date):"N/A"})]}),n.jsxs("div",{className:"mt-5 grid grid-cols-[1fr_1fr_60px] items-start justify-between",children:[n.jsxs("div",{className:"flex flex-col gap-2 items-start",children:[n.jsx("span",{className:"font-iowan text-[12px]",children:"Update Type"}),s.type?n.jsx(te,{display:n.jsxs("span",{className:"flex w-fit flex-nowrap items-center justify-normal gap-[.3125rem]  capitalize",children:[n.jsx("span",{className:"font-iowan-regular min-w-[max-content] max-w-[9.375rem] truncate whitespace-nowrap break-words text-[11px] font-[400] leading-[1.125rem] text-black",children:(e.isChild&&typeof s.type=="string"&&s.type.includes("Request (In)")?"Update (Out)":e.isChild&&typeof s.type=="string"&&s.type.includes("Request (Out)")?"Update (In)":s.type).replace("(In)","").replace("(Out)","")}),(e.isChild&&typeof s.type=="string"&&s.type.includes("Request (In)")?"Update (Out)":e.isChild&&typeof s.type=="string"&&s.type.includes("Request (Out)")?"Update (In)":s.type).includes("(In)")?n.jsx("img",{src:"/assets/incoming.svg",alt:"incoming",className:"w-4 h-4"}):(e.isChild&&typeof s.type=="string"&&s.type.includes("Request (In)")?"Update (Out)":e.isChild&&typeof s.type=="string"&&s.type.includes("Request (Out)")?"Update (In)":s.type).includes("(Out)")?n.jsx("img",{src:"/assets/outgoing.svg",alt:"outgoing",className:"w-4 h-4"}):null]}),place:"left",backgroundColor:"#000",openOnClick:!1,children:n.jsx("div",{className:"flex max-w-[9.375rem] items-center gap-2",children:n.jsx("span",{className:"w-full whitespace-normal break-words font-inter text-[.875rem] font-[400] leading-[1.125rem] text-white",children:(e.isChild&&typeof s.type=="string"&&s.type.includes("Request (In)")?"Update (Out)":e.isChild&&typeof s.type=="string"&&s.type.includes("Request (Out)")?"Update (In)":s.type).replace("(In)","(Incoming)").replace("(Out)","(Outgoing)")})})}):n.jsx("span",{className:"font-iowan text-[14px]",children:"Unknown"})]}),n.jsxs("div",{className:`flex flex-col items-start gap-2 ${typeof s.type=="string"&&s.type.includes("Collaboration")?"-mr-3":""}`,children:[n.jsx("span",{className:"font-iowan text-[12px]",children:"Contributor(s)"}),n.jsx(J,{members:Array.isArray(s.contributors)?s.contributors.map(u=>{if(typeof u=="string")try{return JSON.parse(u)}catch(f){return console.error("Error parsing contributor:",f),u}return u}):[],title:n.jsx(n.Fragment,{})})]}),n.jsxs("div",{className:"flex flex-col gap-2 items-start",children:[n.jsx("span",{className:"font-iowan text-[12px]",children:"To/From"}),(o==null?void 0:o.length)>0?n.jsx(J,{members:o,title:n.jsx(n.Fragment,{})}):"-"]})]})]},`${s.id}-${e.isChild?"child":"parent"}`)};return n.jsxs(k.Fragment,{children:[n.jsx(Ce,{isOpen:g.showModal,onClose:x,update:g.selectedUpdate,onAccept:$,onDecline:V,loading:g.modalLoading}),n.jsxs("div",{className:"flex flex-col",children:[n.jsxs("div",{className:"flex justify-between items-center px-5 mb-4 md:mt-10 md:hidden md:px-0",children:[n.jsx("h3",{className:"text-lg font-semibold font-inter",children:"Most Recent Activity"}),n.jsx(ue,{to:"/member/updates?availability=available",className:"text-sm font-medium underline",children:"View All"})]}),g!=null&&g.updateSeenLoading?n.jsx(ge,{loading:g==null?void 0:g.updateSeenLoading}):null,n.jsx("div",{className:"block md:hidden",children:T?n.jsx(xe,{count:7,counts:[2,2,2,2,2,2]}):((B=d==null?void 0:d.data)==null?void 0:B.length)>0?n.jsx("div",{className:"grid overflow-y-auto overflow-x-hidden gap-4 md:hidden",children:(G=d==null?void 0:d.data)==null?void 0:G.map((e,m)=>Q(e,m))}):n.jsx(P,{children:n.jsx(le,{})})}),n.jsx("div",{className:"mt-[18px] hidden h-full md:block",children:n.jsx(he,{showSearch:!1,useDefaultColumns:!0,defaultColumns:ie,noDataComponent:{use:!0,component:n.jsx(P,{children:n.jsx(le,{})})},onUpdateCurrentTableData:e=>{e(d)},externalData:{use:!0,fetch:q},hasFilter:!1,tableRole:"member",table:"order",actionId:"id",tableTitle:"Most Recent Activity",actions:{view:{show:!1,action:null,multiple:!1},select:{show:!1,action:null,multiple:!1},export:{show:!1,action:null,multiple:!0},delete:{show:!1,action:null,multiple:!0},orders:{show:!0,type:"static",action:()=>{c("/member/updates?availability=available")},children:n.jsx(n.Fragment,{children:"View All"}),className:"!bg-transparent !text-black !underline !shadow-none !border-0 !p-0 !m-0 !w-fit !min-w-fit !max-w-fit"},add:{show:!1,multiple:!0,children:"+ Add"}},defaultPageSize:10,showPagination:!1,updateRef:h,showScrollbar:!1,maxHeight:"md:grid-rows-[auto_1fr_auto] grid-rows-[auto_25rem_auto]",isActivityTable:!0})})]})]})};export{ss as default};
