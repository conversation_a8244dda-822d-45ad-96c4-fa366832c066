import{j as s}from"./@nextui-org/listbox-0f38ca19.js";import{A as p}from"./AddButton-51d1b2cd.js";import{b as u,a as w,u as b,L as r}from"./index-46de5032.js";import{M as t}from"./index-af54e68d.js";import{i as j,b as N}from"./vendor-4cdf2bd1.js";import{A as o}from"./lucide-react-0b94883e.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const G=({update:g=null,localData:e,previousUpdate:x,nextUpdate:h})=>{var i,n,m,d;const{sdk:f}=u();return w(),j(),N(),b(),f.getProjectId(),s.jsxs("div",{className:"flex w-full items-center justify-between gap-5 md:w-auto",children:[s.jsx(r,{children:s.jsx(t,{display:s.jsxs(p,{showPlus:!1,onClick:x,disabled:(e==null?void 0:e.index)==0,className:"!w-fit !border-none !bg-transparent !p-0 !text-[1rem] font-semibold !text-black !shadow-none",children:[s.jsx(r,{children:s.jsx(o,{className:"h-[1.25rem] w-[1.25rem]"})}),"Previous"]}),openOnClick:!1,backgroundColor:"#1f1d1a",className:"!m-0 !p-0",place:"bottom",show:(e==null?void 0:e.index)>0,children:s.jsx("span",{className:"text-white",children:(i=e==null?void 0:e.previousUpdate)==null?void 0:i.name})})}),s.jsx(r,{children:s.jsx(t,{display:s.jsxs(p,{showPlus:!1,onClick:h,className:"!w-fit !border-none !bg-transparent !p-0  !text-[1rem] font-semibold !text-black !shadow-none",disabled:((n=e==null?void 0:e.newUpdates)==null?void 0:n.length)==0||(e==null?void 0:e.index)==((m=e==null?void 0:e.newUpdates)==null?void 0:m.length)-1,children:["Next Update",s.jsx(r,{children:s.jsx(o,{className:"h-[1.25rem] w-[1.25rem] rotate-180"})})]}),openOnClick:!1,backgroundColor:"#1f1d1a",place:"bottom",className:"!m-0 !p-0",children:s.jsx("span",{className:"text-white",children:((d=e==null?void 0:e.nextUpdate)==null?void 0:d.name)??"No New Update"})})})]})};export{G as default};
