import{j as r}from"./@nextui-org/listbox-0f38ca19.js";import{f as y,L as f,C as _}from"./index-46de5032.js";import{M as E}from"./index-d0de8b06.js";import{u as S}from"./useDate-14dbf4c5.js";import{R as a}from"./index-a9c1e6e9.js";import{N as h}from"./index-8a8a991b.js";import{u as k}from"./useGetEngagements-8188941a.js";import{r as p,b as L}from"./vendor-4cdf2bd1.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const M=[{header:"Title",accessor:"update_name",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Comments",accessor:"comments",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Date/Time",accessor:"sent_at",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0}],D=({})=>{var u;const c=p.useRef(null),x=L(),{convertDate:b}=S(),{data:i,updateData:j,refetch:w,loading:N}=k(),m=o=>{x(`/member/update/private/view/${o==null?void 0:o.update_id}#${btoa(`comment:${o==null?void 0:o.id}`)}`)},v=o=>{var e;const t=new Map;for(const s of o==null?void 0:o.data){const d=s.update_id;if(t.has(d)){const l=t.get(d);l.sections.push(s==null?void 0:s.section),l.update_comments.push({comment:s==null?void 0:s.comment,email:s==null?void 0:s.commenter_email,first_name:s==null?void 0:s.commenter_first_name,id:s==null?void 0:s.commenter_id,last_name:s==null?void 0:s.commenter_last_name,photo:s==null?void 0:s.commenter_photo,create_at:s==null?void 0:s.create_at,update_at:s==null?void 0:s.update_at}),t.set(d,l)}else{const l={...s,mobile_data:{...s,update_comments:[{comment:s==null?void 0:s.comment,email:s==null?void 0:s.commenter_email,first_name:s==null?void 0:s.commenter_first_name,id:s==null?void 0:s.commenter_id,last_name:s==null?void 0:s.commenter_last_name,photo:s==null?void 0:s.commenter_photo,create_at:s==null?void 0:s.create_at,update_at:s==null?void 0:s.update_at}]},sent_at:r.jsxs("span",{className:"font-iowan-regular flex items-center gap-2 text-[14px]",children:[r.jsx(_,{}),b(s==null?void 0:s.sent_at,{formatMatcher:"best fit",month:"2-digit",day:"2-digit",year:"2-digit"})]}),sections:[s==null?void 0:s.section],update_comments:[{comment:s==null?void 0:s.comment,email:s==null?void 0:s.commenter_email,first_name:s==null?void 0:s.commenter_first_name,id:s==null?void 0:s.commenter_id,last_name:s==null?void 0:s.commenter_last_name,photo:s==null?void 0:s.commenter_photo,create_at:s==null?void 0:s.create_at,update_at:s==null?void 0:s.update_at}]};t.set(d,l)}}const n=(e=Array.from(t.values()))==null?void 0:e.map(s=>({...s,update_name:r.jsx("button",{onClick:()=>{m(s)},children:r.jsx("b",{children:s==null?void 0:s.update_name})}),comments:r.jsx(f,{children:r.jsx(a,{members:s==null?void 0:s.update_comments,title:"Latest Comments"})})}));return{...o,data:n}},C=(o,t)=>r.jsx("div",{className:`flex flex-col gap-3 rounded-sm px-4 py-4 md:px-0 ${t%2!==0?"border-y border-y-[#E6DCD2] bg-[#F9EADF]":"bg-brown-main-bg"}`,children:r.jsxs("div",{className:"flex gap-2 justify-between items-start",children:[r.jsxs("div",{className:"mt-[-5px] flex flex-col gap-2",children:[r.jsx("button",{onClick:()=>m(o),className:"truncate font-iowan text-base font-medium text-[#1f1d1a]  underline",children:o.update_name}),r.jsxs("div",{className:"flex items-center gap-2 truncate text-[0.875rem] text-[#1f1d1a]",children:[r.jsx(_,{className:"w-4 h-4"}),new Date(o==null?void 0:o.sent_at).toLocaleDateString("en-US",{month:"2-digit",day:"2-digit",year:"2-digit",hour:"2-digit",minute:"2-digit"})," "]})]}),r.jsxs("div",{className:"flex flex-col gap-2",children:[r.jsx("span",{className:"mt-[-3px] font-iowan text-sm text-[#1f1d1a]",children:"Comments"}),r.jsx(a,{members:[{comment:o==null?void 0:o.comment,email:o==null?void 0:o.commenter_email,first_name:o==null?void 0:o.commenter_first_name,id:o==null?void 0:o.commenter_id,last_name:o==null?void 0:o.commenter_last_name,photo:o==null?void 0:o.commenter_photo,create_at:o==null?void 0:o.create_at,update_at:o==null?void 0:o.update_at}],title:"Latest Comments"})]})]})},o.id);return p.useEffect(()=>{var o;i!=null&&i.reload&&(c!=null&&c.current)&&((o=c==null?void 0:c.current)==null||o.click(),j({reload:!1}))},[i==null?void 0:i.reload]),r.jsx(p.Fragment,{children:r.jsxs("div",{className:"flex flex-col mt-5",children:[r.jsx("div",{className:"flex justify-between items-center px-5 mb-4 md:hidden md:px-0",children:r.jsx("h3",{className:"text-lg font-semibold font-inter",children:"Recent Engagement"})}),N?r.jsx("div",{className:"s md:hidden",children:r.jsx(y,{count:7,counts:[2,2,2,2,2,2]})}):r.jsx("div",{className:"grid gap-4 pb-20 md:hidden",children:((u=i==null?void 0:i.data)==null?void 0:u.length)>0?i.data.map((o,t)=>C(o,t)):r.jsx(h,{})}),r.jsx("div",{className:"mt-[11px] hidden md:block",children:r.jsx(E,{useDefaultColumns:!0,maxHeight:"md:grid-rows-[auto_1fr_auto] grid-rows-[auto_25rem_auto]",defaultColumns:M,hasFilter:!1,tableRole:"admin",table:"receipts",tableTitle:"Recent Engagement",actionId:"id",join:["user","division","campaign","warehouse"],noDataComponent:{use:!0,component:r.jsx(f,{children:r.jsx(h,{})})},onUpdateCurrentTableData:o=>{const t=v(i);o(t)},externalData:{...i,fetch:w},actions:{view:{show:!1,action:null,multiple:!1},select:{show:!1,action:null,multiple:!1},delete:{show:!1,action:null,multiple:!0},receipts:{show:!1,type:"static",action:()=>{},children:null,className:"!bg-transparent !text-black !underline !shadow-none !border-0 !p-0 !m-0 !w-fit !min-w-fit !max-w-fit"},add:{show:!1,multiple:!0,children:"+ Add"},export:{show:!1,action:null,multiple:!0}},showSearch:!1,defaultPageSize:10,showScrollbar:!1,showPagination:!1,updateRef:c})})]})})};export{D as default};
