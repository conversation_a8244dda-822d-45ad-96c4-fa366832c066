import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{b as j,u as _,L as w,E as D,d as C,C as E}from"./index-46de5032.js";import{r as c}from"./vendor-4cdf2bd1.js";import{M as N}from"./index-d0de8b06.js";import{C as I}from"./ClipboardDocumentIcon-f03b0627.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const A=[{header:"Status",accessor:"status",selected_column:!0,isSorted:!0,isSortedDesc:!1,mappingExist:!0,mappings:{paid:{text:"Paid",bg:"#9DD321",color:"black"},unpaid:{text:"Unpaid",bg:"#F6A13C",color:"black"},void:{text:"Void",bg:"#F6A13C",color:"black"}}},{header:"Currency",accessor:"currency",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,type:"uppercase"},{header:"Total Amount",accessor:"amount_due",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,type:"currency"},{header:"Discounted amount",accessor:"discounted_amount",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,type:"currency"},{header:"Amount paid",accessor:"amount_paid",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,type:"currency"},{header:"Created at",accessor:"created",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,type:"timestamp"},{header:"Action",accessor:""}],R={usd:"$",eur:"€",gbp:"£",cad:"$",aud:"$",inr:"₹"},k=a=>[0].includes(a)?"0.00":Number(a/100).toFixed(2),m=(a,i,d)=>(i==null?void 0:i.type)==="timestamp"?t.jsxs("span",{className:"flex items-center gap-2",children:[t.jsx(E,{}),new Date(a*1e3).toLocaleString("en-US")]}):(i==null?void 0:i.type)==="currency"?`${R[d==null?void 0:d.currency]||(d==null?void 0:d.currency)}${k(a)}`:(i==null?void 0:i.type)==="uppercase"?a==null?void 0:a.toUpperCase():a,Z=()=>{var h;const a=c.useRef(null),i=c.useRef(null),{operations:d}=j(),[n,b]=c.useState({filter:[],refresh:!1}),{profile:s}=_(),y=(p,f)=>p==null?void 0:p.map(o=>{var g;const r=o!=null&&o.object&&JSON.parse(o==null?void 0:o.object)?JSON.parse(o==null?void 0:o.object):null,l={},x=((g=r==null?void 0:r.total_discount_amounts)==null?void 0:g.reduce((e,u)=>e+((u==null?void 0:u.amount)||0),0))||0,S=x+((r==null?void 0:r.amount_paid)||0);return f==null||f.forEach(e=>{(e==null?void 0:e.accessor)==="discounted_amount"?l[e==null?void 0:e.accessor]=m(x,e,r):(e==null?void 0:e.accessor)==="amount_due"?l[e==null?void 0:e.accessor]=m(S,e,r):l[e==null?void 0:e.accessor]=m(r==null?void 0:r[e==null?void 0:e.accessor],e,r)}),{...o,...l}});return c.useEffect(()=>{s!=null&&s.id&&b(p=>({...p,filter:[`user_id,${d.EQUAL},${s==null?void 0:s.id}`]}))},[s==null?void 0:s.id]),t.jsx(c.Fragment,{children:t.jsxs("div",{className:" grid h-full max-h-full min-h-full w-full grid-rows-[auto_auto_1fr] rounded bg-brown-main-bg px-5 md:px-8 ",children:[t.jsxs("div",{className:" flex w-full items-center justify-between gap-5 pt-3 ",children:[t.jsx("h4",{className:"font-iowan text-[26px] font-bold leading-[2.486rem]",children:"Invoices"}),t.jsx("div",{className:"flex w-fit items-center justify-start gap-5",children:t.jsx(w,{})})]}),t.jsx("div",{className:"mt-4 h-[.125rem] w-full border-[.125rem] border-black bg-black "}),s!=null&&s.id&&((h=n==null?void 0:n.filter)==null?void 0:h.length)>0?t.jsx(N,{showSearch:!1,useDefaultColumns:!0,defaultColumns:[...A],noDataComponent:{use:!0,component:t.jsx(w,{children:t.jsxs("div",{className:"mb-[20px] mt-24 flex flex-col items-center",children:[t.jsx(I,{className:"h-8 w-8 text-gray-700",strokeWidth:2}),t.jsx("p",{className:"mt-4 text-center text-base font-medium",children:"No Invoices"})]})})},onReady:()=>{},processes:[y],hasFilter:!1,tableRole:"admin",actionId:"id",table:"stripe_invoice",join:["user"],defaultFilter:n==null?void 0:n.filter,actions:{view:{show:!1,action:null,multiple:!1},select:{show:!1,action:null,multiple:!1},update:{show:!1,action:null,multiple:!0,children:"Edit",showChildren:!0,icon:t.jsx(D,{}),locations:["dropdown"]},remove:{show:!1,action:null,multiple:!0,children:"Delete",icon:t.jsx(C,{fill:"#292D32"}),locations:["dropdown"]},view_all:{show:!1,type:"static",action:()=>{},children:t.jsx(t.Fragment,{children:"View All"}),className:"!gap-2 !bg-transparent !text-black !underline !shadow-none !border-0"},add:{show:!1,multiple:!0,children:"+ Add"},export:{show:!1,action:null,multiple:!0}},defaultPageSize:20,showPagination:!0,maxHeight:"md:grid-rows-[inherit] grid-rows-[inherit]",actionPostion:["dropdown"],refreshRef:a,updateRef:i}):null]})})};export{Z as default};
