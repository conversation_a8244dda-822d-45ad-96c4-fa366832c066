import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{R as s,r as d}from"./vendor-4cdf2bd1.js";import{P as _,U as D}from"./ManagePlanModal-6de3e251.js";import{a as C,O as b}from"./index-46de5032.js";import"./@nextui-org/theme-345a09ed.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const L=["free","pro","business","enterprise"],V=({onSuccess:p,currentPlan:h})=>{var m;const{globalDispatch:x,authDispatch:g,getMany:l}=C(),[o,j]=s.useState(!0),[u,P]=s.useState("$"),[y,w]=s.useState({USD:1,EUR:1}),[a,n]=s.useState({stripeProducts:!1}),[i,v]=s.useState({stripeProducts:[]}),E=s.useCallback((t={filter:[],join:[]})=>(async()=>{n(r=>({...r,stripeProducts:!0}));try{const r=await l("stripe_product",{filter:[...t==null?void 0:t.filter],join:["stripe_price|product_id",...t==null?void 0:t.join]});return r!=null&&r.error?[]:(v(U=>({...U,stripeProducts:L.map(S=>{var f;return(f=r==null?void 0:r.data)==null?void 0:f.find(c=>(c==null?void 0:c.name)===S)})})),r==null?void 0:r.data)}catch{return[]}finally{n(r=>({...r,stripeProducts:!1}))}})(),[g,x,l]);async function R(){try{const r=await(await fetch("https://api.exchangerate-api.com/v4/latest/USD")).json();w({USD:1,EUR:r.rates.EUR})}catch(t){console.error("Error fetching exchange rates:",t)}}return d.useEffect(()=>{R()},[]),s.useEffect(()=>{E()},[]),e.jsx(d.Fragment,{children:e.jsxs("div",{className:`grid h-full max-h-full min-h-full w-full grid-cols-1 gap-0 ${a!=null&&a.stripeProducts?"grid-rows-[auto_1fr_auto]":"grid-rows-[auto_1fr]"}`,children:[e.jsx("div",{className:"h-full max-h-full min-h-full w-full p-2 md:px-8 lg:px-0",children:e.jsx(_,{setFocusYearly:j,setCurrency:P,focusYearly:o,currency:u})}),a!=null&&a.stripeProducts?e.jsx("div",{class:"relative mt-4 h-full max-h-full min-h-full w-full shrink-0",children:e.jsx(b,{loading:!0,color:"#1f1d1a",size:10})}):null,e.jsx("div",{class:"h-full max-h-full min-h-full w-full overflow-y-auto",children:e.jsx("div",{class:"relative z-[10] mx-auto grid h-fit max-h-fit min-h-fit w-fit grid-cols-[repeat(auto-fill,minmax(17.8125rem,1fr))] items-stretch justify-items-center gap-5 p-5  md:px-8 lg:px-0 ",children:(m=i==null?void 0:i.stripeProducts)==null?void 0:m.map((t,r)=>e.jsx("div",{className:"w-full",children:e.jsx(D,{plan:t,name:t==null?void 0:t.name,currency:u,onSuccess:p,currentPlan:h,focusYearly:o,exchangeRates:y,allPlans:i==null?void 0:i.stripeProducts})},r))})})]})})};export{V as UpdatePlans,V as default};
