import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{A as De,G as st,M as ve,s as qe,t as ot,b as rt,a as it,O as dt,L as U,i as ct,z as lt,H as mt}from"./index-46de5032.js";import{R as pt,C as ut,a as Be,b as ft,c as ht,d as gt,e as xt,f as wt,g as yt,h as vt}from"./index-a613c3fd.js";import{r as l,u as bt,b as _t,h as jt,i as Ut}from"./vendor-4cdf2bd1.js";import"./index-6edcbb0d.js";import{h as ye}from"./moment-a9aaa855.js";import{u as St}from"./useRecentEngagements-74872108.js";import"./yup-342a5df4.js";import{_ as Nt}from"./MoonLoader-6f2b5db4.js";import{C as It}from"./ChevronDownIcon-8b7ce98c.js";import{H as de,t as Et}from"./@headlessui/react-cdd9213e.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const Tt=[{id:1,reason:"I don’t need to send updates to my current team any longer"},{id:2,reason:"I’ve changed positions/companies and no longer need to send updates"},{id:3,reason:"I’ve decided to send updates with another service"},{id:4,reason:"The product isn’t worth it for me, too few features"},{id:5,reason:"It’s too expensive"},{id:6,reason:"Product too complicated"},{id:7,reason:"Forgot to cancel when free trial ended"},{id:8,reason:"I always cancel auto-renew subscriptions"},{id:10,reason:"I only intended to use during free trial"},{id:11,reason:"It’s easier sending updates manually via word/google doc, notion, or via email"},{id:2,reason:"other"}],kt=({setSteps:b,setModal:H,setReason:ce,reason:Y,suggestion:K,setSuggestion:z})=>{const{dispatch:j,state:le}=l.useContext(De),{dispatch:F}=l.useContext(st);l.useState(!1),l.useState([]);const[me,q]=l.useState(!1);l.useState([]);const[S,pe]=l.useState(""),[L,W]=l.useState({reason:"",password:S}),J=Object.keys(Y).length===0;async function $(w){if(w.preventDefault(),J)W({...L,reason:"This field is required"});else if(S==="")W({...L,password:"This field is required"});else{q(!0);try{const P=await new ve().callRawAPI("/v3/api/custom/goodbadugly/confirm-password",{password:S},"POST");q(!1),qe(F,P.message),console.log(P),b(2)}catch(A){q(!1),ot(j,A.message),A.message!=="TOKEN_EXPIRED"&&qe(F,A.message,5e3,"error")}}}return t.jsxs("div",{className:"mt-9 flex h-[inherit] flex-col justify-between space-y-10",children:[t.jsxs("div",{className:"flex flex-row justify-between gap-3",children:[t.jsxs("span",{className:"text-[16px] font-[500] leading-7",children:["What has made you consider ",t.jsx("br",{})," cancelling?"," ",t.jsx("span",{className:"text-[19px] text-red-600",children:"*"})]}),t.jsxs("div",{className:"w-full max-w-[600px]",children:[t.jsxs(de,{value:Y,onChange:ce,as:"div",className:"relative text-left ",children:[t.jsxs(de.Button,{className:"relative inline-flex  w-full rounded-md border px-4 py-2 text-lg font-medium focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75",children:[Y.id?Y.reason:"Select reason",t.jsx("div",{className:"absolute inset-y-0 right-0 flex items-center border-l border-black/60 p-2",children:t.jsx(It,{className:"h-5 w-5 text-gray-400","aria-hidden":"true"})})]}),t.jsx(Et,{as:l.Fragment,enter:"transition ease-out duration-100",enterFrom:"transform opacity-0 scale-95",enterTo:"transform opacity-100 scale-100",leave:"transition ease-in duration-75",leaveFrom:"transform opacity-100 scale-100",leaveTo:"transform opacity-0 scale-95",children:t.jsx(de.Options,{className:"absolute right-0 mt-2 w-full origin-top divide-y divide-gray-100 rounded-md bg-brown-main-bg shadow-lg ring-1 ring-[#1f1d1a]/5 focus:outline-none",children:t.jsx("div",{className:"",children:Tt.map(w=>t.jsx(de.Option,{value:w,children:({active:A})=>t.jsx("button",{className:`${A?"bg-brown-main-bg":""} w-full px-4 py-2 text-left text-lg font-medium`,children:w.reason})},w.id))})})})]}),J&&t.jsx("p",{className:"text-field-error mt-1 text-end text-[15px] italic text-red-500",children:L.reason})]})]}),t.jsxs("div",{className:"flex flex-col",children:[t.jsx("span",{className:"text-[16px] font-[500] leading-7",children:"What should we improve?"}),t.jsx("textarea",{name:"",className:"rounded",id:"",cols:"30",rows:"8",placeholder:"Enter suggestion",value:K,onChange:w=>z(w.target.value)})]}),t.jsxs("div",{className:"flex flex-col",children:[t.jsxs("div",{className:"text-[16px] font-[500] leading-7",children:["Password reconfirmation",t.jsx("span",{className:"text-[19px] text-red-600",children:"*"})]}),t.jsx("input",{type:"password",className:"rounded",placeholder:"********",onChange:w=>pe(w.target.value)}),t.jsxs("span",{className:"mt-1 text-gray-500",children:["This is required for account owner verification and prevention of unauthorized account deletion."," "]}),S===""&&t.jsx("p",{className:"text-field-error text-[15px] italic text-red-500",children:L.password})]}),t.jsxs("div",{className:"mb-5 flex flex-row items-center justify-between",children:[t.jsxs("button",{className:"flex flex-row items-center gap-1 rounded bg-red-500 p-3 text-[17px] font-[500] text-white",onClick:w=>$(w),children:[t.jsx(Nt,{loading:me,size:15,color:"#ffffff"}),"Continue"]}),t.jsx("span",{className:"cursor-pointer text-[17px] text-sky-600 underline",onClick:()=>H(!1),children:"← Nevermind, keep my account"})]})]})},en=({isPublic:b=!1})=>{var $e;const{sdk:H,tdk:ce}=rt(),{engagements:Y,refetch:K}=St(),{state:z}=l.useContext(De);H.getProjectId();const j=bt(),le=_t(),[F,me]=l.useState(!0),[q]=jt(),S=q.get("new");l.useEffect(()=>{const o=j.pathname.includes("/public/view/");C("isPublicView",o),console.log("Setting public view state:",o,j.pathname)},[j.pathname]);const[pe,L]=l.useState(""),W=async o=>{var a,i,c,n,d,p,f;if(o){await H.setTable("updates");const m=await ce.getOne("user",Number(o),{join:["profile|user_id"]});return L(((a=m==null?void 0:m.model)==null?void 0:a.first_name)+" "+((i=m==null?void 0:m.model)==null?void 0:i.last_name)),console.log(m,"bop"),{name:`${(c=m==null?void 0:m.model)==null?void 0:c.first_name} ${(n=m==null?void 0:m.model)==null?void 0:n.last_name}`,position:(f=(p=(d=m==null?void 0:m.model)==null?void 0:d.profile)==null?void 0:p[0])==null?void 0:f.title}}};console.log(pe,"bop");const J=async()=>{try{if(console.log("changestatus called with update:",s),!(s!=null&&s.id)){console.log("Update ID not available yet, skipping changestatus");return}const a=await new ve().callRawAPI(`/v3/api/custom/goodbadugly/activities/${S}/view`,{},"PUT");console.log("changestatus response:",a)}catch(o){console.error("Error calling view endpoint:",o)}};l.useEffect(()=>()=>{K()},[]);const{globalDispatch:$,authState:w,authDispatch:A,custom:P,getSingle:be,setGlobalState:C,getMany:Ge,globalState:I}=it();localStorage.getItem("token");const[_e,Ve]=l.useState([]),[B,ue]=l.useState(!0),[s,Ke]=l.useState(null),[Q,ze]=l.useState(),[je,We]=l.useState(""),[Ue,Je]=l.useState(null),[Qe,Se]=l.useState(""),{update_id:E,public_link_id:Ne}=Ut(),[Xe,Ie]=l.useState(null),[r,X]=l.useState({newUpdates:[],currentUpdate:null,updateNames:[],index:0,previousUpdate:null,nextUpdate:null}),Ee=l.useRef(null);async function Ze(o){const a={private:async()=>{var i;return await be("companies",(i=s==null?void 0:s.companies)==null?void 0:i.id,{method:"GET"})},public:async()=>{var i;return await P({endpoint:`/v3/api/custom/goodbadugly/user/company/${(i=s==null?void 0:s.companies)==null?void 0:i.id}`,method:"GET"})}};try{const i=a==null?void 0:a[o];if(!i)return;const c=await i(),n=(c==null?void 0:c.data)??{};Je(()=>({...n,socials:n!=null&&n.socials?Object.entries(JSON.parse(n==null?void 0:n.socials)).map(([d,p])=>{if(!ct(p))return{key:d,value:p}}).filter(Boolean):[]}))}catch(i){showToast(i.message,5e3,"error"),tokenExpireError(i.message)}finally{}}async function et(o){var i,c,n,d,p,f,m,x,g,h,_,R,M,T,D,ee,y,te,G,ne,ae,se,oe,re,Ce,Ae,Pe,Re,Me,Oe,Le,He,Ye;ue(!0);const a={private:async()=>{var e;const N=await be("updates",Number(E),{join:["companies|company_id","notes","update_questions","update_comments","update_reaction","update_comment_replies","update_question_answers"],method:"GET"}),ie={update_id:E};return await H.callRawAPI("/v3/api/custom/goodbadugly/mentions/mark-all-seen",ie,"PUT"),C("mentionsChange",!(I!=null&&I.mentionsChange)),(e=N==null?void 0:N.data)!=null&&e.notes&&Array.isArray(N.data.notes)&&N.data.notes.sort((O,V)=>{const k=O.order!==void 0&&O.order!==null?O.order:Number.MAX_SAFE_INTEGER,xe=V.order!==void 0&&V.order!==null?V.order:Number.MAX_SAFE_INTEGER;return k-xe}),N},public:async()=>await P({endpoint:`/v3/api/custom/goodbadugly/public/updates/${E}/${Ne}`,method:"GET"})};try{let N=function(v){return v==null?void 0:v.replace(/<[^>]*>/g,"")};const ie=a==null?void 0:a[o];if(!ie)return;const e=await ie();Ke(e==null?void 0:e.data),console.log("Update data loaded:",e==null?void 0:e.data),console.log("Update ID:",(i=e==null?void 0:e.data)==null?void 0:i.id),Se((c=e==null?void 0:e.data)==null?void 0:c.update_question_answers),Ve((n=e==null?void 0:e.data)==null?void 0:n.notes);const O=(p=(d=e==null?void 0:e.data)==null?void 0:d.notes)==null?void 0:p.map(v=>{const{blocks:Fe}=lt(v==null?void 0:v.content,{blocks:[]});if(Fe.length==0)return null;const at=mt(Fe);return{title:v==null?void 0:v.type,item:at}}),V=O==null?void 0:O.map(v=>N(v==null?void 0:v.item)).join(`
`),k=await W((f=e==null?void 0:e.data)==null?void 0:f.user_id);console.log(k,"owner2");const xe=[{role:"user",content:`You are a professional financial analyst creating a summary for investors and stakeholders. Write a comprehensive summary of the following update report.

IMPORTANT: Your summary MUST start with exactly this format:
"This update was sent to you on [month/day/year] by [owner name], [position] at [company name], and is titled [update title]. This update..."

Update Details:
- Update Title: ${((m=e==null?void 0:e.data)==null?void 0:m.name)||"Untitled Update"}
- Date Sent (sent_at field): ${(x=e==null?void 0:e.data)!=null&&x.sent_at?ye((g=e==null?void 0:e.data)==null?void 0:g.sent_at).format("MMMM D, YYYY"):"Not specified"}
- Owner Name: ${k==null?void 0:k.name}
- Company Name: ${((_=(h=e==null?void 0:e.data)==null?void 0:h.companies)==null?void 0:_.name)||"Unknown Company"}
- position :${k==null?void 0:k.position}

Available Financial Metrics:
${(R=e==null?void 0:e.data)!=null&&R.mrr?`- MRR: ${(M=e==null?void 0:e.data)==null?void 0:M.mrr}`:""}
${(T=e==null?void 0:e.data)!=null&&T.arr?`- ARR: ${(D=e==null?void 0:e.data)==null?void 0:D.arr}`:""}
${(ee=e==null?void 0:e.data)!=null&&ee.cash?`- Cash: ${(y=e==null?void 0:e.data)==null?void 0:y.cash}`:""}
${(te=e==null?void 0:e.data)!=null&&te.burnrate?`- Burn Rate: ${(G=e==null?void 0:e.data)==null?void 0:G.burnrate}`:""}
${(ne=e==null?void 0:e.data)!=null&&ne.burnrate&&((ae=e==null?void 0:e.data)!=null&&ae.cash)?`- Runway (months): ${Math.round(((se=e==null?void 0:e.data)==null?void 0:se.cash)/((oe=e==null?void 0:e.data)==null?void 0:oe.burnrate))}`:""}

Available Investment Information:
${(re=e==null?void 0:e.data)!=null&&re.investment_stage?`- Investment Stage: ${(Ce=e==null?void 0:e.data)==null?void 0:Ce.investment_stage}`:""}
${(Ae=e==null?void 0:e.data)!=null&&Ae.invested_to_date?`- Invested to Date: ${(Pe=e==null?void 0:e.data)==null?void 0:Pe.invested_to_date}`:""}
${(Re=e==null?void 0:e.data)!=null&&Re.investors_on_cap_table?`- Fund Managers on Cap Table: ${(Me=e==null?void 0:e.data)==null?void 0:Me.investors_on_cap_table}`:""}
${(Oe=e==null?void 0:e.data)!=null&&Oe.valuation_at_last_round?`- Valuation of Last Round: ${(Le=e==null?void 0:e.data)==null?void 0:Le.valuation_at_last_round}`:""}
${(He=e==null?void 0:e.data)!=null&&He.date_of_last_round?`- Date of Last Round: ${ye((Ye=e==null?void 0:e.data)==null?void 0:Ye.date_of_last_round).format("MM/DD/YYYY")}`:""}

Update Content:
${V}

Instructions:
1. Start with the exact format specified above using the "Date Sent" field
2. Write a professional summary in less than 450 words
3. ONLY mention financial metrics and investment information that are actually provided (not empty/null/undefined)
4. Focus on the update content and key developments
5. Maintain a positive, professional tone suitable for investor communications
6. If no metrics are provided and no substantial content exists, focus on the introduction and any available context
7. Do NOT mention missing data or limitations - focus on what is available
8. Use professional, investor-friendly language that builds confidence
just because format is [company name ] doesnt mean you have to use the dummy thats why we have the data.If position is not set just make sure we construct a good grammer with company name`}],we=await P({endpoint:"/v3/api/custom/goodbadugly/ai/ask",payload:{prompt:xe},method:"POST"});ze(we==null?void 0:we.data),ue(!1)}catch(N){We(N.message)}ue(!1)}l.useEffect(()=>{console.log("mode",S),S&&(s!=null&&s.id)&&(console.log("Running changestatus with mode:",S,"and update ID:",s.id),J())},[S,s==null?void 0:s.id]),l.useEffect(()=>{const o=a=>{(a.key==="F12"||a.ctrlKey&&a.shiftKey&&a.key==="I"||a.ctrlKey&&a.shiftKey&&a.key==="J")&&a.preventDefault()};return window.addEventListener("keydown",o),()=>{window.removeEventListener("keydown",o)}},[]),l.useEffect(()=>{et(b?"public":"private")},[b,E]),l.useEffect(()=>{E&&!b&&Te()},[E,b]),l.useEffect(()=>{if(r.newUpdates.length>0){const o=r.newUpdates.findIndex(a=>a==Number(E));if(o!==-1){const a=o<r.newUpdates.length-1?r.newUpdates[o+1]:null,i=o>0?r.newUpdates[o-1]:null,c=a?r.updateNames.find(d=>d.update_id===a):null,n=i?r.updateNames.find(d=>d.update_id===i):null;X(d=>({...d,currentUpdate:Number(E),index:o,previousUpdate:c,nextUpdate:n}))}}},[r.newUpdates,r.updateNames,s==null?void 0:s.id]),l.useEffect(()=>{Ie({id:"overview",index:0}),$({type:"SETPATH",payload:{path:"overview",sectionIndex:0,hasDuplicates:!1}})},[]);const Z=l.useCallback(o=>{const a=o.target,i=a.querySelectorAll("[id]"),c=a.scrollTop,n=a.clientHeight,d=a.scrollHeight,p=c+n,f=d-p<10,m=c<10;if(console.log("--- Scroll Event Debug ---"),console.log("Scroll Position:",c),console.log("Container Height:",n),console.log("Scroll Bottom:",p),console.log("Is at bottom:",f),console.log("Is at top:",m),m){const g=Array.from(i).find(h=>h.id==="overview");if(g){fe(g);return}}if(f){const g=Array.from(i).find(h=>h.id==="summary");if(g){fe(g);return}}let x=null;for(const g of i){g.getBoundingClientRect();const h=g.offsetTop-a.offsetTop,_=h<=c+50;_&&(x=g),console.log("Section Debug:",{id:g.id,noteId:g.getAttribute("data-note-id"),top:h,isInView:_})}x&&fe(x)},[Xe,$,_e]),fe=l.useCallback(o=>{const a=o.getAttribute("data-note-id"),i=o.id;console.log("Most Visible Section:",{id:i,noteId:a}),Ie(i),C("noteId",a),$({type:"SETPATH",payload:{path:i,noteId:a}})},[$,C]);l.useEffect(()=>{const o=Ee.current;return o&&(console.log("Attaching scroll listener to container"),o.addEventListener("scroll",Z,{passive:!0}),Z({target:o})),()=>{o&&o.removeEventListener("scroll",Z)}},[Z]),l.useEffect(()=>{var a;if(b)return;const o=(a=s==null?void 0:s.notes)==null?void 0:a.map(i=>{var p,f,m;const c=((p=s==null?void 0:s.update_comments)==null?void 0:p.filter(x=>x.note_id===i.id))||[],n=((f=s==null?void 0:s.update_comment_replies)==null?void 0:f.filter(x=>x.note_id===i.id))||[],d=((m=s==null?void 0:s.update_reaction)==null?void 0:m.filter(x=>x.note_id===i.id))||[];return{...i,counts:{comments:c.length,replies:n.length,reactions:d.length}}});C("updateSideNotes",o),C("currentUpdate",s==null?void 0:s.name),C("updateQuestions",s==null?void 0:s.update_questions)},[s==null?void 0:s.notes]),l.useEffect(()=>{if(j!=null&&j.hash&&!B){const[o,a]=j.hash.split("?"),i=o.substring(1),c=decodeURIComponent(i),n=new URLSearchParams(a||""),d=n.get("from");$({type:"SETPATH",payload:{path:d==="engagement"?"engagements":c}});const f=n.get("engagement_id"),m=n.get("engagement_type");d==="engagement"&&f&&m&&Te();const x=10;let g=0;const h=()=>{if(c.includes("comment:")){const[R,M]=c.split(":"),T=document.getElementById(M);if(T)return T.scrollIntoView({behavior:"smooth",block:"center"}),!0}else{const R=document.getElementById(c);if(R)return R.scrollIntoView({behavior:"smooth",block:"start"}),!0}return!1},_=setInterval(()=>{g++,(h()||g>=x)&&clearInterval(_)},200);return()=>clearInterval(_)}else $({type:"SETPATH",payload:{path:"updates"}})},[j==null?void 0:j.hash,B]),l.useEffect(()=>{var o;(o=s==null?void 0:s.companies)!=null&&o.id&&Ze(Ne?"public":"private")},[($e=s==null?void 0:s.companies)==null?void 0:$e.id]);const Te=async(o,a)=>{try{const i=await P({endpoint:"/v3/api/custom/goodbadugly/member/mark-engagement-update-viewed",method:"POST",payload:{update_id:Number(E)}},void 0,!1,null);i!=null&&i.error?console.error("Error marking engagement as viewed:",i.message):C("updateSharedNotificationChange",!(I!=null&&I.updateSharedNotificationChange))}catch(i){console.error("Error marking engagement as viewed:",i)}},tt=async()=>{var o;try{const a=await Ge("update_member",{filter:[`goodbadugly_update_member.user_id,eq,${localStorage.getItem("user")}`],join:["goodbadugly_updates|update_id,id"],fields:["goodbadugly_updates.name"],order:"goodbadugly_update_member.id,desc"});if(!(a!=null&&a.error)){const i=(o=a==null?void 0:a.data)==null?void 0:o.map(n=>({update_id:n==null?void 0:n.update_id,name:n==null?void 0:n.name,date_received:n==null?void 0:n.date_receieve,first_name:n==null?void 0:n.first_name,last_name:n==null?void 0:n.last_name,company_name:n==null?void 0:n.company_name})).filter(Boolean),c=i.map(n=>n.update_id).filter((n,d,p)=>p.indexOf(n)===d);X(n=>({...n,newUpdates:c,updateNames:i}))}}catch(a){console.log(a)}};let u=l.useRef(null);const he=async()=>new Promise(o=>{if(u!=null&&u.current)try{typeof u.current.stop=="function"&&u.current.stop(),typeof u.current.pause=="function"&&u.current.pause(),u.current.currentTime=0,u.current.src="",u.current.onended=null,u.current.oncanplaythrough=null,u.current.onerror=null,u.current=null}catch(a){console.error("Error stopping audio:",a)}setTimeout(o,200)}),ke=async()=>{var o,a,i,c,n;if((r==null?void 0:r.index)>0){await he();const d=(r==null?void 0:r.index)-1,p=(o=r==null?void 0:r.newUpdates)==null?void 0:o[d],f=(a=r==null?void 0:r.newUpdates)==null?void 0:a[d-1],m=(i=r==null?void 0:r.updateNames)==null?void 0:i.find(h=>(h==null?void 0:h.update_id)===f),x=(c=r==null?void 0:r.newUpdates)==null?void 0:c[d+1],g=(n=r==null?void 0:r.updateNames)==null?void 0:n.find(h=>(h==null?void 0:h.update_id)===x);X(h=>({...h,index:d,currentUpdate:p,previousUpdate:m,nextUpdate:g})),le(`/member/update/private/view/${p}`)}},ge=async(o=!1)=>{var a,i,c,n,d,p;if((r==null?void 0:r.index)<((a=r==null?void 0:r.newUpdates)==null?void 0:a.length)-1){await he();const f=(r==null?void 0:r.index)+1,m=(i=r==null?void 0:r.newUpdates)==null?void 0:i[f],x=(c=r==null?void 0:r.newUpdates)==null?void 0:c[f+1],g=(n=r==null?void 0:r.updateNames)==null?void 0:n.find(y=>(y==null?void 0:y.update_id)===x),h=(d=r==null?void 0:r.newUpdates)==null?void 0:d[f-1],_=(p=r==null?void 0:r.updateNames)==null?void 0:p.find(y=>(y==null?void 0:y.update_id)===h);if(X(y=>({...y,index:f,currentUpdate:m,previousUpdate:g,nextUpdate:_})),o&&_){await new Promise(re=>setTimeout(re,300));const{first_name:y,last_name:te,company_name:G,name:ne,date_received:ae}=_;console.log(_,"nextUpdate",G);const se=ye(ae).format("MMMM Do, YYYY [at] h:mm A"),oe=`Your next update is from ${y} ${te} from ${G||`${kt}'s Company`} titled ${ne} sent to you on ${se}`;await nt(oe)}const M=new URLSearchParams(window.location.search).get("listen_option"),T=new URLSearchParams;o&&T.set("autoplay","true"),M&&T.set("listen_option",M);const D=T.toString(),ee=`/member/update/private/view/${m}${D?`?${D}`:""}`;le(ee)}},nt=async o=>{var a;try{await he();const i=new ve;if(I!=null&&I.isPublicView){console.log("Skipping Polly synthesis - public view");return}let c=()=>{};try{const n=await i.callRawAPI("/v3/api/custom/goodbadugly/integrations/polly/synthesize",{text:o},"POST");if(n&&!n.error&&((a=n.data)!=null&&a.audioUrl)){await new Promise(f=>setTimeout(f,100));const d=new Audio(n.data.audioUrl);c=()=>{d&&(d.pause(),d.currentTime=0,d.src="",d.onended=null,d.oncanplaythrough=null,d.onerror=null,d.remove())};const p={current:d};u.current=d,d.onerror=()=>{c(),p.current=null,u.current=null},await new Promise((f,m)=>{d.oncanplaythrough=f,d.onerror=m,d.load()}),await d.play(),d.onended=()=>{c(),p.current=null,u.current=null}}else throw new Error((n==null?void 0:n.error)||"Failed to generate audio")}catch(n){throw c(),n}}catch(i){console.error("Failed to speak message:",i)}};if(l.useEffect(()=>{z.user&&tt()},[z.user]),!b&&!(w!=null&&w.isAuthenticated)&&!B){const a=`${window.location.origin}/member/login`,i=encodeURIComponent("/member/updates?availability=available"),c=`${a}?redirect_uri=${i}`;return window.location.href=c,t.jsx("div",{className:"flex h-screen -scroll-mt-6 items-center justify-center overflow-y-auto scroll-smooth bg-brown-main-bg",children:t.jsx("h2",{children:"Redirecting to Login ...."})})}return je?t.jsx("div",{className:"min-full max-full flex h-full w-full items-center justify-center text-7xl text-gray-700",children:je}):t.jsxs("div",{ref:Ee,className:`min-full max-full relative h-full w-full -scroll-mt-6 space-y-[2rem] scroll-smooth bg-brown-main-bg px-4 py-[1.0375rem] pb-[270px] md:px-[3.125rem] md:pb-[1.9375rem] md:pt-[2.252rem] lg:pb-[100px] ${B?"overflow-y-hidden":"overflow-y-auto"}`,children:[B?t.jsx(dt,{loading:!0}):null,b?t.jsx(U,{children:t.jsx(pt,{})}):null,s&&Ue&&t.jsxs("div",{className:"mx-auto w-full",children:[t.jsx(U,{children:t.jsx(ut,{companyDetails:Ue,update:s})}),t.jsx("hr",{className:"my-6 hidden border-[1px] border-[#1f1d1a] md:block"}),t.jsxs("div",{className:"flex flex-col items-start justify-between gap-5 border-t-2 border-black pt-5 md:flex-row md:items-center md:border-t-0 md:pt-0",children:[t.jsx("div",{className:"block w-full md:hidden",children:!b&&t.jsx(U,{children:t.jsx(Be,{update:s,localData:r,previousUpdate:ke,nextUpdate:ge})})}),t.jsx(U,{children:t.jsx(ft,{update:s})}),t.jsx("div",{className:"hidden w-full md:block md:w-auto",children:!b&&t.jsx(U,{children:t.jsx(Be,{update:s,localData:r,previousUpdate:ke,nextUpdate:ge})})})]}),t.jsxs("div",{className:"mt-[1.5rem] flex w-full  flex-row items-start gap-5 md:mt-8 md:items-center md:justify-between",children:[t.jsx(U,{children:t.jsx(ht,{update:s})}),t.jsxs("div",{className:"flex w-full flex-col items-center gap-[1.5625rem]  md:w-[75%] md:flex-row md:justify-between",children:[t.jsx(U,{children:t.jsx(gt,{data:s,showControls:F,setShowControls:me,summary:Q,nextUpdate:ge,localData:r,activeAudioRef:u,onBeforePlay:()=>{u!=null&&u.current&&(typeof u.current.stop=="function"&&u.current.stop(),typeof u.current.pause=="function"&&u.current.pause(),u.current=null)}})}),t.jsx(U,{children:t.jsx(xt,{update:s})})]})]}),t.jsx("hr",{className:"mt-5 border-[.0625rem] border-black/40 md:block"}),t.jsxs("section",{className:"mb-10 mt-[0.5rem] flex flex-col items-center gap-[2.5rem] md:mb-0 md:mt-[0] md:flex-row md:items-start md:gap-4",children:[b?t.jsx(U,{children:t.jsx(wt,{update:s})}):null,t.jsxs("div",{className:`min-h-[40rem] max-w-[100%] flex-grow border-0 ${b?"border-l-[#1f1d1a] pl-[3.25rem] md:border-l-[.125rem]":""}`,children:[t.jsx(U,{children:t.jsx(yt,{update:s})}),t.jsxs("div",{className:"flex flex-col gap-5",children:[t.jsx(U,{children:t.jsx(vt,{update:s,showControls:F,refetch:K,updateData:_e.map(o=>({...o,"data-note-id":o.id})),questionAnswerState:Qe,setQuestionAnswerState:Se})}),t.jsxs("div",{id:"summary",className:"mt-[16px] bg-[#F2DFCE] p-3 lg:p-5",children:[t.jsx("span",{className:"text-[18px] font-bold",children:"Summary"}),t.jsx("p",{children:t.jsx("p",{className:"font-regular mt-2 font-iowan text-[16px] font-medium leading-7",dangerouslySetInnerHTML:{__html:Q==null?void 0:Q.content}})})]})]})]})]})]})]})};export{en as default};
