import{j as m}from"./@nextui-org/listbox-0f38ca19.js";import{r as A}from"./vendor-4cdf2bd1.js";import{L as i,bX as L,bY as r,bZ as N,b_ as C,b$ as G,c0 as K,c1 as k,c2 as F}from"./index-46de5032.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";var x,b,j,g,u,D,E;const v=[{id:1,reaction:(x=r)==null?void 0:x.GLAD},{id:2,reaction:(b=r)==null?void 0:b.SMILE},{id:3,reaction:(j=r)==null?void 0:j.SAD},{id:4,reaction:(g=r)==null?void 0:g.LIKE},{id:5,reaction:(u=r)==null?void 0:u.DISLIKE},{id:6,reaction:(D=r)==null?void 0:D.INSIGHTFUL},{id:7,reaction:(E=r)==null?void 0:E.ROCKET}],Q=({className:S,reactions:e=[...v],onClick:t})=>{var p,a,n,c,l,d,I;const s=e.length-3,f={[(p=r)==null?void 0:p.SMILE]:N,[(a=r)==null?void 0:a.SAD]:C,[(n=r)==null?void 0:n.GLAD]:L,[(c=r)==null?void 0:c.LIKE]:G,[(l=r)==null?void 0:l.DISLIKE]:K,[(d=r)==null?void 0:d.INSIGHTFUL]:k,[(I=r)==null?void 0:I.ROCKET]:F};return m.jsx(i,{className:`relative flex !h-[3rem] max-h-[3rem] min-h-[3rem] items-center justify-between gap-[.5rem] rounded-[.125rem] border border-primary-black bg-brown-main-bg px-[1rem] py-[.75rem] ${S}`,children:e!=null&&e.length?m.jsxs("div",{className:"flex",children:[e==null?void 0:e.map((o,h)=>{const R=f[o==null?void 0:o.reaction];if([0,1,2].includes(h))return m.jsx(A.Fragment,{children:m.jsx(i,{children:m.jsx(R,{hoverColor:"#1F1D1A",className:"cursor-pointer",onClick:()=>t(o==null?void 0:o.reaction)})})},h)}),s>0?m.jsxs("span",{className:"whitespace-nowrap",children:["+",s," more"]}):e==null?void 0:e.length]}):m.jsx(i,{children:m.jsx(L,{hoverColor:"#1F1D1A",onClick:()=>{var o;return t((o=r)==null?void 0:o.GLAD)}})})})};export{Q as default};
