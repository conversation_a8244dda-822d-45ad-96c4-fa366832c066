import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{I as o}from"./index-46de5032.js";import{r as s}from"./vendor-4cdf2bd1.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";function I({update:i,onAccept:l=()=>{},onDecline:m=()=>{},loading:e=!1}){const[r,a]=s.useState({active:""}),c=n=>{n==="decline"?(a({active:"decline"}),m(i)):n==="accept"&&(a({active:"accept"}),l(i))};return t.jsx(t.Fragment,{children:t.jsxs("div",{className:"mt-[6px] flex w-full items-center justify-between gap-2",children:[t.jsx(o,{loading:e&&r.active==="decline",disabled:e,onClick:()=>c("decline"),className:`flex h-[2rem] w-full items-center justify-center gap-2 rounded-[.125rem] border border-black !bg-transparent font-medium !text-primary-black\r
            text-gray-700 transition-all duration-200 \r
            active:translate-y-[2px] active:shadow-none`,children:e&&r.active==="decline"?null:"Decline"}),t.jsx(o,{loading:e&&r.active==="accept",disabled:e,onClick:()=>c("accept"),className:`flex h-[2rem] w-full items-center justify-center gap-2 rounded-[.125rem] border  border-black bg-black font-medium  \r
            text-white transition-all duration-200 \r
            active:translate-y-[2px] active:shadow-none`,children:e&&r.active==="accept"?null:"Accept"})]})})}export{I as default};
