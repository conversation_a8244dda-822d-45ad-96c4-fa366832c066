import{j as _}from"./@nextui-org/listbox-0f38ca19.js";import{r as e}from"./vendor-4cdf2bd1.js";import{L as p}from"./index-46de5032.js";import{_ as o}from"./qr-scanner-cf010ec4.js";const l=e.lazy(()=>o(()=>import("./UpdateSectionNote-01fd1222.js"),["assets/UpdateSectionNote-01fd1222.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-46de5032.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-d88d3663.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/index-b05aa8a0.js"])),U=e.lazy(()=>o(()=>import("./UpdateSectionNoteTitle-5123a13a.js"),["assets/UpdateSectionNoteTitle-5123a13a.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js"])),x=e.lazy(()=>o(()=>import("./UpdateSectionNoteContent-7a46c0b6.js"),["assets/UpdateSectionNoteContent-7a46c0b6.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-46de5032.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-d88d3663.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/editorjs-react-renderer-ba57cf04.js"])),f=e.lazy(()=>o(()=>import("./UpdateSectionNoteActivities-1e1c0ebc.js"),["assets/UpdateSectionNoteActivities-1e1c0ebc.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-a613c3fd.js","assets/qr-scanner-cf010ec4.js","assets/index-46de5032.js","assets/react-confirm-alert-1abd021a.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-d88d3663.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/index-9ff4d686.js","assets/useReactions-9d0c4b8b.js","assets/useNote-95029f8e.js","assets/useUpdateCollaborator-daff0e7f.js","assets/index-a807e4ab.js"])),E=e.lazy(()=>o(()=>import("./UpdateSectionNoteComments-98b51c87.js"),["assets/UpdateSectionNoteComments-98b51c87.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-46de5032.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-d88d3663.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/useComments-72d3eb3f.js","assets/AddButton-51d1b2cd.js","assets/index-a807e4ab.js","assets/useUpdateCollaborator-daff0e7f.js","assets/react-popper-9a65a9b6.js","assets/@popperjs/core-f3391c26.js","assets/useMentions-6b3c3c1d.js"])),z=e.lazy(()=>o(()=>import("./UpdateSectionNoteComment-c6bf55bf.js").then(s=>s.b),["assets/UpdateSectionNoteComment-c6bf55bf.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-46de5032.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-d88d3663.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/useComments-72d3eb3f.js","assets/index-b05aa8a0.js","assets/index-a807e4ab.js","assets/lucide-react-0b94883e.js"])),A=e.lazy(()=>o(()=>import("./UpdateSectionNoteCommentActivities-e789219d.js"),["assets/UpdateSectionNoteCommentActivities-e789219d.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-a613c3fd.js","assets/qr-scanner-cf010ec4.js","assets/index-46de5032.js","assets/react-confirm-alert-1abd021a.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-d88d3663.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/index-9ff4d686.js","assets/useReactions-9d0c4b8b.js"])),C=e.lazy(()=>o(()=>import("./UpdateSectionNoteCommentContent-43e47320.js"),["assets/UpdateSectionNoteCommentContent-43e47320.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-46de5032.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-d88d3663.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/useUpdateCollaborator-daff0e7f.js","assets/react-popper-9a65a9b6.js","assets/@popperjs/core-f3391c26.js"])),N=e.lazy(()=>o(()=>import("./UpdateSectionNoteCommentUser-7c6f1936.js"),["assets/UpdateSectionNoteCommentUser-7c6f1936.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-46de5032.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-d88d3663.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/index-af54e68d.js"])),u=({update:s=null,note:t=null,onSuccess:a})=>{const[i,r]=e.useState({hideComment:!1}),m=(d,c)=>{r(n=>({...n,[d]:c}))};return _.jsx(p,{children:_.jsxs("div",{id:t==null?void 0:t.type,className:"flex flex-col gap-5","data-section-type":t==null?void 0:t.type,"data-note-id":t==null?void 0:t.id,children:[_.jsx(l,{note:t,update:s,onSuccess:a,toggleComments:m,commentVisibility:i==null?void 0:i.hideComment}),_.jsx("div",{className:`relative  mt-[5px]  ${i!=null&&i.hideComment?"hidden":""}`,children:_.jsx(E,{update:s,note:t})})]})})},O=Object.freeze(Object.defineProperty({__proto__:null,default:u},Symbol.toStringTag,{value:"Module"}));export{U,x as a,f as b,z as c,N as d,C as e,A as f,O as g};
