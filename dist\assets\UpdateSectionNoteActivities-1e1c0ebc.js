import{j as o}from"./@nextui-org/listbox-0f38ca19.js";import{u as A,r as m,i as E}from"./vendor-4cdf2bd1.js";import"./index-a613c3fd.js";import{a as T,u as k,L as g,bU as S}from"./index-46de5032.js";import{U as P}from"./index-9ff4d686.js";import{u as q}from"./useReactions-9d0c4b8b.js";import{u as z}from"./useNote-95029f8e.js";import{u as D}from"./useUpdateCollaborator-daff0e7f.js";import{A as F}from"./index-a807e4ab.js";import"./@nextui-org/theme-345a09ed.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const fi=({update:i=null,note:r=null,toggleComments:j,commentVisibility:n,onSuccess:x})=>{var h;const{setLoading:t,RequestItems:l,showToast:y}=T();A();const[e,C]=m.useState({html:null,data:null,modal:null,showModal:!1}),{reaction:d,loading:H,getReactions:N,react:w}=q(),{loading:O,notes:R,customDeleteNote:b}=z(),{data:{isCollaborator:B,collaborator:G},getUpdateCollaborator:L}=D({filter:[`update_id,eq,${i==null?void 0:i.id}`]}),{profile:c}=k(),{public_link_id:M}=E();(c==null?void 0:c.id)==(i==null?void 0:i.user_id);function f(s,a){C(v=>({...v,showModal:a,modal:a?s:null}))}async function U(){try{const s=await b({updateId:i==null?void 0:i.id,noteId:r==null?void 0:r.id});s!=null&&s.error||(f("delete_note",!1),t(l==null?void 0:l.deleteModel,!1),x&&x())}catch(s){console.error("error >> ",s)}finally{f("delete_note",!1),t(l==null?void 0:l.deleteModel,!1)}}const _=async()=>{N({filter:[`update_id,eq,${i==null?void 0:i.id}`,`note_id,eq,${r==null?void 0:r.id}`,"goodbadugly_update_reaction.type,eq,1"],target:1,note_id:r==null?void 0:r.id,update_id:i==null?void 0:i.id,exposure:M?"public":"private"})},$=async s=>{try{if(i!=null&&i.sent_at){const a=await w({reaction:s,target:1,note_id:r==null?void 0:r.id,update_id:i==null?void 0:i.id});a!=null&&a.error||_()}else{y("Update is not sent yet");return}}catch(a){console.log("error >> ",a)}};return m.useEffect(()=>{c!=null&&c.id&&(i!=null&&i.id)&&(r!=null&&r.id)&&L({filter:[`update_id,eq,${i==null?void 0:i.id}`,`note_id,eq,${r==null?void 0:r.id}`],join:[]})},[c==null?void 0:c.id,i==null?void 0:i.id,r==null?void 0:r.id]),m.useEffect(()=>{i!=null&&i.id&&(r!=null&&r.id)&&_()},[i==null?void 0:i.id,r==null?void 0:r.id]),o.jsxs(m.Fragment,{children:[o.jsx("div",{className:"flex w-full gap-[1rem]",children:o.jsxs("div",{className:"flex w-full gap-[1rem]",children:[o.jsx(g,{children:o.jsx(P,{reactions:d==null?void 0:d.list,onClick:$})}),o.jsx(g,{children:o.jsxs("button",{type:"button",onClick:()=>j("hideComment",!n),className:"flex w-full cursor-pointer gap-[1rem]",children:[o.jsx(S,{})," ",n?o.jsx("span",{className:"font-iowan",children:((h=r==null?void 0:r.update_comments)==null?void 0:h.length)||""}):"",o.jsxs("span",{children:[n?"Show":"Hide"," Comments"]})]})})]})}),o.jsx(g,{children:o.jsx(F,{isOpen:(e==null?void 0:e.showModal)&&(e==null?void 0:e.modal)==="delete_note",onClose:()=>f("delete_note",!1),customMessage:"Are you sure you want to delete this note?",table:"notes",title:"Delete Note",action:"delete",mode:"manual",data:{id:r==null?void 0:r.id},inputConfirmation:!1,onSuccess:s=>{t(l==null?void 0:l.deleteModel,!0),U()}})})]})};export{fi as default};
