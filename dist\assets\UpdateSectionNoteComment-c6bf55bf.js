import{j as r}from"./@nextui-org/listbox-0f38ca19.js";import{r as d,u as N}from"./vendor-4cdf2bd1.js";import{d as M,e as U,f as A}from"./UpdateSection-a6c2ef82.js";import{a as v,u as O,E as D,d as S,bs as P}from"./index-46de5032.js";import{u as R}from"./useComments-72d3eb3f.js";import{_}from"./qr-scanner-cf010ec4.js";import{a as T,D as L}from"./index-b05aa8a0.js";import{A as y}from"./index-a807e4ab.js";import{a as k}from"./lucide-react-0b94883e.js";const z=d.lazy(()=>_(()=>import("./UpdateSectionNoteCommentReplies-18443b31.js"),["assets/UpdateSectionNoteCommentReplies-18443b31.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-46de5032.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-d88d3663.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/useComments-72d3eb3f.js","assets/AddButton-51d1b2cd.js","assets/index-a807e4ab.js","assets/useUpdateCollaborator-daff0e7f.js","assets/react-popper-9a65a9b6.js","assets/@popperjs/core-f3391c26.js","assets/UpdateSection-a6c2ef82.js","assets/index-b05aa8a0.js","assets/lucide-react-0b94883e.js"])),$=d.lazy(()=>_(()=>import("./UpdateSectionNoteCommentReply-5dc5a64e.js"),["assets/UpdateSectionNoteCommentReply-5dc5a64e.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/moment-a9aaa855.js","assets/index-46de5032.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-d88d3663.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/UpdateSectionNoteCommentReplyContent-9038378b.js","assets/useUpdateCollaborator-daff0e7f.js","assets/index-b05aa8a0.js","assets/index-a807e4ab.js","assets/UpdateSection-a6c2ef82.js","assets/useComments-72d3eb3f.js","assets/lucide-react-0b94883e.js"]));d.lazy(()=>_(()=>import("./UpdateSectionNoteCommentReplyActivities-077b597d.js"),["assets/UpdateSectionNoteCommentReplyActivities-077b597d.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-a613c3fd.js","assets/qr-scanner-cf010ec4.js","assets/index-46de5032.js","assets/react-confirm-alert-1abd021a.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-d88d3663.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/index-9ff4d686.js","assets/index-b05aa8a0.js","assets/lucide-react-0b94883e.js"]));d.lazy(()=>_(()=>import("./UpdateSectionNoteCommentReplyContent-9038378b.js"),["assets/UpdateSectionNoteCommentReplyContent-9038378b.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-46de5032.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-d88d3663.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/useUpdateCollaborator-daff0e7f.js"]));const q=d.lazy(()=>_(()=>import("./UpdateSectionNoteCommentReplyUser-8171fd5c.js"),["assets/UpdateSectionNoteCommentReplyUser-8171fd5c.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-46de5032.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-d88d3663.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/index-af54e68d.js"])),I=({note:i=null,update:l=null,comment:e=null,onSuccess:m=null,loadComments:j=null})=>{var w;const{showToast:f,tokenExpireError:V,custom:F,setLoading:p,RequestItems:t}=v();N();const{profile:x}=O(),[o,n]=d.useState({html:null,data:null,modal:null,showReply:!0,showModal:!1,edit_comment:null,comment:"",errors:{comment:{message:""}}}),{loading:u,comments:c,getComments:K,customDeleteNoteComment:g}=R(),h=[{icon:r.jsx(k,{className:"h-[1.125rem] w-[1.125rem]"}),name:"Add Reply",onClick:()=>{if(!(l!=null&&l.sent_at)){f("Update is not sent yet");return}n(s=>({...s,showModal:!0,add_reply:!0,modal:"add_reply"}))},loading:!1},...(x==null?void 0:x.id)==((w=e==null?void 0:e.user)==null?void 0:w.id)?[{icon:r.jsx(D,{className:"h-[1.125rem] w-[1.125rem]"}),name:"Edit",onClick:()=>{if(!(l!=null&&l.sent_at)){f("Update is not sent yet");return}n(s=>({...s,edit_comment:!0,modal:"edit_comment",comment:e==null?void 0:e.comment}))},loading:!1},{icon:r.jsx(S,{fill:"#1F1D1A",className:"h-[1.125rem] w-[1.125rem]"}),name:"Delete",onClick:()=>{if(!(l!=null&&l.sent_at)){f("Update is not sent yet");return}n(s=>({...s,showModal:!0,modal:"delete_comment"}))},loading:u==null?void 0:u.delete}]:[]],a=(s,C)=>{n(b=>({...b,[s]:C}))};async function E(){try{const s=await g({noteId:i==null?void 0:i.id,updateId:l==null?void 0:l.id,commentId:e==null?void 0:e.id});s!=null&&s.error||(a("modal",null),a("showModal",!1),p(t==null?void 0:t.deleteModel,!1),m&&m())}catch(s){console.error("error >> ",s)}finally{p(t==null?void 0:t.deleteModel,!1)}}return d.useEffect(()=>{e!=null&&e.id&&a("comment",e==null?void 0:e.comment)},[e==null?void 0:e.id]),r.jsxs(d.Fragment,{children:[r.jsxs("div",{className:"mb-[1.8rem] flex flex-col gap-[1rem] ",children:[r.jsxs("div",{className:"mb-[0.8rem] flex flex-col ",children:[r.jsxs("div",{className:"flex items-start justify-between",children:[r.jsx(M,{comment:e,update:l,note:i}),r.jsx(T,{className:"!rounded-[.125rem] !bg-brown-main-bg",childrenWrapperClass:"!bg-brown-main-bg !rounded-[.125rem] !px-[1rem] !min-w-[10rem] !w-[10rem]",icon:r.jsx(P,{stroke:"#1F1D1A",className:"!rotate-90"}),children:h==null?void 0:h.map((s,C)=>r.jsx(L,{icon:s==null?void 0:s.icon,name:s==null?void 0:s.name,onClick:s==null?void 0:s.onClick,loading:s==null?void 0:s.loading,disabled:s==null?void 0:s.loading,className:"!h-[2.75rem] !min-h-[2.75rem]"},C))})]}),r.jsx(U,{setExternalData:n,loadComments:j,externalData:o,comment:e,update:l,note:i}),r.jsx(A,{note:i,update:l,comment:e,onSuccess:m,toggleRplies:a,showReply:o==null?void 0:o.showReply})]}),r.jsx(z,{setExternalData:n,externalData:o,comment:e,update:l,note:i})]}),r.jsx(y,{isOpen:(o==null?void 0:o.showModal)&&(o==null?void 0:o.modal)==="delete_comment",onClose:()=>a("showModal",!1),customMessage:"Are you sure you want to delete this comment?",table:"update_comments",title:"Delete Comment",action:"delete",mode:"manual",data:{id:e==null?void 0:e.id},inputConfirmation:!1,onSuccess:s=>{p(t==null?void 0:t.deleteModel,!0),E()}}),r.jsx(y,{isOpen:(o==null?void 0:o.showModal)&&(o==null?void 0:o.modal)==="edit_comment",onClose:()=>{a("showModal",!1),a("modal",null)},title:"Edit Comment",input:"comment",mode:"input_update",inputType:"textarea",initialValue:e==null?void 0:e.comment,customMessage:r.jsx(r.Fragment,{}),table:"update_comments",action:"update",data:{id:e==null?void 0:e.id,note_id:i==null?void 0:i.id,update_id:l==null?void 0:l.id},onSuccess:s=>{a("showModal",!1),a("modal",null),m&&m()}})]})},ee=Object.freeze(Object.defineProperty({__proto__:null,default:I},Symbol.toStringTag,{value:"Module"}));export{$ as U,q as a,ee as b};
