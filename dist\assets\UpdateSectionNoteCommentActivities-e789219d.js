import{j as l}from"./@nextui-org/listbox-0f38ca19.js";import{r as c,i as N}from"./vendor-4cdf2bd1.js";import"./index-a613c3fd.js";import{a as y,L as _,bU as C}from"./index-46de5032.js";import{U as R}from"./index-9ff4d686.js";import{u as b}from"./useReactions-9d0c4b8b.js";import"./@nextui-org/theme-345a09ed.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const J=({note:r=null,update:s=null,comment:i=null,toggleRplies:g,showReply:o,onSuccess:u=null})=>{var p;console.log(r,s,i,"registry"),y(),c.useState({html:null,data:null});const{reaction:d,loading:w,getReactions:j,react:h}=b(),{public_link_id:x}=N(),f=async()=>{j({filter:[`update_id,eq,${s==null?void 0:s.id}`,`note_id,eq,${r==null?void 0:r.id}`,`update_comment_id,eq,${i==null?void 0:i.id}`,"goodbadugly_update_reaction.type,eq,2"],target:2,note_id:r==null?void 0:r.id,update_id:s==null?void 0:s.id,comment_id:i==null?void 0:i.id,exposure:x?"public":"private"})},t=async e=>{try{const a=await h({reaction:e,target:2,note_id:r==null?void 0:r.id,update_id:s==null?void 0:s.id,comment_id:i==null?void 0:i.id});a!=null&&a.error||f()}catch(a){console.log("error >> ",a)}};return c.useEffect(()=>{s!=null&&s.id&&(r!=null&&r.id)&&(i!=null&&i.id)&&f()},[s==null?void 0:s.id,r==null?void 0:r.id,i==null?void 0:i.id,x]),l.jsx(c.Fragment,{children:l.jsx("div",{className:"flex w-full gap-[1rem]",children:l.jsxs("div",{className:"flex w-full gap-[1rem]",children:[l.jsx(_,{children:l.jsx(R,{reactions:d==null?void 0:d.list,onClick:t})}),l.jsx(_,{children:l.jsxs("button",{onClick:()=>g("showReply",!o),className:"flex w-full cursor-pointer gap-[1rem]",children:[l.jsx(C,{})," ",o?l.jsx("span",{className:"font-iowan",children:((p=i==null?void 0:i.update_comment_replies)==null?void 0:p.length)||""}):"",l.jsxs("span",{children:[o?"Hide":"Show"," Replies"]})]})})]})})})};export{J as default};
