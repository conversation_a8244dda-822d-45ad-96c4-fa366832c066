import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{r}from"./vendor-4cdf2bd1.js";import{a as K,u as W,i as Y}from"./index-46de5032.js";import{u as Z}from"./useUpdateCollaborator-daff0e7f.js";import{u as F}from"./react-popper-9a65a9b6.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@popperjs/core-f3391c26.js";const H=(n,d=[])=>{if(!n||typeof n!="string")return console.warn("Invalid text passed to renderTextWithMentions:",n),"";const v=/@[A-Za-z0-9]+(?:\s+[A-Za-z0-9]+)?(?=\s|$)/g;let o=0;const m=[];let l;for(;(l=v.exec(n))!==null;){if(l.index>o){const g=n.slice(o,l.index);m.push(g)}const i=l[0],j=i.substring(1),y=d.some(g=>`${g.first_name} ${g.last_name}`.trim().toLowerCase()===j.toLowerCase());m.push(t.jsx("span",{className:`mr-[4px] rounded-sm ${y?"bg-[#1f1d1a]/5":""} px-[1px] py-0.5 font-medium`,children:i},l.index)),o=v.lastIndex}if(o<n.length){const i=n.slice(o);m.push(i)}return m},pe=({comment:n=null,update:d=null,note:v=null,externalData:o=null,setExternalData:m=null,loadComments:l=null})=>{var M,R,L,P;const{data:i,fetchUpdateContributors:j}=Z();r.useEffect(()=>{d!=null&&d.id&&j(d==null?void 0:d.id)},[d==null?void 0:d.id]);const{setLoading:y,globalState:g,update:_}=K(),{profile:f}=W(),u=r.useRef(null),[p,b]=r.useState(!1);r.useState({x:0,y:0});const E=r.useRef(null),x=r.useRef(null),[A,I]=r.useState(null),{styles:T,attributes:O}=F(E.current,x.current,{placement:"bottom-start",modifiers:[{name:"arrow",options:{element:A}},{name:"offset",options:{offset:[0,8]}},{name:"preventOverflow",options:{padding:8}},{name:"flip",options:{padding:8}}]}),N=()=>{const e=u.current;e&&(e.style.height="auto",e.style.height=e.scrollHeight+"px")};r.useEffect(()=>{o!=null&&o.edit_comment&&N()},[o==null?void 0:o.edit_comment]),r.useEffect(()=>{if(p){const e=window.scrollY,s=window.getComputedStyle(document.body).overflow;return document.body.style.position="fixed",document.body.style.top=`-${e}px`,document.body.style.width="100%",document.body.style.overflow="hidden",()=>{document.body.style.position="",document.body.style.top="",document.body.style.width="",document.body.style.overflow=s,window.scrollTo(0,e)}}},[p]);const U=e=>{const s=e.target.value,c=s[s.length-1],w=u.current;N(),e.nativeEvent.inputType==="deleteContentBackward"&&p&&b(!1),c==="@"&&(E.current=w,b(!0)),m(h=>({...h,comment:s,errors:{...h==null?void 0:h.errors,comment:{message:""}}}))},q=e=>{const s=u.current,c=s.selectionEnd,w=o.comment.substring(0,c),h=o.comment.substring(c),z=`@${e.first_name} ${e.last_name} `;m(C=>({...C,comment:w.slice(0,-1)+z+h})),b(!1),setTimeout(()=>{s.focus();const C=c-1+z.length;s.setSelectionRange(C,C)},0)};r.useEffect(()=>{const e=s=>{p&&!s.target.closest(".mention-modal")&&b(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[p]),r.useEffect(()=>{const e=s=>{o!=null&&o.edit_comment&&u.current&&!u.current.contains(s.target)&&!s.target.closest(".mention-modal")&&B(o==null?void 0:o.modal,!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[o==null?void 0:o.edit_comment]);const B=(e,s)=>{m(c=>({...c,showModal:s,modal:s?e:null,[e]:s}))},S=()=>Y(o==null?void 0:o.comment)?(m(e=>({...e,errors:{...e==null?void 0:e.errors,comment:{message:"Comment is required"}}})),!1):!0,$=async e=>{try{y("editNoteComment",!0);const s={comment:e==null?void 0:e.comment},c=await _("update_comments",n==null?void 0:n.id,s);c!=null&&c.error||(m(w=>({...w,edit_comment:!1,comment:""})),l&&l())}catch(s){console.error("error >> ",s)}finally{y("editNoteComment",!1)}},k=r.useMemo(()=>!(i!=null&&i.updateContributors)||!(f!=null&&f.id)?[]:i.updateContributors.filter(e=>e.id!==f.id),[i==null?void 0:i.updateContributors,f==null?void 0:f.id]);return t.jsx(r.Fragment,{children:o!=null&&o.edit_comment?t.jsxs("div",{className:`relative w-full ${(R=(M=o==null?void 0:o.errors)==null?void 0:M.comment)!=null&&R.message?"mb-5":""}`,children:[t.jsx("textarea",{ref:u,className:"border-bborder-t-0 mt-3 h-auto min-h-[32px] w-full resize-none appearance-none overflow-hidden rounded-sm border-x-0 border-t-0 border-[#1f1d1a] bg-brown-main-bg p-[12px_16px_12px_16px] px-0 text-sm font-normal leading-tight text-[#1f1d1a] placeholder:text-base placeholder:text-[#79716C] focus:border-x-0 focus:border-t-0 focus:shadow-none focus:outline-none focus:outline-0 focus:ring-0",rows:"1",name:"comment",placeholder:"Edit comment... (Press @ to mention someone)",onChange:U,value:(o==null?void 0:o.comment)||"",onKeyDown:e=>{e.key==="Enter"?e.shiftKey?setTimeout(N,0):(e.preventDefault(),S()&&$({comment:o==null?void 0:o.comment})):e.key==="Escape"&&m(s=>({...s,edit_comment:!1,comment:n==null?void 0:n.comment}))}}),((P=(L=o==null?void 0:o.errors)==null?void 0:L.comment)==null?void 0:P.message)&&t.jsx("p",{className:"mt-1 text-sm text-red-500",children:o.errors.comment.message}),t.jsxs("div",{className:"flex hidden gap-2 justify-end mt-2",children:[t.jsx("button",{onClick:()=>{m(e=>({...e,edit_comment:!1,comment:n==null?void 0:n.comment}))},className:"px-3 py-1 text-sm text-gray-600 hover:text-gray-800",children:"Cancel"}),t.jsx("button",{onClick:()=>{S()&&$({comment:o==null?void 0:o.comment})},className:"rounded-sm bg-[#1f1d1a] px-3 py-1 text-sm text-white hover:bg-[#1f1d1a]/90",children:"Save"})]}),p&&k.length>0&&t.jsxs("div",{ref:x,style:T.popper,...O.popper,className:"mention-modal z-50 max-h-[200px] w-[250px] overflow-y-auto rounded-[.125rem] border-[.125rem] border-[#1f1d1a] bg-brown-main-bg px-3 shadow-lg",children:[t.jsx("div",{ref:I,style:T.arrow}),k.map(e=>t.jsxs("div",{className:"flex cursor-pointer items-center gap-2 border-b border-[#1f1d1a]/10 p-3 font-iowan text-[#1f1d1a] last:border-b-0 hover:bg-[#1f1d1a]/5",onClick:()=>q(e),children:[e.photo?t.jsx("img",{src:e.photo,alt:`${e.first_name} ${e.last_name}`,className:"h-7 w-7 rounded-full border border-[#1f1d1a]/20 object-cover"}):t.jsx("div",{className:"flex h-7 w-7 items-center justify-center rounded-full border border-[#1f1d1a]/20 bg-[#1f1d1a]/5 font-iowan text-sm text-[#1f1d1a]",children:e.first_name[0]}),t.jsxs("span",{className:"font-medium",children:[e.first_name," ",e.last_name]})]},e.id))]})]}):t.jsxs("div",{className:"flex flex-col gap-[.75rem]",children:[t.jsx("p",{id:n==null?void 0:n.id,className:"my-[20px] whitespace-pre-wrap font-iowan !text-[1rem] !font-[400] !leading-[1.5rem]",children:H(n==null?void 0:n.comment,(i==null?void 0:i.updateContributors)||[])}),t.jsx("div",{className:"mb-[12px] flex gap-5"})]})})};export{pe as UpdateSectionNoteCommentContent,pe as default};
