import{j as f}from"./@nextui-org/listbox-0f38ca19.js";import{r as l,u as Z,i as I,b as x,R as a}from"./vendor-4cdf2bd1.js";import{a as D,u as ss,i as os,bV as rs}from"./index-46de5032.js";import{u as is}from"./useComments-72d3eb3f.js";import{A as es}from"./AddButton-51d1b2cd.js";import{U as ds}from"./UpdateSectionNoteComment-c6bf55bf.js";import{A as ls}from"./index-a807e4ab.js";import{u as fs}from"./useUpdateCollaborator-daff0e7f.js";import{u as ns}from"./react-popper-9a65a9b6.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./UpdateSection-a6c2ef82.js";import"./index-b05aa8a0.js";import"./lucide-react-0b94883e.js";import"./@popperjs/core-f3391c26.js";const cs=e=>e&&typeof e=="object"&&typeof e.id=="number"&&typeof e.reply=="string",Vs=({note:e=null,update:r=null,comment:d=null,externalData:o=null,setExternalData:c=null})=>{var P,U,q,F;const{showToast:A,tokenExpireError:us,custom:ms,setLoading:v,create:z,RequestItems:p}=D(),t=l.useRef(null),w=l.useRef(null),N=l.useRef(!1);Z(),l.useState({html:null,data:null,modal:null,showModal:!1});const{loading:ps,comments:M,getCommentReplies:B}=is(),{public_link_id:_}=I(),{profile:n}=ss(),V=x();l.useState(!1);const{data:m,fetchUpdateContributors:K}=fs(),[y,h]=l.useState(!1);l.useState({x:0,y:0});const S=l.useRef(null),E=l.useRef(null),[Y,H]=l.useState(null),{styles:k,attributes:G}=ns(S.current,E.current,{placement:"bottom-start",modifiers:[{name:"arrow",options:{element:Y}},{name:"offset",options:{offset:[0,8]}},{name:"preventOverflow",options:{padding:8}},{name:"flip",options:{padding:8}}]});l.useEffect(()=>{r!=null&&r.id&&K(r==null?void 0:r.id)},[r==null?void 0:r.id]);const C=()=>{const s=t.current;s&&(s.style.height="auto",s.style.height=s.scrollHeight+"px")};l.useEffect(()=>{o!=null&&o.add_reply&&C()},[o==null?void 0:o.add_reply]),l.useEffect(()=>{N.current=o==null?void 0:o.add_reply,console.log("isAddingReply updated:",N.current)},[o==null?void 0:o.add_reply]),l.useEffect(()=>{const s=i=>{o!=null&&o.add_reply&&w.current&&!w.current.contains(i.target)&&(c==null||c(u=>({...u,modal:null,add_reply:!1,showModal:!1,reply:"",errors:{...u==null?void 0:u.errors,reply:{message:""}}})))};return document.addEventListener("mousedown",s),()=>document.removeEventListener("mousedown",s)},[o==null?void 0:o.add_reply]);const $=(s,i)=>{c&&c(u=>({...u,[s]:i,showModal:i,modal:i?s:null}))},J=()=>{c&&(os(o==null?void 0:o.reply)?c(s=>({...s,errors:{...s==null?void 0:s.errors,reply:{message:"Reply is required"}}})):T())},T=async()=>{try{if(!(d!=null&&d.id)||!(e!=null&&e.id)||!(r!=null&&r.id)||!(n!=null&&n.id)){console.error("Missing required data for adding reply");return}v(p==null?void 0:p.createModel,!0);const s={note_id:e==null?void 0:e.id,user_id:n==null?void 0:n.id,update_id:r==null?void 0:r.id,reply:o==null?void 0:o.reply,update_comment_id:d==null?void 0:d.id,create_at:rs(new Date)},i=await z("update_comment_replies",s);i!=null&&i.error||R()}catch(s){console.error("Error adding reply:",s)}finally{c&&c(s=>({...s,modal:null,add_reply:!1,showModal:!1,reply:"",errors:{...s==null?void 0:s.errors,reply:{message:""}}})),v(p==null?void 0:p.createModel,!1)}},R=async()=>{if(!(d!=null&&d.id)||!(e!=null&&e.id)||!(r!=null&&r.id)){console.error("Missing required data for loading replies");return}try{await B({filter:[`update_id,eq,${r==null?void 0:r.id}`,`note_id,eq,${e==null?void 0:e.id}`,`update_comment_id,eq,${d==null?void 0:d.id}`],note_id:e==null?void 0:e.id,update_id:r==null?void 0:r.id,comment_id:d==null?void 0:d.id,exposure:_?"public":"private"})}catch(s){console.error("Error loading replies:",s)}};l.useEffect(()=>{r!=null&&r.id&&(e!=null&&e.id)&&(d!=null&&d.id)&&R()},[r==null?void 0:r.id,e==null?void 0:e.id,d==null?void 0:d.id,_]);const Q=s=>Array.isArray(s==null?void 0:s.replies)&&s.replies.every(cs),W=s=>{const i=s.target.value,u=i[i.length-1],j=t.current;C(),s.nativeEvent.inputType==="deleteContentBackward"&&y&&h(!1),u==="@"&&(S.current=j,h(!0)),c&&c(g=>({...g,reply:i,errors:{...g==null?void 0:g.errors,reply:{message:""}}}))},X=s=>{const i=t.current,u=i.selectionEnd,j=o.reply.substring(0,u),g=o.reply.substring(u),O=`@${s.first_name} ${s.last_name} `;c(b=>({...b,reply:j.slice(0,-1)+O+g})),h(!1),setTimeout(()=>{i.focus();const b=u-1+O.length;i.setSelectionRange(b,b)},0)};l.useEffect(()=>{const s=i=>{y&&!i.target.closest(".mention-modal")&&h(!1)};return document.addEventListener("mousedown",s),()=>document.removeEventListener("mousedown",s)},[y]);const L=a.useMemo(()=>!(m!=null&&m.updateContributors)||!(n!=null&&n.id)?[]:m.updateContributors.filter(s=>s.id!==(n==null?void 0:n.id)),[m==null?void 0:m.updateContributors,n==null?void 0:n.id]);return l.useEffect(()=>{if(y){const s=window.scrollY,i=window.getComputedStyle(document.body).overflow;return document.body.style.position="fixed",document.body.style.top=`-${s}px`,document.body.style.width="100%",document.body.style.overflow="hidden",()=>{document.body.style.position="",document.body.style.top="",document.body.style.width="",document.body.style.overflow=i,window.scrollTo(0,s)}}},[y]),f.jsxs(l.Fragment,{children:[f.jsxs("div",{className:`relative ml-[1.125rem] flex flex-col  space-y-[20px] border-l-[.125rem] border-l-[#1F1D1A1A] pl-[1.875rem] ${o!=null&&o.showReply?"":"hidden"}`,children:[Q(M)&&[...M.replies].sort((s,i)=>new Date(i.update_at)-new Date(s.update_at)).map((s,i)=>f.jsx(ds,{comment:d,update:r,reply:s,note:e,loadReplies:R},i)),(o==null?void 0:o.add_reply)&&f.jsxs("div",{ref:w,className:`reply-form-area relative w-full ${(U=(P=o==null?void 0:o.errors)==null?void 0:P.reply)!=null&&U.message?"mb-5":""}`,children:[f.jsx("textarea",{ref:t,className:"border-bborder-t-0 h-auto min-h-[32px] w-full resize-none appearance-none overflow-hidden rounded-sm border-x-0 border-t-0 border-[#1f1d1a] bg-brown-main-bg p-[12px_16px_12px_16px] px-0 text-sm font-normal leading-tight text-[#1f1d1a] placeholder:text-base placeholder:text-[#79716C] focus:border-x-0 focus:border-t-0 focus:border-t-0 focus:shadow-none focus:outline-none focus:outline-0 focus:ring-0",rows:"1",name:"reply",placeholder:"Reply to this comment... (Press @ to mention someone)",onChange:W,value:(o==null?void 0:o.reply)||"",onKeyDown:s=>{var i;s.key==="Enter"&&(s.shiftKey?setTimeout(C,0):(s.preventDefault(),(i=o==null?void 0:o.reply)!=null&&i.trim()&&J()))}}),((F=(q=o==null?void 0:o.errors)==null?void 0:q.reply)==null?void 0:F.message)&&f.jsx("p",{className:"mt-1 text-sm text-red-500",children:o.errors.reply.message}),y&&L.length>0&&f.jsxs("div",{ref:E,style:k.popper,...G.popper,className:"mention-modal z-50 max-h-[200px] w-[250px] overflow-y-auto rounded-[.125rem] border-[.125rem] border-[#1f1d1a] bg-brown-main-bg px-3 shadow-lg",children:[f.jsx("div",{ref:H,style:k.arrow}),L.map(s=>f.jsxs("div",{className:"flex cursor-pointer items-center gap-2 border-b border-[#1f1d1a]/10 p-3 font-iowan text-[#1f1d1a] last:border-b-0 hover:bg-[#1f1d1a]/5",onClick:()=>X(s),children:[s.photo?f.jsx("img",{src:s.photo,alt:`${s.first_name} ${s.last_name}`,className:"h-7 w-7 rounded-full border border-[#1f1d1a]/20 object-cover"}):f.jsx("div",{className:"flex h-7 w-7 items-center justify-center rounded-full border border-[#1f1d1a]/20 bg-[#1f1d1a]/5 font-iowan text-sm text-[#1f1d1a]",children:s.first_name[0]}),f.jsxs("span",{className:"font-medium",children:[s.first_name," ",s.last_name]})]},s.id))]})]}),f.jsx("div",{className:"flex gap-5",children:!(o!=null&&o.add_reply)&&f.jsx(es,{onClick:()=>{if(_)V("/member/sign-up"),A("Please Signup to interact with the update",3e3);else if(r!=null&&r.sent_at)$("add_reply",!0);else{A("Update is not sent yet");return}},className:"!h-[36px] !w-[174px] !min-w-[10.875rem] !gap-[.625rem] !rounded-[.125rem] !border-[.0625rem] !border-black !bg-brown-main-bg !py-[.5rem] px-[1rem] font-iowan !text-[1rem] !font-bold !leading-[1.25rem] !text-black",children:"Add Reply"})})]}),f.jsx(ls,{title:"Reply",mode:"manual",action:"Add",multiple:!1,onSuccess:T,inputConfirmation:!1,onClose:()=>$("add_reply",!0),customMessage:f.jsx(f.Fragment,{children:"Are you sure you want to add this reply?"}),isOpen:(o==null?void 0:o.showModal)&&(o==null?void 0:o.modal)==="confirm_reply",className:"action-confirmation-modal"})]})};export{Vs as default};
