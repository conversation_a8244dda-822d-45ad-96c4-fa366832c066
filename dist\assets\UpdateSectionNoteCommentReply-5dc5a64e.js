import{j as o}from"./@nextui-org/listbox-0f38ca19.js";import{u as g,r as c}from"./vendor-4cdf2bd1.js";import"./moment-a9aaa855.js";import{a as w,u as j,E as b,d as C,bs as E,T as N}from"./index-46de5032.js";import{a as D}from"./UpdateSectionNoteComment-c6bf55bf.js";import{UpdateSectionNoteCommentReplyContent as y}from"./UpdateSectionNoteCommentReplyContent-9038378b.js";import{a as _,D as k}from"./index-b05aa8a0.js";import{A as M}from"./index-a807e4ab.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./UpdateSection-a6c2ef82.js";import"./useComments-72d3eb3f.js";import"./lucide-react-0b94883e.js";import"./useUpdateCollaborator-daff0e7f.js";const R=t=>t&&typeof t=="object"&&typeof t.id=="number"&&typeof t.reply=="string",te=({comment:t=null,update:r=null,reply:s=null,note:f=null,loadReplies:d=null})=>{var u;const{showToast:i,tokenExpireError:S,custom:U,setLoading:A}=w();g();const{profile:p}=j(),[m,n]=c.useState({html:null,data:null,showModal:!1,modal:null,edit_reply:!1,reply:""}),a=[...(p==null?void 0:p.id)==((u=s==null?void 0:s.user)==null?void 0:u.id)?[{icon:o.jsx(b,{className:"h-[1.125rem] w-[1.125rem]"}),name:"Edit",onClick:()=>{if(!(r!=null&&r.sent_at)){i("Update is not sent yet");return}n(e=>({...e,edit_reply:!0,reply:s==null?void 0:s.reply}))},loading:!1},{icon:o.jsx(C,{fill:"#1F1D1A",className:"h-[1.125rem] w-[1.125rem]"}),name:"Delete",onClick:()=>{if(!(r!=null&&r.sent_at)){i("Update is not sent yet");return}n(e=>({...e,showModal:!0,modal:"delete_reply"}))}}]:[]],x=async()=>{try{const l=new N().delete("update_comment_replies",s==null?void 0:s.id);l!=null&&l.error||(n(h=>({...h,showModal:!1,modal:null})),d&&d(),i("Reply deleted successfully"))}catch(e){console.error("Error deleting reply:",e),err.message!=="TOKEN_EXPIRED"&&i(e.message||"Error deleting reply",5e3,"error")}};return R(s)?o.jsxs(c.Fragment,{children:[o.jsxs("div",{className:"flex flex-col gap-[1rem]",children:[o.jsxs("div",{className:"flex items-start justify-between",children:[o.jsx(D,{comment:t,update:r,reply:s,note:f}),a.length>0&&o.jsx(_,{className:"!rounded-[.125rem] !bg-brown-main-bg",childrenWrapperClass:"!bg-brown-main-bg !rounded-[.125rem] !px-[1rem] !min-w-[10rem] !w-[10rem]",icon:o.jsx(E,{stroke:"#1F1D1A",className:"!rotate-90"}),children:a==null?void 0:a.map((e,l)=>o.jsx(k,{icon:e==null?void 0:e.icon,name:e==null?void 0:e.name,onClick:e==null?void 0:e.onClick,loading:e==null?void 0:e.loading,disabled:e==null?void 0:e.loading,className:"!h-[2.75rem] !min-h-[2.75rem]"},l))})]}),o.jsx(y,{comment:t,update:r,reply:s,note:f,externalData:m,setExternalData:n,loadReplies:d})]}),o.jsx(M,{isOpen:(m==null?void 0:m.showModal)&&(m==null?void 0:m.modal)==="delete_reply",onClose:()=>n(e=>({...e,showModal:!1})),customMessage:"Are you sure you want to delete this reply?",title:"Delete Reply",action:"delete",mode:"manual",inputConfirmation:!1,onSuccess:x})]}):null};export{te as default};
