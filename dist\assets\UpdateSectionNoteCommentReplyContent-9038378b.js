import{j as h}from"./@nextui-org/listbox-0f38ca19.js";import{r as d}from"./vendor-4cdf2bd1.js";import{a as C,M as E,T as R}from"./index-46de5032.js";import{u as T}from"./useUpdateCollaborator-daff0e7f.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const N=s=>s&&typeof s=="object"&&typeof s.id=="number"&&typeof s.reply=="string",j=(s,r=[])=>{if(!s||typeof s!="string")return"";const g=/@[A-Za-z0-9]+(?:\s+[A-Za-z0-9]+)?(?=\s|$)/g;let f=0;const o=[];let i;for(;(i=g.exec(s))!==null;){if(i.index>f){const n=s.slice(f,i.index);o.push(n)}const c=i[0],u=c.substring(1),y=r.some(n=>`${n.first_name} ${n.last_name}`.trim().toLowerCase()===u.toLowerCase());o.push(h.jsx("span",{className:`mr-[4px] rounded-sm ${y?"bg-[#1f1d1a]/5":""} px-[1px] py-0.5 font-medium`,children:c},i.index)),f=g.lastIndex}if(f<s.length){const c=s.slice(f);o.push(c)}return o},B=({reply:s=null,update:r=null,note:g=null,comment:f=null,externalData:o=null,setExternalData:i=null,loadReplies:c=null})=>{const{data:u,fetchUpdateContributors:y}=T(),n=d.useRef(null),p=d.useRef(null),{showToast:l}=C();new E,d.useEffect(()=>{r!=null&&r.id&&y(r==null?void 0:r.id)},[r==null?void 0:r.id]);const b=()=>{const e=n.current;e&&(e.style.height="auto",e.style.height=`${e.scrollHeight}px`)};d.useEffect(()=>{o!=null&&o.edit_reply&&n.current&&(n.current.focus(),b())},[o==null?void 0:o.edit_reply]),d.useEffect(()=>{const e=t=>{o!=null&&o.edit_reply&&p.current&&!p.current.contains(t.target)&&i(m=>({...m,edit_reply:!1,reply:s==null?void 0:s.reply}))};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[o==null?void 0:o.edit_reply,s==null?void 0:s.reply]);const _=e=>{const t=e.target.value;b(),i&&i(m=>({...m,reply:t}))},w=async()=>{var e;if(!((e=o==null?void 0:o.reply)!=null&&e.trim())){l("Reply cannot be empty",5e3,"error");return}try{const m=await new R().update("update_comment_replies",s==null?void 0:s.id,{reply:o.reply});m!=null&&m.error||(l("Reply updated successfully",5e3,"success"),c&&c(),i&&i(v=>({...v,edit_reply:!1})))}catch(t){console.error("Error updating reply:",t),err.message!=="TOKEN_EXPIRED"&&l(t.message||"Error updating reply",5e3,"error")}};return!N(s)&&!(o!=null&&o.edit_reply)?null:o!=null&&o.edit_reply?h.jsx("div",{ref:p,className:"relative mt-2",children:h.jsx("textarea",{ref:n,className:"mt-3 h-auto min-h-[32px] w-full resize-none appearance-none overflow-hidden rounded-sm border-x-0 border-b border-t-0 border-[#1f1d1a] bg-brown-main-bg p-[12px_16px_12px_16px] px-0 text-sm font-normal leading-tight text-[#1f1d1a] placeholder:text-base placeholder:text-[#79716C] focus:border-x-0 focus:border-t-0 focus:shadow-none focus:outline-none focus:outline-0 focus:ring-0",rows:"1",name:"reply",placeholder:"Edit your reply...",onChange:_,value:(o==null?void 0:o.reply)||"",onKeyDown:e=>{e.key==="Enter"&&!e.shiftKey?(e.preventDefault(),w()):e.key==="Escape"?i(t=>({...t,edit_reply:!1,reply:s==null?void 0:s.reply})):e.key==="Enter"&&e.shiftKey&&setTimeout(b,0)}})}):h.jsx("div",{className:"mt-2 text-sm",children:j(s==null?void 0:s.reply,(u==null?void 0:u.updateContributors)||[])})};export{B as UpdateSectionNoteCommentReplyContent};
