import{j as s}from"./@nextui-org/listbox-0f38ca19.js";import{A as x,a9 as f,L as j}from"./index-46de5032.js";import{M as n}from"./index-af54e68d.js";import{h as p}from"./moment-a9aaa855.js";import{r as u}from"./vendor-4cdf2bd1.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const P=({update:N=null,note:M=null,comment:r=null})=>{var a,e,l,i,t;const{state:d}=u.useContext(x),h=u.useMemo(()=>{try{return d.user}catch(o){return console.error("Error parsing user from localStorage:",o),null}},[])===((a=r==null?void 0:r.user)==null?void 0:a.id);return s.jsxs("div",{className:"flex !h-[2.25rem] !max-h-[2.25rem] !min-h-[2.25rem] w-full gap-[.75rem]",children:[s.jsx("div",{className:"h-[2.25rem] w-[2.25rem] rounded-[50%]",children:(e=r==null?void 0:r.user)!=null&&e.photo?s.jsx("img",{className:"rounded-[50%] object-cover",src:(l=r==null?void 0:r.user)==null?void 0:l.photo,alt:"avatar"}):s.jsx(f,{className:"h-[2.25rem] w-[2.25rem] rounded-[50%]"})}),s.jsxs("div",{className:"flex !h-full !max-h-full !min-h-full flex-col gap-[.125rem]",children:[s.jsx("span",{className:"font-inter text-[.875rem] font-[600] leading-[1.0588rem]",children:h?"Me":((i=r==null?void 0:r.user)==null?void 0:i.first_name)+" "+((t=r==null?void 0:r.user)==null?void 0:t.last_name)}),s.jsx(j,{children:s.jsx(n,{display:s.jsx("p",{children:p(r==null?void 0:r.create_at).fromNow()}),openOnClick:!1,backgroundColor:"#1f1d1a",children:s.jsx("span",{className:"text-white",children:p(r==null?void 0:r.create_at).format("MMMM D, YYYY [at] h:mm A")})})})]})]})};export{P as default};
