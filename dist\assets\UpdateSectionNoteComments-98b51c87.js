import{j as r}from"./@nextui-org/listbox-0f38ca19.js";import{r as m,i as W,b as X,u as Z}from"./vendor-4cdf2bd1.js";import{a as D,u as ee,i as te,bE as q,bV as se}from"./index-46de5032.js";import{u as oe}from"./useComments-72d3eb3f.js";import{A as ne}from"./AddButton-51d1b2cd.js";import{c as re}from"./UpdateSection-a6c2ef82.js";import{A as me}from"./index-a807e4ab.js";import{u as ie}from"./useUpdateCollaborator-daff0e7f.js";import{u as de}from"./react-popper-9a65a9b6.js";import{u as ce}from"./useMentions-6b3c3c1d.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@popperjs/core-f3391c26.js";const le=({children:s})=>{try{return s}catch(n){return console.error("Comment Error:",n),null}},Be=({update:s=null,note:n=null})=>{var T,$,L,R,P;const{create:B,setLoading:E,showToast:j,setGlobalState:I}=D(),[t,b]=m.useState({html:null,data:null,modal:null,comment:"",showModal:!1,errors:{comment:{message:""}}}),{loading:fe,comments:f,getComments:U}=oe(),{public_link_id:p}=W(),{profile:c}=ee(),O=X(),a=Z(),[x,w]=m.useState(!1),g=m.useRef(null),{data:h,fetchUpdateContributors:z}=ie(),{markAllMentionsAsSeen:F}=ce(),S=m.useRef(null),A=m.useRef(null),[V,K]=m.useState(null),{styles:N,attributes:Y}=de(S.current,A.current,{placement:"bottom-start",modifiers:[{name:"arrow",options:{element:V}},{name:"offset",options:{offset:[0,8]}},{name:"preventOverflow",options:{padding:8}},{name:"flip",options:{padding:8}}]});m.useEffect(()=>{s!=null&&s.id&&z(s==null?void 0:s.id)},[s==null?void 0:s.id]);const y=(e,o)=>{b(i=>({...i,[e]:o,showModal:o,modal:o?e:null}))},C=()=>{U({note_id:n==null?void 0:n.id,update_id:s==null?void 0:s.id,exposure:p?"public":"private",filter:[`update_id,eq,${s==null?void 0:s.id}`,`note_id,eq,${n==null?void 0:n.id}`]})},G=()=>{te(t==null?void 0:t.comment)?b(e=>({...e,errors:{...e==null?void 0:e.errors,comment:{message:"Comment is required"}}})):M()},M=async()=>{var e,o;try{E((e=q)==null?void 0:e.createModel,!0);const i={note_id:n==null?void 0:n.id,user_id:c==null?void 0:c.id,update_id:s==null?void 0:s.id,comment:t==null?void 0:t.comment,create_at:se(new Date)},d=await B("update_comments",i);b(l=>({...l,comment:""})),d!=null&&d.error||C()}catch(i){console.error("error >> ",i)}finally{y("add_comment",!1),E((o=q)==null?void 0:o.createModel,!1)}},v=()=>{const e=g.current;e&&(e.style.height="auto",e.style.height=e.scrollHeight+"px")};m.useEffect(()=>{t!=null&&t.add_comment&&v()},[t==null?void 0:t.add_comment]),m.useEffect(()=>{if(x){const e=window.scrollY,o=window.getComputedStyle(document.body).overflow;return document.body.style.position="fixed",document.body.style.top=`-${e}px`,document.body.style.width="100%",document.body.style.overflow="hidden",()=>{document.body.style.position="",document.body.style.top="",document.body.style.width="",document.body.style.overflow=o,window.scrollTo(0,e)}}},[x]);const H=e=>{const o=e.target.value,i=o[o.length-1],d=g.current;v(),e.nativeEvent.inputType==="deleteContentBackward"&&x&&w(!1),i==="@"&&(S.current=d,w(!0)),b(l=>({...l,comment:o,errors:{...l==null?void 0:l.errors,comment:{message:""}}}))},J=e=>{const o=g.current,i=o.selectionEnd,d=t.comment.substring(0,i),l=t.comment.substring(i),_=`@${e.first_name} ${e.last_name} `;b(u=>({...u,comment:d.slice(0,-1)+_+l})),w(!1),setTimeout(()=>{o.focus();const u=i-1+_.length;o.setSelectionRange(u,u)},0)};m.useEffect(()=>{const e=o=>{x&&!o.target.closest(".mention-modal")&&w(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[x]),m.useEffect(()=>{s!=null&&s.id&&(n!=null&&n.id)&&(C(),s!=null&&s.id&&!p&&F(s.id).then(e=>{e&&I("mentionsChange",!(globalState!=null&&globalState.mentionsChange))}))},[s==null?void 0:s.id,n==null?void 0:n.id,p]),m.useEffect(()=>{const e=o=>{t!=null&&t.add_comment&&g.current&&!g.current.contains(o.target)&&!o.target.closest(".mention-modal")&&y(t==null?void 0:t.modal,!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[t==null?void 0:t.add_comment]),m.useEffect(()=>{var e;if(a!=null&&a.hash&&((e=f==null?void 0:f.list)!=null&&e.length)){const[o,i]=a==null?void 0:a.hash.split("#"),d=(()=>{try{return atob(i)}catch{return i}})();if(d.includes("comment:")){const[l,_]=d==null?void 0:d.split(":"),u=document.getElementById(_);u&&(u==null||u.scrollIntoView({behavior:"smooth",block:"center"}))}}},[a==null?void 0:a.hash,(T=f==null?void 0:f.list)==null?void 0:T.length]),m.useEffect(()=>{},[f,n,s]);const Q=e=>Array.isArray(e==null?void 0:e.list)&&e.list.every(o=>o&&typeof o=="object"&&typeof o.id=="number"&&typeof o.comment=="string"),k=m.useMemo(()=>!(h!=null&&h.updateContributors)||!(c!=null&&c.id)?[]:h.updateContributors.filter(e=>e.id!==c.id),[h==null?void 0:h.updateContributors,c==null?void 0:c.id]);return r.jsxs(m.Fragment,{children:[r.jsx("div",{className:"relative  flex flex-col gap-[1rem]",children:Q(f)?[...f.list].sort((e,o)=>new Date(e.update_at)-new Date(o.update_at)).map((e,o)=>r.jsx(le,{children:r.jsx(re,{loadComments:C,comment:e,update:s,note:n,onSuccess:()=>{C()}})},o)):null}),t!=null&&t.add_comment?r.jsxs("div",{className:`relative w-full ${(L=($=t==null?void 0:t.errors)==null?void 0:$.comment)!=null&&L.message?"mb-5":""}`,children:[r.jsx("textarea",{ref:g,className:"border-bborder-t-0 h-auto min-h-[32px] w-full resize-none appearance-none overflow-hidden rounded-sm border-x-0 border-t-0 border-[#1f1d1a] bg-brown-main-bg p-[12px_16px_12px_16px] px-0 text-sm font-normal leading-tight text-[#1f1d1a] placeholder:text-base placeholder:text-[#79716C] focus:border-x-0 focus:border-t-0 focus:shadow-none focus:outline-none focus:outline-0 focus:ring-0",rows:"1",name:"comment",placeholder:"Comment on this section... (Press @ to mention someone)",onChange:H,value:t==null?void 0:t.comment,onKeyDown:e=>{var o;e.key==="Enter"&&(e.shiftKey?setTimeout(v,0):(e.preventDefault(),(o=t==null?void 0:t.comment)!=null&&o.trim()&&G()))}}),((P=(R=t==null?void 0:t.errors)==null?void 0:R.comment)==null?void 0:P.message)&&r.jsx("p",{className:"mt-1 text-sm text-red-500",children:t.errors.comment.message}),x&&k.length>0&&r.jsxs("div",{ref:A,style:N.popper,...Y.popper,className:"mention-modal z-50 max-h-[200px] w-[250px] overflow-y-auto rounded-[.125rem] border-[.125rem] border-[#1f1d1a] bg-brown-main-bg px-3 shadow-lg",children:[r.jsx("div",{ref:K,style:N.arrow}),k.map(e=>r.jsxs("div",{className:"flex cursor-pointer items-center gap-2 border-b border-[#1f1d1a]/10 p-3 font-iowan text-[#1f1d1a] last:border-b-0 hover:bg-[#1f1d1a]/5",onClick:()=>J(e),children:[e.photo?r.jsx("img",{src:e.photo,alt:`${e.first_name} ${e.last_name}`,className:"h-7 w-7 rounded-full border border-[#1f1d1a]/20 object-cover"}):r.jsx("div",{className:"flex h-7 w-7 items-center justify-center rounded-full border border-[#1f1d1a]/20 bg-[#1f1d1a]/5 font-iowan text-sm text-[#1f1d1a]",children:e.first_name[0]}),r.jsxs("span",{className:"font-medium",children:[e.first_name," ",e.last_name]})]},e.id))]})]}):null,r.jsx("div",{className:` flex gap-5 ${t!=null&&t.add_comment?"mt-8":"mt-0"}`,children:!(t!=null&&t.add_comment)&&r.jsx(ne,{onClick:()=>{if(p)j("Please Signup to interact with the update",3e3),O("/member/sign-up");else if(s!=null&&s.sent_at)y("add_comment",!0);else{j("Update is not sent yet");return}},className:"!h-[36px] !w-[174px] !min-w-[10.875rem] !gap-[.625rem] !rounded-[.125rem] !border-[.0625rem] !border-black !bg-brown-main-bg !py-[.5rem] px-[1rem] font-iowan !text-[1rem] !font-bold !leading-[1.25rem] !text-black",children:"Add Comment"})}),r.jsx(me,{action:"Add",mode:"manual",multiple:!1,title:"Add Comment",onSuccess:M,inputConfirmation:!1,onClose:()=>y("add_comment",!0),customMessage:r.jsx(r.Fragment,{children:"Are you sure you want to add this comment?"}),isOpen:(t==null?void 0:t.showModal)&&(t==null?void 0:t.modal)==="confirm_comment"})]})};export{Be as default};
