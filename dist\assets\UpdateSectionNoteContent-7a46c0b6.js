import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{r as o}from"./vendor-4cdf2bd1.js";import{z as n}from"./index-46de5032.js";import{O as s}from"./editorjs-react-renderer-ba57cf04.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const d=({data:r})=>!r.text||r.text.trim()===""?t.jsx("div",{style:{minHeight:"1.5rem",marginBottom:"0.5rem"}}):t.jsx("p",{dangerouslySetInnerHTML:{__html:r.text}}),C=({update:r=null,note:e=null})=>{const[i,m]=o.useState({data:null});return o.useEffect(()=>{const a=n(e.content,{blocks:[{id:"zbGZFPM-iI",type:"paragraph",data:{text:""}}]});m(p=>({...p,data:a}))},[e==null?void 0:e.content]),t.jsxs(o.Fragment,{children:[t.jsx("style",{children:`
          .preview-editor p:empty {
            min-height: 1.5rem;
            margin-bottom: 0.5rem;
          }
          .preview-editor p {
            margin: 5px 0;
            font-size: 1rem;
            font-weight: 500;
            line-height: 1.5rem;
            font-family: "Iowan";
          }
          .preview-editor p:first-child {
            margin-top: 0;
          }
          .preview-editor p:last-child {
            margin-bottom: 0;
          }
          .preview-editor h4,h5,h6 {
           margin:5px 0;
          }
         
        `}),t.jsx("div",{className:"preview-editor font-iowan text-[1rem] font-[500] leading-[1.5rem]",children:(i==null?void 0:i.data)&&t.jsx(s,{data:i.data,renderers:{paragraph:d},style:{paragraph:{fontFamily:"iowan",fontSize:"1rem",fontWeight:"500",lineHeight:"1.5rem",margin:"5px 0"}}})})]})};export{C as default};
