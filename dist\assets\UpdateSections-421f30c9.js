import{j as s}from"./@nextui-org/listbox-0f38ca19.js";import{r as i,i as Y,u as ee}from"./vendor-4cdf2bd1.js";import{h as se}from"./moment-a9aaa855.js";import{i as te,j as re}from"./index-a613c3fd.js";import{z as ne,a as oe,G as ie,A as le,O as ce,L as F,M as O,T as B}from"./index-46de5032.js";import{u as ae}from"./useNote-95029f8e.js";import{B as de}from"./BottomSheet-be14d402.js";import"./@nextui-org/theme-345a09ed.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";function me({title:r,titleId:g,...f},p){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:p,"aria-labelledby":g},f),r?i.createElement("title",{id:g},r):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 12a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM12.75 12a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM18.75 12a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Z"}))}const fe=i.forwardRef(me),ue=fe,xe=({update:r=null,onClose:g=null})=>{var p,b,E,w;const f=l=>{g&&g(),setTimeout(()=>{const c=document.getElementById(l);if(c){history.pushState(null,null,`#${l}`),c.scrollIntoView();const d=-100;window.scrollBy(0,d)}},100)};return s.jsx("div",{className:"flex flex-col",children:s.jsxs("div",{className:"divide-y divide-gray-200",children:[(b=(p=r==null?void 0:r.notes)==null?void 0:p.filter(l=>{const{blocks:c}=ne(l.content,{blocks:[]});return c.length>0}))==null?void 0:b.sort((l,c)=>{const d=l.order!==void 0&&l.order!==null?l.order:Number.MAX_SAFE_INTEGER,N=c.order!==void 0&&c.order!==null?c.order:Number.MAX_SAFE_INTEGER;return d-N}).map(l=>s.jsx("button",{onClick:()=>f(l.type),className:"hover:bg-opacity/90 flex w-full items-center justify-between px-4 py-4 text-left",children:s.jsx("span",{className:"font-iowan text-lg font-semibold text-[#1f1d1a]",children:l.type})},l.id)),(E=r==null?void 0:r.update_questions)!=null&&E.length?s.jsxs("button",{onClick:()=>f("update-asks"),className:"hover:bg-opacity/90 flex w-full items-center justify-between px-4 py-4 text-left",children:[s.jsx("span",{className:"font-iowan text-lg font-semibold text-[#1f1d1a]",children:"Asks"}),s.jsx("div",{className:"flex h-[20px] w-[20px] items-center justify-center rounded-full bg-[#990F3D] text-sm text-white",children:((w=r==null?void 0:r.update_questions)==null?void 0:w.length)||0})]}):null]})})},qe=({update:r=null,refetch:g,questionAnswerState:f,setQuestionAnswerState:p,showControls:b})=>{var T,$,q;const{setGlobalState:E,showToast:w}=oe();i.useContext(ie);const{state:l}=i.useContext(le),{loading:c,notes:d,getNotes:N}=ae(),{public_link_id:A}=Y(),Q=ee(),[a,j]=i.useState({modal:null,showModal:!1,selectedItems:[],questionAnswer:"",editingAnswerId:null,editAnswer:""}),G=i.useRef({}),[P,k]=i.useState(!1),S=new URLSearchParams(Q.search).get("question"),D=t=>{j(e=>({...e,questionAnswer:t}))},z=async(t,e,n)=>{try{const o=n||(a==null?void 0:a.questionAnswer),x=await new O().callRawAPI(`/v3/api/custom/goodbadugly/updates/questions/answers/${t}`,{question_id:t,update_id:e,answer:o},"POST");x!=null&&x.error||(j(y=>({...y,questionAnswer:""})),await Promise.all([I()]),w("Question Answered",3e3))}catch(o){console.log(o),w(o.message,5e3,"error")}},K=async t=>{try{const n=await new B().update("update_question_answers",t,{answer:a.editAnswer});n!=null&&n.error||(j(o=>({...o,editingAnswerId:null,editAnswer:""})),await I(),w("Answer updated successfully",3e3))}catch(e){console.log(e),w(e.message,5e3,"error")}},X=t=>{j(e=>({...e,editingAnswerId:t.id,editAnswer:t.answer}))},U=()=>{j(t=>({...t,editingAnswerId:null,editAnswer:""}))},I=async()=>{const e=await new B().getList("update_question_answers",{filter:[`update_id,eq,${r==null?void 0:r.id}`],join:["user|user_id"]});console.log("result with user details >.",e),p(e==null?void 0:e.list)};i.useEffect(()=>{r!=null&&r.id&&(N({update_id:r==null?void 0:r.id,filter:[`update_id,eq,${r==null?void 0:r.id}`],exposure:A?"public":"private"}),A||I())},[r==null?void 0:r.id,A]);const R=()=>{try{return l.user}catch(t){return console.error("Error parsing user from localStorage:",t),null}},H=t=>{if(!(f!=null&&f.length))return null;const e=f.filter(n=>n.update_question_id===t);return e.length===0?null:e.sort((n,o)=>new Date(o.create_at)-new Date(n.create_at))[0]},Z=async t=>{var o,m,x;const e=new O;e.setTable("user");const n=await e.callRestAPI({id:t},"GET");return console.log(n,"contributor"),(o=n==null?void 0:n.model)!=null&&o.first_name?`${(m=n==null?void 0:n.model)==null?void 0:m.first_name} ${(x=n==null?void 0:n.model)==null?void 0:x.last_name}`:"Anonymous User"},[C,V]=i.useState({}),u=(()=>{var n;if(!((n=r==null?void 0:r.update_questions)!=null&&n.length))return console.log("No questions found"),[];const t=R();return t?r.user_id==t?r.update_questions:r.update_questions.filter(o=>o.investor_id==t):[]})(),v=R(),J=(r==null?void 0:r.user_id)===v;i.useEffect(()=>{const t=e=>{a.editingAnswerId&&!e.target.closest("textarea")&&U()};return document.addEventListener("mousedown",t),()=>{document.removeEventListener("mousedown",t)}},[a.editingAnswerId]),i.useEffect(()=>{(async()=>{if(!(u!=null&&u.length))return;const e={...C},n=u.map(o=>o.investor_id).filter(o=>!e[o]);if(n.length!==0){for(const o of n)try{const m=await Z(o);e[o]=m}catch(m){console.error(`Error fetching name for user ${o}:`,m),e[o]="Anonymous User"}V(e)}})()},[u,C]);const[L,W]=i.useState(!1);return i.useEffect(()=>{S&&!L&&u.length>0&&setTimeout(()=>{const t=document.getElementById("update-asks");t&&(t.scrollIntoView({behavior:"smooth",block:"start"}),W(!0))},500)},[S,u,L]),s.jsx(s.Fragment,{children:s.jsxs("div",{className:"relative space-y-10 md:mt-0",children:[c!=null&&c.list?s.jsx(ce,{loading:!0}):null,s.jsx("div",{className:`fixed  right-4 z-50 md:hidden ${b?"bottom-[260px]":"bottom-[180px]"}`,children:s.jsx("div",{className:"flex items-center gap-2",children:s.jsx("button",{onClick:()=>k(!0),className:"flex h-[60px] min-h-[60px] w-[60px] min-w-[60px] items-center justify-center rounded-full bg-[#1f1d1a]",children:s.jsx(F,{children:s.jsx("img",{src:"/assets/section-icon.svg",alt:"",className:"h-8 w-8 object-cover"})})})})}),s.jsx(de,{isOpen:P,onClose:()=>k(!1),title:s.jsx("div",{className:"flex items-center justify-start",children:s.jsx("span",{className:"font-iowan text-xl",children:"Sections"})}),className:"!pb-0",children:s.jsx("div",{className:"px-0",children:s.jsx(xe,{update:r,onClose:()=>k(!1)})})}),(T=d==null?void 0:d.list)!=null&&T.length?(q=($=d==null?void 0:d.list)==null?void 0:$.filter(t=>(t==null?void 0:t.content)!==null))==null?void 0:q.sort((t,e)=>{const n=t.order!==void 0&&t.order!==null?t.order:Number.MAX_SAFE_INTEGER,o=e.order!==void 0&&e.order!==null?e.order:Number.MAX_SAFE_INTEGER;return n-o}).map((t,e)=>s.jsxs(F,{children:[s.jsx(te,{note:t,update:r,onSuccess:()=>{N({filter:[`update_id,eq,${r==null?void 0:r.id}`]})}},e),s.jsx("hr",{className:",md:my-8 my-4 border-[1px] border-[#1f1d1a] md:border-[.125rem] "})]},e)):null,u.length>0&&s.jsxs(s.Fragment,{children:[s.jsxs("section",{id:"update-asks",className:"bg-[#F2DFCE] p-5",children:[s.jsx("h4",{className:"text-lg font-bold",children:"Asks"}),s.jsx("div",{className:"flex flex-col gap-4",children:u.map(t=>{var m,x,y,M;const e=H(t.id),n=t.investor_id==v,o=(e==null?void 0:e.user_id)==v;return s.jsx("div",{className:"border-b-[1px] border-[#1F1D1A1A]",children:s.jsx("div",{className:"md:start flex flex-col items-start justify-between p-2 md:flex-row",children:s.jsxs("div",{className:`flex  w-full justify-between gap-2  ${e?"flex-col":"flex-row"}`,children:[s.jsxs("div",{className:"flex flex-row items-center gap-1",children:[J&&!n&&s.jsxs("span",{className:"font-iowan-regular text-base text-[#1f1d1a]",children:["Question for"," ",s.jsx("span",{className:"font-iowan",children:t.investor_id==v?"me":C[t.investor_id]||"Loading..."}),":"]}),s.jsx("span",{className:"overflow-hidden truncate text-ellipsis break-words font-iowan text-[14px] font-[600]",children:t.question})]}),e?s.jsxs("div",{className:"mt-2 flex items-center gap-2 rounded bg-brown-main-bg p-2",children:[s.jsx("img",{className:"h-6 w-6 rounded-[50%] object-cover",src:((m=e==null?void 0:e.user)==null?void 0:m.photo)||"/default.png",alt:`${((x=e==null?void 0:e.user)==null?void 0:x.first_name)||"User"}'s photo`}),s.jsxs("div",{className:"flex w-full flex-col",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("span",{className:"text-[12px] font-semibold text-[#1f1d1a]",children:o?"Your Response":`${(y=e==null?void 0:e.user)==null?void 0:y.first_name} ${(M=e==null?void 0:e.user)==null?void 0:M.last_name}'s Response`}),o&&s.jsx("div",{className:"relative",children:s.jsx("button",{onClick:()=>X(e),className:"rounded-full p-1 hover:bg-gray-200",children:s.jsx(ue,{className:"h-5 w-5"})})})]}),a.editingAnswerId===e.id?s.jsx("div",{className:"mt-2",children:s.jsx("textarea",{value:a.editAnswer,onChange:h=>j(_=>({..._,editAnswer:h.target.value})),onKeyDown:h=>{var _;h.key==="Enter"&&!h.shiftKey?(h.preventDefault(),(_=a.editAnswer)!=null&&_.trim()&&K(e.id)):h.key==="Escape"&&U()},className:"h-auto min-h-[32px] w-full resize-none appearance-none overflow-hidden rounded-sm border-x-0 border-b border-t-0 border-[#1f1d1a] bg-transparent p-[12px_16px_12px_16px] px-0 text-sm font-normal leading-tight text-[#1f1d1a] placeholder:text-base placeholder:text-[#79716C] focus:border-x-0 focus:border-t-0 focus:shadow-none focus:outline-none focus:outline-0 focus:ring-0",placeholder:"Edit your response...",rows:"1",autoFocus:!0})}):s.jsxs(s.Fragment,{children:[s.jsx("span",{className:"text-sm",children:e.answer}),s.jsx("span",{className:"text-[10px] text-[#1f1d1a]",children:se(e.update_at).fromNow()})]})]})]}):n&&s.jsx(F,{children:s.jsx(re,{ref:h=>G.current[t.id]=h,questionId:t.id,updateId:r==null?void 0:r.id,questionAnswer:a==null?void 0:a.questionAnswer,setQuestionAnswer:D,handleRespondQuestion:z,question:t.question,autoOpen:S===t.id.toString()})})]})})},t.id)})})]}),s.jsx("hr",{className:"my-8 hidden border-[.125rem]  border-[#1f1d1a] md:block"})]})]})})};export{qe as default};
