import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{r as l,R as U}from"./vendor-4cdf2bd1.js";import{a as X,L as C,aI as K,aJ as M,aK as _,aL as q,p as le}from"./index-46de5032.js";import"./moment-a9aaa855.js";import{M as Q}from"./MkdInput-a0090fba.js";import{M as ie}from"./index-af54e68d.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";const p={STOP:"stop",PLAY:"play",PAUSE:"pause",PAUSED:"paused",STOPPED:"stopped",PLAYING:"playing",SELECTION:"selection"},oe={SELECT:"select",ENTIRE_UPDATE:"entire_update",UPDATE_NO_METRICS:"update_no_metrics",summary:"summary"},$=new Map([["entire_update","Entire update"],["update_no_metrics","Update w/o metrics"],["summary","Summary only"]]),ae=({update:x,summary:h})=>{const m=l.useRef(null),O=l.useRef(null),j=l.useRef(null);X();const[S,v]=U.useState({currentTime:0,duration:0,progress:0}),[N,T]=U.useState({synthesizer:!1,targets:{}}),[n,c]=U.useState({synthesizer:[],target:"",autoPlay:!1}),[b,w]=U.useState({state:p.STOP,played:null});l.useEffect(()=>(typeof window<"u"&&window.speechSynthesis&&(m.current=window.speechSynthesis),()=>{var s;(s=m.current)!=null&&s.speaking&&m.current.cancel(),j.current&&clearInterval(j.current)}),[]);const P=l.useCallback(()=>{var u,o;if(!x||!h)return;const s=[{target:"entire_update",text:`${x.name}. ${(u=x.notes)==null?void 0:u.map(i=>i.content).join(". ")}. ${h.content}`},{target:"update_no_metrics",text:`${x.name}. ${(o=x.notes)==null?void 0:o.map(i=>i.content).join(". ")}`},{target:"summary",text:h.content||"No summary available."}];c(i=>({...i,synthesizer:s}))},[x,h]),g=l.useCallback(()=>{m.current&&(m.current.cancel(),j.current&&clearInterval(j.current),w({state:p.STOP,played:null}),v({currentTime:0,duration:0,progress:0}))},[]),y=l.useCallback(s=>{const o=s.split(/\s+/).length/2,i=Date.now();return j.current=setInterval(()=>{var a;if(!((a=m.current)!=null&&a.speaking)){clearInterval(j.current);return}const E=(Date.now()-i)/1e3,F=Math.min(E/o*100,100);v({currentTime:E,duration:o,progress:F})},100),o},[]),e=l.useCallback(s=>{var o;if(!m.current)return;const u=(o=n==null?void 0:n.synthesizer)==null?void 0:o.find(i=>i.target===s);if(u)try{if(s===n.target&&m.current.speaking){b.state===p.PLAY?(m.current.pause(),w(a=>({...a,state:p.PAUSE}))):(m.current.resume(),w(a=>({...a,state:p.PLAY})));return}g(),c(a=>({...a,target:s}));const i=new SpeechSynthesisUtterance(u.text);O.current=i,i.rate=1,i.pitch=1,i.volume=1;const E=m.current.getVoices(),F=E.find(a=>a.lang.startsWith("en")&&a.name.includes("Female"))||E.find(a=>a.lang.startsWith("en"))||E[0];F&&(i.voice=F),i.onstart=()=>{w({played:s,state:p.PLAY}),y(u.text)},i.onpause=()=>{w(a=>({...a,state:p.PAUSE}))},i.onresume=()=>{w(a=>({...a,state:p.PLAY}))},i.onend=()=>{g()},i.onerror=()=>{g()},m.current.speak(i)}catch(i){console.error("Speech error:",i),g()}},[n,b.state,g,y]),Y=l.useCallback(s=>{!m.current||!(n!=null&&n.target)||(s===p.PLAY?e(n.target):s===p.STOP?g():s===p.PAUSE&&m.current.speaking&&(m.current.pause(),w(u=>({...u,state:p.PAUSE}))))},[n==null?void 0:n.target,e,g]),A=l.useCallback(()=>{const s=Array.from($.keys()),u=s.indexOf(n==null?void 0:n.target),o=u===s.length-1?0:u+1;e(s[o])},[n==null?void 0:n.target,e]),L=l.useCallback(()=>{const s=Array.from($.keys()),u=s.indexOf(n==null?void 0:n.target),o=u<=0?s.length-1:u-1;e(s[o])},[n==null?void 0:n.target,e]);return l.useEffect(()=>{x!=null&&x.id&&(h!=null&&h.content)&&P()},[x==null?void 0:x.id,h==null?void 0:h.content,P]),{data:n,player:b,loading:N,setData:c,PlayerMap:p,TargetMap:oe,optionMap:$,setPlayer:w,formatTime:s=>{if(!s)return"0:00";const u=Math.floor(s/60),o=Math.floor(s%60);return`${u}:${o.toString().padStart(2,"0")}`},handleNext:A,handleSpeech:e,handlePlayer:Y,audioProgress:S,handlePrevious:L,stopSpeaking:g}},Ue=({data:x,summary:h,nextUpdate:m,activeAudioRef:O,showControls:j,setShowControls:S,localData:v})=>{var G,J;console.log(v,"localData");const{player:N,setData:T,PlayerMap:n,optionMap:c,formatTime:b,handleNext:w,handleSpeech:P,handlePlayer:g,audioProgress:y,data:e,handleTimeUpdate:Y,setPlayer:A,handlePrevious:L}=ae({update:x,summary:h}),[H,s]=l.useState(!1),{showToast:u,globalState:o}=X(),[i,E]=l.useState(!0),F=l.useRef(null),a=l.useRef(null),[D,z]=l.useState(!1),[ee,V]=l.useState(0),k=l.useRef(null);l.useRef(null),l.useRef(null);const te=l.useRef(null),I=l.useRef(!1),Z=()=>{u("Please login to interact with the update",3e3)};l.useEffect(()=>{const r=new URLSearchParams(location.search),d=r.get("listen_option"),f=r.get("autoplay");return d&&c.has(d)&&(console.log("Initial mount: Setting target from URL param:",d),T(R=>({...R,target:d,autoPlay:f==="true"})),f==="true"&&(P(d,n.SELECTION),u("Starting playback...",3e3))),()=>{window.speechSynthesis&&window.speechSynthesis.cancel()}},[]),l.useEffect(()=>{if(i)return;const r=new URLSearchParams(location.search),d=r.get("listen_option");if((e==null?void 0:e.target)==="entire_update"&&d){console.log("Preventing update to entire_update"),T(f=>({...f,target:d}));return}if(e!=null&&e.target&&d!==e.target){console.log("Updating URL:",{from:d,to:e.target}),r.set("listen_option",e.target);const f=r.get("autoplay");f&&r.set("autoplay",f);const R=`${window.location.pathname}?${r.toString()}`;window.history.pushState({path:R},"",R)}},[e==null?void 0:e.target,i]),l.useEffect(()=>()=>{F.current&&clearTimeout(F.current)},[]),l.useEffect(()=>{if(e!=null&&e.target){const r=new Audio;return r.preload="auto",a.current=r,()=>{a.current&&(a.current.src="",a.current=null)}}},[e==null?void 0:e.target]),l.useCallback(()=>{if(A(r=>({...r,state:n.STOP,played:null})),e!=null&&e.autoPlay){const r=new URLSearchParams(window.location.search);r.set("autoplay","true"),e!=null&&e.target&&r.set("listen_option",e.target);const d=`${window.location.pathname}?${r.toString()}`;window.history.pushState({path:d},"",d),m(!0)}},[A,n,e==null?void 0:e.autoPlay,e==null?void 0:e.target,m]);const re=r=>{k.current=r.touches[0].clientY,I.current=!0,z(!0)},ne=r=>{if(!I.current||!k.current)return;const f=r.touches[0].clientY-k.current;V(f),f>100?S(!1):S(!0)},se=r=>{if(!I.current||!k.current)return;const f=r.changedTouches[0].clientY-k.current;I.current=!1,k.current=null,z(!1),V(0),f<=100&&S(!0)},B=()=>{if(o!=null&&o.isPublicView){Z();return}Array.from(c==null?void 0:c.keys()).includes(e==null?void 0:e.target)&&g(n.PLAY)},W=()=>{if(o!=null&&o.isPublicView){Z();return}T(r=>({...r,autoPlay:!(r!=null&&r.autoPlay)})),!(e!=null&&e.autoPlay)&&(e!=null&&e.target)&&P(e.target)};return console.log(e==null?void 0:e.target,c,"hello"),t.jsxs(t.Fragment,{children:[t.jsx("div",{className:"hidden w-full md:block md:grow",children:t.jsxs("div",{className:"flex w-full flex-col flex-wrap items-start gap-[1rem] font-iowan font-semibold md:flex-row md:items-center",children:[t.jsx("div",{className:"flex flex-col",children:t.jsx(C,{children:t.jsx(Q,{type:"mapping",labelStyle:"!text-[14px]",className:"!h-[34px] !border !border-primary-black !p-[4px] !px-[7px] !pl-[12px] font-iowan",value:e==null?void 0:e.target,label:"Listen to :",noneText:"Select",onChange:r=>P(r.target.value,n.SELECTION),options:Array.from(c.keys()),mapping:Array.from(c.entries()).reduce((r,[d,f])=>(r[d]=f,r),{})})})}),t.jsxs("div",{className:"flex w-full items-end justify-center gap-[.5rem] md:w-auto md:grow",children:[t.jsxs("div",{className:"flex grow flex-col items-center justify-center gap-[.5rem]",children:[t.jsxs("div",{className:"flex flex-row items-center justify-center gap-[1.5rem]",children:[t.jsx(C,{children:t.jsx(K,{className:`cursor-pointer ${e!=null&&e.target?"":"opacity-50"}`,onClick:e!=null&&e.target?L:void 0})}),[n.STOP,n.PAUSE].includes(N==null?void 0:N.state)?t.jsx(C,{children:t.jsx(M,{className:`cursor-pointer ${Array.from(c==null?void 0:c.keys()).includes(e==null?void 0:e.target)?"":"opacity-50"}`,onClick:B})}):t.jsx(C,{children:t.jsx(_,{className:"cursor-pointer",onClick:()=>{g(n.PAUSE)}})}),t.jsx(C,{children:t.jsx(q,{className:`cursor-pointer ${e!=null&&e.target?"":"opacity-50"}`,onClick:e!=null&&e.target?w:void 0})})]}),t.jsxs("div",{className:"grid w-full min-w-full max-w-full grid-cols-[auto_18.75rem_auto] grid-rows-1 items-center justify-center gap-[.5rem]",children:[t.jsx("code",{className:"text-xs text-gray-600 w-fit min-w-fit max-w-fit",children:b(y.currentTime)}),t.jsx("div",{className:"h-[.5rem] w-full overflow-hidden rounded-full bg-[#1F1D1A33]",children:t.jsx("div",{className:"h-full transition-all duration-100 bg-primary-black",style:{width:`${y.progress}%`}})}),t.jsx("code",{className:"text-xs text-gray-600 w-fit min-w-fit max-w-fit",children:b(y.duration)})]})]}),t.jsx("div",{className:"relative",children:t.jsx(ie,{place:"top",openOnClick:!1,display:t.jsxs("div",{className:"flex flex-col items-center justify-center gap-[1.3125rem] ",children:[t.jsx("span",{className:"text-base font-semibold whitespace-nowrap font-iowan",children:"Auto Play"}),t.jsx("div",{className:"relative h-[.75rem] w-[2rem] rounded-full bg-[#D2C6BC] ",children:t.jsx("button",{onClick:W,className:`absolute inset-y-0 m-auto flex h-[1rem] w-[1rem] items-center justify-center rounded-full bg-primary-black ${e!=null&&e.autoPlay?"right-0":""}`,children:e!=null&&e.autoPlay?t.jsx(_,{className:"!h-[50%] !w-[50%]",pathFill:"#FFF0E5"}):t.jsx(M,{className:"!h-[50%] !w-[50%]",pathFill:"#FFF0E5"})})})]}),backgroundColor:"#1f1d1a",tooltipClasses:"!left-[0px] !md:left-[20px] !absolute ",children:t.jsxs("span",{className:"text-xs text-white",children:["Autoplay ",e!=null&&e.autoPlay?"On":"Off"]})})})]})]})}),t.jsxs("div",{className:"fixed bottom-0 left-0 right-0 z-50 flex w-full flex-col border-t-2 border-t-[#F6A03C] bg-[#1f1d1a] p-4 pt-0 text-[#F2DFCE] shadow-[0_-4px_6px_-1px_rgba(0,0,0,0.1)] md:hidden",ref:te,style:{transform:`translateY(${ee}px)`,transition:D?"none":"transform 0.3s ease-out"},children:[t.jsx("div",{className:"flex flex-col items-center w-full",children:t.jsx("svg",{onTouchStart:re,onTouchMove:ne,className:`mb-4 mt-2 cursor-pointer ${j?"rotate-[0deg]":"rotate-[180deg]"}`,onTouchEnd:se,onClick:()=>S(!j),width:"29",height:"9",viewBox:"0 0 29 9",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M2 2L14.5 6L27 2",stroke:"#F2DFCE","stroke-width":"4","stroke-linecap":"round"})})}),t.jsxs("div",{className:"flex justify-between items-center",children:[t.jsxs("div",{className:"flex flex-col gap-2",children:[t.jsx("h4",{className:"font-iowan-regular text-[12px] font-[400]",children:"Currently Playing:"}),t.jsx("h2",{className:"font-iowan text-[12px]",children:((G=Array.from(c.entries()).find(([r])=>r===(e==null?void 0:e.target)))==null?void 0:G[1])||""})]}),t.jsxs("div",{className:"flex flex-col gap-2",children:[t.jsx("h4",{className:"font-iowan-regular text-[12px] font-[400]",children:"Playing Next:"}),t.jsx("h2",{onClick:m,className:"cursor-pointer font-iowan text-[12px]",children:((J=v==null?void 0:v.nextUpdate)==null?void 0:J.name)??"No New Update"})]})]}),t.jsx(t.Fragment,{children:t.jsxs("div",{className:"mb-[4px] mt-4 flex items-center justify-center gap-8",children:[t.jsx(C,{children:t.jsx(K,{pathFill:"#F2DFCE",className:`cursor-pointer ${e!=null&&e.target?"":"opacity-50"}`,onClick:e!=null&&e.target?L:void 0})}),[n.STOP,n.PAUSE].includes(N==null?void 0:N.state)?t.jsx(C,{children:t.jsx(M,{fill:"#F2DFCE",pathFill:"#F2DFCE",className:`cursor-pointer text-[#F2DFCE] ${Array.from(c==null?void 0:c.keys()).includes(e==null?void 0:e.target)?"":"opacity-50"}`,onClick:B})}):t.jsx(C,{children:t.jsx(_,{fill:"#F2DFCE",pathFill:"#F2DFCE",className:"text-white cursor-pointer",onClick:()=>{g(n.PAUSE)}})}),t.jsx(C,{children:t.jsx(q,{fill:"#F2DFCE",pathFill:"#F2DFCE",className:`cursor-pointer text-white ${e!=null&&e.target?"":"opacity-50"}`,onClick:e!=null&&e.target?w:void 0})})]})}),t.jsx("div",{className:"mb-[2px] flex w-full items-center justify-center gap-2",children:t.jsx("div",{className:"h-[.5rem] w-full overflow-hidden rounded-full bg-[#F2DFCE]",children:t.jsx("div",{className:"h-full bg-[#F6A03C] transition-all duration-100",style:{width:`${y.progress}%`}})})}),t.jsxs("div",{className:"flex justify-between items-center mb-2",children:[t.jsx("code",{className:"w-fit min-w-fit max-w-fit font-iowan text-xs text-[#F2DFCE]",children:b(y.currentTime)}),t.jsx("code",{className:"w-fit min-w-fit max-w-fit font-iowan text-xs text-[#F2DFCE]",children:b(y.duration)})]}),j&&t.jsxs("div",{className:"mb-3 mt-4 flex w-full flex-row-reverse items-center justify-center gap-[140px] px-10 md:mt-0 md:w-auto md:flex-row",children:[t.jsxs("div",{className:"flex-[unset] md:flex-1",children:[t.jsx("div",{className:"relative top-[30px] ml-[-160%] h-[1px] w-[40px] rotate-[90deg] bg-[#F2DFCE]"}),t.jsxs(C,{children:[t.jsx("div",{className:"hidden md:block",children:t.jsx(Q,{type:"mapping",labelStyle:"!text-[14px]",className:"!h-[34px] !w-[151px] !border !border-primary-black !p-[4px] !px-[7px] !pl-[12px] font-iowan md:w-auto",value:e==null?void 0:e.target,label:"Listen to:",noneText:"Select",onChange:r=>P(r.target.value,n.SELECTION),options:Array.from(c.keys()),mapping:Array.from(c.entries()).reduce((r,[d,f])=>(r[d]=f,r),{})})}),t.jsxs("div",{className:"flex flex-col gap-2 items-end md:hidden",children:[t.jsx("h4",{className:"text-base font-semibold whitespace-nowrap font-iowan",children:"Listen to"}),t.jsx("button",{onClick:()=>s(!0),className:"flex gap-2 items-center",children:t.jsx("svg",{width:"22",height:"18",className:"ml-[-30px] h-6 w-6",viewBox:"0 0 22 18",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M0.875 2C0.875 1.70163 0.993526 1.41548 1.2045 1.2045C1.41548 0.993526 1.70163 0.875 2 0.875H18.5C18.7984 0.875 19.0845 0.993526 19.2955 1.2045C19.5065 1.41548 19.625 1.70163 19.625 2C19.625 2.29837 19.5065 2.58452 19.2955 2.7955C19.0845 3.00647 18.7984 3.125 18.5 3.125H2C1.70163 3.125 1.41548 3.00647 1.2045 2.7955C0.993526 2.58452 0.875 2.29837 0.875 2ZM2 9.125H12.875C13.1734 9.125 13.4595 9.00647 13.6705 8.7955C13.8815 8.58452 14 8.29837 14 8C14 7.70163 13.8815 7.41548 13.6705 7.20451C13.4595 6.99353 13.1734 6.875 12.875 6.875H2C1.70163 6.875 1.41548 6.99353 1.2045 7.20451C0.993526 7.41548 0.875 7.70163 0.875 8C0.875 8.29837 0.993526 8.58452 1.2045 8.7955C1.41548 9.00647 1.70163 9.125 2 9.125ZM8.375 12.875H2C1.70163 12.875 1.41548 12.9935 1.2045 13.2045C0.993526 13.4155 0.875 13.7016 0.875 14C0.875 14.2984 0.993526 14.5845 1.2045 14.7955C1.41548 15.0065 1.70163 15.125 2 15.125H8.375C8.67337 15.125 8.95952 15.0065 9.1705 14.7955C9.38147 14.5845 9.5 14.2984 9.5 14C9.5 13.7016 9.38147 13.4155 9.1705 13.2045C8.95952 12.9935 8.67337 12.875 8.375 12.875ZM21.8272 7.94844C21.7406 8.23339 21.5445 8.47236 21.2819 8.61295C21.0193 8.75354 20.7118 8.78429 20.4266 8.69844L18.125 8.01219V14C18.125 14.7164 17.8971 15.4143 17.4741 15.9926C17.0512 16.5708 16.4552 16.9995 15.7724 17.2165C15.0896 17.4336 14.3555 17.4277 13.6763 17.1998C12.9971 16.9718 12.408 16.5337 11.9944 15.9487C11.5807 15.3637 11.3639 14.6623 11.3754 13.946C11.3869 13.2296 11.6261 12.5355 12.0583 11.9641C12.4904 11.3927 13.0933 10.9737 13.7794 10.7677C14.4656 10.5616 15.1995 10.5793 15.875 10.8181V6.5C15.8751 6.32463 15.9161 6.15171 15.9949 5.99502C16.0736 5.83834 16.1879 5.70224 16.3287 5.59759C16.4694 5.49294 16.6326 5.42263 16.8053 5.39229C16.9781 5.36194 17.1555 5.3724 17.3234 5.42281L21.0734 6.54781C21.3591 6.63366 21.5989 6.82943 21.7403 7.09208C21.8816 7.35472 21.9129 7.66275 21.8272 7.94844ZM15.875 14C15.875 13.7775 15.809 13.56 15.6854 13.375C15.5618 13.19 15.3861 13.0458 15.1805 12.9606C14.975 12.8755 14.7488 12.8532 14.5305 12.8966C14.3123 12.94 14.1118 13.0472 13.9545 13.2045C13.7972 13.3618 13.69 13.5623 13.6466 13.7805C13.6032 13.9988 13.6255 14.225 13.7106 14.4305C13.7958 14.6361 13.94 14.8118 14.125 14.9354C14.31 15.059 14.5275 15.125 14.75 15.125C15.0484 15.125 15.3345 15.0065 15.5455 14.7955C15.7565 14.5845 15.875 14.2984 15.875 14Z",fill:"#FCF1E6"})})})]})]})]}),t.jsxs("div",{className:"flex flex-col gap-5 items-center ml-4",children:[t.jsx("span",{className:"text-base font-semibold whitespace-nowrap font-iowan",children:"Auto Play"}),t.jsx("div",{className:"relative h-[.75rem] w-[2rem] rounded-full bg-[#F2DFCE]",children:t.jsx("button",{onClick:W,className:`absolute inset-y-0 m-auto flex h-[1rem] w-[1rem] items-center justify-center rounded-full bg-[#F6A03C] ${e!=null&&e.autoPlay?"right-0":""}`,children:e!=null&&e.autoPlay?t.jsx(_,{className:"!h-[50%] !w-[50%]",pathFill:"#FCF1E6"}):t.jsx(M,{className:"!h-[50%] !w-[50%]",pathFill:"#FCF1E6"})})})]})]})]}),t.jsx(C,{children:t.jsx(le,{clickOutToClose:!0,modalHeader:!1,modalHeaderClassName:"!bg-black text-[#1f1d1a]",isOpen:H,modalCloseClick:()=>s(!1),classes:{modalDialog:"!bg-black h-fit min-h-fit max-h-fit w-full !max-h-[500px] md:!w-fit !max-w-full min-w-full md:!min-w-[595px] md:!max-w-[595px] !gap-0 !m-0 !mt-auto !rounded-t-2xl",modalContent:"!bg-[#FCF1E6] !text-black !z-10 !mt-0 overflow-y-auto max-h-[500px] !pt-0",modal:"h-full items-end",modal:"!p-0"},children:t.jsxs("div",{className:"py-2 pb-6 md:py-6",children:[t.jsx("div",{onClick:()=>s(!1),className:"flex justify-center mt-1 mb-7",children:t.jsx("div",{className:"h-[6px] w-[60px] rounded-full bg-[#F2DFCE]"})}),t.jsx("div",{className:"flex gap-2 justify-start items-center mb-6",children:t.jsx("span",{className:"flex justify-start items-center text-xl font-iowan",children:"Listen to"})}),t.jsx("div",{className:"space-y-4",children:Array.from(c.entries()).map(([r,d])=>t.jsx("button",{onClick:()=>{P(r,n.SELECTION),s(!1)},className:`w-full rounded-sm border-b border-b-[#1f1d1a]/10 p-4 text-left font-iowan text-[#1f1d1a] transition-colors ${(e==null?void 0:e.target)===r?" border-b-2 font-bold text-[#1f1d1a]":"hover:text-[#1f1d1a]"}`,children:d},r))})]})})})]})};export{Ue as default};
