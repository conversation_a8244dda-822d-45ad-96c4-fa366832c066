import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{R as a,i as b,b as f}from"./vendor-4cdf2bd1.js";import{u as w}from"./react-hook-form-9f4fcfa9.js";import{o as j}from"./yup-c41d85d2.js";import{c as y,a as N}from"./yup-342a5df4.js";import{A as v,G as S,M as A,s as E}from"./index-46de5032.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const Q=()=>{var i,m;const l=y({email:N().email().required()}).required();a.useContext(v);const{dispatch:n}=a.useContext(S),[c,s]=a.useState(!1),o=b();f();const{register:p,handleSubmit:d,setError:u,formState:{errors:r}}=w({resolver:j(l)}),x=async g=>{let h=new A;try{s(!0);const t=await h.magicLoginAttempt(g.email,o==null?void 0:o.role);t.error||(s(!1),console.log(t),E(n,"Please check your mail to complete login attempt"))}catch(t){s(!1),console.log("Error",t),u("email",{type:"manual",message:t.message})}};return e.jsxs("div",{className:"mx-auto w-full max-w-xs",children:[e.jsxs("form",{onSubmit:d(x),className:"mb-4 mt-8 rounded bg-[#FFF4EC] px-8 pb-8 pt-6 shadow-md ",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"email",children:"Email"}),e.jsx("input",{type:"email",placeholder:"Email",...p("email"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(i=r.email)!=null&&i.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(m=r.email)==null?void 0:m.message})]}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("button",{type:"submit",className:"focus:shadow-outline rounded bg-blue-500 px-4 py-2 font-bold text-white hover:bg-blue-700 focus:outline-none",children:[c?"Attempting Log In...":"Sign In"," "]})})]}),e.jsxs("p",{className:"text-center text-xs text-gray-500",children:["© ",new Date().getFullYear()," manaknightdigital inc. All rights reserved."]})]})};export{Q as default};
