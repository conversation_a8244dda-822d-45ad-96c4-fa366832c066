import{j as r}from"./@nextui-org/listbox-0f38ca19.js";import{R as s}from"./vendor-4cdf2bd1.js";import{T as f}from"./CompanyUpdates-70f300fe.js";import{L as e}from"./index-46de5032.js";import"./@nextui-org/theme-345a09ed.js";import"./yup-342a5df4.js";import"./react-hook-form-9f4fcfa9.js";import"./yup-c41d85d2.js";import"./@hookform/resolvers-fad2f3d1.js";import"./MkdInput-a0090fba.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./qr-scanner-cf010ec4.js";import"./useCompanyMember-f698e8a0.js";import"./@headlessui/react-cdd9213e.js";import"./index-23a711b5.js";import"./moment-a9aaa855.js";import"./DocumentTextIcon-54b5e200.js";import"./DocumentIcon-22c47322.js";import"./XMarkIcon-cfb26fe7.js";import"./InteractiveButton-060359e0.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";import"./index-abd77600.js";import"./index-a807e4ab.js";import"./index-af54e68d.js";import"./useDate-14dbf4c5.js";import"./lucide-react-0b94883e.js";import"./index-3ff0ef21.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const or=({children:i,view:o,views:t,setView:p,viewsMap:l})=>{const a=s.Children.toArray(i);return r.jsxs("div",{className:"grid !h-full max-h-full min-h-full w-full min-w-full max-w-full grid-rows-[auto_1fr]",children:[r.jsx("div",{className:"mb-5 w-full min-w-full max-w-full overflow-auto",children:r.jsx(e,{children:r.jsx(f,{tabs:t,view:o,setView:p,viewsMap:l})})}),a.map(m=>m.props.view===o?m:null)]})};export{or as default};
