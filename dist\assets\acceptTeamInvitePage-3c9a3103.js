import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{r as o,b as M,i as L}from"./vendor-4cdf2bd1.js";import{M as T,A as F,G,u as O,I as w,n as I,_ as x}from"./index-46de5032.js";import{l as R}from"./AuthAction-52ee0934.js";import{u as q}from"./useLocalStorage-53cfe2d8.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";new T;const me=()=>{const m=o.useRef(null),c=o.useRef(null),{state:B,dispatch:u}=o.useContext(F),{dispatch:h}=o.useContext(G),[f,i]=o.useState(!1),[_,d]=o.useState(""),[p,C]=o.useState(!1),[K,b]=o.useState(!1),[$,A]=o.useState(!1),[r,y]=o.useState(5),[l,v]=o.useState(""),[s,P]=o.useState(null),V=M(),{company_member:j,company:N}=L();O();const{localStorageData:Y,setLocalStorage:a,getLocalStorage:z}=q(["user","token"]),g=e=>{console.log("Attempting to redirect to:",e);try{V(e),console.log("Navigate function called")}catch(n){console.log("Navigate failed, using window.location:",n),window.location.href=e}},S=()=>{y(5),c.current=setInterval(()=>{y(e=>(console.log("Countdown:",e),e<=1?(clearInterval(c.current),l?(console.log("Redirecting to:",l),g(l)):console.log("No redirect path available!"),0):e-1))},1e3)},k=async()=>{i(!1),d("");const e=await I(h,u,{endpoint:`/v3/api/custom/goodbadugly/invite/validate/${j}/${N}`,method:"POST"});if(e.error)i(!0),d(e.message||"Failed to validate invitation");else{console.log("Validation Response:",e),P(e),C(!0);const n=e.is_onboarded?`/${x[e.role]}/dashboard`:`/${x[e.role]}/get-started?company_email=${e.company_email}`;v(n),console.log("Set redirect path:",n),a("company_email",e==null?void 0:e.company_email),a("token",e==null?void 0:e.token),a("user",e==null?void 0:e.user),a("role",e==null?void 0:e.role),R(u,{role:e.role,token:e.token,user_id:e.user}),S()}},E=async()=>{b(!0),i(!1),d("");const e=await I(h,u,{endpoint:`/v3/api/custom/goodbadugly/member/accept/${j}/${N}`,method:"POST"});if(e.error)i(!0),d(e.message||"Failed to accept invitation"),b(!1);else{console.log("Accept Response:",e);const n=s.is_onboarded?`/${x[s.role]}/dashboard`:`/${x[s.role]}/get-started?company_email=${s.company_email}`;v(n),console.log("Set redirect path:",n),a("company_email",s==null?void 0:s.company_email),a("token",s==null?void 0:s.token),a("user",s==null?void 0:s.user),a("role",s==null?void 0:s.role),R(u,{role:s.role,token:s.token,user_id:s.user}),A(!0),S()}};return o.useEffect(()=>(k(),()=>{m!=null&&m.current&&clearInterval(m.current),c!=null&&c.current&&clearInterval(c.current)}),[]),console.log("error >>",f,"isValidated >>",p,"redirectPath >>",l,"countdown >>",r),t.jsx("div",{className:"flex h-svh max-h-svh min-h-svh w-full items-center justify-center",children:f?t.jsxs("div",{className:"flex flex-col items-center space-y-4",children:[t.jsx("div",{className:"text-center",children:t.jsx("p",{className:"mb-4 text-red-600",children:_})}),t.jsx(w,{type:"button",className:"my-4 flex h-[2.75rem] w-fit items-center justify-center rounded-[.375rem] bg-[#1f1d1a] px-5 py-2 tracking-wide text-white outline-none focus:outline-none",color:"black",onClick:()=>{i(!1),d(""),p?E():k()},children:p?"Retry Accept":"Retry Validation"})]}):$?t.jsx("div",{className:"flex flex-col items-center space-y-4",children:t.jsxs("div",{className:"text-center",children:[t.jsx("p",{className:"mb-2 text-green-600",children:"Invitation accepted successfully!"}),t.jsxs("p",{className:"text-sm text-gray-600",children:["Redirecting to app in ",r," second",r!==1?"s":"","..."]}),r===0&&t.jsxs("div",{className:"mt-2",children:[t.jsx("p",{className:"text-xs text-blue-600",children:"Redirecting now..."}),t.jsx("button",{className:"mt-1 text-xs text-blue-500 underline",onClick:()=>g(l),children:"Click here if not redirected automatically"})]})]})}):p?t.jsx("div",{className:"flex flex-col items-center space-y-4",children:t.jsxs("div",{className:"text-center",children:[t.jsx("p",{className:"mb-4 text-green-600",children:"Invitation validated successfully!"}),t.jsxs("p",{className:"mb-4 text-sm text-gray-600",children:["You have been automatically logged in and will be redirected in"," ",r," second",r!==1?"s":"","..."]}),r===0&&t.jsxs("div",{className:"mt-2",children:[t.jsx("p",{className:"text-xs text-blue-600",children:"Redirecting now..."}),t.jsx("button",{className:"mt-1 text-xs text-blue-500 underline",onClick:()=>g(l),children:"Click here if not redirected automatically"})]})]})}):t.jsx(w,{type:"button",className:"!broder-red-600 !border !bg-transparent",loading:!0,disabled:!0,color:"black",children:t.jsx("span",{children:"Validating Invitation..."})})})};export{me as default};
