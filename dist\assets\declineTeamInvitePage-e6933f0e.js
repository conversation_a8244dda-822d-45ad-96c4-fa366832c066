import{j as r}from"./@nextui-org/listbox-0f38ca19.js";import{i as a,b as p,r as e}from"./vendor-4cdf2bd1.js";import{M as n}from"./index-46de5032.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const c=new n,M=()=>{const{company_member:t,company:o}=a(),i=p(),[m,s]=e.useState(!1);return e.useEffect(()=>{(async()=>{(await c.callRawAPI(`/v3/api/custom/goodbadugly/member/decline/${t}/${o}`,{},"POST")).error?s(!0):i("/member/sign-up")})()},[]),r.jsx("div",{children:m&&r.jsx("button",{children:"Reload"})})};export{M as default};
