import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{G as m,A as d}from"./index-46de5032.js";import{r as a,j as o,O as s}from"./vendor-4cdf2bd1.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";function C(){var i;const{dispatch:n}=a.useContext(m),{state:r}=a.useContext(d);return a.useEffect(()=>{n({type:"SETPATH",payload:{path:"account"}})},[]),t.jsxs("div",{children:[t.jsx("h3",{className:"mt-6 px-4 text-3xl font-normal",children:(i=r.company)==null?void 0:i.name}),t.jsxs("ul",{className:"custom-overflow mt-8 flex w-full overflow-x-auto border-y border-[#0003] px-8 py-1",children:[t.jsx("li",{children:t.jsx(o,{to:"/fundmanager/account",end:!0,className:({isActive:e})=>`font block rounded-md px-6 py-2 font-medium text-[#1f1d1a] ${e?"bg-[#1f1d1a] text-white":"text-[#1f1d1a]"}`,children:"Profile"})}),t.jsx("li",{children:t.jsx(o,{to:"/fundmanager/account/email",className:({isActive:e})=>`font block rounded-md px-6 py-2 font-medium text-[#1f1d1a] ${e?"bg-[#1f1d1a] text-white":"text-[#1f1d1a]"}`,children:"Email"})}),t.jsx("li",{children:t.jsx(o,{to:"/fundmanager/account/security",className:({isActive:e})=>`font block rounded-md px-6 py-2 font-medium text-[#1f1d1a] ${e?"bg-[#1f1d1a] text-white":"text-[#1f1d1a]"}`,children:"Password/Security"})}),t.jsx("li",{children:t.jsx(o,{to:"/fundmanager/account/notifications",className:({isActive:e})=>`font block rounded-md px-6 py-2 font-medium text-[#1f1d1a] ${e?"bg-[#1f1d1a] text-white":"text-[#1f1d1a]"}`,children:"Notifications"})})]}),r.profile.email?t.jsx(s,{}):null]})}export{C as default};
