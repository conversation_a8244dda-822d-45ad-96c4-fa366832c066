import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{o as H}from"./yup-c41d85d2.js";import{A as T,G as P,I as E,M as S,t as k,s as m,ay as $,az as J}from"./index-46de5032.js";import{u as Q}from"./useCompanyMembers-4792264d.js";import{r as s}from"./vendor-4cdf2bd1.js";import{u as Z}from"./react-hook-form-9f4fcfa9.js";import{c as ee,a as ae}from"./yup-342a5df4.js";import{T as te}from"./TrashIcon-e6ce5aef.js";import{X as L}from"./XMarkIcon-cfb26fe7.js";import{t as j,S as y}from"./@headlessui/react-cdd9213e.js";import{C as se}from"./ClockIcon-a30de2d8.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";function oe({member:x,afterRemove:p}){const[i,o]=s.useState(!1),{dispatch:r}=s.useContext(T),{dispatch:w}=s.useContext(P),[d,h]=s.useState(!1);async function c(){h(!0);try{await new S().callRawAPI(`/v4/api/records/startup_member/${x.id}`,{},"DELETE"),o(!1),p()}catch(n){k(r,n.message),n.message!=="TOKEN_EXPIRED"&&m(w,n.message,5e3,"error")}h(!1)}return e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:()=>o(!0),children:e.jsx(te,{className:"h-6 w-6",strokeWidth:2})}),e.jsx(j,{appear:!0,show:i,as:s.Fragment,children:e.jsxs(y,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>o(!1),children:[e.jsx(j.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(j.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(y.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(y.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Are you sure"}),e.jsx("button",{onClick:()=>o(!1),type:"button",children:e.jsx(L,{className:"h-6 w-6"})})]}),e.jsx("p",{className:"mt-2",children:"Are you sure you want to remove this member from the company"}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>o(!1),children:"Cancel"}),e.jsx(E,{loading:d,disabled:d,onClick:c,className:"rounded-lg bg-[#1f1d1a] py-2 text-center font-iowan font-semibold text-white transition-colors duration-100 disabled:bg-opacity-60",children:"Yes, I'm sure"})]})]})})})})]})})]})}function ne({member:x,refetch:p}){const[i,o]=s.useState(!1),{dispatch:r,state:w}=s.useContext(T),{dispatch:d}=s.useContext(P),[h,c]=s.useState(!1);async function n(){c(!0);try{const v=await new S().callRawAPI("/v3/api/custom/goodbadugly/users/make-owner",{company_id:w.company.id,email:x.email},"POST");console.log(v),o(!1),p()}catch(f){k(r,f.message),f.message!=="TOKEN_EXPIRED"&&m(d,f.message,5e3,"error")}c(!1)}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"mt-2 w-fit cursor-pointer rounded bg-green-600 px-1 text-[10px] text-white",onClick:()=>o(!0),children:"Make Account Owner"}),e.jsx(j,{appear:!0,show:i,as:s.Fragment,children:e.jsxs(y,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>o(!1),children:[e.jsx(j.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(j.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(y.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(y.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Are you sure"}),e.jsx("button",{onClick:()=>o(!1),type:"button",children:e.jsx(L,{className:"h-6 w-6"})})]}),e.jsx("p",{className:"mt-2",children:"Are you sure you want to make this account an owner?"}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>o(!1),children:"Cancel"}),e.jsx(E,{loading:h,disabled:h,onClick:n,className:"disabled:bg-disabledblack rounded-lg bg-black py-2 text-center font-semibold text-white transition-colors duration-100",children:"Yes, I'm sure"})]})]})})})})]})})]})}const z=new S;function Me(){var M,A,F,D,O;const{dispatch:x,state:p}=s.useContext(T),{dispatch:i}=s.useContext(P),{members:o,refetch:r,setMembers:w}=Q(p.company.id),[d,h]=s.useState(""),[c,n]=s.useState(!1),f=ee({email:ae().required("This field is required")}),[v,G]=useSearchParams(),[I,X]=s.useState(""),{register:B,handleSubmit:K,setError:le,reset:R,formState:{errors:C,isSubmitting:ie}}=Z({resolver:H(f),defaultValues:{email:""}});function Y(a){a.preventDefault(),console.log(v);const l=I.trim().toLowerCase(),t=o==null?void 0:o.filter(g=>{var N,b;return(b=(N=g==null?void 0:g.user)==null?void 0:N.email)==null?void 0:b.toLowerCase().includes(l)});w(t)}async function q(a){var l;console.log("manager"),console.log(),n(!0);try{await z.callRawAPI("/v3/api/custom/goodbadugly/users/make-manager",{company_id:p.company.id,email:a.email},"POST"),n(!1),R(),m(i,"Invite sent"),r()}catch(t){n(!1),k(x,t.message),console.log(t),m(i,(t==null?void 0:t.message)||((l=t==null?void 0:t.mailResult)==null?void 0:l.message)||"Something went wrong",5e3,"error")}}async function U(a){var l;n(!0),console.log("other");try{await z.callRawAPI(`/v3/api/custom/goodbadugly/member/${p.company.id}/invite-to-team`,{email:a.email,role:d},"POST"),n(!1),R(),m(i,"Invite sent"),r()}catch(t){n(!1),console.log(t),k(x,t.message),m(i,(t==null?void 0:t.message)||((l=t==null?void 0:t.mailResult)==null?void 0:l.message)||"Something went wrong",5e3,"error")}}const _=a=>{d==="manager"?q(a):U(a)};async function W(a,l){try{await new S().callRawAPI(`/v4/api/records/startup_member/${a}`,{member_role:l},"PUT"),r(),m(i,"Saved")}catch(t){k(x,t.message),t.message!=="TOKEN_EXPIRED"&&m(i,t.message,5e3,"error")}}return e.jsxs("div",{className:"max-w-5xl overflow-x-auto px-8 py-8 xl:max-w-[1100px]",children:[e.jsx("h2",{className:"text-3xl",children:"Manage team"}),e.jsxs("form",{className:"mt-10 flex flex-row items-center",onSubmit:a=>Y(a),children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block  text-lg font-bold capitalize capitalize text-[#1f1d1a]",children:"Search"}),e.jsx("div",{children:e.jsx("input",{type:"text",placeholder:"Team members email",onChange:a=>X(a.target.value),className:`focus:shadow-outline w-[100px] w-[250px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 leading-tight text-[#1d1f1a] shadow   focus:outline-none sm:w-[180px] ${(M=C.group_name)!=null&&M.message?"border-red-500":""}`})}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(A=C.group_name)==null?void 0:A.message})]}),e.jsxs("div",{className:"ml-3 flex gap-4",children:[e.jsx("button",{type:"submit",disabled:c||I==="",className:"font-iowan-regularrounded-md bg-primary-black/80 px-4 py-1 font-semibold text-white hover:bg-primary-black",children:"Search"}),e.jsx("button",{type:"button",disabled:c,className:"rounded-md px-4 py-1 font-semibold text-[#1f1d1a]",onClick:()=>{v.delete("search"),r(),G(v)},children:"Clear"})]})]}),e.jsxs("form",{onSubmit:K(_),children:[e.jsxs("div",{className:"mt-8 flex gap-4",children:[e.jsx("input",{type:"text",autoComplete:"off",...B("email"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(F=C.email)!=null&&F.message?"border-red-500":""}`,placeholder:"Add email address"}),e.jsxs("select",{value:d,onChange:a=>h(a.target.value),className:"focus:shadow-outline w-full max-w-[200px] appearance-none rounded border py-2 pl-6 pr-8 leading-tight text-[#1f1d1a] shadow focus:outline-none ",children:[e.jsx("option",{value:"",children:"Select Role"}),e.jsx("option",{value:"investor",children:"Fund Manager"}),e.jsx("option",{value:"collaborator",children:"Collaborator"}),e.jsx("option",{value:"stakeholder",children:"Stakeholder"}),e.jsx("option",{value:"manager",children:"Manager/Founder"})]}),e.jsx(E,{loading:c,disabled:c,type:"submit",className:"disabled:bg-disabledblack whitespace-nowrap rounded-lg bg-primary-black/90 px-5 py-2 text-center text-lg font-semibold text-white transition-colors duration-100",children:"Send Invite"})]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(D=C.email)==null?void 0:D.message})]}),e.jsx("div",{className:"mt-12 w-full  max-w-[1100px] space-y-8",children:(O=o==null?void 0:o.toReversed())==null?void 0:O.map(a=>{var l,t,g,N,b;return e.jsxs("div",{className:"flex w-full items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("img",{src:((l=a==null?void 0:a.user)==null?void 0:l.photo)||"/default.png",alt:"",className:"h-14 w-14 rounded-[50%] object-cover"}),e.jsxs("div",{className:"",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("p",{className:"text-[16px] font-medium md:text-xl",children:[(a==null?void 0:a.member_status)==$.PENDING?(t=a==null?void 0:a.user)==null?void 0:t.email:`${(g=a==null?void 0:a.user)==null?void 0:g.first_name} ${(N=a==null?void 0:a.user)==null?void 0:N.last_name}`," "]})," "]}),a.member_status==$.PENDING?e.jsxs("div",{className:"mt-1 flex items-center gap-3 font-medium text-gray-500",children:[e.jsx(se,{className:"h-5 w-5",strokeWidth:2})," ",e.jsx("p",{children:"Invitation Pending"})," ",e.jsx("button",{className:"text-sm text-primary-black",onClick:()=>{var u;return _({email:(u=a==null?void 0:a.user)==null?void 0:u.email})},children:"Resend invite"})]}):e.jsxs("p",{className:"mt-1 font-medium text-gray-500",children:[(b=a==null?void 0:a.user)==null?void 0:b.email,(a==null?void 0:a.member_role)==="manager"&&e.jsx(ne,{member:a,refetch:r})]})]})]}),e.jsxs("div",{className:"flex items-center gap-6 text-xl font-medium",children:[Object.entries(J).map(([u,V])=>{if(u!="member")return e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"radio",checked:(a==null?void 0:a.member_role)==u,value:u,onChange:()=>W(a.id,u)}),V]},u)}),e.jsx(oe,{member:a,afterRemove:r})]})]},a==null?void 0:a.id)})})]})}export{Me as default};
