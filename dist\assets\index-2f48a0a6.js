import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as t,h as $,b as B}from"./vendor-4cdf2bd1.js";import{A as j,G as y,M as C,t as k,s as S,I as L,T as K}from"./index-46de5032.js";import{u as O}from"./react-hook-form-9f4fcfa9.js";import{o as X}from"./yup-c41d85d2.js";import{c as M,a as V}from"./yup-342a5df4.js";import{A as G}from"./AddButton-51d1b2cd.js";import{E as W}from"./ExportButton-eb4cf1f9.js";import{InteractiveButton2 as Y}from"./InteractiveButton-060359e0.js";import{X as I}from"./XMarkIcon-cfb26fe7.js";import{t as u,S as f}from"./@headlessui/react-cdd9213e.js";import{R as q}from"./ResponsiveTableWrapperCompanies-80bd517a.js";import F from"./Loader-24da96b3.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@hookform/resolvers-fad2f3d1.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";import"./index.esm-7add6cfb.js";import"./react-icons-36ae72b7.js";import"./index-23a711b5.js";function H(d){const[x,m]=t.useState(!1),[s,a]=t.useState([]),[l]=$(),{dispatch:p}=t.useContext(j),{dispatch:n}=t.useContext(y),c=l.get("limit")||30,o=l.get("page"),i=l.get("name");async function b(){m(!0);try{const w=await new C().callRawAPI(`/v4/api/records/companies?filter=user_id,eq,${d}${i?`&filter=name,cs,${i}`:""}&page=${o??1},${c??10}&order=id,asc`);a(w.list)}catch(h){k(p,h.message),h.message!=="TOKEN_EXPIRED"&&S(n,h.message,5e3,"error")}m(!1)}return t.useEffect(()=>{b()},[c,o,i]),{loading:x,companies:s,refetch:b}}function J({company:d,afterDelete:x}){const[m,s]=t.useState(!1),{dispatch:a}=t.useContext(j),{dispatch:l}=t.useContext(y),[p,n]=t.useState(!1);async function c(){n(!0);try{await new C().callRawAPI(`/v4/api/records/companies/${d.id}`,{},"DELETE"),s(!1),x()}catch(o){k(a,o.message),o.message!=="TOKEN_EXPIRED"&&S(l,o.message,5e3,"error")}n(!1)}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"cursor-pointer px-1 text-sm text-red-500  underline hover:underline",onClick:()=>s(!0),children:e.jsx("span",{children:"Delete"})}),e.jsx(u,{appear:!0,show:m,as:t.Fragment,children:e.jsxs(f,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>s(!1),children:[e.jsx(u.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(u.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(f.Panel,{className:"w-full max-w-xl transform overflow-hidden rounded-md bg-brown-main-bg bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(f.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Delete Company"}),e.jsx("button",{onClick:()=>s(!1),type:"button",children:e.jsx(I,{className:"h-6 w-6"})})]}),e.jsxs("p",{className:"mt-2",children:["You are about to delete company ",d.id,", note that this action is irreversible ",e.jsx("br",{}),e.jsx("br",{}),e.jsxs("span",{className:"font-semibold",children:[" ","This action cannot be undone."]})]}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border py-2 text-center font-medium",type:"button",onClick:()=>s(!1),children:"Cancel"}),e.jsx(Y,{loading:p,disabled:p,onClick:c,className:"rounded-lg bg-[#1f1d1a] py-2 text-center font-iowan font-semibold text-white transition-colors duration-100 disabled:bg-opacity-60",children:"Yes, delete"})]})]})})})})]})})]})}function Q({company:d,afterSuccess:x}){const[m,s]=t.useState(!1),{dispatch:a,state:l}=t.useContext(j),{dispatch:p}=t.useContext(y),[n,c]=t.useState(!1);async function o(){c(!0);try{const i=new K;await i.updateWhere("companies",{user_id:l.user},{default_company:0}),await i.update("companies",d.id,{default_company:1}),s(!1),x()}catch(i){k(a,i.message),i.message!=="TOKEN_EXPIRED"&&S(p,i.message,5e3,"error")}c(!1)}return e.jsxs(e.Fragment,{children:[d.default_company==1?e.jsx("span",{children:"(Default)"}):e.jsx("button",{className:"cursor-pointer px-1 text-xs font-medium text-primary-black hover:underline",onClick:()=>s(!0),children:e.jsx("span",{children:"Set as default"})}),e.jsx(u,{appear:!0,show:m,as:t.Fragment,children:e.jsxs(f,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>s(!1),children:[e.jsx(u.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(u.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(f.Panel,{className:"w-full max-w-xl transform overflow-hidden rounded-md bg-brown-main-bg bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(f.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Set default"}),e.jsx("button",{onClick:()=>s(!1),type:"button",children:e.jsx(I,{className:"h-6 w-6"})})]}),e.jsx("p",{className:"mt-2",children:"Are you sure you want to set this company as default"}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border py-2 text-center font-medium",type:"button",onClick:()=>s(!1),children:"Cancel"}),e.jsx(L,{loading:n,disabled:n,onClick:o,className:"disabled:bg-disabledblack rounded-lg bg-primary-black py-2 text-center font-semibold text-white transition-colors duration-100",children:"Save"})]})]})})})})]})})]})}let P=new C;const A=[{header:"Logo",accessor:"logo"},{header:"Name",accessor:"name"},{header:"Action",accessor:""}],Fe=()=>{var T,D;const{dispatch:d,state:x}=t.useContext(j),{dispatch:m}=t.useContext(y),s=B(),[a,l]=$(),{companies:p,loading:n,refetch:c}=H(x.user),o=M({name:V()}),{register:i,handleSubmit:b,setError:h,reset:w,formState:{errors:E}}=O({resolver:X(o),defaultValues:async()=>({name:a.get("name")??""})});async function R(){P.setTable("companies"),await P.exportCSV()}t.useEffect(()=>{m({type:"SETPATH",payload:{path:"companies"}})},[]);const _=()=>{s("/member/add-companies")};function z(r){a.set("name",r.name),a.set("page",1),l(a)}return e.jsx(e.Fragment,{children:n?e.jsx(F,{}):e.jsx(e.Fragment,{children:e.jsxs("div",{className:"rounded bg-brown-main-bg",children:[e.jsx("div",{className:"flex justify-between mb-3 w-full item-center",children:e.jsx("h4",{className:"text-left text-[16px] font-[600] sm:text-[20px]",children:"Search"})}),e.jsxs("form",{onSubmit:b(z),className:"flex flex-col gap-4 items-end",children:[e.jsx("div",{className:"flex flex-wrap gap-4",children:e.jsxs("div",{className:"w-full sm:w-auto",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize text-[#1f1d1a]",children:"Name"}),e.jsx("input",{type:"text",...i("name"),className:`focus:shadow-outline w-full appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal capitalize leading-tight text-[#1d1f1a] shadow focus:outline-none   sm:w-[150px] sm:w-[180px] ${(T=E.name)!=null&&T.message?"border-red-500":""}`}),e.jsx("p",{className:"italic text-red-500 text-field-error",children:(D=E.name)==null?void 0:D.message})]})}),e.jsxs("div",{className:"flex gap-4 items-end",children:[e.jsx("button",{type:"submit",disabled:n,className:"px-4 py-1 font-semibold text-white font-iowan-regularrounded-md bg-primary-black/80 hover:bg-primary-black",children:"Search"}),e.jsx("button",{type:"button",onClick:()=>{w({name:"",members:""}),a.set("name",""),a.set("page",1),l(a)},disabled:n,className:"rounded-md px-4 py-1 font-semibold text-[#1f1d1a]",children:"Clear"})]})]}),e.jsxs("div",{className:"overflow-x-auto p-5 px-0 mt-10 rounded bg-brown-main-bg md:mt-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-3 w-full text-center",children:[e.jsx("h4",{className:"text-left text-[16px] font-[600] sm:text-[20px]",children:"Companies"}),e.jsxs("div",{className:"flex gap-3 items-center",children:[e.jsx(W,{onClick:R,className:"py-2 font-medium sm:px-2"}),e.jsx(G,{onClick:_,className:"py-2 font-medium sm:px-2"})]})]}),e.jsx("div",{className:`${n?"":"custom-overflow overflow-x-auto"}`,children:n?e.jsx("div",{className:"flex justify-center items-center py-5 max-w-full max-h-fit min-h-fit min-w-fit",children:e.jsx(F,{size:50})}):e.jsx(e.Fragment,{children:e.jsx(q,{children:e.jsxs("table",{className:"min-w-full divide-y divide-[#1f1d1a]/10",children:[e.jsx("thead",{children:e.jsx("tr",{children:A.map((r,N)=>e.jsx("th",{scope:"col",className:"font  whitespace-nowrap border-b-[#1f1d1a]/10  px-4 text-left font-[700] md:border-0 md:border-b-[3px] md:border-dashed md:px-6 md:py-3",children:r.header},N))})}),e.jsx("tbody",{className:"font-iowan-regulardivide-y divide-[#1f1d1a]/10",children:p.map((r,N)=>e.jsx("tr",{className:"  md:h-[60px]",children:A.map((g,v)=>g.accessor===""?e.jsxs("td",{className:"flex gap-2 justify-center items-center px-6 py-4 whitespace-nowrap",children:[e.jsx("button",{className:"cursor-pointer text-xs font-medium text-[#292829fd] hover:underline",onClick:()=>{s("/member/edit-companies/"+r.id,{state:r})},children:e.jsx("span",{children:"Edit"})}),e.jsx("button",{className:"cursor-pointer px-1 text-sm font-medium text-[#1f1d1a] underline underline-offset-2",onClick:()=>{s("/member/view-companies/"+r.id,{state:r})},children:e.jsx("span",{children:"View"})}),e.jsx(J,{company:r,afterDelete:c}),e.jsx(Q,{company:r,afterSuccess:c})]},v):g.accessor==="logo"?e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("img",{src:r[g.accessor],className:"h-[50px] w-[80px] rounded",alt:""})},v):e.jsx("td",{className:"px-3 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:r[g.accessor]},v))},N))})]})})})})]})]})})})};export{Fe as default};
