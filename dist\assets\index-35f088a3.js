import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as s,b as q,h as J}from"./vendor-4cdf2bd1.js";import{A,G as I,M,t as $,s as L,a5 as Q}from"./index-46de5032.js";import{u as Z}from"./react-hook-form-9f4fcfa9.js";import{o as ee}from"./yup-c41d85d2.js";import{c as te,a as b}from"./yup-342a5df4.js";import{A as ae}from"./AddButton-51d1b2cd.js";import{E as se}from"./ExportButton-eb4cf1f9.js";import{u as re}from"./useCompanyMembers-4792264d.js";import{InteractiveButton2 as le}from"./InteractiveButton-060359e0.js";import{X as ne}from"./XMarkIcon-cfb26fe7.js";import{t as v,S as y}from"./@headlessui/react-cdd9213e.js";import{L as ie}from"./index-6edcbb0d.js";import{R as oe}from"./tableWrapper-ca490fb1.js";import{P as me}from"./index-3283c9b7.js";import{C as de}from"./ClipboardDocumentIcon-f03b0627.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@hookform/resolvers-fad2f3d1.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";import"./index.esm-7add6cfb.js";import"./react-icons-36ae72b7.js";function ce({user:c,afterDelete:g}){const[p,n]=s.useState(!1),{dispatch:a}=s.useContext(A),{dispatch:d}=s.useContext(I),[l,i]=s.useState(!1);async function j(){i(!0);try{const r=new M;await r.callRawAPI(`/v4/api/records/profile/${c.id}`,{},"DELETE"),await r.callRawAPI(`/v4/api/records/user/${c.user_id}`,{},"DELETE"),n(!1),g()}catch(r){$(a,r.message),r.message!=="TOKEN_EXPIRED"&&L(d,r.message,5e3,"error")}i(!1)}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"cursor-pointer px-1 text-sm font-medium text-red-500 underline hover:underline",onClick:()=>n(!0),children:e.jsx("span",{children:"Delete"})}),e.jsx(v,{appear:!0,show:p,as:s.Fragment,children:e.jsxs(y,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>n(!1),children:[e.jsx(v.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(v.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(y.Panel,{className:"w-full max-w-xl transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(y.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Delete user"}),e.jsx("button",{onClick:()=>n(!1),type:"button",children:e.jsx(ne,{className:"h-6 w-6"})})]}),e.jsxs("p",{className:"mt-4",children:["You are about to delete user ",c.user_id,", note that this action is irreversible"]}),e.jsxs("span",{className:"mt-4 font-semibold",children:[" ","This action cannot be undone."]}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border py-2 text-center font-medium",type:"button",onClick:()=>n(!1),children:"Cancel"}),e.jsx(le,{loading:l,disabled:l,onClick:j,className:"rounded-lg bg-[#1f1d1a] py-2 text-center font-iowan font-semibold text-white transition-colors duration-100 disabled:bg-opacity-60",children:"Yes, delete"})]})]})})})})]})})]})}const pe=[{header:"First name",accessor:"first_name"},{header:"Last name",accessor:"last_name"},{header:"Role",accessor:"role"},{header:"Email",accessor:"email"},{header:"Status",accessor:"member_status"},{header:"Action",accessor:""}],xe=[{label:"Manager/Founder",value:"member"},{label:"Collaborator",value:"collaborator"},{label:"Fund manager",value:"investor"},{label:"Stakeholder",value:"stakeholder"}],He=()=>{var _,S,C,E,P,k,T,z,D;const{dispatch:c,state:g}=s.useContext(A),{dispatch:p}=s.useContext(I),n=q(),[a,d]=J(),{members:l,loading:i,refetch:j,totalCount:r}=re(g.company.id),[o,w]=s.useState(parseInt(a.get("page")||"1")),[N,B]=s.useState(parseInt(a.get("limit")||"30")),O=te({first_name:b(),last_name:b(),email:b(),role:b()}),{register:x,handleSubmit:U,setError:ue,reset:X,formState:{errors:m}}=Z({resolver:ee(O),defaultValues:async()=>{const t=a.get("first_name")??"",u=a.get("last_name")??"",f=a.get("email")??"",h=a.get("role")??"";return{first_name:t,last_name:u,email:f,role:h}}});async function G(){try{const t=new M;t.setTable("user"),await t.exportCSV()}catch(t){$(c,t.message),t.message!=="TOKEN_EXPIRED"&&L(p,t.message,5e3,"error")}}s.useEffect(()=>{p({type:"SETPATH",payload:{path:"users"}})},[]);const K=()=>{n("/member/add-user")};function Y(t){a.set("first_name",t.first_name),a.set("last_name",t.last_name),a.set("email",t.email),a.set("role",t.role),a.set("page",1),d(a)}const V=t=>{B(t),a.set("limit",t.toString()),a.set("page","1"),d(a)},W=()=>{o>1&&(w(o-1),a.set("page",(o-1).toString()),d(a))},H=()=>{w(o+1),a.set("page",(o+1).toString()),d(a)};return e.jsx("div",{className:"px-5 pt-8 md:px-8",children:i?e.jsx(ie,{}):e.jsx(e.Fragment,{children:e.jsxs("div",{className:"rounded bg-brown-main-bg",children:[e.jsxs("div",{className:"item-center mb-3 flex w-full justify-between",children:[e.jsx("h4",{className:"text-[16px] font-semibold md:text-xl",children:"Search"}),e.jsx("div",{className:"flex"})]}),e.jsxs("form",{onSubmit:U(Y),className:"flex flex-col gap-4",children:[e.jsxs("div",{className:"flex flex-row flex-wrap gap-4",children:[e.jsxs("div",{className:"w-full sm:w-auto",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"First name"}),e.jsx("input",{type:"text",...x("first_name"),className:`focus:shadow-outline w-full appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm text-sm text-sm font-normal font-normal font-normal capitalize leading-tight text-[#1d1f1a] shadow focus:outline-none    sm:w-[180px] ${(_=m.first_name)!=null&&_.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(S=m.first_name)==null?void 0:S.message})]}),e.jsxs("div",{className:"w-full sm:w-auto",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Last name"}),e.jsx("input",{type:"text",...x("last_name"),className:`focus:shadow-outline w-full appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm text-sm text-sm font-normal font-normal font-normal capitalize leading-tight text-[#1d1f1a] shadow focus:outline-none   sm:w-[150px] sm:w-[180px] ${(C=m.last_name)!=null&&C.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(E=m.last_name)==null?void 0:E.message})]}),e.jsxs("div",{className:"w-full sm:w-auto",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Email"}),e.jsx("input",{type:"text",...x("email"),className:`focus:shadow-outline w-full appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm text-sm text-sm font-normal font-normal font-normal capitalize leading-tight text-[#1d1f1a] shadow focus:outline-none   sm:w-[150px] sm:w-[180px] ${(P=m.email)!=null&&P.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(k=m.email)==null?void 0:k.message})]}),e.jsxs("div",{className:"w-full sm:w-auto",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Role"}),e.jsxs("select",{className:`focus:shadow-outline w-full appearance-none rounded border border border-[#1f1d1a] bg-transparent py-2 pl-6 pr-8 font-Inter text-sm font-normal leading-tight text-[#1d1f1a] shadow shadow-none focus:outline-none sm:w-[180px] ${(T=m.role)!=null&&T.message?"border-red-500":""}`,...x("role"),children:[e.jsx("option",{value:"",children:"-Select Role-"}),xe.map(t=>e.jsx("option",{value:t.value,children:t.label},t.value))]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(z=m.role)==null?void 0:z.message})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{type:"submit",disabled:i,className:"font-iowan-regularrounded-md bg-primary-black/80 px-4 py-1 font-semibold text-white hover:bg-primary-black",children:"Search"}),e.jsx("button",{type:"button",onClick:()=>{X({first_name:"",last_name:"",role:"",email:""}),a.set("first_name",""),a.set("last_name",""),a.set("email",""),a.set("role",""),a.set("page",1),d(a)},disabled:i,className:"rounded-md px-4 py-1 font-semibold text-[#1f1d1a]",children:"Clear"})]})]}),e.jsxs("div",{className:"mt-10 overflow-x-auto rounded bg-brown-main-bg p-5 px-0 md:mt-8",children:[e.jsxs("div",{className:"mb-3 flex w-full items-center justify-between text-center",children:[e.jsx("h4",{className:"text-left text-[16px] font-[600] sm:text-[20px]",children:"User management"}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(se,{onClick:G,className:"py-2 font-medium sm:px-2"}),e.jsx(ae,{onClick:K,className:"py-2 font-medium sm:px-2"})]})]}),e.jsx("div",{className:`${i?"":"custom-overflow overflow-x-auto"}`,children:e.jsx(e.Fragment,{children:e.jsx(oe,{children:e.jsxs("table",{className:"min-w-full divide-y divide-[#1f1d1a]/10",children:[e.jsx("thead",{children:e.jsx("tr",{children:pe.map((t,u)=>e.jsx("th",{scope:"col",className:"font  whitespace-nowrap border-b-[#1f1d1a]/10  px-4 text-left font-[700] md:border-0 md:border-b-[3px] md:border-dashed md:px-6 md:py-3",children:t.header},u))})}),e.jsx("tbody",{className:"font-iowan-regulardivide-y divide-[#1f1d1a]/10",children:(D=l==null?void 0:l.toReversed())==null?void 0:D.map((t,u)=>{var f,h,F,R;return e.jsxs("tr",{className:"md:h-[60px]",children:[e.jsx("td",{className:"whitespace-nowrap px-3 md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:(f=t==null?void 0:t.user)==null?void 0:f.first_name}),e.jsx("td",{className:"whitespace-nowrap px-3 md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:(h=t==null?void 0:t.user)==null?void 0:h.last_name}),e.jsx("td",{className:"whitespace-nowrap px-3 md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:(F=t==null?void 0:t.user)==null?void 0:F.role}),e.jsx("td",{className:"whitespace-nowrap px-3 md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:(R=t==null?void 0:t.user)==null?void 0:R.email}),e.jsx("td",{className:"whitespace-nowrap px-3 md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:Q[t.member_status]}),e.jsx("td",{className:"flex items-center justify-start gap-2 whitespace-nowrap px-6 py-4",children:e.jsxs("div",{className:"flex h-auto items-center gap-2 sm:h-[60px] sm:justify-center",children:[e.jsx("button",{className:"font-iowan-regularcursor-pointer text-sm underline hover:underline",onClick:()=>{n(`/member/edit-user/${t==null?void 0:t.id}`)},children:"Edit"}),e.jsx(ce,{afterDelete:j,user:t})]})})]},t==null?void 0:t.id)})})]})})})}),(l==null?void 0:l.length)==0?e.jsxs("div",{className:"mb-[20px] mt-24 flex flex-col items-center",children:[e.jsx(de,{className:"h-8 w-8 text-gray-700",strokeWidth:2}),e.jsx("p",{className:"mt-4 text-center text-base font-medium",children:"No users added yet"})]}):null,e.jsx(me,{currentPage:o,pageCount:Math.ceil(r/N),pageSize:N,canPreviousPage:o>1,canNextPage:o<Math.ceil(r/N),updatePageSize:V,previousPage:W,nextPage:H,dataLoading:i,totalCount:r})]})]})})})};export{He as default};
