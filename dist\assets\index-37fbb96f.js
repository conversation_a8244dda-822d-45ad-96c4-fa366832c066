import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{u as B,r as i,b as G,h as O}from"./vendor-4cdf2bd1.js";import{u as W}from"./react-hook-form-9f4fcfa9.js";import{A as V,G as H,a4 as Q,s as J}from"./index-46de5032.js";import{o as K}from"./yup-c41d85d2.js";import{c as X,a as m}from"./yup-342a5df4.js";import{u as Z,A as v}from"./AcceptUpdateButton-9891e041.js";import{h as c}from"./moment-a9aaa855.js";import w from"./Loader-24da96b3.js";import"./react-scroll-9384d626.js";import{R as ee}from"./tableWrapper-ca490fb1.js";import{P as te}from"./index-3283c9b7.js";import{C as ae}from"./ClipboardDocumentIcon-f03b0627.js";import{L as p,t as se}from"./@headlessui/react-cdd9213e.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@hookform/resolvers-fad2f3d1.js";import"./XMarkIcon-cfb26fe7.js";import"./index-23a711b5.js";import"./index.esm-7add6cfb.js";import"./react-icons-36ae72b7.js";const ne=[{header:"Company Name",accessor:"company_name"},{header:"Update Name",accessor:"update_name"},{header:"Date received",accessor:"sent_at"},{header:"Status",accessor:"status"},{header:"Action",accessor:""}],Re=()=>{var g,j,y,N;B();const[_,ie]=i.useState(""),[S,le]=i.useState("");window.location.href.split("/"),i.useContext(V);const{dispatch:x}=i.useContext(H),P=G(),[s,u]=O(),{updates:a,loading:l,refetch:h,currentPage:C,pageCount:U,pageSize:k,updatePageSize:D,previousPage:E,nextPage:L,canPreviousPage:T,canNextPage:z}=Z(_,S),[A,r]=i.useState(!1);i.useState(!1);const q=X({company_name:m(),status:m(),availability:m()}),{register:f,handleSubmit:F,setError:re,reset:M,formState:{errors:o}}=W({resolver:K(q),defaultValues:async()=>{const t=s.get("company_name")??"",n=s.get("status")??"",d=s.get("availability")??"";return{company_name:t,status:n,availability:d}}});i.useEffect(()=>{x({type:"SETPATH",payload:{path:"update_requests"}})},[]);function $(t){s.set("company_name",t.company_name),s.set("status",t.status),s.set("availability",t.availability),u(s)}const I=window.location.href,R=new URL(I).origin,Y=t=>{navigator.clipboard.writeText(R+t),J(x,"Link Copied")},b=a==null?void 0:a.filter(t=>t.request==0);return console.log(a),e.jsx(e.Fragment,{children:l?e.jsx(w,{}):e.jsx(e.Fragment,{children:e.jsxs("div",{className:"rounded bg-brown-main-bg p-5 pt-8 md:px-8",children:[e.jsx("div",{className:"item-center mb-3 flex w-full justify-between ",children:e.jsx("h4",{className:"text-left text-[16px] font-[600] sm:text-[20px]",children:"Search"})}),e.jsxs("form",{onSubmit:F($),className:"flex max-w-3xl flex-wrap gap-4",children:[e.jsxs("div",{className:"flex w-full flex-row flex-wrap gap-4",children:[e.jsxs("div",{className:"w-full sm:w-auto",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Company name"}),e.jsx("input",{type:"text",...f("company_name"),className:`focus:shadow-outline w-full w-full appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm text-sm font-normal font-normal leading-tight text-[#1d1f1a] shadow focus:outline-none   sm:w-[180px] ${(g=o.company_name)!=null&&g.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(j=o.company_name)==null?void 0:j.message})]}),e.jsxs("div",{className:"w-full sm:w-auto",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Status"}),e.jsxs("select",{...f("status"),className:`focus:shadow-outline w-full appearance-none text-ellipsis rounded border border-[#1f1d1a] bg-transparent py-2 pl-3 pr-8 text-sm font-normal capitalize leading-tight text-[#1f1d1a] focus:outline-none sm:w-[180px] ${(y=o.status)!=null&&y.message?"border-red-500":""}`,children:[e.jsx("option",{value:"",children:" - select - "}),Object.entries(Q).map(([t,n])=>e.jsx("option",{value:t,children:n},t))]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(N=o.status)==null?void 0:N.message})]})]}),e.jsxs("div",{className:"row-start-3 flex items-center gap-4 sm:row-start-auto",children:[e.jsx("button",{type:"submit",disabled:l,className:"font-iowan-regular  rounded-md bg-primary-black/80 px-4 py-1 font-semibold text-white hover:bg-primary-black",children:"Search"}),e.jsx("button",{type:"button",onClick:()=>{M({company_name:"",status:""}),s.delete("company_name"),s.delete("status"),u(s)},disabled:l,className:"rounded-md px-4 py-1 font-semibold text-[#1f1d1a]",children:"Clear"})]})]}),e.jsxs("div",{className:"mt-8 w-full rounded bg-brown-main-bg p-5 px-0",children:[e.jsx("div",{className:"mb-6 flex w-full justify-between text-center",children:e.jsx("div",{className:"flex w-full flex-row justify-between ",children:e.jsxs("div",{className:"text-left text-[16px] font-[600] sm:text-[20px]",children:["Updates(",b?b.length:null,")"]})})}),e.jsxs("div",{className:`${l?"":"custom-overflow overflow-x-auto"} `,children:[l?e.jsx("div",{className:"flex max-h-fit min-h-fit min-w-fit max-w-full items-center justify-center  py-5",children:e.jsx(w,{size:50})}):e.jsx(e.Fragment,{children:e.jsx(ee,{children:e.jsxs("table",{className:"min-w-full divide-y divide-[#1f1d1a]/10 ",children:[e.jsx("thead",{children:e.jsx("tr",{children:ne.map((t,n)=>e.jsx("th",{scope:"col",className:"font  whitespace-nowrap border-b-[#1f1d1a]/10  px-4 text-left font-[700] md:border-0 md:border-b-[3px] md:border-dashed md:px-6 md:py-3",children:t.header},n))})}),e.jsx("tbody",{className:"font-iowan-regular  divide-y divide-[#1f1d1a]/10",children:a==null?void 0:a.toReversed().map(t=>{var d;(d=t==null?void 0:t.public_url)==null||d.split("/");const n=c(t.sent_at).add(t.recipient_access,"days").toDate()<new Date;return t.sent_at&&c(t.sent_at).add(t.recipient_access??0,"days").diff(c(),"days"),e.jsxs("tr",{children:[e.jsx("td",{className:"whitespace-nowrap px-3 md:max-w-lg md:whitespace-normal md:px-6 md:py-6 md:pb-[50px]",children:e.jsx(p,{className:"relative ",children:({open:de})=>e.jsxs(e.Fragment,{children:[e.jsx(p.Button,{as:"div",className:"max-w-[300px] cursor-pointer",onMouseEnter:()=>r(t.id),onMouseLeave:()=>r(!1),children:t.company_name}),e.jsx(se,{as:i.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",show:t.id===A,onMouseEnter:()=>r(t.id),onMouseLeave:()=>r(!1),className:"absolute z-[999999999999999999] w-[300px]",children:e.jsx(p.Panel,{className:"px-4",children:e.jsx("div",{className:" hidden    rounded-lg bg-[#1f1d1a] p-4 px-4 text-white shadow-lg ring-1 ring-[#1f1d1a]/5 md:block",children:e.jsxs("div",{className:"flex flex-col  gap-2 text-[14px] font-medium",children:[e.jsx("span",{className:"text-[17px]",children:t.company_name}),e.jsx("p",{className:"line-clamp-5 overflow-ellipsis text-[13px] text-gray-300",children:t.description})]})})})})]})},t.id)}),e.jsx("td",{className:"whitespace-nowrap px-3 md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:t.update_name.startsWith("Update ")&&t.update_name.slice(7).match(/^\d{4}-\d{2}-\d{2}$/)?`Update ${new Date(t.update_name.slice(7)).toLocaleString("en-US",{month:"short",day:"2-digit",year:"numeric"}).replace(/,/g,",")}`:t.update_name}),e.jsxs("td",{className:"whitespace-nowrap px-3 md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:[c(t.sent_at).format("MMM DD, YYYY")," "]}),e.jsx("td",{className:"whitespace-nowrap px-3 md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:t.status==="New"?e.jsx("span",{className:"text-green-500",children:"New"}):t.status}),e.jsx("td",{className:"flex flex-col items-start justify-center gap-4 px-6 py-4 md:max-w-[500px]",children:e.jsxs("div",{className:"flex flex-col items-start justify-center gap-4",children:[e.jsx("button",{className:"cursor-pointer font-medium text-[#292829fd] underline underline-offset-4 hover:underline disabled:cursor-not-allowed disabled:text-gray-400",onClick:()=>{P(t.update_link)},disabled:(n||t.request===0)&&t.is_requested!==1,children:e.jsx("span",{children:"View"})}),e.jsx("button",{className:"cursor-pointer px-0 font-medium text-[#292829fd] underline hover:underline disabled:cursor-not-allowed disabled:text-gray-400",onClick:()=>{Y(`${t.update_link}`)},disabled:n||t.request===0||t.public_link_enabled==0,children:e.jsx("span",{children:"Share"})}),t.request===0&&e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx(v,{refetch:h,row:t}),e.jsx(v,{reject:!0,refetch:h,row:t})]})]})})]},t.id)})})]})})}),(a==null?void 0:a.length)==0?e.jsxs("div",{className:"mb-[20px] mt-24 flex flex-col items-center",children:[e.jsx(ae,{className:"h-8 w-8 text-gray-700",strokeWidth:2}),e.jsx("p",{className:"mt-4 text-center text-base font-medium",children:"No Company Updates"})]}):null]}),(a==null?void 0:a.length)>0&&e.jsx(te,{currentPage:C,pageCount:U,pageSize:k,canPreviousPage:T,canNextPage:z,updatePageSize:D,previousPage:E,nextPage:L,dataLoading:l})]})]})})})};export{Re as default};
