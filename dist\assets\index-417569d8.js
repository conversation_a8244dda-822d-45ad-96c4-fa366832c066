import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as t}from"./vendor-4cdf2bd1.js";import{A as j,G as x,M as v,t as y,s as f,I as w,T as N}from"./index-46de5032.js";import{X as E}from"./XMarkIcon-cfb26fe7.js";import{t as u,S as p}from"./@headlessui/react-cdd9213e.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";function S(){const{dispatch:i}=t.useContext(j),{dispatch:d}=t.useContext(x),[c,o]=t.useState([]),[s,r]=t.useState(!1);async function a(){r(!0);try{const m=await new v().callRawAPI("/v4/api/records/setting");o(m.list)}catch(n){y(i,n.message),n.message!=="TOKEN_EXPIRED"&&f(d,n.message,5e3,"error")}r(!1)}return t.useEffect(()=>{a()},[]),{settings:c,refetch:a,loading:s}}function b({value:i,setting_key:d,afterEdit:c}){const[o,s]=t.useState(!1),{dispatch:r}=t.useContext(j),{dispatch:a}=t.useContext(x),[n,m]=t.useState(!1),[g,h]=t.useState("");t.useEffect(()=>{h(i)},[i]);async function _(){m(!0);try{await new N().updateWhere("setting",{setting_key:d},{setting_value:g}),s(!1),c(),f(a,"settings updated")}catch(l){y(r,l.message),l.message!=="TOKEN_EXPIRED"&&f(a,l.message,5e3,"error")}m(!1)}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"rounded-md bg-primary-black px-2 py-1 text-sm font-medium text-white",onClick:()=>s(!0),children:"Edit"}),e.jsx(u,{appear:!0,show:o,as:t.Fragment,children:e.jsxs(p,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>s(!1),children:[e.jsx(u.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(u.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(p.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(p.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Edit"}),e.jsx("button",{onClick:()=>s(!1),type:"button",children:e.jsx(E,{className:"h-6 w-6"})})]}),e.jsx("input",{className:"focus:shadow-outline mt-3 w-[100px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 leading-tight text-[#1d1f1a] shadow   focus:outline-none sm:w-[180px] ",value:g,onChange:l=>h(l.target.value),placeholder:"Type a number"}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>s(!1),children:"Cancel"}),e.jsx(w,{loading:n,disabled:n,onClick:_,className:"disabled:bg-disabledblack rounded-lg bg-primary-black py-2 text-center font-semibold text-white transition-colors duration-100",children:"Save"})]})]})})})})]})})]})}const H=()=>{const{dispatch:i}=t.useContext(x),{loading:d,settings:c,refetch:o}=S(),s=c.reduce((r,a)=>(r[a.setting_key]=a.setting_value,r),{});return t.useEffect(()=>{i({type:"SETPATH",payload:{path:"settings"}})},[]),e.jsx("div",{className:"mx-auto rounded p-5 shadow-md",children:e.jsxs("div",{className:"mt-6 max-w-lg",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{children:"Number of free reports for a startup"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("p",{className:"font-medium",children:s.start_up_free_reports_count}),e.jsx(b,{setting_key:"start_up_free_reports_count",value:s.start_up_free_reports_count,afterEdit:o})]})]}),e.jsx("hr",{className:"my-4"}),e.jsxs("div",{className:"mt-4 flex items-center justify-between",children:[e.jsx("p",{children:"Number of invites for a free report"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("p",{className:"font-medium",children:s.start_up_free_report_invite_count}),e.jsx(b,{setting_key:"start_up_free_report_invite_count",value:s.start_up_free_report_invite_count,afterEdit:o})]})]})]})})};export{H as default};
