import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as m,b as le,h as me}from"./vendor-4cdf2bd1.js";import{u as K}from"./react-hook-form-9f4fcfa9.js";import{o as X}from"./yup-c41d85d2.js";import{c as H,a as k,d as ce}from"./yup-342a5df4.js";import{S as de}from"./CreateGroupModal-9562fe27.js";import{S as pe}from"./SelectGroupType-3d2b3831.js";import{u as Y}from"./useRecipientGroups-19574462.js";import{a as J,L as w,I as R,b as ue,M as xe}from"./index-46de5032.js";import{M as D}from"./MkdInput-a0090fba.js";import{X as z}from"./XMarkIcon-6ed09631.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./MkdCustomInput-af54c64d.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./InteractiveButton-060359e0.js";import"./index-dc002f62.js";import"./qr-scanner-cf010ec4.js";import"./react-spinners-b860a5a3.js";import"./XMarkIcon-cfb26fe7.js";import"./@headlessui/react-cdd9213e.js";import"./index-6edcbb0d.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const ge=({isOpen:a,onClose:C,fundManagers:_=[],onSubmit:d,loading:t=!1,onRemoveFundManager:h})=>{const[n,g]=m.useState(!1),{authState:c}=J(),{groups:j}=Y(c==null?void 0:c.user),P=H({group_id:k().required("Please select a group or create a new one"),new_group_name:n?k().required("Group name is required"):k()}),{register:f,handleSubmit:I,formState:{errors:y,isSubmitting:N},reset:v,watch:F,setValue:S}=K({resolver:X(P),defaultValues:{group_id:"",new_group_name:""}});F("group_id"),m.useEffect(()=>{n?S("group_id","new"):(S("group_id",""),S("new_group_name",""))},[n,S]);const G=l=>{d({...l,fundManagers:_,isNewGroup:n})},E=()=>{v(),g(!1),C()};return a?e.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50",children:e.jsxs("div",{className:"mx-4 max-h-[90vh] w-full max-w-2xl overflow-y-auto rounded-lg bg-brown-main-bg shadow-xl",children:[e.jsxs("div",{className:"flex items-center justify-between border-b p-6",children:[e.jsx("h2",{className:"font-iowan text-xl font-semibold",children:"Add Investors to Group"}),e.jsx("button",{onClick:E,className:"text-gray-400 transition-colors hover:text-gray-600",children:e.jsx(z,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-6 rounded-lg bg-brown-main-bg/70 p-4",children:[e.jsxs("div",{className:"mb-3 flex items-center gap-4",children:[e.jsx("span",{className:"text-3xl font-bold text-primary-black",children:_.length}),e.jsxs("span",{className:"text-lg font-medium",children:["Investor",_.length!==1?"s":""," Selected"]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("p",{className:"mb-2 text-sm font-medium text-gray-700",children:"Selected:"}),e.jsx("div",{className:"flex max-h-32 flex-wrap gap-2 overflow-y-auto",children:_.map(l=>e.jsxs("div",{className:"flex items-center gap-2 rounded-full border border-gray-300 bg-gray-100 px-3 py-1 text-sm",title:`${l.first_name} ${l.last_name} from ${l.fund_name}`,children:[e.jsxs("span",{className:"text-gray-700",children:[l.first_name," ",l.last_name]}),h&&e.jsx("button",{onClick:()=>h(l.id),className:"ml-1 text-gray-500 transition-colors hover:text-red-600",title:"Remove from selection",children:e.jsx(z,{className:"h-4 w-4"})})]},l.id))})]})]}),e.jsxs("form",{onSubmit:I(G),className:"space-y-4",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Choose an option:"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"radio",checked:!n,onChange:()=>g(!1),className:"mr-2"}),e.jsx("span",{children:"Add to existing group"})]}),e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"radio",checked:n,onChange:()=>g(!0),className:"mr-2"}),e.jsx("span",{children:"Create new group"})]})]})]}),!n&&e.jsx("div",{children:e.jsx(w,{children:e.jsx(D,{type:"select",register:f,name:"group_id",label:"Select Existing Group",errors:y,options:(j==null?void 0:j.map(l=>l.group_name))||[],noneText:"Choose a group...",className:"!h-[2.75rem] !w-full !rounded-md !border"})})}),n&&e.jsx("div",{children:e.jsx(w,{children:e.jsx(D,{type:"text",register:f,name:"new_group_name",label:"New Group Name",errors:y,placeholder:"Enter group name...",className:"!h-[2.75rem] !w-full !rounded-md !border"})})}),e.jsxs("div",{className:"flex gap-3 pt-4",children:[e.jsx(w,{children:e.jsx(R,{type:"submit",loading:N||t,disabled:N||t,className:"flex-1 rounded-md bg-primary-black px-4 py-2 font-medium text-white transition-colors hover:bg-gray-800",children:"Add to Group"})}),e.jsx("button",{type:"button",onClick:E,disabled:N||t,className:"flex-1 rounded-md border border-black bg-transparent px-4 py-2 font-medium text-gray-800 transition-colors disabled:opacity-50",children:"Cancel"})]})]})]})]})}):null},ts=()=>{const{sdk:a}=ue(),{globalDispatch:C,authDispatch:_,authState:d,globalState:{fundManagersToAdd:t,subscriptionData:h},setGlobalState:n,tokenExpireError:g,showToast:c,create:j,custom:P}=J(),[f,I]=m.useState([]),[y,N]=m.useState(""),{groups:v,refetch:F,loading:S}=Y(d==null?void 0:d.user),[G,E]=m.useState({members:[]}),[l,A]=m.useState(!1),[Q,V]=m.useState(!1),W=H({group_id:k().required("This field is required"),members:ce().min(1,"You must add at least one member").of(k())}),u=le(),[Z]=me(),L=Z.get("updateId");let x=v.find(s=>(s==null?void 0:s.group_name)==y)||null;const{register:he,handleSubmit:ee,setError:q,setValue:b,formState:{errors:p,isSubmitting:T,defaultValues:fe},control:M,clearErrors:se,watch:O}=K({resolver:X(W),defaultValues:{group_id:"",members:[]}}),[re,B]=O(["members","group_id"]);async function te(){try{a.setTable("recipient_group");const s=await a.callRawAPI(`/v4/api/records/recipient_group/${x.id}`,void 0,"GET");a.setTable("recipient_member");const{list:r}=await a.callRawAPI(`/v4/api/records/recipient_member?filter=recipient_group_id,eq,${x.id}`);return{group_id:s.model.group_id,members:r.map(o=>String(o.user_id))}}catch(s){return g(s.message),{group_id:"",members:[]}}}const ie=async(s,r)=>{try{const o=await j("update_group",{update_id:s,group_id:r},!1);o!=null&&o.error||u(`/member/edit-updates/${s}?next=3`)}catch(o){g(o.message),q("group_id",{type:"manual",message:o.message})}};console.log(v,f,"selected",B);const oe=async s=>{var o;console.log("hii");const r=v.find(i=>i.group_name===s.group_id);console.log(r,"selected");try{const i=await P({endpoint:"/v3/api/custom/goodbadugly/recipients/add-fund-manager",method:"POST",payload:{group_id:Number(r==null?void 0:r.id),fund_managers:(o=s==null?void 0:s.members)==null?void 0:o.map($=>Number($))}});i!=null&&i.error||(c(i==null?void 0:i.message),u("/member/recipient_group"))}catch(i){c(i==null?void 0:i.message)}finally{}},ae=async s=>{V(!0);try{let r;if(s.isNewGroup)a.setTable("recipient_group"),r=(await a.callRestAPI({group_id:s.new_group_name,members:"",user_id:d.user},"POST")).data;else{const i=f.find($=>$.group_name==s.group_id);console.log(s.group_id,i,"selected",f),r=i==null?void 0:i.id}if(!r)throw new Error("Failed to get group ID");const o=await P({endpoint:"/v3/api/custom/goodbadugly/recipients/add-fund-manager",method:"POST",payload:{group_id:Number(r),fund_managers:s.fundManagers.map(i=>Number(i.id))}});if(!(o!=null&&o.error))c(`Successfully added ${s.fundManagers.length} fund manager${s.fundManagers.length!==1?"s":""} to ${s.isNewGroup?s.new_group_name:s.group_id}`),n("fundManagersToAdd",null),A(!1),u("/member/recipient_group");else throw new Error(o.message||"Failed to add fund managers")}catch(r){c((r==null?void 0:r.message)||"An error occurred")}finally{V(!1)}},ne=async s=>{if(console.log(s),t&&(t!=null&&t.length)){await oe(s);return}try{if(x){a.setTable("recipient_group"),await a.callRestAPI({id:x.id,group_id:s.group_id,members:s.members.join(","),user_id:d.user},"PUT");const r=s.members.filter(i=>!G.members.includes(i)),o=G.members.filter(i=>!s.members.includes(i));console.log(r,o),a.setTable("recipient_member"),await Promise.all(r.map(i=>a.callRestAPI({user_id:i.id,recipient_group_id:x.id},"POST"))),await Promise.all(o.map(i=>a.callRawAPI("/v4/api/records/recipient_member",{user_id:i.id,recipient_group_id:x.id},"DELETE"))),c("Added"),u("/member/recipient_group")}else{a.setTable("recipient_group");const r=await a.callRestAPI({group_id:s.group_id,members:s.members.join(","),user_id:d.user},"POST");a.setTable("recipient_member"),await Promise.all(s.members.map(o=>a.callRestAPI({recipient_group_id:r.data,user_id:o},"POST"))),c("Added"),L?ie(L,r.data):u("/member/recipient_group")}}catch(r){g(r.message),q("group_id",{type:"manual",message:r.message})}},U=async()=>{const r=await new xe().callRawAPI(`/v4/api/records/group?filter=user_id,in,'NULL',${d.user}`);console.log(r,"selected"),I(r.list)};return m.useEffect(()=>{C({type:"SETPATH",payload:{path:"recipient_group"}})},[]),m.useEffect(()=>{se(["members","group_id"])},[re,B]),m.useEffect(()=>{(async function(){if(x){await U();const s=await te();E(s),b("members",s.members)}else b("members",[])})()},[x,y]),m.useEffect(()=>{t&&(t!=null&&t.length)&&(A(!0),b("members",[...t==null?void 0:t.map(s=>s==null?void 0:s.id)]),U())},[t==null?void 0:t.length]),e.jsxs("div",{className:"min-h-screen p-5 pt-8 sm:px-8",children:[e.jsxs("button",{className:"mb-7 flex h-[2.25rem] w-[5.1875rem] items-center justify-center gap-2 ",onClick:()=>u(-1),children:[e.jsx("div",{className:"flex min-h-[32px] min-w-[32px] items-center justify-center  gap-2 rounded border border-[#1f1d1a] ",children:e.jsxs("svg",{className:"min-h-5 min-w-5",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M17.5 10.0001L2.5 10.0001",stroke:"black","stroke-width":"1.66667","stroke-linecap":"round","stroke-linejoin":"round"}),e.jsx("path",{d:"M8.33203 15.8335L2.4987 10.0002L8.33203 4.16683",stroke:"black","stroke-width":"1.66667","stroke-linecap":"round","stroke-linejoin":"round"})]})}),e.jsx("span",{className:"font-inter font-[400]",children:" Back"})]}),e.jsxs("div",{className:"flex h-fit flex-col items-start gap-5",children:[e.jsxs("div",{children:[e.jsxs("h4",{className:"mb-2 font-iowan text-[16px] font-[600] sm:text-[20px]",children:["Create Recipient Group ",e.jsx("br",{}),e.jsx("span",{className:"font-inter text-[12px] font-normal md:text-[14px]",children:"Add existing users to existing or new recipient group"})]}),e.jsxs("form",{className:"mt-5 w-full max-w-[500px]",onSubmit:ee(ne,s=>{console.error(s)}),children:[e.jsx(pe,{control:M,name:"group_id",errors:p,setGroupName:N,setValue:s=>b("group_id",s),allowedRoles:["investor","stakeholder"],onGroupChange:(s,r)=>{console.log("ADD PAGE - onGroupChange called with:",s,r),b("members",r||[]),console.log("ADD PAGE - Form members value after setValue:",O("members"))}}),p&&(p==null?void 0:p.group_id)&&e.jsx("p",{className:"text-field-error absolute inset-x-0 top-[90%] m-auto mt-2 text-[.8rem] italic text-red-500",children:"Group is Required"}),t&&(t!=null&&t.length)?e.jsxs("div",{className:"font-iowan-regular flex grow items-center justify-start gap-5",children:[e.jsx("b",{className:"text-[3.125rem]",children:t==null?void 0:t.length})," ",e.jsx("span",{children:"Fund Managers Selected"})]}):e.jsx(de,{control:M,name:"members",setValue:s=>b("members",s)}),p&&(p==null?void 0:p.members)&&e.jsx("p",{className:"text-field-error absolute inset-x-0 top-[90%] m-auto mt-2 text-[.8rem] italic text-red-500",children:"Members is Required"}),e.jsxs("div",{className:"flex items-center justify-start gap-5",children:[e.jsx(w,{children:e.jsx(R,{type:"submit",loading:T,disabled:T,className:"focus:shadow-outline mt-4 w-[88px] rounded bg-primary-black px-4 py-2 font-iowan font-bold text-white focus:outline-none",children:"Submit"})}),t&&(t!=null&&t.length)?e.jsx(w,{children:e.jsx(R,{type:"button",disabled:T,onClick:()=>{n("fundManagersToAdd",null)},className:"focus:shadow-outline mt-4 rounded bg-primary-black px-4 py-2 font-iowan font-bold text-white focus:outline-none",children:"Remove Fund Managers"})}):null]})]})]}),e.jsx("div",{className:"my-6 flex h-[.125rem] max-h-[.125rem] min-h-[.125rem]  w-full grow flex-row items-center gap-4  border  border-[#1f1d1a]/10 "}),e.jsxs("div",{className:"mb-[30px] flex h-fit w-full flex-col space-y-5 font-iowan",children:[e.jsxs("h4",{className:"mb-2 font-iowan text-[16px] font-[600] sm:text-[20px]",children:["Add Investors to a Recipient Group ",e.jsx("br",{}),!(h!=null&&h.subscription)&&e.jsxs("span",{className:"font-inter text-[12px] font-normal md:text-[14px]",children:[e.jsx("span",{className:"tex-sm cursor-pointer font-semibold underline underline-offset-2",onClick:()=>{u("/member/billing")},children:"Upgrade"})," to a Pro, Business, or Enterprise plan to access more features."]})]}),e.jsx("div",{children:e.jsx(w,{children:e.jsx(R,{onClick:()=>u("/member/search-fund-managers"),className:"!rounded-[.125rem] !px-4 !py-2 font-iowan text-white",children:"Search fund managers"})})})]})]}),e.jsx(ge,{isOpen:l,onClose:()=>{A(!1),n("fundManagersToAdd",null)},fundManagers:t||[],onSubmit:ae,loading:Q,onRemoveFundManager:s=>{const r=t==null?void 0:t.filter(o=>o.id!==s);n("fundManagersToAdd",r)}})]})};export{ts as default};
