import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{o as A}from"./yup-c41d85d2.js";import{A as q,G as L,a9 as U,E as M,I as E,M as S,s as y,t as P}from"./index-46de5032.js";import{r as p}from"./vendor-4cdf2bd1.js";import{u as V}from"./react-hook-form-9f4fcfa9.js";import{c as O,a as l}from"./yup-342a5df4.js";import{S as $,a as K,b as G}from"./SelectCity-6a0fe89f.js";import"./InteractiveButton-060359e0.js";import{M as c}from"./MkdInput-a0090fba.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./countries-912e22d5.js";import"./ChevronDownIcon-8b7ce98c.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";function Pe(){const{dispatch:d,state:o}=p.useContext(q),{dispatch:h}=p.useContext(L),C=O({first_name:l().required("This field is required"),last_name:l().required("This field is required"),title:l().required("This field is required"),company_name:l().required("This field is required"),country:l().nullable(),state:l().nullable(),city:l().nullable(),contact_link:l().nullable()}),{register:a,handleSubmit:X,setError:B,reset:g,watch:w,setValue:n,formState:{errors:m,isSubmitting:j,dirtyFields:s,isDirty:k,defaultValues:i},control:b}=V({resolver:A(C),defaultValues:{first_name:o.profile.first_name,last_name:o.profile.last_name,title:o.profile.title,company_name:o.company.name,photo:o.profile.photo,country:o.profile.country,state:o.profile.state,city:o.profile.city,contact_link:o.profile.contact_link}});console.log(o.profile);const I=()=>{g(i)};async function _(){try{const r=await new S().getProfilePreference();d({type:"SET_PROFILE",payload:r})}catch(e){P(d,e.message),e.message!=="TOKEN_EXPIRED"&&y(h,e.message,5e3,"error")}}const T=async e=>{console.log("Form submitted with data:",e);try{const r=new S;let N="";if(s.photo||s.first_name||s.last_name){if(s.photo&&e.photo instanceof FileList&&e.photo.length>0){const R=await r.upload(e.photo[0]);N=r.baseUrl()+R.url}await r.callRawAPI(`/v4/api/records/user/${o.user}`,{first_name:e.first_name,last_name:e.last_name,...s.photo&&{photo:N}},"PUT"),await _()}s.company_name&&(await r.callRawAPI(`/v4/api/records/companies/${o.company.id}`,{name:e.company_name},"PUT"),d({type:"REFETCH_COMPANY"})),(s.country||s.state||s.city||s.contact_link||s.title)&&await r.callRawAPI("/v4/api/records/profile",{updateCondition:{user_id:o.user},country:e.country,state:e.state,city:e.city,contact_link:e.contact_link,title:e.title},"PUT"),await _(),y(h,"Changes saved");const F={first_name:e.first_name,last_name:e.last_name,title:e.title,company_name:e.company_name,photo:e.photo,country:e.country,state:e.state,city:e.city,contact_link:e.contact_link};g(F,{keepDirty:!1,keepErrors:!1,keepIsSubmitted:!1,keepTouched:!1,keepIsValid:!1,keepSubmitCount:!1})}catch(r){console.error("Error submitting form:",r),P(d,r.message),r.message!=="TOKEN_EXPIRED"&&y(h,r.message,5e3,"error")}},[f,u,x]=w(["photo","country","state"]);p.useEffect(()=>{u===""&&(n("state",""),n("city","")),x===""&&n("city","")},[u,x]);const v=p.useMemo(()=>f instanceof FileList&&f.length>0?URL.createObjectURL(f[0]):null,[f]),D=async()=>{console.log("Save button clicked");const e=w();console.log("Current form data:",e),console.log("Form errors:",m),console.log("Is dirty:",k),console.log("Dirty fields:",s);try{await T(e)}catch(r){console.error("Error in handleSave:",r)}};return t.jsx("div",{className:"mx-auto grid h-full max-h-full min-h-full w-full grid-cols-1 grid-rows-1 overflow-auto px-8 py-6",children:t.jsxs("form",{className:"grid h-full max-h-full min-h-full w-full grid-cols-1 grid-rows-[1fr_auto]",onSubmit:e=>{e.preventDefault(),console.log("Form submit event"),D()},noValidate:!0,children:[t.jsxs("div",{className:"h-full max-h-full min-h-full w-full overflow-auto",children:[t.jsx("div",{className:"mb-4 font-iowan text-[20px] font-[700] md:text-[1.5rem] md:leading-[1.865rem] ",children:"Personal Info"}),t.jsx("p",{className:"mb-6 font-inter text-[1rem] font-[400] leading-[1.21rem]",children:"Update your personal info"}),t.jsx("div",{className:"mb-6 flex items-center",children:t.jsxs("div",{className:"relative flex h-[7.5rem] w-[7.5rem] items-center justify-center rounded-full border border-[#1F1D1A] bg-transparent",children:[v||i!=null&&i.photo?t.jsx("img",{src:v||(i==null?void 0:i.photo)||"/default.png",alt:"profile",className:"h-full min-h-full w-full min-w-full rounded-[50%] object-cover sm:h-full sm:min-h-full sm:w-full sm:min-w-full"}):t.jsx(U,{}),t.jsxs("label",{htmlFor:"photo",className:"absolute bottom-0 right-0 flex h-8 w-8 cursor-pointer items-center justify-center rounded-full border border-[#1F1D1A] bg-brown-main-bg",children:[t.jsx("input",{type:"file",id:"photo",...a("photo"),className:"hidden"}),t.jsx(M,{})]})]})}),t.jsxs("div",{className:"flex w-full flex-col items-start gap-0 md:w-[45%]",children:[t.jsxs("div",{className:"mb-6 grid w-full grid-cols-1 gap-4 md:grid-cols-2",children:[t.jsx("div",{children:t.jsx(c,{type:"text",id:"first-name",label:"First Name",name:"first_name",errors:m,register:a,className:"mt-1 block w-full rounded-md  !border !border-black shadow-sm focus:border-gray-500 focus:ring-gray-500"})}),t.jsx("div",{children:t.jsx(c,{type:"text",id:"last-name",name:"last_name",label:"Last Name",errors:m,register:a,className:"mt-1 block w-full rounded-md  !border !border-black shadow-sm focus:border-gray-500 focus:ring-gray-500"})})]}),t.jsx("div",{className:"mb-6 grid w-full grid-cols-1 gap-4",children:t.jsx("div",{children:t.jsx(c,{type:"text",id:"title",label:"Title",name:"title",errors:m,register:a,className:"mt-1 block w-full rounded-md !border !border-black shadow-sm focus:border-gray-500 focus:ring-gray-500"})})}),t.jsx("div",{className:"mb-6 w-full",children:t.jsx(c,{type:"text",id:"member",label:"member",name:"company_name",errors:m,register:a,className:"mt-1 block w-full rounded-md !border !border-black shadow-sm focus:border-gray-500 focus:ring-gray-500"})}),t.jsx("div",{className:"mb-6 w-full",children:t.jsx($,{control:b,name:"country",setValue:e=>n("country",e)})}),t.jsxs("div",{className:"mb-6 grid w-full grid-cols-1 gap-4 md:grid-cols-2",children:[t.jsx("div",{children:t.jsx(K,{control:b,name:"state",setValue:e=>n("state",e),country:u})}),t.jsx("div",{children:t.jsx(G,{control:b,name:"city",setValue:e=>n("city",e),country:u,state:x})})]}),t.jsxs("div",{className:"mb-6",children:[t.jsx("div",{className:"mb-4 font-iowan text-[20px] font-[700] md:text-[1.5rem] md:leading-[1.865rem] ",children:"Personal Calendar Link"}),t.jsx("p",{className:"mb-6 font-inter text-[1rem] font-[400] leading-[1.21rem]",children:"This link will be displayed on reports for fund managers and stakeholders, allowing them to schedule a meeting with you."}),t.jsx(c,{type:"text",id:"calendar-link",name:"contact_link",errors:m,register:a,className:"mt-1 block w-full rounded-md  !border !border-black shadow-sm focus:border-gray-500 focus:ring-gray-500",placeholder:"http://calendly.com/example"})]})]})]}),t.jsxs("div",{className:"flex justify-end gap-4",children:[t.jsx(E,{type:"button",className:"flex h-[2.75rem] w-fit items-center justify-center whitespace-nowrap rounded-[.0625rem] !border !border-black bg-transparent px-2 py-2 font-iowan !text-[1rem] tracking-wide text-black md:px-5",color:"black",onClick:I,children:"Discard Changes"}),t.jsx(E,{className:" flex h-[2.75rem] w-fit items-center justify-center whitespace-nowrap rounded-[.0625rem] bg-[#1f1d1a] px-2 py-2 font-iowan !text-[1rem] tracking-wide text-white md:px-5",loading:j,disabled:j||!k,type:"submit",children:"Save Changes"})]})]})})}export{Pe as default};
