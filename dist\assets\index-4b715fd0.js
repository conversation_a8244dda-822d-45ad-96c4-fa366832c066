import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{o as w}from"./yup-c41d85d2.js";import{A as y,G as _,I as p,ac as C,ad as I,ae as v,af as N,ag as S,ah as T,ai as E,aj as D,ak as R,al as G,am as P,an as A,ao as M,ap as F,aq as $,ar as B,as as L,at as O,z as X,M as Y,s as f,t as z}from"./index-46de5032.js";import"./InteractiveButton-060359e0.js";import{r as x}from"./vendor-4cdf2bd1.js";import{u as H}from"./react-hook-form-9f4fcfa9.js";import{c as J,a as s}from"./yup-342a5df4.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";const n=[{name:"Linkedin",icon:t.jsx(C,{}),field:"linkedin"},{name:"Github",icon:t.jsx(I,{}),field:"github"},{name:"X",icon:t.jsx(v,{}),field:"x"},{name:"Medium",icon:t.jsx(N,{}),field:"medium"},{name:"Facebook",icon:t.jsx(S,{}),field:"facebook"},{name:"YouTube",icon:t.jsx(T,{}),field:"youtube"},{name:"Reddit",icon:t.jsx(E,{}),field:"reddit"},{name:"Angellist",icon:t.jsx(D,{}),field:"angel_list"},{name:"Crunch base",icon:t.jsx(R,{}),field:"crunch_base"},{name:"Trust Pilot",icon:t.jsx(G,{}),field:"trust_pilot"},{name:"TikTok",icon:t.jsx(P,{}),field:"tiktok"},{name:"Slack",icon:t.jsx(A,{}),field:"slack"},{name:"G2",icon:t.jsx(M,{}),field:"g2"},{name:"Capterra",icon:t.jsx(F,{}),field:"capterra"},{name:"Trust Radius",icon:t.jsx($,{}),field:"trust_radius"},{name:"Glassdoor",icon:t.jsx(B,{}),field:"glassdoor"},{name:"Instagram",icon:t.jsx(L,{}),field:"instagram"},{name:"Discord",icon:t.jsx(O,{}),field:"discord"},{name:"Bluesky",icon:t.jsxs("svg",{width:"22",height:"20",viewBox:"0 0 22 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[t.jsx("g",{"clip-path":"url(#clip0_7961_24849)",children:t.jsx("path",{d:"M4.97705 1.66143C7.41523 3.54524 10.0378 7.36483 11.0006 9.41464C11.9636 7.36498 14.586 3.5452 17.0242 1.66143C18.7835 0.302148 21.634 -0.749588 21.634 2.59709C21.634 3.26547 21.2616 8.21181 21.0432 9.01483C20.2841 11.8067 17.518 12.5188 15.0574 12.0878C19.3584 12.8411 20.4525 15.3365 18.0896 17.8319C13.602 22.5712 11.6396 16.6428 11.1365 15.1238C11.0444 14.8453 11.0012 14.715 11.0006 14.8258C10.9999 14.715 10.9568 14.8453 10.8646 15.1238C10.3618 16.6428 8.39942 22.5713 3.91153 17.8319C1.54859 15.3365 2.64268 12.841 6.94376 12.0878C4.48313 12.5188 1.71692 11.8067 0.957924 9.01483C0.73953 8.21173 0.367188 3.26539 0.367188 2.59709C0.367188 -0.749588 3.21773 0.302148 4.97692 1.66143H4.97705Z",fill:"#1F1D1A"})}),t.jsx("defs",{children:t.jsx("clipPath",{id:"clip0_7961_24849",children:t.jsx("rect",{width:"22",height:"20",fill:"white"})})})]}),field:"bluesky"}];function be(){const{dispatch:o,state:l}=x.useContext(y),{dispatch:d}=x.useContext(_),h=J({linkedin:s(),github:s(),x:s(),medium:s(),facebook:s(),youtube:s(),instagram:s(),reddit:s(),angel_list:s(),crunch_base:s(),capterra:s(),discord:s(),g2:s(),glassdoor:s(),slack:s(),tiktok:s(),trust_pilot:s(),trust_radius:s(),bluesky:s()}),{register:g,handleSubmit:b,setError:K,reset:k,formState:{errors:i,isSubmitting:a,isDirty:m}}=H({resolver:w(h),defaultValues:async()=>{const e=X(l.company.socials,{});return{linkedin:e.linkedin,github:e.github,x:e.x,medium:e.medium,facebook:e.facebook,youtube:e.youtube,instagram:e.instagram,reddit:e.reddit,angel_list:e.angel_list,crunch_base:e.crunch_base,g2:e.g2,glassdoor:e.glassdoor,tiktok:e.tiktok,slack:e.slack,discord:e.discord,trust_pilot:e.trust_pilot,trust_radius:e.trust_radius,capterra:e.capterra,bluesky:e.bluesky}}});async function j(e){try{await new Y().callRawAPI(`/v4/api/records/companies/${l.company.id}`,{socials:JSON.stringify({linkedin:e.linkedin,github:e.github,x:e.x,medium:e.medium,facebook:e.facebook,youtube:e.youtube,instagram:e.instagram,reddit:e.reddit,angel_list:e.angel_list,crunch_base:e.crunch_base,g2:e.g2,glassdoor:e.glassdoor,tiktok:e.tiktok,slack:e.slack,discord:e.discord,trust_pilot:e.trust_pilot,trust_radius:e.trust_radius,capterra:e.capterra,bluesky:e.bluesky})},"PUT"),o({type:"REFETCH_COMPANY"}),f(d,"Changes saved")}catch(r){z(o,r.message),r.message!=="TOKEN_EXPIRED"&&f(d,r.message,5e3,"error")}}return t.jsx("div",{className:"h-full max-h-full min-h-full w-full max-w-full items-start gap-12 p-4 sm:p-8 md:flex md:p-12",children:t.jsxs("form",{className:"grid h-full max-h-full min-h-full w-full flex-grow grid-cols-1 grid-rows-[1fr_auto]",onSubmit:b(j),children:[t.jsx("div",{className:"h-full max-h-full min-h-full w-full overflow-auto sm:gap-6",children:t.jsx("div",{className:"grid w-full grid-cols-1 gap-4  px-5 pb-7 sm:grid-cols-2 sm:px-2 md:w-[55%] md:px-0 md:pb-0",children:n==null?void 0:n.map((e,r)=>{var c,u;return t.jsxs("div",{children:[t.jsx("label",{className:"mb-2 block font-iowan text-base font-semibold capitalize text-[#1f1d1a]",children:e==null?void 0:e.name}),t.jsxs("div",{className:"flex max-w-[500px] items-center gap-3 rounded-[.125rem] border  border-[#1f1d1a] px-3",children:[e==null?void 0:e.icon,t.jsx("input",{type:"text",autoComplete:"off",...g(e==null?void 0:e.field),className:`no-box-shadow h-[41.6px] w-full max-w-[500px] appearance-none border-none bg-transparent py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none ${(c=i==null?void 0:i[e==null?void 0:e.field])!=null&&c.message?"border-red-500":""}`,placeholder:`${e==null?void 0:e.name} URL`})]}),t.jsx("p",{className:"text-field-error italic text-red-500",children:(u=i==null?void 0:i[e==null?void 0:e.field])==null?void 0:u.message})]},r)})})}),t.jsxs("div",{className:"flex justify-end gap-4",children:[t.jsx(p,{type:"button",className:"flex h-[2.75rem] w-fit items-center justify-center whitespace-nowrap rounded-[.0625rem] !border !border-black bg-transparent px-2 py-2 font-iowan !text-[1rem] tracking-wide text-black md:px-5",color:"black",onClick:()=>{k()},disabled:a||!m,children:"Discard Changes"}),t.jsx(p,{className:" flex h-[2.75rem] w-fit items-center justify-center whitespace-nowrap rounded-[.0625rem] bg-[#1f1d1a] px-2 py-2 font-iowan !text-[1rem] tracking-wide text-white md:px-5",loading:a,disabled:a||!m,type:"submit",children:"Save Changes"})]})]})})}export{be as default};
