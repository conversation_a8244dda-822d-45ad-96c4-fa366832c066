import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as d,b as I}from"./vendor-4cdf2bd1.js";import{o as G}from"./yup-c41d85d2.js";import{A as D}from"./AddButton-51d1b2cd.js";import{b as R,L as a,aO as U,aP as B,aQ as L,aR as O,aS as P,aT as z,aU as K,aV as $,i as H,a as V,u as q,aW as J,aX as Q}from"./index-46de5032.js";import{M as m}from"./MkdInput-a0090fba.js";import{u as W}from"./react-hook-form-9f4fcfa9.js";import{c as X,a as o}from"./yup-342a5df4.js";import{M as Y}from"./index-d0de8b06.js";import{u as Z}from"./useSubscription-58f7fe18.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";const ee=({setFilter:g,selectedFundManagers:c=[],onAddToGroup:_})=>{const{sdk:u}=R(),S=u.getProjectId(),x=X({search:o(),niches:o(),check_size_buckets:o(),based_in_city:o(),based_in_state:o(),based_in_country:o(),geographies_investing_in:o(),leads_round:o(),title:o(),stages:o()}),{register:l,handleSubmit:f,reset:y,formState:{errors:i}}=W({resolver:G(x)});function j(t){const r=Object.entries(t).map(([s,b])=>!H(b)&&!["All","all"].includes(b)?`${S}_fund_manager.${s},cs,${b}`:null).filter(Boolean);if(!(r!=null&&r.length))return g(()=>"");g(()=>r==null?void 0:r.join("|"))}return e.jsx(e.Fragment,{children:e.jsx("div",{className:"space-y-10 py-6",children:e.jsx("div",{className:"flex flex-col",children:e.jsx("form",{className:"mt-1 flex flex-row flex-wrap gap-6",onSubmit:f(j),children:e.jsxs("div",{className:"relative grid h-fit w-full grid-cols-[repeat(auto-fill,minmax(11rem,1fr))] gap-[.75rem]",children:[e.jsx("div",{className:"",children:e.jsx(a,{children:e.jsx(m,{type:"select",errors:i,register:l,name:"stages",label:"Stages",noneText:"All",options:Object.entries(U).map(([,t])=>t),className:"!h-[2.25rem] !w-full !rounded-[0.125rem] !border !py-[0.5rem]"})})}),e.jsxs("div",{children:[e.jsx(a,{children:e.jsx(m,{type:"select",errors:i,register:l,name:"title",label:"Title",noneText:"All",options:Object.entries(B).map(([,t])=>t),className:"!h-[2.25rem] !w-full !rounded-[0.125rem] !border !py-[0.5rem]"})})," "]}),e.jsxs("div",{children:[e.jsx(a,{children:e.jsx(m,{type:"select",errors:i,register:l,name:"niches",label:"Niches",noneText:"All",options:Object.entries(L).map(([,t])=>t),className:"!h-[2.25rem] !w-full !rounded-[0.125rem] !border !py-[0.5rem]"})})," "]}),e.jsxs("div",{children:[e.jsx(a,{children:e.jsx(m,{type:"select",errors:i,register:l,name:"geographies_investing_in",label:"investing in",noneText:"All",options:Object.entries(O).map(([,t])=>t),className:"!h-[2.25rem] !w-full !rounded-[0.125rem] !border !py-[0.5rem]"})})," "]}),e.jsxs("div",{children:[e.jsx(a,{children:e.jsx(m,{type:"select",errors:i,register:l,name:"based_in_country",label:"Based in Country",noneText:"All",options:["United States","Canada","United Kingdom","Germany","France","Japan","India","Israel","Singapore","Sweden","Switzerland","Taiwan","Vietnam"],className:"!h-[2.25rem] !w-full !rounded-[0.125rem] !border !py-[0.5rem]"})})," "]}),e.jsxs("div",{children:[e.jsx(a,{children:e.jsx(m,{type:"select",errors:i,register:l,name:"based_in_state",label:"Based in state",noneText:"All",options:Object.entries(P).map(([,t])=>t),className:"!h-[2.25rem] !w-full !rounded-[0.125rem] !border !py-[0.5rem]"})})," "]}),e.jsxs("div",{children:[e.jsx(a,{children:e.jsx(m,{type:"select",errors:i,register:l,name:"based_in_city",label:"Based in city",noneText:"All",options:Object.entries(z).map(([,t])=>t),className:"!h-[2.25rem] !w-full !rounded-[0.125rem] !border !py-[0.5rem]"})})," "]}),e.jsxs("div",{children:[e.jsx(a,{children:e.jsx(m,{type:"select",errors:i,register:l,name:"leads_round",label:"Leads round",noneText:"All",options:Object.entries(K).map(([,t])=>t),className:"!h-[2.25rem] !w-full !rounded-[0.125rem] !border !py-[0.5rem]"})})," "]}),e.jsxs("div",{children:[e.jsx(a,{children:e.jsx(m,{type:"select",errors:i,register:l,name:"check_size_buckets",label:"Check size buckets",noneText:"All",options:Object.entries($).map(([,t])=>t),className:"!h-[2.25rem] !w-full !rounded-[0.125rem] !border !py-[0.5rem]"})})," "]}),e.jsxs("div",{className:"flex !w-fit items-end gap-[.75rem]",children:[e.jsx(a,{children:e.jsx(D,{showPlus:!1,type:"submit",className:"!rounded-[.125rem]",children:"Search"})}),e.jsx("button",{type:"button",onClick:()=>{y(),f(j)()},className:"font-semibold text-[#1f1d1a]",children:"Clear"})]}),e.jsx("div",{className:"absolute -bottom-[3.125rem] right-0 m-auto mt-4 flex w-fit items-center gap-4 md:bottom-0",children:e.jsx(a,{children:e.jsx(D,{type:"button",showPlus:!1,disabled:c.length===0,onClick:_,className:"!border-none !bg-[#F2DFCE] p-1 px-4 font-iowan text-[1rem] font-[700] leading-[1.25rem] !text-primary-black",children:"Add to Group"})})})]})})})})})},se=[{header:"Stages",accessor:"stages",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,type:"fund_manager"},{header:"Company",accessor:"fund_name",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,type:"fund_manager"},{header:"First Name",accessor:"first_name",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,type:"fund_manager"},{header:"Last Name",accessor:"last_name",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,type:"fund_manager"},{header:"Title",accessor:"title",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,type:"fund_manager"},{header:"Country",accessor:"based_in_country",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,type:"fund_manager"},{header:"City",accessor:"based_in_city",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,type:"fund_manager"},{header:"State",accessor:"based_in_state",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,type:"fund_manager"}],Ge=()=>{var N,w,A,v,E,k,F;const g=d.useRef(null),c=d.useRef(null),{setGlobalState:_}=V(),[u,S]=d.useState(""),[x,l]=d.useState([]),[f,y]=d.useState([]),[i,j]=d.useState({maxSelection:0}),t=I(),{profile:r}=q({}),{data:s,getSubscription:b,getAccessedFundManager:T,loading:te}=Z();console.log("data",s);const h=(k=J)==null?void 0:k[(E=(v=(A=(w=(N=s==null?void 0:s.object)==null?void 0:N.plan)==null?void 0:w.nickname)==null?void 0:A.split(" "))==null?void 0:v[0])==null?void 0:E.trim()],M=()=>{const n=f==null?void 0:f.map(p=>{if(x!=null&&x.includes(p==null?void 0:p.id))return p}).filter(Boolean);_("fundManagersToAdd",n),t("/member/add-recipient_group")},C=()=>{if(!(s!=null&&s.accessedFundManagers))return j(p=>({...p,maxSelection:h}));const n=h-(s==null?void 0:s.accessedFundManagers);return j(p=>({...p,maxSelection:n}))};return d.useEffect(()=>{r!=null&&r.id&&(b({filter:[`user_id,eq,${r==null?void 0:r.id}`,"cancelled,eq,0","status,eq,'active'"],join:[]}),T(r))},[r==null?void 0:r.id]),d.useEffect(()=>{var n;c!=null&&c.current&&((n=c==null?void 0:c.current)==null||n.click())},[u]),d.useEffect(()=>{C()},[s==null?void 0:s.accessedFundManagers,h]),e.jsx(e.Fragment,{children:e.jsxs(a,{children:[e.jsxs("div",{className:"flex items-center justify-between gap-5 p-7",children:[e.jsxs("button",{className:" flex h-[2.25rem] w-[5.1875rem] items-center justify-center gap-2 ",onClick:()=>t(-1),children:[e.jsx("div",{className:"flex min-h-[32px] min-w-[32px] items-center justify-center  gap-2 rounded border border-[#1f1d1a] ",children:e.jsxs("svg",{className:"min-h-5 min-w-5",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M17.5 10.0001L2.5 10.0001",stroke:"black","stroke-width":"1.66667","stroke-linecap":"round","stroke-linejoin":"round"}),e.jsx("path",{d:"M8.33203 15.8335L2.4987 10.0002L8.33203 4.16683",stroke:"black","stroke-width":"1.66667","stroke-linecap":"round","stroke-linejoin":"round"})]})}),e.jsx("span",{className:"font-inter font-[400]",children:" Back"})]}),((F=s==null?void 0:s.subscription)==null?void 0:F.status)==="active"?e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[s!=null&&s.accessedFundManagers?e.jsxs("span",{children:[h-(s==null?void 0:s.accessedFundManagers)," /"," ",h]}):e.jsx("span",{children:h}),e.jsx("span",{children:"Available investors "})]}):null]}),e.jsxs("div",{className:"flex items-center justify-between gap-5 px-7",children:[e.jsx("p",{className:"font-iowan text-[2rem] font-bold leading-[2.4863rem]",children:"Investors"}),e.jsx("div",{children:e.jsxs("div",{className:"flex h-[2.25rem] cursor-pointer items-center justify-between gap-3 rounded-[.125rem] border border-black px-2 py-1",children:[e.jsx(Q,{className:"!h-[1.25rem] !w-[1.25rem] text-black"}),e.jsx("input",{type:"text",placeholder:"Search Fund Managers",className:"w-full border-none bg-transparent p-0 placeholder:text-left placeholder:text-black focus:outline-none",style:{boxShadow:"0 0 transparent"},onKeyDown:n=>{n.key==="Enter"&&n.preventDefault()},onKeyUp:n=>{}})]})})]}),e.jsxs("div",{className:"custom-overflow w-full space-y-6 overflow-x-auto p-7",children:[e.jsx(ee,{setFilter:S,selectedFundManagers:x,onAddToGroup:()=>{M()}}),e.jsx(a,{children:e.jsx(Y,{showSearch:!1,useDefaultColumns:!0,defaultColumns:se,noDataComponent:{use:!0,component:e.jsx(a,{})},onReady:n=>{y(n)},hasFilter:!1,tableRole:"member",table:"fund_manager",actionId:"id",tableTitle:e.jsx(e.Fragment,{}),defaultFilter:(u==null?void 0:u.split("|").filter(Boolean))||[],actions:{view:{show:!1,action:null,multiple:!1},select:{show:!0,action:n=>{l(()=>[...n])},multiple:!0,max:i==null?void 0:i.maxSelection},export:{show:!1,action:null,multiple:!0},delete:{show:!1,action:null,multiple:!0}},defaultPageSize:20,showPagination:!0,updateRef:g,refreshRef:c,showScrollbar:!1,maxHeight:"md:grid-rows-[inherit] grid-rows-[inherit]"})})]})]})})};export{Ge as default};
