import{j as r}from"./@nextui-org/listbox-0f38ca19.js";import{r as o,j as t,O as a}from"./vendor-4cdf2bd1.js";import{G as l}from"./index-46de5032.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const D=()=>{const{dispatch:i}=o.useContext(l);return o.useEffect(()=>{i({type:"SETPATH",payload:{path:"billings"}})},[]),r.jsxs("div",{className:"mb-[30px] mt-6 grid h-full w-full grid-cols-1 grid-rows-[auto_1fr] px-4 md:mb-[60px] md:px-6",children:[r.jsxs("ul",{className:"scrollbar-hide custom-overflow mb-6  flex w-full gap-2 overflow-x-auto border-b border-b-[1px] border-[#0003] border-b-[#1f1d1a]/20  py-1  md:overflow-hidden",children:[r.jsx("li",{children:r.jsx(t,{to:"/member/billing",end:!0,className:({isActive:e})=>` mr-6 block whitespace-nowrap border-b-[.375rem] py-2 pb-3   font-iowan text-[1.25rem] font-[700] leading-[1.5537rem] text-[#1f1d1a] transition-all duration-200 ease-out ${e?"border-b-[.375rem] border-b-[#1F1D1A]":"border-b-transparent"}`,children:"Plans"})}),r.jsx("li",{children:r.jsx(t,{to:"/member/billing/payment-details",className:({isActive:e})=>` mr-6 block whitespace-nowrap border-b-[.375rem] py-2 pb-3   font-iowan text-[1.25rem] font-[700] leading-[1.5537rem] text-[#1f1d1a] transition-all duration-200 ease-out ${e?"border-b-[.375rem] border-b-[#1F1D1A]":"border-b-transparent"}`,children:"Payment Details"})}),r.jsx("li",{children:r.jsx(t,{to:"/member/billing/invoices",className:({isActive:e})=>` mr-6 block whitespace-nowrap border-b-[.375rem] py-2 pb-3   font-iowan text-[1.25rem] font-[700] leading-[1.5537rem] text-[#1f1d1a] transition-all duration-200 ease-out ${e?"border-b-[.375rem] border-b-[#1F1D1A]":"border-b-transparent"}`,children:"Invoices"})})]}),r.jsx(a,{})]})};export{D as default};
