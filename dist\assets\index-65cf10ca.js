import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{o as E}from"./yup-c41d85d2.js";import{A as S,G as k,M as u,s as x,t as v}from"./index-46de5032.js";import{InteractiveButton2 as A}from"./InteractiveButton-060359e0.js";import{L as P}from"./index-6edcbb0d.js";import{T as F}from"./TwoFactorAuthenticate-b3dd6aab.js";import{r as e}from"./vendor-4cdf2bd1.js";import{u as I}from"./react-hook-form-9f4fcfa9.js";import{c as T,a as C}from"./yup-342a5df4.js";import{E as _,a as D}from"./EyeIcon-b7c71a85.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";function dt(){var d,p;const{dispatch:f}=e.useContext(S),{dispatch:r}=e.useContext(k);e.useState(!1);const[i,w]=e.useState(!1),[h,o]=e.useState(null),[l,n]=e.useState(!0),b=T({password:C().required("This field is required")}),{register:g,handleSubmit:j,setError:G,reset:L,formState:{errors:m,isSubmitting:c}}=I({resolver:E(b),defaultValues:{password:""}});async function N(s){try{await new u().updatePassword(s.password),x(r,"Password updated successfully")}catch(a){v(f,a.message),a.message!=="TOKEN_EXPIRED"&&x(r,a.message,5e3,"error")}}const y=async()=>{const s=new u;s.setTable("user");const a=localStorage.getItem("user");return await s.callRestAPI({id:a},"GET")};return e.useEffect(()=>{n(!0),y().then(s=>{s.model.two_factor_authentication===null?o(!1):s.model.two_factor_authentication===1&&o(!0),n(!1)})},[]),l?t.jsx(P,{}):t.jsx("div",{className:"p-5 px-4 pt-8 md:px-8",children:t.jsxs("div",{className:"w-full max-w-7xl ",children:[t.jsx("h3",{className:"text-xl ",children:"Password"}),t.jsx("p",{className:"font-iowan-regular  mt-1 text-base",children:"Update your account's password"}),t.jsxs("form",{onSubmit:j(N),className:"mt-8 flex flex-col gap-4 sm:flex-row sm:items-end",children:[t.jsxs("div",{className:"",children:[t.jsx("label",{className:"mb-2 block font-iowan text-base font-semibold capitalize capitalize text-[#1f1d1a]",children:"New Password"}),t.jsxs("div",{className:"relative w-[400px] max-w-full rounded-md",children:[t.jsx("input",{type:i?"text":"password",autoComplete:"off",...g("password"),className:`no-box-shadow h-[41.6px] w-[400px] max-w-full appearance-none  rounded-md border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none ${(d=m.password)!=null&&d.message?"border-red-500":""}`}),t.jsx("button",{className:"absolute right-2 translate-y-1/2",type:"button",onClick:()=>w(s=>!s),children:i?t.jsx(_,{className:"h-5"}):t.jsx(D,{className:"h-5"})})]}),t.jsx("p",{className:"text-field-error italic text-red-500",children:(p=m.password)==null?void 0:p.message})]}),t.jsx(A,{loading:c,disabled:c,type:"submit",className:" disabled:bg-disabledblack block h-[41.6px] w-[160px] whitespace-nowrap rounded-md bg-primary-black/90 px-3 py-1 text-center text-sm font-semibold text-white transition-colors duration-100",children:"Update Password"})]}),t.jsx("div",{className:"mt-8 max-w-[576px]",children:t.jsx(F,{active2FA:h,setActive2FA:o,loading:l})})]})})}export{dt as default};
