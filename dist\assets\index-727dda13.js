import{j as s}from"./@nextui-org/listbox-0f38ca19.js";import{o as k}from"./yup-c41d85d2.js";import{A as S,G as A,M as x,s as f,t as P}from"./index-46de5032.js";import{InteractiveButton2 as v}from"./InteractiveButton-060359e0.js";import{L as F}from"./index-6edcbb0d.js";import{T}from"./TwoFactorAuthenticate-b3dd6aab.js";import{r as e}from"./vendor-4cdf2bd1.js";import{u as C}from"./react-hook-form-9f4fcfa9.js";import{c as I,a as _}from"./yup-342a5df4.js";import{E as D,a as G}from"./EyeIcon-b7c71a85.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";function ps(){var p,u;const{dispatch:w,state:o}=e.useContext(S),{dispatch:i}=e.useContext(A);e.useState(!1);const[l,h]=e.useState(!1),[b,r]=e.useState(null),[n,m]=e.useState(!0),g=I({password:_().required("This field is required")}),{register:j,handleSubmit:N,setError:L,reset:R,formState:{errors:c,isSubmitting:d}}=C({resolver:k(g),defaultValues:{password:""}});async function y(t){try{await new x().updatePassword(t.password),f(i,"Password updated successfully")}catch(a){P(w,a.message),a.message!=="TOKEN_EXPIRED"&&f(i,a.message,5e3,"error")}}const E=async()=>{const t=new x;t.setTable("user");const a=o==null?void 0:o.user;return await t.callRestAPI({id:a},"GET")};return e.useEffect(()=>{m(!0),E().then(t=>{t.model.two_factor_authentication===null?r(!1):t.model.two_factor_authentication===1&&r(!0),m(!1)})},[]),n?s.jsx(F,{}):s.jsx("div",{className:"p-5 px-4 pt-8 md:px-8",children:s.jsxs("div",{className:"w-full max-w-7xl",children:[s.jsx("h3",{className:"text-xl",children:"Password"}),s.jsx("p",{className:"font-iowan-regular mt-1 text-base",children:"Update your account's password"}),s.jsxs("form",{onSubmit:N(y),className:"mt-8 flex flex-col gap-4 sm:flex-row sm:items-end",children:[s.jsxs("div",{className:"",children:[s.jsx("label",{className:"mb-2 block font-iowan text-base font-semibold capitalize capitalize text-[#1f1d1a]",children:"New Password"}),s.jsxs("div",{className:"relative w-[400px] max-w-full rounded-md",children:[s.jsx("input",{type:l?"text":"password",autoComplete:"off",...j("password"),className:`no-box-shadow h-[41.6px] w-[400px] max-w-full appearance-none  rounded-md border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none ${(p=c.password)!=null&&p.message?"border-red-500":""}`}),s.jsx("button",{className:"absolute right-2 translate-y-1/2",type:"button",onClick:()=>h(t=>!t),children:l?s.jsx(D,{className:"h-5"}):s.jsx(G,{className:"h-5"})})]}),s.jsx("p",{className:"text-field-error italic text-red-500",children:(u=c.password)==null?void 0:u.message})]}),s.jsx(v,{loading:d,disabled:d,type:"submit",className:" disabled:bg-disabledblack block h-[41.6px] w-[160px] whitespace-nowrap rounded-md bg-primary-black/90 px-3 py-1 text-center text-sm font-semibold text-white transition-colors duration-100",children:"Update Password"})]}),s.jsx("div",{className:"mt-8 max-w-[576px]",children:s.jsx(T,{active2FA:b,setActive2FA:r,loading:n})})]})})}export{ps as default};
