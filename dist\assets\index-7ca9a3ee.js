import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{A as D,G as F,M as P,aw as U,s as y,t as A,I as G}from"./index-46de5032.js";import{r as t,b as k}from"./vendor-4cdf2bd1.js";import{h as _}from"./moment-a9aaa855.js";import{o as M}from"./yup-c41d85d2.js";import{S as L}from"./SelectGroupType-3d2b3831.js";import{c as O,a as Y,d as R}from"./yup-342a5df4.js";import{u as $}from"./react-hook-form-9f4fcfa9.js";import{InteractiveButton2 as z}from"./InteractiveButton-060359e0.js";import{X as I}from"./XMarkIcon-cfb26fe7.js";import{t as f,S as h}from"./@headlessui/react-cdd9213e.js";import"./MkdCustomInput-af54c64d.js";import{L as B}from"./index-6edcbb0d.js";import{L as H}from"./index-23a711b5.js";import{D as Q}from"./DocumentTextIcon-54b5e200.js";import{D as X}from"./DocumentIcon-22c47322.js";import{B as K}from"./react-spinners-b860a5a3.js";import{u as V}from"./useGroups-d4ba52a9.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@hookform/resolvers-fad2f3d1.js";import"./CreateGroupModal-9562fe27.js";import"./XMarkIcon-6ed09631.js";import"./index-dc002f62.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";function W({updateRequest:s}){const[m,a]=t.useState(!1),{dispatch:x,state:b}=t.useContext(D),{dispatch:n}=t.useContext(F),[d,l]=t.useState(!1),i=k(),p=O({group_id:Y().required("This field is required"),members:R().min(1,"You must add at least one member").of(Y())});console.log(s);const{register:u,handleSubmit:v,setError:j,setValue:E,formState:{errors:S,isSubmitting:N},control:C,clearErrors:r,watch:T}=$({resolver:M(p)});async function g(o){console.log("hoo",o),j("group_id",{type:"manual",message:S.message});try{const c=new P;await c.callRawAPI(`/v4/api/records/updates/${s.update_id}`,{status:0,name:`Update ${_().format("MMM D, YYYY")}`,date:_().format("MMM D, YYYY")},"PUT"),c.setTable("recipient_group");const w=await c.callRestAPI({group_id:o.group_id,members:"",user_id:s==null?void 0:s.investor_id},"POST");console.log(w,"rresult"),console.log("po"),c.setTable("recipient_member"),c.callRestAPI({recipient_group_id:w.data,user_id:s.investor_id},"POST"),await c.callRawAPI("/v3/api/goodbadugly/customer/accept-or-reject",{status:U.ACCEPTED,request_id:s.update_request_id},"POST"),await c.callRawAPI("/v4/api/records/update_group",{update_id:s.update_id,group_id:w.data},"POST"),x({type:"REFETCH_REQUESTED_UPDATES"}),i(`/member/edit-updates/${s.update_id}`),a(!1),y(n,"Request accepted")}catch(c){A(x,c.message),c.message!=="TOKEN_EXPIRED"&&y(n,c.message,5e3,"error"),console.log(c)}l(!1)}return console.log(T("group_id")),e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"h-[180px] cursor-pointer border-[2px] border-black/60 p-3 text-[19px] font-bold leading-8 hover:shadow-lg sm:h-[120px]",onClick:()=>a(!0),children:e.jsxs("p",{children:["An existing recipient group ",e.jsx("br",{}),e.jsx("span",{className:"text-[13px] font-normal text-gray-800",children:"(10 max/mo)"})]})}),e.jsx(f,{appear:!0,show:m,as:t.Fragment,children:e.jsxs(h,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>a(!1),children:[e.jsx(f.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(f.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(h.Panel,{className:" w-full max-w-lg  transform rounded-md  bg-brown-main-bg p-5 text-left text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(h.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Select a group"}),e.jsx("button",{onClick:()=>a(!1),type:"button",children:e.jsx(I,{className:"h-6 w-6"})})]}),e.jsxs("form",{className:"mt-3 flex h-full max-h-[250px] flex-col justify-between gap-5",onSubmit:v(g),children:[e.jsx(L,{control:C,name:"group_id",setValue:o=>E("group_id",o),allowedRoles:["investor","stakeholder"]}),e.jsx(z,{type:"submit",loading:N,disabled:N,className:"disabled:bg-disabledblack h-[41.7px] rounded-lg bg-primary-black py-2 text-center font-semibold text-white transition-colors duration-100",children:"Select and Add"})]})]})})})})]})})]})}function J({updateRequest:s}){var T;const[m,a]=t.useState(!1),{dispatch:x,state:b}=t.useContext(D),{dispatch:n}=t.useContext(F);t.useState("");const d=k(),l=O({group_name:Y().required("This field is required"),members:R().min(1,"You must add at least one member").of(Y())}),{register:i,handleSubmit:p,setError:u,setValue:v,formState:{errors:j,isSubmitting:E},control:S,clearErrors:N,watch:C}=$({resolver:M(l)});async function r(g){console.log("hoo",g),u("group_name",{type:"manual",message:j.message});try{const o=new P;await o.callRawAPI(`/v4/api/records/updates/${s.update_id}`,{status:0,name:`Update ${_().format("MMM D, YYYY")}`,date:_().format("MMM D, YYYY")},"PUT");const c=await o.callRawAPI("/v4/api/records/group",{user_id:b.user,group_name:g==null?void 0:g.group_name,role:"NULL"},"POST");o.setTable("recipient_group");const w=await o.callRestAPI({group_id:c==null?void 0:c.data,members:"",user_id:b.user},"POST");o.setTable("recipient_member"),o.callRestAPI({recipient_group_id:w.data,user_id:s.investor_id},"POST"),await o.callRawAPI(`/v4/api/records/update_requests/${s.update_request_id}`,{status:U.ACCEPTED},"PUT"),await o.callRawAPI("/v4/api/records/update_group",{update_id:s.update_id,group_id:w==null?void 0:w.data},"POST"),x({type:"REFETCH_REQUESTED_UPDATES"}),d(`/member/edit-updates/${s.update_id}`),a(!1),y(n,"Request accepted")}catch(o){A(x,o.message),o.message!=="TOKEN_EXPIRED"&&y(n,o.message,5e3,"error"),console.log(o)}}return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"h-[180px] cursor-pointer border-[2px] border-black/60 p-3 text-[19px] font-bold leading-8 hover:shadow-lg sm:h-[120px]",onClick:()=>a(!0),children:e.jsxs("p",{className:"",children:["A new recipient group ",e.jsx("br",{}),e.jsx("span",{className:"text-[13px] font-normal text-gray-800",children:"(10 max/mo)"})]})}),e.jsx(f,{appear:!0,show:m,as:t.Fragment,children:e.jsxs(h,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>a(!1),children:[e.jsx(f.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(f.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(h.Panel,{className:" w-full max-w-lg  transform rounded-md  bg-brown-main-bg p-5 text-left text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(h.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Create new group"}),e.jsx("button",{onClick:()=>a(!1),type:"button",children:e.jsx(I,{className:"h-6 w-6"})})]}),e.jsxs("form",{className:"mt-3 flex h-full max-h-[250px] flex-col justify-between gap-5",onSubmit:p(r),children:[e.jsxs("div",{className:"mt-8",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Group name"}),e.jsx("input",{className:"focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ",onChange:g=>v("group_name",g.target.value),placeholder:"New group name"}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(T=j==null?void 0:j.group_name)==null?void 0:T.message})]}),e.jsx(z,{type:"submit",loading:E,disabled:E,className:"disabled:bg-disabledblack h-[41.7px] rounded-lg bg-primary-black py-2 text-center font-semibold text-white transition-colors duration-100",children:"Create and Add"})]})]})})})})]})})]})}function Z({isOpen:s,closeModal:m,updateRequest:a,groups:x,showModifyRecentOption:b,isInvestorInGroups:n,investorGroupId:d}){const{dispatch:l,state:i}=t.useContext(D),{dispatch:p}=t.useContext(F),[u,v]=t.useState(!1),j=k(),E=async()=>{v(!0);try{const r=new P;await r.callRawAPI(`/v4/api/records/updates/${a.update_id}`,{status:0,name:`Update ${_().format("MMM D, YYYY")}`,date:_().format("MMM D, YYYY")},"PUT"),n&&d&&await r.callRawAPI("/v4/api/records/update_group",{update_id:a.update_id,group_id:d},"POST"),l({type:"REFETCH_REQUESTED_UPDATES"}),localStorage.setItem("requested_update_id",a.update_id),j("/member/select-template"),m(),y(p,"Request accepted")}catch(r){A(l,r.message),y(p,r.message,5e3,"error")}v(!1)},S=async()=>{try{const r=new P,T=await r.callRawAPI(`/v4/api/records/updates/${a.update_id}`,{status:0,name:`Update ${_().format("MMM D, YYYY")}`,date:_().format("MMM D, YYYY")},"PUT");n&&d&&await r.callRawAPI("/v4/api/records/update_group",{update_id:a.update_id,group_id:d},"POST"),l({type:"REFETCH_REQUESTED_UPDATES"}),j(`/member/edit-updates/${a.update_id}?autofocus=true`),setOpen(!1),y(p,"Request accepted")}catch{A(l,err.message),err.message!=="TOKEN_EXPIRED"&&y(p,err.message,5e3,"error")}};async function N(){v(!0);try{const r=new P;await r.callRawAPI(`/v4/api/records/updates/${a.update_id}`,{status:0,name:`Update ${_().format("MMM D, YYYY")}`,date:_().format("MMM D, YYYY")},"PUT");const g=(await r.callRawAPI(`/v4/api/records/updates?filter=id,lt,${a.update_id}&page=1,1&order=id,desc`,void 0,"GET")).list[0];g&&await C(g.id,a.update_id),n&&d&&await r.callRawAPI("/v4/api/records/update_group",{update_id:a.update_id,group_id:d},"POST"),l({type:"REFETCH_REQUESTED_UPDATES"}),m(),j(`/member/edit-updates/${a.update_id}?autofocus=true`)}catch(r){A(l,r.message),r.message!=="TOKEN_EXPIRED"&&y(p,r.message,5e3,"error")}v(!1)}async function C(r,T){const g=new P,{list:o}=await g.callRawAPI(`/v4/api/records/notes?filter=update_id,eq,${r}&order=id,asc`,void 0,"GET");for(let c=0;c<o.length;c++){const w=o[c];await g.callRawAPI("/v4/api/records/notes",{update_id:T,content:w.content,type:w.type,status:0},"POST")}}return e.jsx(f,{appear:!0,show:s,as:t.Fragment,children:e.jsxs(h,{as:"div",className:"relative z-[50]",onClose:m,children:[e.jsx(f.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(f.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(h.Panel,{className:"relative w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg text-left align-middle text-base shadow-xl transition-all",children:[u?e.jsx("div",{className:"absolute inset-0 z-10 flex items-center justify-center",children:e.jsx(H,{})}):null,e.jsxs("div",{className:"divide-y divide-[#1f1d1a]/10",children:[b?e.jsxs("button",{className:"flex w-full items-start gap-4 px-6 py-4 hover:bg-brown-main-bg",onClick:N,disabled:u,children:[e.jsx("img",{src:"/signature.png",alt:"",className:"h-8 w-8 object-cover"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-left text-xl font-semibold",children:"Modify Most Recent"}),e.jsx("p",{className:"mt-1 font-medium",children:"Start with most recent sent update"})]})]}):null,e.jsxs("button",{className:"flex w-full items-start gap-4 px-6 py-4 hover:bg-brown-main-bg",onClick:E,disabled:u,children:[e.jsx(Q,{className:"h-8 w-8 text-primary-black",strokeWidth:2}),e.jsxs("div",{children:[e.jsx("p",{className:"text-left text-xl font-semibold",children:"New Template Update"}),e.jsx("p",{className:"mt-1 font-medium",children:"Start an update using the default template"})]})]}),e.jsxs("button",{className:"flex w-full items-start gap-4 px-6 py-4 hover:bg-brown-main-bg",onClick:S,disabled:u,children:[e.jsx(X,{className:"h-8 w-8 text-primary-black",strokeWidth:2}),e.jsxs("div",{children:[e.jsx("p",{className:"text-left text-xl font-semibold",children:"New Blank Update"}),e.jsx("p",{className:"mt-1 font-medium",children:"Start an update from scratch"})]})]})]})]})})})})]})})}function q({updateRequest:s,groups:m}){const[a,x]=t.useState(!1);console.log(s),t.useContext(D),t.useContext(F),t.useState(!1);const[b,n]=t.useState(!1),[d,l]=t.useState(!1),[i,p]=t.useState(null);k(),t.useEffect(()=>{if(console.log("skjsjsjjs"),console.log(m,s),l(!0),m&&s){let j=null;m.some(S=>{const N=S.members.some(C=>C.id===s.investor_id.toString());return N&&(j=S),N})?(console.log("Yes, investor is in existing groups"),n(!0),p(j.id)):(n(!1),p(null),console.log("No, investor is not in existing groups"))}l(!1)},[m,s]),console.log(d);const[u,v]=t.useState(!1);return e.jsxs(e.Fragment,{children:[e.jsx(Z,{isOpen:u,closeModal:()=>v(!1),updateRequest:s,groups:m,isInvestorInGroups:b,investorGroupId:i,showModifyRecentOption:!0}),e.jsx("button",{className:"rounded-lg bg-black px-6 py-2 font-iowan font-medium text-white",onClick:async()=>{b?(l(!0),v(!0),l(!1)):x(!0)},children:d?e.jsx(K,{size:10,color:"white"}):e.jsx("span",{children:"Accept"})}),e.jsx(f,{appear:!0,show:a,as:t.Fragment,children:e.jsxs(h,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>x(!1),children:[e.jsx(f.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(f.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(h.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base  transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(h.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Add this fund manager to:"}),e.jsx("button",{onClick:()=>x(!1),type:"button",children:e.jsx(I,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx(W,{updateRequest:s}),e.jsx(J,{updateRequest:s})]})]})})})})]})})]})}function ee({updateRequest:s}){const[m,a]=t.useState(!1),{dispatch:x}=t.useContext(D),{dispatch:b}=t.useContext(F),[n,d]=t.useState(!1);async function l(){d(!0);try{await new P().callRawAPI("/v3/api/goodbadugly/customer/accept-or-reject",{status:U.ACCEPTED,request_id:s.update_request_id},"POST"),x({type:"REFETCH_REQUESTED_UPDATES"}),a(!1),y(b,"Request denied")}catch(i){A(x,i.message),i.message!=="TOKEN_EXPIRED"&&y(b,i.message,5e3,"error")}d(!1)}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"rounded-lg bg-red-500 px-6 py-2 font-iowan font-medium text-white",onClick:()=>a(!0),children:"Deny"}),e.jsx(f,{appear:!0,show:m,as:t.Fragment,children:e.jsxs(h,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>a(!1),children:[e.jsx(f.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(f.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(h.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(h.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Are you sure"}),e.jsx("button",{onClick:()=>a(!1),type:"button",children:e.jsx(I,{className:"h-6 w-6"})})]}),e.jsx("p",{className:"mt-2",children:"Are you sure you want to deny this request?"}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>a(!1),children:"Cancel"}),e.jsx(G,{loading:n,disabled:n,onClick:l,className:"rounded-lg bg-red-500 py-2 text-center font-semibold text-white transition-colors duration-100 disabled:bg-opacity-60",children:"Yes, Decline"})]})]})})})})]})})]})}function Ge(){var d,l;const{state:s}=t.useContext(D),{groups:m,loading:a}=V(),{dispatch:x}=t.useContext(F);t.useEffect(()=>{x({type:"SETPATH",payload:{path:"requested_updates"}})},[]),console.log((d=s==null?void 0:s.requested_updates)==null?void 0:d.list);const b=(l=s==null?void 0:s.requested_updates)==null?void 0:l.list.reduce((i,p)=>{const u=p.investor_id;return i[u]?(i[u].request_count+=1,i[u].update_request_id>p.update_request_id&&(i[u]={...p,request_count:i[u].request_count})):i[u]={...p,request_count:1},i},{}),n=Object.values(b);return console.log(n),a?e.jsx(B,{}):e.jsxs("div",{className:"px-5 pt-8 md:px-8",children:[e.jsx("h2",{className:"my-4 text-[20px] font-[600] sm:text-[24px] md:text-[28px] ",children:"Requested Updates"}),e.jsx("div",{className:"mt-6 grid grid-cols-1 gap-5 bg-brown-main-bg pb-12 sm:grid-cols-2 md:grid-cols-2 xl:grid-cols-4",children:n==null?void 0:n.map(i=>e.jsxs("div",{className:"relative h-full min-h-[9.475rem] w-full max-w-[250px]",children:[e.jsx("p",{className:"absolute right-[-8px] top-[-4px] z-10 flex h-5 w-5 items-center justify-center rounded-[50%] bg-black text-[12px] text-white",children:i.request_count}),e.jsx("div",{className:"group  relative flex h-full min-h-[9.475rem] max-w-[250px] items-center justify-center truncate rounded-[.625rem] border border-[#0003] bg-brown-main-bg p-5 shadow-md",children:e.jsxs("div",{className:"flex flex-col items-center justify-center gap-2",children:[e.jsx("p",{className:"font-iowan text-lg font-semibold sm:text-xl",children:"New update request"}),e.jsx("p",{className:"font-900 font-regular mt-2 whitespace-nowrap text-lg font-bold capitalize text-black sm:text-xl",children:i.display_name||"Investor Name"}),e.jsxs("p",{className:`font whitespace-nowrap px-6 py-4 capitalize ${!i.investor_first_name&&!i.investor_last_name&&"invisible"}`,children:[i.investor_first_name," ",i.investor_last_name,!i.investor_first_name&&!i.investor_last_name&&"invisible"]}),e.jsxs("div",{className:"mt-4 flex gap-2 sm:gap-4",children:[e.jsx(q,{updateRequest:i,groups:m}),e.jsx(ee,{updateRequest:i})]})]})})]},i.update_request_id))})]})}export{Ge as default};
