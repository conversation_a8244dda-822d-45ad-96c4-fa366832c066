import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{o as P}from"./yup-c41d85d2.js";import{A as S,G as v,M as l,s as c,t as A}from"./index-46de5032.js";import{InteractiveButton2 as E}from"./InteractiveButton-060359e0.js";import"./index-6edcbb0d.js";import{M as p}from"./index-36531d2b.js";import{T as k}from"./TwoFactorAuthenticate-b3dd6aab.js";import{r as s}from"./vendor-4cdf2bd1.js";import{u as T}from"./react-hook-form-9f4fcfa9.js";import{c as y,a as u,e as F}from"./yup-342a5df4.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";function me(){const{dispatch:f,state:x}=s.useContext(S),{dispatch:o}=s.useContext(v);s.useState(!1),s.useState(!1);const[w,a]=s.useState(null),[h,i]=s.useState(!0),b=y({password:u().required("This field is required"),confirm_password:u().oneOf([F("password"),null],"Password must match").required("This field is required")}),{register:m,handleSubmit:g,setError:_,reset:C,formState:{errors:n,isSubmitting:d}}=T({resolver:P(b),defaultValues:{password:""}});async function j(t){try{await new l().updatePassword(t.password),c(o,"Password updated successfully")}catch(r){A(f,r.message),r.message!=="TOKEN_EXPIRED"&&c(o,r.message,5e3,"error")}}const N=async()=>{const t=new l;t.setTable("user");const r=x.user;return await t.callRestAPI({id:r},"GET")};return s.useEffect(()=>{i(!0),N().then(t=>{t.model.two_factor_authentication===null?a(!1):t.model.two_factor_authentication===1&&a(!0),i(!1)})},[]),e.jsx("div",{className:"px-4 md:px-8",children:e.jsxs("div",{className:"w-full max-w-7xl",children:[e.jsx("div",{className:"mb-2 font-iowan text-[20px] font-[700] md:text-[1.5rem] md:leading-[1.865rem] ",children:"Password"}),e.jsx("p",{className:"font-iowan-regular mt-3 text-[16px] font-[400] leading-[1.21rem]",children:"Update your password"}),e.jsx("form",{onSubmit:g(j),className:"mt-6 flex flex-col gap-4 sm:flex-row sm:items-end",children:e.jsxs("div",{className:"flex w-full flex-col gap-[1rem]",children:[e.jsx("div",{className:"sm:w-[45%]",children:e.jsx(p,{name:"password",label:"New Password",errors:n,register:m,className:"h-[2.75rem]  w-full appearance-none  !rounded-[.125rem] border border-[#1f1d1a] bg-transparent text-sm font-normal text-[#1f1d1a] "})}),e.jsx("div",{className:"sm:w-[45%]",children:e.jsx(p,{name:"confirm_password",label:"Confirm Password",errors:n,register:m,className:"h-[2.75rem]  w-full appearance-none  !rounded-[.125rem] border border-[#1f1d1a] bg-transparent text-sm font-normal text-[#1f1d1a] "})}),e.jsx(E,{loading:d,disabled:d,type:"submit",className:"disabled:bg-disabledblack mt-3 block h-[2.6rem] w-[10rem] whitespace-nowrap rounded-[.125rem] bg-black px-3 py-1 text-center font-iowan text-sm font-semibold text-white transition-colors duration-100",children:"Update Password"})]})}),e.jsx("div",{className:"mt-8 hidden max-w-[576px]",children:e.jsx(k,{active2FA:w,setActive2FA:a,loading:h})})]})})}export{me as default};
