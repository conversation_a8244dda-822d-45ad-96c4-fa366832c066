import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{i as c,r}from"./vendor-4cdf2bd1.js";import{h as i}from"./moment-a9aaa855.js";import{G as x,m as h,z as u,H as f}from"./index-46de5032.js";import{u as w}from"./useNotes-5360b2a2.js";import{u as j}from"./useUpdate-d2d686a3.js";import{u as N}from"./useUpdateRequests-ef7a8c19.js";import{u as b}from"./useUpdateQuestions-0b25f483.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const y=[{header:"Name"},{header:"Opened"},{header:"Viewed"},{header:"Shared"},{header:"Last viewed at"}],v=[{header:"Question"},{header:"Response"}];function z(){const{id:t}=c(),{update:d}=j(t),{notes:n}=w(t),{updateRequests:l}=N(t),{questions:m}=b(t),{dispatch:p}=r.useContext(x);return r.useEffect(()=>{p({type:"SETPATH",payload:{path:"updates"}})},[]),e.jsx("div",{className:"flex flex-wrap-reverse items-end justify-between gap-4 p-5",children:e.jsxs("div",{className:"w-full max-w-6xl flex-grow",children:[e.jsxs("h1",{className:"text-4xl font-medium",children:[d.name," Insights"]}),e.jsxs("div",{className:"mt-6 flex items-center justify-between",children:[e.jsx("p",{className:"font-medium",children:"Report Date"}),e.jsx("p",{children:i(d.date).format("MMM DD YYYY")})]}),e.jsxs("div",{className:"mt-6 flex items-center justify-between",children:[e.jsx("p",{className:"font-medium",children:"Report Status"}),e.jsx("p",{children:h[d.status]})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("p",{className:"font-medium",children:"People who viewed it"}),e.jsxs("table",{className:"mt-4 min-w-full divide-y divide-[#1f1d1a]/10 border",children:[e.jsx("thead",{children:e.jsx("tr",{children:y.map((s,a)=>e.jsx("th",{scope:"col",className:"font  whitespace-nowrap border-b-[#1f1d1a]/10  px-4 text-left font-[700] md:border-0 md:border-b-[3px] md:border-dashed md:px-6 md:py-3",children:s.header},a))})}),e.jsx("tbody",{className:"font-iowan-regular divide-y divide-[#1f1d1a]/10",children:l.map((s,a)=>e.jsxs("tr",{className:"  md:h-[60px]",children:[e.jsx("td",{className:"whitespace-nowrap px-3 md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:s.user.first_name+" "+s.user.last_name}),e.jsx("td",{className:"whitespace-nowrap px-3 md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:s.view_count==0?"No":"Yes"}),e.jsx("td",{className:"whitespace-nowrap px-3 md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:s.view_count==0?"No":"Yes"}),e.jsx("td",{className:"whitespace-nowrap px-3 md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:s.view_count}),e.jsx("td",{className:"whitespace-nowrap px-3 md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:s.view_count==0?"N/A":i(s.update_at).format("DD MMM, hh:mm a")})]},a))})]})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("p",{className:"font-medium",children:"Asks"}),e.jsxs("table",{className:"mt-4 min-w-full divide-y divide-[#1f1d1a]/10 border",children:[e.jsx("thead",{children:e.jsx("tr",{children:v.map((s,a)=>e.jsx("th",{scope:"col",className:"font  whitespace-nowrap border-b-[#1f1d1a]/10  px-4 text-left font-[700] md:border-0 md:border-b-[3px] md:border-dashed md:px-6 md:py-3",children:s.header},a))})}),e.jsx("tbody",{className:"font-iowan-regulardivide-y divide-[#1f1d1a]/10",children:m.map((s,a)=>e.jsxs("tr",{className:"  md:h-[60px]",children:[e.jsx("td",{className:"whitespace-nowrap px-3 md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:s.question}),e.jsx("td",{className:"whitespace-nowrap px-3 md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:s.reply_text})]},a))})]}),m.length==0?e.jsx("p",{className:"my-8 text-center",children:"No questions was asked"}):""]}),n.map(s=>{const{blocks:a}=u(s.content,{blocks:[]}),o=f(a);return e.jsxs("div",{className:"mt-6 flex items-start justify-between",children:[e.jsx("p",{className:"font-medium",children:s.type}),s.content?e.jsx("p",{className:"max-w-lg",dangerouslySetInnerHTML:{__html:o}}):"N/A"]})})]})})}export{z as default};
