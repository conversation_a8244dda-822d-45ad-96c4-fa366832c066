import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as s,i as J,u as Q,b as Z,h as ee}from"./vendor-4cdf2bd1.js";import{u as te}from"./react-hook-form-9f4fcfa9.js";import{M as z,A as M,G as B,I,s as T,t as ae,L as se,l as ie}from"./index-46de5032.js";import{o as ne}from"./yup-c41d85d2.js";import{c as le,a as C}from"./yup-342a5df4.js";import{u as re}from"./useUpdates-13e2f9af.js";import{h as o}from"./moment-a9aaa855.js";import{X as de}from"./XMarkIcon-cfb26fe7.js";import{t as h,S,L as P}from"./@headlessui/react-cdd9213e.js";import"./react-scroll-9384d626.js";import{A as ce}from"./AddTime-a07c6c16.js";import{R as oe}from"./tableWrapper-ca490fb1.js";import E from"./Loader-24da96b3.js";import{P as me}from"./index-3283c9b7.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@hookform/resolvers-fad2f3d1.js";import"./index.esm-7add6cfb.js";import"./react-icons-36ae72b7.js";import"./index-23a711b5.js";new z;function R({refetch:b,reject:m,row:r}){const[F,d]=s.useState(!1),{dispatch:f}=s.useContext(M),{dispatch:i}=s.useContext(B),[c,a]=s.useState(!1);J();const n=()=>{d(!0)};async function p(_,k){a(!0);try{await new z().callRawAPI(`/v3/api/goodbadugly/customer/fund-update-requests?page=1&limit=10&request=${_}&update_id=${k}`),d(!1),T(i,"Accepted"),b()}catch(x){ae(f,x.message),x.message!=="TOKEN_EXPIRED"&&T(i,x.message,5e3,"error")}setUpdating(!1)}const y=`Are you sure you want to accept this update from ${r.company_name}?`,j=`Are you sure you want to reject this update from ${r.company_name}?`,g=m?j:y,v=`If accepted, ${r.company_name} will be able to send and you will be able to request updates moving forward.`,N=`If rejected, ${r.company_name} will be prohibited from sending you updates and you will be prohibited from requesting updates moving forward.`,w=m?N:v;return e.jsxs(e.Fragment,{children:[m?e.jsx("div",{className:" flex w-[110px] cursor-pointer flex-row items-center justify-center bg-red-500 font-medium text-white ",onClick:()=>n(),children:"Reject"}):e.jsx("div",{className:" flex w-[110px] cursor-pointer flex-row items-center justify-center bg-black font-medium text-white ",onClick:()=>n(),children:"Accept"}),e.jsx(h,{appear:!0,show:F,as:s.Fragment,children:e.jsxs(S,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>d(!1),children:[e.jsx(h.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(h.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(S.Panel,{className:"flex h-[300px] w-full max-w-[600px] transform flex-col justify-between overflow-hidden rounded-md bg-[#f2dfce] p-6 text-left align-middle  shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between gap-4",children:[e.jsx(S.Title,{as:"h3",className:"text-[16px] font-medium  leading-7 text-gray-900 sm:text-[20px]",children:g}),e.jsx("button",{onClick:()=>d(!1),type:"button",children:e.jsx(de,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"flex w-full flex-row items-center justify-between gap-3",children:[m?e.jsx(I,{loading:c,disabled:c,onClick:()=>p(2,r.update_id),className:" disabled:bg-disabledblack h-[70px] w-full max-w-[200px] bg-red-500 py-2 text-center text-[20px] font-bold text-white transition-colors duration-100",children:"Yes, reject"}):e.jsx(I,{loading:c,disabled:c,onClick:()=>p(1,r.update_id),className:" disabled:bg-disabledblack h-[70px] w-full max-w-[200px] bg-black py-2 text-center text-[20px] font-bold text-white transition-colors duration-100",children:"Yes, accept"}),e.jsx("p",{children:w})]}),e.jsxs("div",{className:"flex flex-row justify-between",children:[e.jsx("div",{className:"cursor-pointer text-[17px] text-red-500",children:"No, reject this update →"}),e.jsx("div",{className:"cursor-pointer text-[17px] text-blue-600",onClick:()=>d(!1),children:"Close"})]})]})})})})]})})]})}const pe=[{header:"Company Name",accessor:"company_name"},{header:"Update URL",accessor:"update_url"},{header:"Date received",accessor:"sent_at"},{header:"Availability",accessor:"availability"},{header:"Action",accessor:""}],Ge=()=>{var L,U;Q();const[b,m]=s.useState(""),[r,F]=s.useState("");s.useContext(M);const{dispatch:d}=s.useContext(B),f=Z(),[i,c]=ee(),{updates:a,loading:n,refetch:p,currentPage:y,pageCount:j,pageSize:g,updatePageSize:v,previousPage:N,nextPage:w,canPreviousPage:_,canNextPage:k}=re(b,r),[x,q]=s.useState(!1);s.useState(!1);const Y=le({company_name:C(),status:C(),availability:C()}),{register:O,handleSubmit:H,setError:xe,reset:X,formState:{errors:A}}=te({resolver:ne(Y),defaultValues:async()=>{const t=i.get("company_name")??"",l=i.get("status")??"",u=i.get("availability")??"";return{company_name:t,status:l,availability:u}}});s.useEffect(()=>{d({type:"SETPATH",payload:{path:"update_requests"}})},[]);function G(t){i.set("company_name",t.company_name),i.set("status",t.status),i.set("availability",t.availability),c(i)}const K=window.location.href,V=new URL(K).origin,W=t=>{navigator.clipboard.writeText(V+t),T(d,"Link Copied")},D=a==null?void 0:a.filter(t=>t.request==0);return e.jsx(e.Fragment,{children:n?e.jsx(E,{}):e.jsx(e.Fragment,{children:e.jsxs("div",{className:"p-5 pt-8 rounded bg-brown-main-bg md:px-8",children:[e.jsx("div",{className:"flex justify-between mb-3 w-full item-center",children:e.jsx("h4",{className:"text-left text-[16px] font-[600] sm:text-[20px]",children:"Search"})}),e.jsxs("form",{onSubmit:H(G),className:"flex flex-col gap-4",children:[e.jsx("div",{className:"flex flex-wrap gap-4 w-full",children:e.jsxs("div",{className:"w-full sm:w-auto",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize  text-[#1f1d1a]",children:"Company name"}),e.jsx("input",{type:"text",...O("company_name"),className:`focus:shadow-outline w-full appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal leading-tight text-[#1f1d1a] shadow   focus:outline-none sm:w-[180px] ${(L=A.company_name)!=null&&L.message?"border-red-500":""}`}),e.jsx("p",{className:"italic text-red-500 text-field-error",children:(U=A.company_name)==null?void 0:U.message})]})}),e.jsxs("div",{className:"flex row-start-3 gap-4 items-center sm:row-start-auto",children:[e.jsx("button",{type:"submit",disabled:n,className:"px-4 py-1 font-semibold text-white rounded-md font-iowan-regular bg-primary-black/80 hover:bg-primary-black",children:"Search"}),e.jsx("button",{type:"button",onClick:()=>{X({company_name:"",status:"",availability:""}),i.delete("company_name"),i.delete("status"),i.delete("availability"),c(i)},disabled:n,className:"rounded-md px-4 py-1 font-semibold text-[#1f1d1a]",children:"Clear"})]})]}),e.jsxs("div",{className:"p-5 px-0 mt-8 w-full rounded",children:[e.jsx("div",{className:"flex justify-between mb-6 w-full text-center",children:e.jsx("div",{className:"flex flex-row justify-between w-full",children:e.jsxs("div",{className:"text-left text-[16px] font-[600] sm:text-[20px]",children:["Updates(",D?D.length:null,")"]})})}),console.log(a),e.jsx("div",{className:`${n?"":"custom-overflow overflow-x-auto"}`,children:n?e.jsx("div",{className:"flex justify-center items-center py-5 max-w-full max-h-fit min-h-fit min-w-fit",children:e.jsx(E,{size:50})}):e.jsx(e.Fragment,{children:e.jsx(oe,{children:e.jsxs("table",{className:"min-w-full divide-y divide-[#1f1d1a]/10",children:[e.jsx("thead",{className:"",children:e.jsx("tr",{children:pe.map((t,l)=>e.jsx("th",{scope:"col",className:"font  whitespace-nowrap border-b-[#1f1d1a]/10  px-4 text-left font-[700] md:border-0 md:border-b-[3px] md:border-dashed md:px-6 md:py-3",children:t.header},l))})}),e.jsx("tbody",{className:"font-iowan-regular  divide-y divide-[#1f1d1a]/10",children:a==null?void 0:a.toReversed().map(t=>{var $;($=t==null?void 0:t.public_url)==null||$.split("/");const l=o(t.sent_at).add(t.recipient_access,"days").toDate()<new Date,u=t.sent_at?o(t.sent_at).add(t.recipient_access??0,"days").diff(o(),"days"):0;return e.jsxs("tr",{cl:!0,children:[e.jsx("td",{className:"relative whitespace-nowrap px-3 md:max-w-lg md:whitespace-normal md:px-6 md:py-6 md:pb-[50px]",children:e.jsx(P,{className:"relative",children:({open:fe})=>e.jsxs(e.Fragment,{children:[e.jsx(P.Button,{as:"div",className:"max-w-[300px] cursor-auto",onMouseEnter:()=>q(t.id),onMouseLeave:()=>q(!1),children:t.company_name}),e.jsx(h,{as:s.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",className:"absolute z-[999999999999999999] w-[300px]",children:e.jsx(P.Panel,{className:"hidden px-4",children:e.jsx("div",{className:" hidden    rounded-lg bg-[#1f1d1a] p-4 px-4 text-white shadow-lg ring-1 ring-[#1f1d1a]/5 md:block",children:e.jsxs("div",{className:"flex flex-col  gap-2 text-[14px] font-medium",children:[e.jsx("span",{className:"text-[17px]",children:t.company_name}),e.jsx("p",{className:"line-clamp-5 overflow-ellipsis text-[13px] text-gray-300",children:t.description})]})})})})]})},t.id)}),e.jsx("td",{className:"px-6 py-6 whitespace-nowrap disabled:cursor-not-allowed disabled:text-gray-400 md:max-w-lg md:whitespace-normal",children:e.jsx("button",{className:"underline cursor-pointer underline-offset-6 font-iowan disabled:cursor-not-allowed disabled:text-gray-400",onClick:()=>{f(t.update_link)},disabled:(l||t.request===0)&&t.is_requested!==1,children:t.update_name.startsWith("Update ")&&t.update_name.slice(7).match(/^\d{4}-\d{2}-\d{2}$/)?`Update ${new Date(t.update_name.slice(7)).toLocaleString("en-US",{month:"short",day:"2-digit",year:"numeric"}).replace(/,/g,",")}`:t.update_name})}),e.jsx("td",{className:"px-3 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:o(t.sent_at).format("MMM DD, YYYY")}),e.jsx("td",{className:"px-3 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:l?e.jsx("span",{children:"0 hrs / 0 minutes (update expired)"}):e.jsxs("span",{children:[u<1?"":`${u} days,  `,t.sent_at?o(t.sent_at).add(t.recipient_access,"days").diff(o().add(u,"days"),"hours"):0," ","hrs"]})}),e.jsx("td",{className:"flex flex-col items-start justify-center gap-4 px-6 py-4 md:max-w-[500px]",children:e.jsxs("div",{className:"flex flex-row gap-4 items-start md:justify-center",children:[e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx("button",{className:"flex cursor-pointer justify-start font-medium text-[#292829fd] underline hover:underline disabled:cursor-not-allowed disabled:text-gray-400",onClick:()=>{f(t.update_link)},disabled:(l||t.request===0)&&t.is_requested!==1,children:e.jsx("span",{children:"View"})}),e.jsx("button",{className:"flex cursor-pointer justify-start px-0 font-medium text-[#292829fd] underline hover:underline disabled:cursor-not-allowed disabled:text-gray-400",onClick:()=>{W(`${t.update_link}`)},disabled:(l||t.request===0||t.public_link_enabled==0)&&!t.is_requested,children:e.jsx("span",{children:"Share"})}),l?e.jsx("button",{className:"flex cursor-pointer justify-start  font-medium text-[#292829fd] underline hover:underline disabled:text-[#1f1d1a]",onClick:()=>{},children:e.jsx("span",{className:"text-green-500",children:e.jsx(ce,{data:t})})}):null]}),t.request===0&&t.is_requested!==1&&e.jsx(e.Fragment,{children:e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx(R,{refetch:p,row:t}),e.jsx(R,{reject:!0,refetch:p,row:t})]})})]})})]},t.id)})})]})})})}),(a==null?void 0:a.length)==0?e.jsxs("div",{className:"mb-[20px] mt-24 flex flex-col items-center",children:[e.jsx(se,{children:e.jsx(ie,{fill:"black",className:"!h-[5rem] !w-[5rem]"})}),e.jsx("p",{className:"mt-4 text-base font-medium text-center",children:"No Company Updates"})]}):null,(a==null?void 0:a.length)>0&&e.jsx(me,{currentPage:y,pageCount:j,pageSize:g,canPreviousPage:_,canNextPage:k,updatePageSize:v,previousPage:N,nextPage:w,dataLoading:n})]})]})})})};export{Ge as default};
