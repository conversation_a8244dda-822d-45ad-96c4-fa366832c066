import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{G as i,A as m}from"./index-46de5032.js";import{r as e,j as p,O as n}from"./vendor-4cdf2bd1.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";function A(){const{dispatch:r}=e.useContext(i),{state:o}=e.useContext(m);return e.useEffect(()=>{r({type:"SETPATH",payload:{path:"member"}})},[]),o.profile.email?t.jsxs("div",{children:[t.jsx("h3",{className:"mt-6 px-4 text-3xl font-normal",children:o.company.name}),t.jsx("ul",{className:"mt-8 flex border-y border-[#0003] px-6 py-1",children:t.jsx("li",{children:t.jsx(p,{to:"/fundmanager/fund-profile/",end:!0,className:({isActive:a})=>`font block whitespace-nowrap rounded-md bg-[#1f1d1a] px-6 py-2 font-medium text-white  ${a?"bg-[#1f1d1a] text-white":"text-[#1f1d1a]"}`,children:"Fund Profile"})})}),t.jsx(n,{})]}):null}export{A as default};
