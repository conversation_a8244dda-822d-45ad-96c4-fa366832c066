import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as o,L as E,h as D,b as U}from"./vendor-4cdf2bd1.js";import{u as F}from"./react-hook-form-9f4fcfa9.js";import{L as M,a3 as A,A as L,G as P,M as R,t as z,s as I,a4 as k}from"./index-46de5032.js";import{o as q}from"./yup-c41d85d2.js";import{c as H,a as $}from"./yup-342a5df4.js";import{h as u}from"./moment-a9aaa855.js";import"./react-scroll-9384d626.js";import{R as Y}from"./tableWrapperDashboard-6c11f374.js";import{L as g,t as T}from"./@headlessui/react-cdd9213e.js";import C from"./Loader-24da96b3.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@hookform/resolvers-fad2f3d1.js";import"./index.esm-7add6cfb.js";import"./react-icons-36ae72b7.js";import"./index-23a711b5.js";const V=[{header:"Title",accessor:"title"},{header:"Date/Time",accessor:"time/date"},{header:"Comments",accessor:"comment"}],G=({data:i,showTitle:y=!0})=>{var b,n,p;o.useState(5),o.useState(!1),(b=i==null?void 0:i.comments)==null||b.filter(t=>t.comment==="");const f=({row:t})=>{var j;const[l,x]=o.useState(!1),[h,m]=o.useState(null);return e.jsx("div",{className:"relative",onMouseEnter:()=>x(!0),onMouseLeave:()=>x(!1),children:e.jsxs("div",{className:"flex -space-x-2",children:[t.comments.slice(0,3).map((a,r)=>e.jsx(g,{className:"relative",children:({open:c})=>e.jsxs(e.Fragment,{children:[e.jsx(g.Button,{as:"div",className:"object-cover relative w-8 h-8 rounded-full cursor-pointer",onMouseEnter:()=>m(a.comment_id),onMouseLeave:()=>m(null),children:e.jsx("img",{src:"/default.png",className:"object-cover w-8 h-8 rounded-full",alt:"default"})}),e.jsx(T,{as:o.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",show:h===a.comment_id,className:"absolute z-50 max-w-[300px]",children:e.jsx(g.Panel,{className:"px-4",children:e.jsx("div",{className:"px-4 py-1 text-white bg-black rounded-lg ring-1 shadow-lg ring-black/5",children:e.jsxs("div",{className:"flex flex-col gap-2 text-[10px] font-medium",children:[e.jsx("p",{className:"font-semibold",children:a.first_name?a.first_name:"  "+a.last_name?a.last_name:""}),e.jsx("p",{children:a.comment})]})})})})]})},r)),((j=t==null?void 0:t.comments)==null?void 0:j.length)>3&&e.jsx(g,{className:"relative",children:({open:a})=>e.jsxs(e.Fragment,{children:[e.jsxs(g.Button,{as:"div",className:"flex relative justify-center items-center w-8 h-8 text-center text-white bg-black rounded-full cursor-pointer",onMouseEnter:()=>m("more"),onMouseLeave:()=>m(null),children:["+",t.comments.length-3]}),e.jsx(T,{as:o.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",show:h==="more",className:"absolute z-50 max-w-[300px]",children:e.jsx(g.Panel,{className:"px-4",children:e.jsx("div",{className:"px-4 py-1 text-white bg-black rounded-lg ring-1 shadow-lg ring-black/5",children:e.jsx("div",{className:"flex flex-col gap-2 text-[10px] font-medium",children:t.comments.slice(3).map((r,c)=>e.jsxs("div",{children:[e.jsx("p",{className:"font-semibold",children:r.first_name?r.first_name:"  "+r.last_name?r.last_name:""}),e.jsx("p",{children:r.comment})]},c))})})})})]})})]})})};return e.jsxs("div",{className:"lg:px-3 lg:pl-3 xl:w-full xl:pl-6 2xl:mt-0 2xl:w-[40%]",children:[e.jsxs("div",{className:"flex flex-row justify-between items-center",children:[y?e.jsx("h3",{className:"my-4 text-[16px] font-[600] sm:text-[20px] ",children:"Recent Engagement"}):null,e.jsx(E,{to:"/member/engagement",className:"text-[14px] font-[600] underline sm:text-[16px] ",children:"View all"})]}),e.jsx("div",{className:"overflow-x-auto w-full custom-overflow",children:e.jsx(Y,{children:e.jsxs("table",{className:"w-full divide-y divide-[#1f1d1a]/10",children:[e.jsx("thead",{className:"",children:e.jsx("tr",{className:"",children:V.map((t,l)=>e.jsx("th",{scope:"col",className:`font  whitespace-nowrap border-b-[#1f1d1a]/10  px-4 text-left font-[700] md:border-0 md:border-b-[3px] md:border-dashed md:px-6 md:py-3 ${t.header=="Title"?"xl:min-w-[30%]":""}`,children:t.header},l))})}),e.jsx("tbody",{className:"font-iowan-regular  divide-y divide-[#1f1d1a]/10",children:(n=i==null?void 0:i.comments)==null?void 0:n.reduce((t,l)=>t.find(h=>h.sent_at===l.sent_at)?t:t.concat([l]),[]).map((t,l)=>e.jsxs("tr",{className:"!h-[3rem] !max-h-[3rem] !min-h-[3rem]",children:[e.jsx("td",{className:"!w-[auto]   !min-w-[6.25rem] !max-w-[auto] overflow-ellipsis whitespace-nowrap px-3 font-iowan  text-[15px] font-[700] md:h-[60px] md:max-h-[60px] md:max-w-lg  md:whitespace-normal ",children:e.jsx(E,{className:"cursor-pointer  whitespace-nowrap font-iowan text-[14px] font-[700] text-[#292829fd] hover:underline disabled:cursor-not-allowed disabled:text-gray-400",to:`${t.update_link}`,children:t.name.startsWith("Update ")&&t.name.slice(7).match(/^\d{4}-\d{2}-\d{2}$/)?`Update ${new Date(t.name.slice(7)).toLocaleString("en-US",{month:"short",day:"2-digit",year:"numeric"}).replace(/,/g,",")}`:t.name})}),e.jsx("td",{className:"!w-[auto]  !min-w-[6.25rem] !max-w-[auto] whitespace-nowrap px-3 font-iowan capitalize md:max-w-lg md:whitespace-nowrap",children:u(t.sent_at).fromNow()}),e.jsx("td",{className:"px-3 whitespace-nowrap md:max-w-lg",children:e.jsx(f,{row:t})})]},l))})]})})}),console.log(i==null?void 0:i.comments),((p=i==null?void 0:i.comments)==null?void 0:p.length)==0?e.jsxs("div",{className:"mb-[20px] mt-24 flex flex-col items-center",children:[e.jsx(M,{children:e.jsx(A,{fill:"black",className:"!h-[5rem] !w-[5rem]"})}),e.jsx("h3",{children:"You Have No Engagement"}),e.jsx("p",{className:"mt-4 text-base font-medium text-center",children:"Recent engagement from your updates will be shown here"})]}):null]})};function O(i){const[y,f]=o.useState(!1),[b,n]=o.useState([]),[p]=D(),{dispatch:t}=o.useContext(L),{dispatch:l}=o.useContext(P),x=p.get("limit")||30,h=p.get("page"),m=p.get("company_name");async function j(){f(!0);try{const a=new R,r=await a.callRawAPI(`/v4/api/records/update_requests?join=companies|company_id&join=updates|update_id&filter=investor_id,eq,${i}&filter=${a.getProjectId()}_update_requests.status,eq,1${m?`&filter=name,cs,${m}`:""}&page=${h??1},${x??10}`);n(r.list.map(c=>{var w,v,N,_,s;return{id:c.id,company_id:(w=c.updates)==null?void 0:w.company_id,company_name:(v=c.companies)==null?void 0:v.name,public_url:`${window.location.origin}/reports/public/view/${c.update_id}/${(N=c.updates)==null?void 0:N.public_link_id}`,sent_at:(_=c.updates)==null?void 0:_.sent_at,status:c.view_count==0?"New":`Viewed(${c.view_count})`,recipient_access:(s=c.updates)==null?void 0:s.recipient_access}}))}catch(a){z(t,a.message),a.message!=="TOKEN_EXPIRED"&&I(l,a.message,5e3,"error")}f(!1)}return o.useEffect(()=>{j()},[x,h,m]),{loading:y,updates:b,refetch:j}}const B=[{header:"Company Name",accessor:"company_name"},{header:"Update URL",accessor:"update_url"},{header:"Date received",accessor:"sent_at"},{header:"Status",accessor:"status"},{header:"Availability",accessor:"availability"},{header:"Action",accessor:""}],we=()=>{var w,v,N,_;const{dispatch:i,state:y}=o.useContext(L),{dispatch:f}=o.useContext(P),b=U(),[n,p]=D(),{updates:t,loading:l}=O(y.user),x=H({status:$(),availability:$()}),{register:h,handleSubmit:m,setError:j,reset:a,formState:{errors:r}}=F({resolver:q(x),defaultValues:async()=>{const s=n.get("company_name")??"",d=n.get("status")??"",S=n.get("availability")??"";return{company_name:s,status:d,availability:S}}});o.useEffect(()=>{f({type:"SETPATH",payload:{path:"update_requests"}})},[]);function c(s){n.set("company_name",s.company_name),n.set("status",s.status),n.set("availability",s.availability),p(n)}return e.jsx("div",{className:"px-5 pt-8 md:px-8",children:l?e.jsx(C,{}):e.jsx(e.Fragment,{children:e.jsxs("div",{className:"  rounded bg-brown-main-bg ",children:[e.jsx("div",{className:"item-center mb-3 flex w-full justify-between  ",children:e.jsx("h4",{className:"text-left text-[16px] font-[600] sm:text-[20px]",children:"Search"})}),e.jsxs("form",{onSubmit:m(c),className:"flex flex-col gap-4",children:[e.jsxs("div",{className:"flex flex-wrap items-center gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Status"}),e.jsxs("select",{className:`focus:shadow-outline w-full appearance-none text-ellipsis rounded border border-[#1f1d1a] bg-transparent py-2 pl-3 pr-8 text-sm font-normal capitalize leading-tight text-[#1f1d1a] focus:outline-none sm:w-[180px] ${(w=r.status)!=null&&w.message?"border-red-500":""}`,children:[e.jsx("option",{children:" - select - "}),Object.entries(k).map(([s,d])=>e.jsx("option",{value:s,children:d},s))]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(v=r.status)==null?void 0:v.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Availability"}),e.jsxs("select",{className:`focus:shadow-outline w-[100px] appearance-none text-ellipsis rounded border border-[#1f1d1a] bg-transparent py-2 pl-3 pr-8 leading-tight text-[#1f1d1a] shadow focus:outline-none sm:w-[180px] ${(N=r.availability)!=null&&N.message?"border-red-500":""}`,children:[e.jsx("option",{children:" - select - "}),Object.entries(k).map(([s,d])=>e.jsx("option",{value:s,children:d},s))]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(_=r.availability)==null?void 0:_.message})]})]}),e.jsxs("div",{className:"row-start-3 flex items-center gap-4 self-end sm:row-start-auto",children:[e.jsx("button",{type:"submit",disabled:l,className:"font-iowan-regular  rounded-md bg-primary-black/80 px-4 py-1 font-semibold text-white hover:bg-primary-black",children:"Search"}),e.jsx("button",{type:"button",onClick:()=>{a({company_name:"",status:"",availability:""}),n.delete("company_name"),n.delete("status"),n.delete("availability"),p(n)},disabled:l,className:"rounded-md px-4 py-1 font-semibold text-[#1f1d1a]",children:"Clear"})]})]}),e.jsxs("div",{className:"custom-overflow mt-10   rounded bg-brown-main-bg p-5 px-0 md:mt-8",children:[e.jsxs("div",{className:"flex flex-row justify-between",children:[e.jsx("div",{className:"text-left text-[16px] font-[600] sm:text-[20px]",children:"Updates"}),e.jsx("div",{className:"text-left text-[16px] font-[600] sm:text-[20px]",children:"Recent Engagement"})]}),e.jsxs("div",{className:"flex flex-row justify-between",children:[e.jsx("div",{className:`${l?"":"custom-overflow overflow-x-auto lg:pr-3 xl:pr-6"} `,children:l?e.jsx("div",{className:"flex max-h-fit min-h-fit min-w-fit max-w-full items-center justify-center  py-5",children:e.jsx(C,{size:50})}):e.jsx(e.Fragment,{children:e.jsxs("table",{className:"min-w-full divide-y divide-[#1f1d1a]/10 ",children:[e.jsx("thead",{className:"",children:e.jsx("tr",{className:" border-b border-b-[#1f1d1a]/20",children:B.map((s,d)=>e.jsx("th",{scope:"col",className:`font whitespace-nowrap  border-b-[#1f1d1a]/10 px-3 text-left md:border-0 md:border-b-[3px] md:border-dashed md:px-6 md:px-6 md:py-3 md:py-3 ${s.header=="Title"?"xl:min-w-[30%]":""}`,children:s.header},d))})}),e.jsx("tbody",{className:"font-iowan-regular  divide-y divide-[#1f1d1a]/10",children:t.map(s=>{const d=u(s.sent_at).add(s.recipient_access,"days").toDate()<new Date,S=s.sent_at?u(s.sent_at).add(s.recipient_access??0,"days").diff(u(),"days"):0;return e.jsxs("tr",{children:[e.jsx("td",{className:"whitespace-nowrap px-3 md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:s.company_name}),e.jsx("td",{className:"whitespace-nowrap px-3 md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:s.public_url}),e.jsx("td",{className:"whitespace-nowrap px-3 md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:u(s.sent_at).format("MMM DD, YYYY")}),e.jsx("td",{className:"whitespace-nowrap px-3 md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:s.status}),e.jsx("td",{className:"whitespace-nowrap px-3 md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:d?e.jsx("span",{children:"0 hrs / 0 minutes (update expired)"}):e.jsxs("span",{children:[S<1?"":`${S} days,  `,s.sent_at?u(s.sent_at).add(s.recipient_access,"days").diff(u().add(S,"days"),"hours"):0," ","hrs"]})}),e.jsxs("td",{className:"flex flex-col items-start justify-center gap-4 whitespace-nowrap px-6 py-4",children:[e.jsx("button",{className:"cursor-pointer font-medium text-[#292829fd] hover:underline disabled:text-gray-500",onClick:()=>{b("/stakeholder/view-update/"+s.id,{state:s})},disabled:d,children:e.jsx("span",{children:"View"})}),e.jsx("button",{className:"cursor-pointer px-0 font-medium text-[#292829fd] hover:underline disabled:text-gray-500",onClick:()=>{},disabled:d,children:e.jsx("span",{children:"Share"})}),d?e.jsx("button",{className:"cursor-pointer px-0 font-medium text-[#292829fd] hover:underline disabled:text-gray-500",onClick:()=>{},children:e.jsx("span",{children:"Add time +"})}):null]})]},s.id)})})]})})}),e.jsx(G,{showTitle:!1})]})]})]})})})};export{we as default};
