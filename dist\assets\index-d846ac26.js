import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{o as h}from"./yup-c41d85d2.js";import{A as b,G as g,M as E,s as l,t as w}from"./index-46de5032.js";import{InteractiveButton2 as j}from"./InteractiveButton-060359e0.js";import{r as p}from"./vendor-4cdf2bd1.js";import{u as y}from"./react-hook-form-9f4fcfa9.js";import{c as N,a as S}from"./yup-342a5df4.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";function W(){var i,m;const{dispatch:a,state:n}=p.useContext(b),{dispatch:r}=p.useContext(g),d=N({email:S().required("This field is required")}),{register:c,handleSubmit:x,setError:k,reset:C,formState:{errors:s,isSubmitting:o}}=y({resolver:h(d),defaultValues:{email:n.profile.email}});async function u(f){try{await new E().updateEmail(f.email),l(r,"Email changed successfully"),a({type:"REFETCH_PROFILE"})}catch(t){w(a,t.message),t.message!=="TOKEN_EXPIRED"&&l(r,t.message,5e3,"error")}}return e.jsx("div",{className:"p-5 px-4 pt-8 md:px-8",children:e.jsxs("div",{className:"w-full max-w-7xl pb-32 ",children:[e.jsx("h3",{className:"text-xl",children:"Email"}),e.jsx("p",{className:"font-iowan-regular mt-1 text-base",children:"Update your account's email"}),e.jsxs("form",{onSubmit:x(u),children:[e.jsxs("div",{className:"mt-8 flex flex-col gap-4 sm:flex-row sm:items-end",children:[e.jsx("input",{type:"text",autoComplete:"off",...c("email"),className:`no-box-shadow h-[41.6px] w-full max-w-[500px] appearance-none rounded-md border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none md:max-w-[400px] ${(i=s.email)!=null&&i.message?"border-red-500":""}`}),e.jsx(j,{loading:o,disabled:o,type:"submit",className:"disabled:bg-disabledblack h-[41.6px] w-[160px] whitespace-nowrap rounded-md bg-primary-black/90  px-5 py-2  text-center text-sm font-semibold text-white transition-colors duration-100",children:"Update Email"})]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(m=s.email)==null?void 0:m.message})]})]})})}export{W as default};
