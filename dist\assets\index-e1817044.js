import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{G as l,A as n}from"./index-46de5032.js";import{r,j as o,O as m}from"./vendor-4cdf2bd1.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";function C(){var i;const{dispatch:s}=r.useContext(l),{state:a}=r.useContext(n);return r.useEffect(()=>{s({type:"SETPATH",payload:{path:"account"}})},[]),t.jsxs("div",{children:[t.jsx("h3",{className:"mt-6 px-4 text-3xl font-normal",children:(i=a.company)==null?void 0:i.name}),t.jsxs("ul",{className:"custom-overflow mt-8 flex w-full overflow-x-auto border-y border-[#0003] px-8 py-1",children:[t.jsx("li",{children:t.jsx(o,{to:"/stakeholder/account",end:!0,className:({isActive:e})=>`font block rounded-md px-6 py-2 font-medium text-gray-600 ${e?"bg-[#1f1d1a] text-white":"text-gray-600"}`,children:"Profile"})}),t.jsx("li",{children:t.jsx(o,{to:"/stakeholder/account/email",className:({isActive:e})=>`font block rounded-md px-6 py-2 font-medium text-gray-600 ${e?"bg-[#1f1d1a] text-white":"text-gray-600"}`,children:"Email"})}),t.jsx("li",{children:t.jsx(o,{to:"/stakeholder/account/security",className:({isActive:e})=>`font block rounded-md px-6 py-2 font-medium text-gray-600 ${e?"bg-[#1f1d1a] text-white":"text-gray-600"}`,children:"Password/Security"})}),t.jsx("li",{children:t.jsx(o,{to:"/stakeholder/account/notifications",className:({isActive:e})=>`font block rounded-md px-6 py-2 font-medium text-gray-600 ${e?"bg-[#1f1d1a] text-white":"text-gray-600"}`,children:"Notifications"})})]}),a.profile.email?t.jsx(m,{}):null]})}export{C as default};
