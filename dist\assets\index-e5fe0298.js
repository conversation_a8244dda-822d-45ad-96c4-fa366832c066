import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{o as $}from"./yup-c41d85d2.js";import{A as M,G as U,az as V,M as q,s as C,t as G}from"./index-46de5032.js";import{r as i,u as S}from"./vendor-4cdf2bd1.js";import{u as B}from"./react-hook-form-9f4fcfa9.js";import{c as K,a as n}from"./yup-342a5df4.js";import{S as X,a as Y,b as H}from"./SelectCity-ecf3746c.js";import{u as J}from"./useFundProfile-1f384383.js";import{b as Q}from"./index.esm-54e24cf9.js";import{InteractiveButton2 as W}from"./InteractiveButton-060359e0.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./countries-912e22d5.js";import"./ChevronDownIcon-8b7ce98c.js";import"./react-icons-36ae72b7.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";function Le(){var h,y,g,w,j,N,_;const{dispatch:E,state:t}=i.useContext(M),{dispatch:u}=i.useContext(U),{loading:Z,profileData:P,refetch:ee}=J(),l=i.useRef(null),z=S();i.useEffect(()=>{console.log("djhjdjhdhdh"),window.scrollTo(0,0),l.current&&l.current.scrollIntoView({behavior:"smooth"})},[z.pathname,l]);const k=K({first_name:n().required("This field is required"),last_name:n().required("This field is required"),country:n(),state:n(),city:n()}),{register:p,handleSubmit:R,setError:te,reset:D,watch:L,setValue:a,formState:{errors:s,defaultValues:d,dirtyFields:A,isSubmitting:b,isDirty:F},control:f}=B({resolver:$(k),defaultValues:{first_name:t==null?void 0:t.profile.first_name,last_name:t==null?void 0:t.profile.last_name,country:t==null?void 0:t.profile.country,state:t==null?void 0:t.profile.state,city:t==null?void 0:t.profile.city}});console.log(t.company.id);async function I(o){console.log(o);try{const r=new q;let v="";if(A.logo&&o.logo instanceof FileList&&o.logo.length>0){const O=await r.upload(o.logo[0]);v=r.baseUrl()+O.url}await r.callRawAPI("/v3/api/custom/goodbadugly/fundmanager/preference",{first_name:o.first_name,last_name:o.last_name,photo:v,country:o.country,state:o.state,city:o.city},"POST"),C(u,"Changes saved")}catch(r){G(E,r.message),r.message!=="TOKEN_EXPIRED"&&C(u,r.message,5e3,"error")}}const[m,c,x]=L(["photo","country","state"]);i.useEffect(()=>{c===""&&(a("state",""),a("city","")),x===""&&a("city","")},[c,x]);const T=i.useMemo(()=>m instanceof FileList&&m.length>0?URL.createObjectURL(m[0]):null,[m]);return e.jsxs("div",{ref:l,className:"mt-10 w-full max-w-7xl items-start gap-6 p-5 pt-8 sm:px-8 md:flex",children:[e.jsxs("div",{className:"flex flex-col items-center rounded-md border border-[#1f1d1a] px-16 py-8",children:[e.jsx("img",{src:T||(d==null?void 0:d.photo)||"/default.png",alt:"profile",className:"h-20 min-h-20 w-20 min-w-20 rounded-[50%] object-cover sm:h-40 sm:min-h-40 sm:w-40 sm:min-w-40"}),e.jsxs("label",{className:"md:max-w-auto mt-4 flex max-w-[200px] cursor-pointer flex-row items-center justify-center gap-3 rounded-lg border border-[#1f1d1a] px-3 py-2 text-center text-lg font-medium capitalize capitalize",children:[e.jsx("input",{type:"file",...p("photo"),className:"hidden"}),e.jsx(Q,{className:"min-h-5 min-w-5"}),e.jsxs("span",{className:"font-iowan-regular whitespace-nowrap text-sm",children:[" ","Upload Profile Picture"]})]})]}),e.jsxs("form",{className:"mb-20 mt-8 max-w-[500px] flex-grow md:mt-0",onSubmit:R(I),children:[e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block cursor-pointer font-iowan text-base font-bold capitalize capitalize text-[#1f1d1a]",children:"First name"}),e.jsx("input",{type:"text",autoComplete:"off",...p("first_name"),className:`w-full appearance-none rounded-md border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none ${(h=s.first_name)!=null&&h.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(y=s.first_name)==null?void 0:y.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block cursor-pointer font-iowan text-base font-bold capitalize capitalize text-[#1f1d1a]",children:"Last name"}),e.jsx("input",{type:"text",autoComplete:"off",...p("last_name"),className:`w-full appearance-none rounded-md border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none ${(g=s.last_name)!=null&&g.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(w=s.last_name)==null?void 0:w.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block cursor-pointer font-iowan text-base font-bold capitalize capitalize text-[#1f1d1a]",children:"Title"}),e.jsx("input",{type:"text",autoComplete:"off",readOnly:!0,value:V[t==null?void 0:t.profile.role],className:`w-full appearance-none rounded-md border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none ${(j=s.role)!=null&&j.message?"border-red-500":""}`})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block cursor-pointer font-iowan text-base font-bold capitalize capitalize text-[#1f1d1a]",children:"Fund"}),e.jsx("input",{readOnly:!0,type:"text",autoComplete:"off",value:P.display_name,className:`w-full appearance-none rounded-md border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none ${(N=s.company_name)!=null&&N.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(_=s.company_name)==null?void 0:_.message})]}),e.jsx(X,{control:f,name:"country",setValue:o=>a("country",o)}),e.jsx(Y,{control:f,name:"state",setValue:o=>a("state",o),country:c}),e.jsx(H,{control:f,name:"city",setValue:o=>a("city",o),country:c,state:x}),e.jsxs("div",{className:"mt-6 flex items-center justify-end gap-4",children:[" ",F?e.jsx("button",{className:"h-[40px] rounded-md border border-[#1f1d1a] px-4 py-2 font-iowan text-[10px] font-medium sm:text-[12px]    xl:text-base",type:"button",onClick:()=>D(),children:"Discard Changes"}):null,e.jsx(W,{loading:b,disabled:b,type:"submit",className:`whitespace-nowr disabled:bg-disabledblack h-[40px]  w-[115px] min-w-fit rounded-md bg-primary-black px-6 py-2 text-center text-[10px] font-semibold font-semibold text-white transition-colors \r
duration-100 sm:!text-[12px] md:!w-[146px]  xl:!text-base`,children:"Save changes"})]})]})]})}export{Le as default};
