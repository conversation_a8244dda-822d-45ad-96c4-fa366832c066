import{j as r}from"./@nextui-org/listbox-0f38ca19.js";import{L as n}from"./index-6edcbb0d.js";import{G as s,A as l}from"./index-46de5032.js";import{r as t,j as o,O as m}from"./vendor-4cdf2bd1.js";import"./@nextui-org/theme-345a09ed.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";function S(){const{dispatch:i}=t.useContext(s),{state:a}=t.useContext(l);return t.useEffect(()=>{i({type:"SETPATH",payload:{path:"account"}})},[]),r.jsxs("div",{className:"",children:[r.jsxs("ul",{className:"scrollbar-hide custom-overflow mb-6 flex w-full overflow-x-auto border-y border-b-[1px] border-[#0003] border-b-[#1f1d1a]/20 px-6 py-1 md:overflow-hidden",children:[r.jsx("li",{children:r.jsx(o,{to:"/member/account",end:!0,className:({isActive:e})=>` block whitespace-nowrap border-b-[.375rem] px-6 py-2 pb-3   font-iowan text-[1.25rem] font-[700] leading-[1.5537rem] text-[#1f1d1a] transition-all duration-200 ease-out ${e?"border-b-[.375rem] border-b-[#1F1D1A]":"border-b-transparent"}`,children:"Profile"})}),r.jsx("li",{children:r.jsx(o,{to:"/member/account/email",className:({isActive:e})=>` block whitespace-nowrap border-b-[.375rem] px-6 py-2 pb-3   font-iowan text-[1.25rem] font-[700] leading-[1.5537rem] text-[#1f1d1a] transition-all duration-200 ease-out ${e?"border-b-[.375rem] border-b-[#1F1D1A]":"border-b-transparent"}`,children:"Email"})}),r.jsx("li",{children:r.jsx(o,{to:"/member/account/security",className:({isActive:e})=>` block whitespace-nowrap border-b-[.375rem] px-6 py-2 pb-3   font-iowan text-[1.25rem] font-[700] leading-[1.5537rem] text-[#1f1d1a] transition-all duration-200 ease-out ${e?"border-b-[.375rem] border-b-[#1F1D1A]":"border-b-transparent"}`,children:"Password/Security"})}),r.jsx("li",{children:r.jsx(o,{to:"/member/account/notifications",className:({isActive:e})=>` block whitespace-nowrap border-b-[.375rem] px-6 py-2 pb-3   font-iowan text-[1.25rem] font-[700] leading-[1.5537rem] text-[#1f1d1a] transition-all duration-200 ease-out ${e?"border-b-[.375rem] border-b-[#1F1D1A]":"border-b-transparent"}`,children:"Notifications"})})]}),a.profile.email&&a.company.id?r.jsx(t.Suspense,{fallback:r.jsx(n,{}),children:r.jsx(m,{})}):null]})}export{S as default};
