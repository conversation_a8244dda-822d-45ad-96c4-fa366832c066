import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{A as g,G as f,I as y,M as b,s as h,t as j}from"./index-46de5032.js";import{r as t,b as w}from"./vendor-4cdf2bd1.js";import"./moment-a9aaa855.js";import{X as v}from"./XMarkIcon-cfb26fe7.js";import{t as d,S as m}from"./@headlessui/react-cdd9213e.js";import"./react-addons-update-ccb093e5.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";function C({updateRequest:r,hanleClick:u}){const[i,a]=t.useState(!1),{dispatch:o,state:s}=t.useContext(g),{dispatch:n}=t.useContext(f),[l,x]=t.useState(!1);w();async function c(N){x(!0);try{await new b().callRawAPI(`/v3/api/custom/goodbadugly/updates/time-requests/${N}`,{type:"accept"},"POST"),o({type:"REFETCH_REQUESTED_TIMES"}),a(!1),h(n,"Request accepted")}catch(p){j(o,p.message),p.message!=="TOKEN_EXPIRED"&&h(n,p.message,5e3,"error"),console.log(p)}x(!1)}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"rounded-lg bg-primary-600 px-6 py-2 font-medium text-white",onClick:()=>a(!0),children:"Accept"}),e.jsx(d,{appear:!0,show:i,as:t.Fragment,children:e.jsxs(m,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>a(!1),children:[e.jsx(d.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(d.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(m.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(m.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Are you sure"}),e.jsx("button",{onClick:()=>a(!1),type:"button",children:e.jsx(v,{className:"h-6 w-6"})})]}),e.jsx("p",{className:"mt-2 leading-6",children:"Are you sure you want to accept this request?"}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>a(!1),children:"Cancel"}),e.jsx(y,{loading:l,disabled:l,onClick:()=>c(r.id),className:"disabled:bg-disabledblack rounded-lg bg-primary-black py-2 text-center font-semibold text-white transition-colors duration-100",children:"Yes"})]})]})})})})]})})]})}function T({updateRequest:r}){const[u,i]=t.useState(!1),{dispatch:a}=t.useContext(g),{dispatch:o}=t.useContext(f),[s,n]=t.useState(!1);async function l(x){n(!0);try{await new b().callRawAPI(`/v3/api/custom/goodbadugly/updates/time-requests/${x}`,{type:"deny"},"POST"),i(!1),h(o,"Request denied"),a({type:"REFETCH_REQUESTED_TIMES"})}catch(c){j(a,c.message),c.message!=="TOKEN_EXPIRED"&&h(o,c.message,5e3,"error")}n(!1)}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"rounded-lg bg-red-500 px-6 py-2 font-medium text-white",onClick:()=>i(!0),children:"Deny"}),e.jsx(d,{appear:!0,show:u,as:t.Fragment,children:e.jsxs(m,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>i(!1),children:[e.jsx(d.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(d.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(m.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(m.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Are you sure"}),e.jsx("button",{onClick:()=>i(!1),type:"button",children:e.jsx(v,{className:"h-6 w-6"})})]}),e.jsx("p",{className:"mt-2",children:"Are you sure you want to deny this request?"}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>i(!1),children:"Cancel"}),e.jsx(y,{loading:s,disabled:s,onClick:()=>l(r.id),className:"rounded-lg bg-red-500 py-2 text-center font-semibold text-white transition-colors duration-100 disabled:bg-opacity-60",children:"Yes, Decline"})]})]})})})})]})})]})}function Q(){var o;const{state:r}=t.useContext(g),{dispatch:u}=t.useContext(f);t.useEffect(()=>{u({type:"SETPATH",payload:{path:"requested_times"}})},[]);const i=(o=r==null?void 0:r.requested_times)==null?void 0:o.list.reduce((s,n)=>{const l=n.investor_id;return(!s[l]||s[l].id<n.id)&&(s[l]=n),s},{}),a=Object.values(i);return e.jsxs("div",{className:"px-6 py-5",children:[e.jsx("h1",{className:"font-normal",children:r.company.name}),e.jsx("h2",{className:"mt-6 text-3xl font-medium",children:"Requested Time"}),e.jsx("div",{className:"mt-6 grid grid-cols-1 gap-5 bg-brown-main-bg pb-12 sm:grid-cols-2 md:grid-cols-2 xl:grid-cols-4",children:a==null?void 0:a.map(s=>e.jsx("div",{className:"group relative flex h-full min-h-[9.475rem] items-center justify-center truncate rounded-[.625rem] border border-[#0003] bg-brown-main-bg p-5 shadow-md",children:e.jsxs("div",{className:"flex flex-col items-center justify-center gap-2",children:[e.jsxs("p",{className:"whitespace-nowrap px-6 py-4 capitalize",children:[s.first_name," ",s.last_name]}),e.jsx("p",{className:"text-lg font-semibold",children:s.name}),e.jsxs("p",{className:"text-lg font-semibold",children:[s.time," Days"]}),e.jsxs("div",{className:"mt-4 flex gap-4",children:[e.jsx(C,{updateRequest:s}),e.jsx(T,{updateRequest:s})]})]})},s.update_id))})]})}export{Q as default};
