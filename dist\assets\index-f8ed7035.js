import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{o as A}from"./yup-c41d85d2.js";import{A as D,G as M,az as U,M as V,s as R,t as G}from"./index-46de5032.js";import{r as u}from"./vendor-4cdf2bd1.js";import{a as F,u as H}from"./react-hook-form-9f4fcfa9.js";import{c as Q,a as N}from"./yup-342a5df4.js";import{c as C}from"./countries-912e22d5.js";import{C as z}from"./ChevronDownIcon-8b7ce98c.js";import{W as l,t as E}from"./@headlessui/react-cdd9213e.js";import{b as W}from"./index.esm-54e24cf9.js";import{InteractiveButton2 as K}from"./InteractiveButton-060359e0.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-fad2f3d1.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./react-icons-36ae72b7.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";function Y({control:y,name:o,setValue:p,disabled:j=!1}){var i,c;const[m,v]=u.useState(""),{field:x,formState:b,fieldState:g}=F({control:y,name:o}),d=m===""?C:C.filter(t=>t.name.toLowerCase().replace(/\s+/g,"").includes(m.toLowerCase().replace(/\s+/g,"")));return e.jsx(e.Fragment,{children:e.jsx(l,{value:x.value,onChange:p,disabled:j,children:e.jsxs("div",{className:"relative mt-6",children:[e.jsx("label",{className:"mb-2 block font-iowan text-base font-semibold capitalize capitalize text-[#1f1d1a]",children:"Country"}),e.jsxs("div",{children:[e.jsxs("div",{className:"relative w-full cursor-default rounded-md  text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300",children:[e.jsx(l.Input,{className:`focus:shadow-outline h-[41.6px] w-full appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm text-sm font-normal font-normal leading-tight text-[#1d1f1a]    focus:outline-none ${(i=g.error)!=null&&i.message?"border-red-500":""}`,placeholder:"Country",displayValue:t=>{const n=C.find(r=>r.name==t)??{};return`${n.emoji??""}   ${n.name??""}`},onChange:t=>{const n=t.target.value.replace(/\p{Emoji}/gu,"").trim();v(n),n===""&&p("")},onBlur:x.onBlur,ref:x.ref,name:x.name,autoComplete:"off",defaultValue:"oooo"}),e.jsx(l.Button,{className:"absolute inset-y-0 right-0 flex items-center border-l border-black/60 p-2",children:e.jsx(z,{className:"h-5 w-5 text-gray-400","aria-hidden":"true"})})]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(c=g.error)==null?void 0:c.message})]}),e.jsx(E,{as:u.Fragment,leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx(l.Options,{className:"absolute z-10 mt-1 h-fit w-full rounded-md border bg-brown-main-bg py-1 text-base shadow-lg ring-1 ring-[#1f1d1a]/5 focus:outline-none",children:e.jsx("div",{className:"max-h-96 overflow-auto",children:d.length===0&&m!==""?e.jsx("div",{className:"relative cursor-default select-none bg-brown-main-bg px-4 py-2 text-gray-700",children:"Nothing found."}):d.map(t=>e.jsx(l.Option,{className:({active:n})=>`relative flex cursor-default select-none items-center gap-4 py-2 pl-10 pr-4 ${n?"bg-primary-black text-white":"text-gray-900"}`,value:t.name,children:({selected:n,active:r})=>e.jsxs(e.Fragment,{children:[e.jsx("span",{children:t.emoji}),e.jsx("span",{className:"block truncate font-medium",children:t.name})]})},t.id))})})})]})})})}function X({control:y,name:o,setValue:p,country:j,disabled:m=!1}){var t,n;const[v,x]=u.useState(""),{field:b,formState:g,fieldState:d}=F({control:y,name:o}),i=u.useMemo(()=>{const r=C.find(a=>a.name==j);return(r==null?void 0:r.states)??[]},[j]),c=v===""?i:i.filter(r=>r.name.toLowerCase().replace(/\s+/g,"").includes(v.toLowerCase().replace(/\s+/g,"")));return e.jsx(e.Fragment,{children:e.jsx(l,{value:b.value,onChange:p,disabled:m,children:e.jsxs("div",{className:"relative mt-6",children:[e.jsx("label",{className:"mb-2 block font-iowan text-base font-semibold capitalize capitalize text-[#1f1d1a]",children:"State"}),e.jsxs("div",{children:[e.jsxs("div",{className:"relative w-full cursor-default rounded-md  text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300",children:[e.jsx(l.Input,{className:`focus:shadow-outline h-[41.6px] w-full appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm text-sm font-normal font-normal leading-tight text-[#1d1f1a]    focus:outline-none ${(t=d.error)!=null&&t.message?"border-red-500":""}`,placeholder:"State",displayValue:r=>(i.find(f=>f.name==r)??{}).name,onChange:r=>{x(r.target.value),r.target.value===""&&p("")},onBlur:b.onBlur,ref:b.ref,name:b.name,autoComplete:"off"}),e.jsx(l.Button,{className:"absolute inset-y-0 right-0 flex items-center border-l border-black/60 p-2",children:e.jsx(z,{className:"h-5 w-5 text-gray-400","aria-hidden":"true"})})]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(n=d.error)==null?void 0:n.message})]}),e.jsx(E,{as:u.Fragment,leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx(l.Options,{className:"absolute z-10 mt-1 h-fit w-full rounded-md border bg-brown-main-bg py-1 text-base shadow-lg ring-1 ring-[#1f1d1a]/5 focus:outline-none",children:e.jsx("div",{className:"max-h-96 overflow-auto",children:c.length===0&&v!==""?e.jsx("div",{className:"relative cursor-default select-none bg-brown-main-bg px-4 py-2 text-gray-700",children:"Nothing found."}):c.map(r=>e.jsx(l.Option,{className:({active:a})=>`relative flex cursor-default select-none items-center gap-4 py-2 pl-10 pr-4 ${a?"bg-primary-black text-white":"text-gray-900"}`,value:r.name,children:({selected:a,active:f})=>e.jsx(e.Fragment,{children:e.jsx("span",{className:"block truncate font-medium",children:r.name})})},r.id))})})})]})})})}function J({control:y,name:o,setValue:p,country:j,state:m,disabled:v=!1}){var n,r;const[x,b]=u.useState(""),{field:g,formState:d,fieldState:i}=F({control:y,name:o}),c=u.useMemo(()=>{const a=C.find(h=>h.name==j),f=a==null?void 0:a.states.find(h=>h.name==m);return(f==null?void 0:f.cities)??[]},[j,m]),t=x===""?c:c.filter(a=>a.name.toLowerCase().replace(/\s+/g,"").includes(x.toLowerCase().replace(/\s+/g,"")));return e.jsx(e.Fragment,{children:e.jsx(l,{value:g.value,onChange:p,disabled:v,children:e.jsxs("div",{className:"relative mt-6",children:[e.jsx("label",{className:"mb-2 block font-iowan text-base font-semibold capitalize capitalize text-[#1f1d1a]",children:"City"}),e.jsxs("div",{children:[e.jsxs("div",{className:"relative w-full cursor-default rounded-md  text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300",children:[e.jsx(l.Input,{className:`focus:shadow-outline h-[41.6px] w-full appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm text-sm font-normal font-normal leading-tight text-[#1d1f1a]    focus:outline-none ${(n=i.error)!=null&&n.message?"border-red-500":""}`,placeholder:"City",displayValue:a=>(c.find(h=>h.name==a)??{}).name,onChange:a=>{b(a.target.value),a.target.value===""&&p("")},onBlur:g.onBlur,ref:g.ref,name:g.name,autoComplete:"off"}),e.jsx(l.Button,{className:"absolute inset-y-0 right-0 flex items-center border-l border-black/60 p-2",children:e.jsx(z,{className:"h-5 w-5 text-gray-400","aria-hidden":"true"})})]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(r=i.error)==null?void 0:r.message})]}),e.jsx(E,{as:u.Fragment,leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx(l.Options,{className:"absolute z-10 mt-1 h-fit w-full rounded-md border bg-brown-main-bg py-1 text-base shadow-lg ring-1 ring-[#1f1d1a]/5 focus:outline-none",children:e.jsx("div",{className:"max-h-96 overflow-auto",children:t.length===0&&x!==""?e.jsx("div",{className:"relative cursor-default select-none bg-brown-main-bg px-4 py-2 text-gray-700",children:"Nothing found."}):t.map(a=>e.jsx(l.Option,{className:({active:f})=>`relative flex cursor-default select-none items-center gap-4 py-2 pl-10 pr-4 ${f?"bg-primary-black text-white":"text-gray-900"}`,value:a.name,children:({selected:f,active:h})=>e.jsx(e.Fragment,{children:e.jsx("span",{className:"block truncate font-medium",children:a.name})})},a.id))})})})]})})})}function Se(){var $,P,L,T,I,O;const{dispatch:y,state:o}=u.useContext(D),{dispatch:p}=u.useContext(M),j=Q({first_name:N().required("This field is required"),last_name:N().required("This field is required"),role:N().required("This field is required"),country:N(),state:N(),city:N()}),{register:m,handleSubmit:v,setError:x,reset:b,watch:g,setValue:d,formState:{errors:i,defaultValues:c,dirtyFields:t,isSubmitting:n,isDirty:r},control:a}=H({resolver:A(j),defaultValues:{first_name:o.profile.first_name,last_name:o.profile.last_name,role:U[o.profile.role],company_name:o.company.name,photo:o.profile.photo,country:o.profile.country,state:o.profile.state,city:o.profile.city,contact_link:o.profile.contact_link}});async function f(s){try{const w=new V;let S="";if(t.photo||t.first_name||t.last_name){if(t.photo&&s.photo instanceof FileList&&s.photo.length>0){const q=await w.upload(s.photo[0]);S=w.baseUrl()+q.url}await w.callRawAPI(`/v4/api/records/user/${o.user}`,{first_name:s.first_name,last_name:s.last_name,...t.photo&&{photo:S}},"PUT"),y({type:"REFETCH_PROFILE"})}t.company_name&&(await w.callRawAPI(`/v4/api/records/companies/${o.company.id}`,{name:s.company_name},"PUT"),y({type:"REFETCH_COMPANY"})),t.country||t.state||t.city,await w.callRawAPI("/v4/api/records/profile",{updateCondition:{user_id:o.user},country:s.country,state:s.state,city:s.city,contact_link:s.contact_link},"PUT"),b({...c,first_name:s.first_name,last_name:s.last_name,...t.photo&&{photo:S},company_name:s.company_name,country:s.country,state:s.state,city:s.city},{keepDefaultValues:!1}),R(p,"Changes saved")}catch(w){G(y,w.message),w.message!=="TOKEN_EXPIRED"&&R(p,w.message,5e3,"error")}}const[h,_,k]=g(["photo","country","state"]);u.useEffect(()=>{_===""&&(d("state",""),d("city","")),k===""&&d("city","")},[_,k]);const B=u.useMemo(()=>h instanceof FileList&&h.length>0?URL.createObjectURL(h[0]):null,[h]);return e.jsxs("div",{className:"w-full max-w-7xl items-start gap-6 p-4 pt-8 sm:p-8 md:flex lg:flex lg:p-12 lg:px-8",children:[e.jsxs("div",{className:"flex flex-col items-center rounded-md border border-[#1f1d1a] px-16 py-8",children:[e.jsx("img",{src:B||(c==null?void 0:c.photo)||"/default.png",alt:"profile",className:"h-20 min-h-20 w-20 min-w-20 rounded-[50%] object-cover sm:h-40 sm:min-h-40 sm:w-40 sm:min-w-40"}),e.jsxs("label",{className:"md:max-w-auto mt-4 flex max-w-[200px] cursor-pointer flex-row items-center justify-center gap-3 rounded-lg border border-[#1f1d1a] px-3 py-2 text-center text-lg font-medium capitalize capitalize",children:[e.jsx("input",{type:"file",...m("photo"),className:"hidden"}),e.jsx(W,{className:"min-h-5 min-w-5"}),e.jsxs("span",{className:"font-iowan-regular whitespace-nowrap text-sm",children:[" ","Upload Profile Picture"]})]})]}),e.jsxs("form",{className:"mb-20 mt-8 max-w-[500px] flex-grow md:mt-0",onSubmit:v(f),children:[" ",e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block font-iowan text-base font-semibold capitalize capitalize text-[#1f1d1a]",children:"First name"}),e.jsx("input",{type:"text",autoComplete:"off",...m("first_name"),className:`w-full appearance-none rounded-md border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none ${($=i.first_name)!=null&&$.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(P=i.first_name)==null?void 0:P.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block font-iowan text-base font-semibold capitalize capitalize text-[#1f1d1a]",children:"Last name"}),e.jsx("input",{type:"text",autoComplete:"off",...m("last_name"),className:`focus:outline-non w-full appearance-none rounded-md border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal text-[#1f1d1a] ${(L=i.last_name)!=null&&L.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(T=i.last_name)==null?void 0:T.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block font-iowan text-base font-semibold capitalize capitalize text-[#1f1d1a]",children:"Title"}),e.jsx("input",{type:"text",autoComplete:"off",readOnly:!0,...m("role"),className:`focus:outline-non w-full appearance-none rounded-md border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal text-[#1f1d1a] ${(I=i.role)!=null&&I.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(O=i.role)==null?void 0:O.message})]}),e.jsx(Y,{control:a,name:"country",setValue:s=>d("country",s)}),e.jsx(X,{control:a,name:"state",setValue:s=>d("state",s),country:_}),e.jsx(J,{control:a,name:"city",setValue:s=>d("city",s),country:_,state:k}),e.jsxs("div",{className:"mt-6 flex items-center justify-end gap-4",children:[" ",e.jsx("button",{className:"h-[40px] rounded-md border border-[#1f1d1a] px-2 py-2 font-iowan text-[10px] font-medium sm:px-4  sm:text-[12px]  xl:text-base",type:"button",onClick:()=>b(),children:"Discard Changes"}),e.jsx(K,{loading:n,disabled:n,type:"submit",className:`whitespace-nowr disabled:bg-disabledblack h-[40px]  w-[115px] min-w-fit rounded-md bg-primary-black px-2 py-2 text-center text-[10px] font-semibold font-semibold text-white transition-colors duration-100 \r
sm:px-6 sm:!text-[12px] md:!w-[146px]  xl:!text-base`,children:"Save changes"})]})]})]})}export{Se as default};
