import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as t,i as M}from"./vendor-4cdf2bd1.js";import{A as E,G as D,M as F,t as S,s as w,I as Y,T as te,a1 as W,N as I,u as se,a as ae,z as V,Q as K,V as Z,W as ne,a2 as ie}from"./index-46de5032.js";import{o as U}from"./yup-c41d85d2.js";import{u as G}from"./react-hook-form-9f4fcfa9.js";import{c as B,a as A,b as H}from"./yup-342a5df4.js";import{InteractiveButton2 as z}from"./InteractiveButton-060359e0.js";import{X as P}from"./XMarkIcon-cfb26fe7.js";import{t as N,L as X,S as C}from"./@headlessui/react-cdd9213e.js";import{b as re,u as le,a as oe,c as ce,L as de}from"./useUpdateCollaborators-e993df7b.js";import{c as me}from"./index.esm-be5e1c14.js";import{h as O}from"./moment-a9aaa855.js";import{E as ue}from"./ExclamationTriangleIcon-2f987159.js";import{T as xe}from"./@editorjs/editorjs-3bc58744.js";import{n as pe}from"./@editorjs/paragraph-9d333c59.js";import{c as fe}from"./@editorjs/header-da8c369a.js";import{d as he}from"./@editorjs/list-86b325f6.js";import{I as ge}from"./@editorjs/link-7a38da73.js";import{n as be}from"./@editorjs/delimiter-89018da8.js";import{f as je}from"./@editorjs/checklist-1b2b7ac3.js";import"./lodash-82bd9112.js";import{g as ve}from"./react-audio-voice-recorder-a95781ec.js";import{S as ye,M as we}from"./MicrophoneIcon-ed3ea0f8.js";import{u as Ne}from"./useNote-95029f8e.js";import{T as q}from"./index-cb9e08c3.js";import{L as _e}from"./LockClosedIcon-a004efdc.js";import{L as Ce}from"./index-6edcbb0d.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@hookform/resolvers-fad2f3d1.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";import"./react-icons-36ae72b7.js";function ke(){const[s,p]=t.useState(!1),[d,n]=t.useState([]),{dispatch:a,state:r}=t.useContext(E),{dispatch:l}=t.useContext(D);async function i(){p(!0);try{const m=await new F().callRawAPI(`/v3/api/custom/goodbadugly/member/get-recipient-groups?user_id=${r.company.user_id}&limit=1000`,void 0,"GET");n(m.list)}catch(o){S(a,o.message),o.message!=="TOKEN_EXPIRED"&&w(l,o.message,5e3,"error")}p(!1)}return t.useEffect(()=>{i()},[]),{loading:s,groups:d}}const Te=({updateGroups:s,refetch:p})=>{var j,k;const{id:d}=M(),{dispatch:n,state:a}=t.useContext(E),{groups:r}=ke(),{dispatch:l}=t.useContext(D),i=B({group_id:A().required("This field is required")}),{register:o,handleSubmit:m,formState:{isSubmitting:c,errors:h},reset:v}=G({resolver:U(i),defaultValues:{group_id:""}});async function u(g){try{await new F().callRawAPI("/v4/api/records/update_group",{update_id:d,group_id:g.group_id},"POST"),w(l,"Group added successfully",5e3,"success"),v({group_id:""}),p()}catch(f){S(n,f.message),f.message!=="TOKEN_EXPIRED"&&w(l,f.message,5e3,"error")}}async function b(g){try{await new F().callRawAPI(`/v4/api/records/update_group/${g}`,{},"DELETE"),w(l,"Group removed successfully",5e3,"success"),p()}catch(f){S(n,f.message),f.message!=="TOKEN_EXPIRED"&&w(l,f.message,5e3,"error")}}return e.jsxs("div",{className:"flex items-start gap-12",children:[e.jsx("p",{className:"whitespace-nowrap font-semibold",children:"Add recipients"}),e.jsxs("div",{className:"flex flex-col items-end",children:[e.jsxs(Listbox,{value:selectedCompany,onChange:setSelectedCompany,as:"div",className:"relative mt-5 w-full text-left",children:[e.jsxs(Listbox.Button,{className:"relative inline-flex w-full rounded-md border px-4 py-2 text-lg font-medium focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75",children:[selectedCompany.id?selectedCompany.name:"Select Company",e.jsx("div",{className:"absolute inset-y-0 right-0 flex items-center border-l border-black/60 p-2",children:e.jsx(ChevronDownIcon,{className:"h-5 w-5 text-gray-400","aria-hidden":"true"})})]}),e.jsx(N,{as:t.Fragment,enter:"transition ease-out duration-100",enterFrom:"transform opacity-0 scale-95",enterTo:"transform opacity-100 scale-100",leave:"transition ease-in duration-75",leaveFrom:"transform opacity-100 scale-100",leaveTo:"transform opacity-0 scale-95",children:e.jsxs(Listbox.Options,{className:"absolute right-0 mt-2 w-full origin-top divide-y divide-gray-100 rounded-md bg-brown-main-bg shadow-lg ring-1 ring-[#1f1d1a]/5 focus:outline-none",children:[e.jsx("div",{className:"",children:companies.map(g=>e.jsx(Listbox.Option,{value:g,children:({active:f})=>e.jsx("button",{className:`${f?"bg-brown-main-bg":""} w-full px-4 py-2 text-left text-lg font-medium`,children:g.name})},g.id))}),e.jsx("button",{className:"mt-6 p-4 font-medium text-gray-500 underline",onClick:()=>setAddCompanyModal(!0),children:"+Add New Recipient"})]})})]}),e.jsxs("form",{className:"flex gap-4",onSubmit:m(u),children:[e.jsxs("div",{children:[e.jsxs("select",{...o("group_id"),className:`focus:shadow-outline w-full appearance-none rounded border px-12 py-2 leading-tight text-[#1f1d1a] shadow focus:outline-none ${(j=h.group_id)!=null&&j.message?"border-red-500":""}`,children:[e.jsx("option",{value:"",children:"Select Group"}),r.filter(g=>!s.some(f=>f.group_id==g.id)).map(g=>e.jsx("option",{value:g.id,children:g.group_name},g.id))]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(k=h.group_id)==null?void 0:k.message})]}),e.jsx(z,{loading:c,disabled:c,type:"submit",className:"rounded bg-primary-black px-4 py-2 text-white focus:outline-none",children:"Add"})]}),e.jsx("div",{className:"mt-2 flex flex-wrap justify-end gap-4",children:s.map(g=>e.jsx(X,{className:"relative",children:({open:f})=>e.jsxs(e.Fragment,{children:[e.jsxs(X.Button,{as:"div",className:"inline-flex items-center gap-3 rounded bg-brown-main-bg px-4 py-2 hover:text-gray-900",children:[g.group_name,e.jsx("button",{type:"button",onClick:x=>{x.stopPropagation(),b(g.id)},className:"ml-1 focus:outline-none",children:e.jsx(P,{className:"h-5 w-5",strokeWidth:2})})]},g.id),e.jsx(N,{as:t.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",children:e.jsx(X.Panel,{className:"absolute left-0 z-10 mt-3 w-fit -translate-x-1/2 transform whitespace-nowrap px-4",children:e.jsx("div",{className:"overflow-hidden  rounded-lg bg-[#1f1d1a] p-4 text-white shadow-lg ring-1 ring-[#1f1d1a]/5",children:e.jsxs("div",{className:"text-sm font-medium",children:[g.members.length==0?e.jsx("span",{children:"No members"}):null,g.members.map((x,y,_)=>e.jsxs("span",{className:"mr-2",children:[x.first_name," ",x.last_name," ",y==_.length-1?"":"•"]},x.id))]})})})})]})}))})]})]})};function Se({investors:s}){var k,g,f,x;const{id:p}=M(),{questions:d,refetch:n}=re(p),[a,r]=t.useState(!1),{dispatch:l}=t.useContext(E),{dispatch:i}=t.useContext(D),o=B({question:A().required("This field is required"),investor_id:A().required("This field is required")}),{register:m,handleSubmit:c,formState:{isSubmitting:h,errors:v},reset:u}=G({resolver:U(o),defaultValues:{question:"",investor_id:""}});async function b(y){try{await new F().callRawAPI("/v4/api/records/update_questions",{update_id:p,investor_id:y.investor_id,question:y.question},"POST"),w(i,"Question added successfully",5e3,"success"),u({question:"",investor_id:""}),n()}catch(_){S(l,_.message),_.message!=="TOKEN_EXPIRED"&&w(i,_.message,5e3,"error")}}async function j(y){r(!0);try{await new F().callRawAPI(`/v4/api/records/update_questions/${y}`,{},"DELETE"),w(i,"Question deleted successfully",5e3,"success"),n()}catch(_){S(l,_.message),_.message!=="TOKEN_EXPIRED"&&w(i,_.message,5e3,"error")}r(!1)}return e.jsxs("div",{className:"mt-12",children:[e.jsx("h4",{className:"text-xl font-bold",children:"Asks"}),e.jsx("div",{className:"mt-4 space-y-2",children:d.map(y=>e.jsxs("div",{className:"group flex max-w-xl items-start justify-between",children:[e.jsxs("p",{children:["- ",y.question," - ",y.user.email]}),e.jsx("button",{className:"hidden hover:text-red-500 group-hover:block",onClick:()=>j(y.id),disabled:a,children:e.jsx(me,{size:15})})]},y.id))}),e.jsxs("form",{onSubmit:c(b),className:"mt-4 flex items-center gap-4",children:[e.jsxs("div",{className:"flex max-w-5xl flex-grow items-center gap-6",children:[e.jsxs("div",{className:"flex-grow",children:[e.jsx("input",{type:"text",...m("question"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(k=v.question)!=null&&k.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(g=v.question)==null?void 0:g.message})]}),e.jsxs("div",{children:[e.jsxs("select",{className:`focus:shadow-outline w-full max-w-[200px] appearance-none rounded border border border-[#1f1d1a] bg-transparent py-2 pl-6 pr-8 font-Inter text-sm font-normal leading-tight text-[#1d1f1a] shadow shadow-none focus:outline-none ${(f=v.investor_id)!=null&&f.message?"border-red-500":""}`,...m("investor_id"),children:[e.jsx("option",{value:"",children:"-select fund manager-"}),s.map(y=>e.jsxs("option",{value:y.id,children:[y.first_name," ",y.last_name]},y.id))]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(x=v.investor_id)==null?void 0:x.message})]})]}),e.jsx(z,{type:"submit",loading:h,disabled:h||a,className:"rounded bg-primary-black px-4 py-2 font-bold text-white",children:"Ask"})]})]})}function Ee(s){const[p,d]=t.useState(!1),[n,a]=t.useState([]),{dispatch:r}=t.useContext(E),{dispatch:l}=t.useContext(D);async function i(){d(!0);try{const m=await new F().callRawAPI(`/v3/api/custom/goodbadugly/startup/updates/${s}/groups`);a(m.list)}catch(o){S(r,o.message),o.message!=="TOKEN_EXPIRED"&&w(l,o.message,5e3,"error")}d(!1)}return t.useEffect(()=>{s&&i()},[s]),{loading:p,updateGroups:n,refetch:i}}function Fe({afterRestore:s}){const[p,d]=t.useState(!1),{dispatch:n}=t.useContext(E),{dispatch:a}=t.useContext(D),[r,l]=t.useState(!1),{id:i}=M();async function o(){l(!0);try{const m=new te;await m.delete("notes",{update_id:i});for(let c=0;c<W.length;c++){const h=W[c];await m.create("notes",{type:h,update_id:i,status:0})}d(!1),s(),w(a,"Default template restored")}catch(m){S(n,m.message),m.message!=="TOKEN_EXPIRED"&&w(a,m.message,5e3,"error")}l(!1)}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"font-medium text-primary-black underline",onClick:()=>d(!0),children:"Clear template"}),e.jsx(N,{appear:!0,show:p,as:t.Fragment,children:e.jsxs(C,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>d(!1),children:[e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(C.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(C.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Are you sure"}),e.jsx("button",{onClick:()=>d(!1),type:"button",children:e.jsx(P,{className:"h-6 w-6"})})]}),e.jsx("p",{className:"mt-2",children:"Are you sure you want to restore to default?"}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>d(!1),children:"Cancel"}),e.jsx(Y,{loading:r,disabled:r,onClick:o,className:"disabled:bg-disabledblack h-[36px] rounded-lg bg-primary-black py-2 text-center font-iowan font-semibold text-white transition-colors duration-100",children:"Yes, restore"})]})]})})})})]})})]})}function De({update:s,afterEdit:p}){var v,u,b,j,k,g,f,x;const[d,n]=t.useState(!1),{dispatch:a}=t.useContext(E),{dispatch:r}=t.useContext(D),l=B({mrr:A().required("This field is required"),arr:A().required("This field is required"),cash:A().required("This field is required"),burnrate:A().required("This field is required")}),{register:i,handleSubmit:o,formState:{isSubmitting:m,errors:c}}=G({resolver:U(l),defaultValues:{mrr:s.mrr,arr:s.arr,cash:s.cash,burnrate:s.burnrate}});async function h(y){try{await new F().callRawAPI(`/v4/api/records/updates/${s.id}`,{mrr:y.mrr,arr:y.arr,burnrate:y.burnrate,cash:y.cash,sync:I.MANUAL},"PUT"),n(!1),p()}catch(_){S(a,_.message),_.message!=="TOKEN_EXPIRED"&&w(r,_.message,5e3,"error")}}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"rounded-md bg-primary-black p-2 font-iowan font-medium text-white",onClick:()=>n(!0),children:"Edit manually"}),e.jsx(N,{appear:!0,show:d,as:t.Fragment,children:e.jsxs(C,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>n(!1),children:[e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(C.Panel,{as:"form",className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",onSubmit:o(h),children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(C.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Edit update"}),e.jsx("button",{onClick:()=>n(!1),type:"button",children:e.jsx(P,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"MRR"}),e.jsx("input",{type:"text",...i("mrr"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(v=c.mrr)!=null&&v.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(u=c.mrr)==null?void 0:u.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"ARR"}),e.jsx("input",{type:"text",...i("arr"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(b=c.arr)!=null&&b.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(j=c.arr)==null?void 0:j.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Cash"}),e.jsx("input",{type:"text",...i("cash"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(k=c.cash)!=null&&k.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(g=c.cash)==null?void 0:g.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Burnrate"}),e.jsx("input",{type:"text",...i("burnrate"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(f=c.burnrate)!=null&&f.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(x=c.burnrate)==null?void 0:x.message})]}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>n(!1),children:"Cancel"}),e.jsx(z,{loading:m,disabled:m,type:"submit",className:"disabled:bg-disabledblack rounded-lg bg-primary-black py-2 text-center font-semibold text-white transition-colors duration-100",children:"Save"})]})]})})})})]})})]})}function Ae({update:s,refetchUpdate:p}){const[d,n]=t.useState(!1);t.useContext(E),t.useContext(D);const{profile:a}=se(),{getMany:r,showToast:l}=ae();t.useState(!1);const[i,o]=t.useState(!1),[m,c]=t.useState(!1),[h,v]=t.useState({stripe:null,plaid:null}),u=async()=>{var b,j,k,g,f,x;if(a!=null&&a.id)try{c(!0);const y=await r("stripe_integration",{filter:[`user_id,eq,${a==null?void 0:a.id}`,`company_id,eq,${(j=(b=a==null?void 0:a.companies)==null?void 0:b[0])==null?void 0:j.id}`]}),_=await r("plaid_integration",{filter:[`user_id,eq,${a==null?void 0:a.id}`,`company_id,eq,${(g=(k=a==null?void 0:a.companies)==null?void 0:k[0])==null?void 0:g.id}`]});v({stripe:((f=y==null?void 0:y.data)==null?void 0:f[0])||null,plaid:((x=_==null?void 0:_.data)==null?void 0:x[0])||null})}catch(y){console.error(y),l("Error fetching integrations",5e3,"error")}finally{c(!1)}};return useEffect(()=>{(async()=>a!=null&&a.id&&await u())()},[a==null?void 0:a.id]),e.jsxs(e.Fragment,{children:[e.jsxs("button",{className:"flex w-full items-center justify-between text-sm font-medium text-[#1f1d1a] md:text-[18px]",onClick:()=>n(!0),children:["Show financial metrics"," ",e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M17.5 18.3333H2.5C2.15833 18.3333 1.875 18.05 1.875 17.7083C1.875 17.3667 2.15833 17.0833 2.5 17.0833H17.5C17.8417 17.0833 18.125 17.3667 18.125 17.7083C18.125 18.05 17.8417 18.3333 17.5 18.3333Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M15.8495 2.89999C14.2328 1.28332 12.6495 1.24166 10.9912 2.89999L9.98283 3.90832C9.89949 3.99166 9.86616 4.12499 9.89949 4.24166C10.5328 6.44999 12.2995 8.21666 14.5078 8.84999C14.5412 8.85832 14.5745 8.86666 14.6078 8.86666C14.6995 8.86666 14.7828 8.83332 14.8495 8.76666L15.8495 7.75832C16.6745 6.94166 17.0745 6.14999 17.0745 5.34999C17.0828 4.52499 16.6828 3.72499 15.8495 2.89999Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M13.0089 9.60832C12.7673 9.49166 12.5339 9.37499 12.3089 9.24166C12.1256 9.13332 11.9506 9.01666 11.7756 8.89166C11.6339 8.79999 11.4673 8.66666 11.3089 8.53332C11.2923 8.52499 11.2339 8.47499 11.1673 8.40832C10.8923 8.17499 10.5839 7.87499 10.3089 7.54166C10.2839 7.52499 10.2423 7.46666 10.1839 7.39166C10.1006 7.29166 9.95892 7.12499 9.83392 6.93332C9.73392 6.80832 9.61726 6.62499 9.50892 6.44166C9.37559 6.21666 9.25892 5.99166 9.14226 5.75832C9.1246 5.72049 9.10752 5.68286 9.09096 5.64544C8.96798 5.36767 8.60578 5.28647 8.39098 5.50126L3.61726 10.275C3.50892 10.3833 3.40892 10.5917 3.38392 10.7333L2.93392 13.925C2.85059 14.4917 3.00892 15.025 3.35892 15.3833C3.65892 15.675 4.07559 15.8333 4.52559 15.8333C4.62559 15.8333 4.72559 15.825 4.82559 15.8083L8.02559 15.3583C8.17559 15.3333 8.38392 15.2333 8.48392 15.125L13.2517 10.3572C13.468 10.1409 13.3864 9.76972 13.105 9.64967C13.0734 9.63615 13.0414 9.62238 13.0089 9.60832Z",fill:"#1F1D1A"})]})]}),e.jsx(N,{appear:!0,show:d,as:t.Fragment,children:e.jsxs(C,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>n(!1),children:[e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(C.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(C.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Financial Overview"}),e.jsx("button",{onClick:()=>n(!1),type:"button",children:e.jsx(P,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-4 space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"text-sm font-normal md:text-base",children:[s.sync==I.MANUAL?"*":"","MRR"]}),e.jsx("p",{children:s.mrr})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"text-sm font-normal md:text-base",children:[s.sync==I.MANUAL?"*":"","ARR"]}),e.jsx("p",{children:s.arr})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"text-sm font-normal md:text-base",children:[s.sync==I.MANUAL?"*":"","Cash"]}),e.jsx("p",{children:s.cash})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"text-sm font-normal md:text-base",children:[s.sync==I.MANUAL?"*":"","Burnrate"]}),e.jsx("p",{children:s.burnrate})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:"text-sm font-normal md:text-base",children:"Last sync"}),e.jsxs("p",{children:[" ",s.last_sync?O(s.last_sync).format("DD MMM hh:mm a z"):"N/A"]})]}),e.jsx("div",{className:"grid grid-cols-2 gap-4",children:s.id?e.jsx(De,{update:s,afterEdit:p,loading:m}):null}),i?e.jsx("div",{className:"mt-4 border border-black/60 bg-brown-main-bg p-3",children:e.jsxs("p",{className:"flex items-center gap-3 font-semibold",children:[e.jsx(ue,{className:"h-5 text-yellow-500",strokeWidth:2}),"No integrations setup currently."]})}):null]})]})})})})]})})]})}function Ie({update:s,afterEdit:p}){var v,u,b,j,k,g,f,x,y,_;const[d,n]=t.useState(!1),{dispatch:a}=t.useContext(E),{dispatch:r}=t.useContext(D),l=B({investment_stage:A().required("This field is required"),invested_to_date:A().required("This field is required"),investors_on_cap_table:A().required("This field is required"),valuation_at_last_round:A().required("This field is required"),date_of_last_round:A().required("This field is required")}),{register:i,handleSubmit:o,formState:{isSubmitting:m,errors:c}}=G({resolver:U(l),defaultValues:{investment_stage:s.investment_stage,invested_to_date:s.invested_to_date,investors_on_cap_table:s.investors_on_cap_table,valuation_at_last_round:s.valuation_at_last_round,date_of_last_round:s.date_of_last_round}});async function h($){try{await new F().callRawAPI(`/v4/api/records/updates/${s.id}`,{investment_stage:$.investment_stage,invested_to_date:$.invested_to_date,valuation_at_last_round:$.valuation_at_last_round,investors_on_cap_table:$.investors_on_cap_table,date_of_last_round:$.date_of_last_round,investment_details_sync:I.MANUAL},"PUT"),n(!1),p()}catch(R){S(a,R.message),R.message!=="TOKEN_EXPIRED"&&w(r,R.message,5e3,"error")}}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"rounded-md bg-primary-black p-2 font-iowan font-medium text-white",onClick:()=>n(!0),children:"Edit manually"}),e.jsx(N,{appear:!0,show:d,as:t.Fragment,children:e.jsxs(C,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>n(!1),children:[e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(C.Panel,{as:"form",className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",onSubmit:o(h),children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(C.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Edit update"}),e.jsx("button",{onClick:()=>n(!1),type:"button",children:e.jsx(P,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Investment Stage"}),e.jsx("input",{type:"text",...i("investment_stage"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(v=c.investment_stage)!=null&&v.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(u=c.investment_stage)==null?void 0:u.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Invested to Date"}),e.jsx("input",{type:"text",...i("invested_to_date"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(b=c.invested_to_date)!=null&&b.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(j=c.invested_to_date)==null?void 0:j.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Fund managers on Cap Table"}),e.jsx("input",{type:"text",...i("investors_on_cap_table"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(k=c.investors_on_cap_table)!=null&&k.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(g=c.investors_on_cap_table)==null?void 0:g.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Valuation at Last Round"}),e.jsx("input",{type:"text",...i("valuation_at_last_round"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(f=c.valuation_at_last_round)!=null&&f.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(x=c.valuation_at_last_round)==null?void 0:x.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Date of Last Round"}),e.jsx("input",{type:"date",...i("date_of_last_round"),className:`focus:shadow-outline w-full max-w-[500px]  appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 pr-5 text-sm font-normal leading-tight   text-[#1d1f1a] shadow focus:outline-none sm:pr-3 ${(y=c.date_of_last_round)!=null&&y.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(_=c.date_of_last_round)==null?void 0:_.message})]}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>n(!1),children:"Cancel"}),e.jsx(z,{loading:m,disabled:m,type:"submit",className:"disabled:bg-disabledblack rounded-lg bg-primary-black py-2 text-center font-semibold text-white transition-colors duration-100",children:"Save"})]})]})})})})]})})]})}function Pe({update:s,refetchUpdate:p}){const[d,n]=t.useState(!1);return t.useContext(E),t.useContext(D),t.useState(!1),e.jsxs(e.Fragment,{children:[e.jsxs("button",{className:"flex w-full items-center justify-between text-[16px] font-medium text-[#1f1d1a] md:text-[18px]",onClick:()=>n(!0),children:["Show investment metrics"," ",e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M17.5 18.3333H2.5C2.15833 18.3333 1.875 18.05 1.875 17.7083C1.875 17.3667 2.15833 17.0833 2.5 17.0833H17.5C17.8417 17.0833 18.125 17.3667 18.125 17.7083C18.125 18.05 17.8417 18.3333 17.5 18.3333Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M15.8495 2.89999C14.2328 1.28332 12.6495 1.24166 10.9912 2.89999L9.98283 3.90832C9.89949 3.99166 9.86616 4.12499 9.89949 4.24166C10.5328 6.44999 12.2995 8.21666 14.5078 8.84999C14.5412 8.85832 14.5745 8.86666 14.6078 8.86666C14.6995 8.86666 14.7828 8.83332 14.8495 8.76666L15.8495 7.75832C16.6745 6.94166 17.0745 6.14999 17.0745 5.34999C17.0828 4.52499 16.6828 3.72499 15.8495 2.89999Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M13.0089 9.60832C12.7673 9.49166 12.5339 9.37499 12.3089 9.24166C12.1256 9.13332 11.9506 9.01666 11.7756 8.89166C11.6339 8.79999 11.4673 8.66666 11.3089 8.53332C11.2923 8.52499 11.2339 8.47499 11.1673 8.40832C10.8923 8.17499 10.5839 7.87499 10.3089 7.54166C10.2839 7.52499 10.2423 7.46666 10.1839 7.39166C10.1006 7.29166 9.95892 7.12499 9.83392 6.93332C9.73392 6.80832 9.61726 6.62499 9.50892 6.44166C9.37559 6.21666 9.25892 5.99166 9.14226 5.75832C9.1246 5.72049 9.10752 5.68286 9.09096 5.64544C8.96798 5.36767 8.60578 5.28647 8.39098 5.50126L3.61726 10.275C3.50892 10.3833 3.40892 10.5917 3.38392 10.7333L2.93392 13.925C2.85059 14.4917 3.00892 15.025 3.35892 15.3833C3.65892 15.675 4.07559 15.8333 4.52559 15.8333C4.62559 15.8333 4.72559 15.825 4.82559 15.8083L8.02559 15.3583C8.17559 15.3333 8.38392 15.2333 8.48392 15.125L13.2517 10.3572C13.468 10.1409 13.3864 9.76972 13.105 9.64967C13.0734 9.63615 13.0414 9.62238 13.0089 9.60832Z",fill:"#1F1D1A"})]})]}),e.jsx(N,{appear:!0,show:d,as:t.Fragment,children:e.jsxs(C,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>n(!1),children:[e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(C.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(C.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Investment Overview"}),e.jsx("button",{onClick:()=>n(!1),type:"button",children:e.jsx(P,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-4 space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"text-sm font-normal md:text-base",children:[s.investment_details_sync==I.MANUAL?"*":"","Investment Stage"]}),e.jsx("p",{children:s.investment_stage??"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"text-sm font-normal  md:text-base",children:[s.investment_details_sync==I.MANUAL?"*":"","Invested to Date"]}),e.jsx("p",{children:s.invested_to_date??"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"text-sm font-normal  md:text-base",children:[s.investment_details_sync==I.MANUAL?"*":"","Fund managers on Cap Table"]}),e.jsx("p",{children:s.investors_on_cap_table??"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"text-sm font-normal  md:text-base",children:[s.investment_details_sync==I.MANUAL?"*":"","Valuation at Last Round"]}),e.jsx("p",{children:s.valuation_at_last_round??"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:"text-sm font-normal  md:text-base",children:"Date of Last Round"}),e.jsxs("p",{children:[" ",s.date_of_last_round?O(s.date_of_last_round).format("DD MMM"):"N/A"]})]}),e.jsx("div",{className:"grid grid-cols-2 gap-4",children:s.id?e.jsx(Ie,{update:s,afterEdit:p}):null})]})]})})})})]})})]})}const Re={paragraph:pe,checkList:je,list:he,header:fe,delimiter:be,link:ge},Oe=t.memo(({data:s,editing:p=!0,editorID:d,note_id:n,afterEdit:a,setUpdated:r,updateSaved:l,report:i=!1})=>{const{dispatch:o}=t.useContext(E),{dispatch:m}=t.useContext(D),[c,h]=t.useState(!1);console.log(d);async function v(b){try{const k=await new F().callRawAPI(`/v4/api/records/notes/${n}`,{content:JSON.stringify(b)},"PUT");i&&a()}catch(j){S(o,j.message),j.message!=="TOKEN_EXPIRED"&&w(m,j.message,5e3,"error")}}const u=t.useRef();return t.useEffect(()=>{if(!u.current){const b=new xe({holder:d,minHeight:30,readOnly:!p,tools:Re,data:s,async onChange(j,k){const g=await j.saver.save();v(g)},onReady:()=>{u.current=b}})}return()=>{u.current&&u.current.destroy&&u.current.destroy()}},[]),t.useEffect(()=>{u.current&&u.current.readOnly.toggle(!p)},[p]),e.jsx("div",{className:`${p?"editorjs-container border border-green-300":"editorjs-container-transparent  "} rounded   px-3 py-2`,id:d,onBlur:()=>h(!0)})});function $e({note_id:s,afterDelete:p}){const[d,n]=t.useState(!1),{dispatch:a}=t.useContext(E),{dispatch:r}=t.useContext(D),[l,i]=t.useState(!1);async function o(){i(!0);try{await new F().callRawAPI(`/v4/api/records/notes/${s}`,{},"DELETE"),n(!1),p()}catch(m){S(a,m.message),m.message!=="TOKEN_EXPIRED"&&w(r,m.message,5e3,"error")}i(!1)}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"rounded bg-red-500 px-4 py-2 font-medium text-white",onClick:()=>n(!0),children:"Delete"}),e.jsx(N,{appear:!0,show:d,as:t.Fragment,children:e.jsxs(C,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>n(!1),children:[e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(C.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(C.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Are you sure you want to delete this entry?"}),e.jsx("button",{onClick:()=>n(!1),type:"button",children:e.jsx(P,{className:"h-6 w-6"})})]}),e.jsx("p",{className:"mt-2",children:"This action cannot be undone."}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>n(!1),children:"Cancel"}),e.jsx(z,{loading:l,disabled:l,onClick:o,className:"rounded-lg bg-[#1f1d1a] py-2 text-center font-iowan font-semibold text-white transition-colors duration-100 disabled:bg-opacity-60",children:"Yes, delete"})]})]})})})})]})})]})}function Le({note:s,afterAsking:p}){const[d,n]=t.useState(!1),{dispatch:a}=t.useContext(E),{dispatch:r}=t.useContext(D),l=B({expandOrShorten:A().nullable().optional(),rephrase:H(),correctGrammar:H()}),{register:i,handleSubmit:o,formState:{isSubmitting:m},reset:c}=G({resolver:U(l),defaultValues:{expandOrShorten:"",rephrase:!1,correctGrammar:!1}});function h(u){return!u||!u.blocks?"":u.blocks.map(b=>b.type==="header"?`${b.data.text}`:b.type==="list"?b.data.items.map(j=>`• ${j}`).join(`
`):b.type==="paragraph"?b.data.text:"").join(`

`)}async function v(u){try{let b=h(V(s.content,{blocks:[]}));const j=new F;if(u.expandOrShorten||u.correctGrammar||u.rephrase){const g=`
        ${u.expandOrShorten?`${u.expandOrShorten} text. 
`:""}
        ${u.rephrase?`Rewrite text. 
`:""}
        ${u.correctGrammar?`Fix grammar. 
`:""}
            ${b}
        `.trim(),f=[{role:"system",content:"You are an expert auditor auditing for a startup. create and update for the investor do not explain"},{role:"user",content:g}];b=(await j.callRawAPI("/v3/api/custom/goodbadugly/ai/ask",{temperature:1,prompt:f},"POST")).data[0].message.content,await j.callRawAPI(`/v4/api/records/notes/${s.id}`,{content:JSON.stringify({time:Date.now(),blocks:[{id:Math.floor(Math.random()*999)+1,type:"list",data:{style:"unordered",items:[b]}}]})},"PUT")}p(),n(!1),c({expandOrShorten:"",rephrase:!1,correctGrammar:!1})}catch(b){S(a,b.message),b.message!=="TOKEN_EXPIRED"&&w(r,b.message,5e3,"error")}}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"rounded bg-primary-black px-4 py-2 font-medium text-white",onClick:()=>{n(!0),p()},children:"Ask AI"}),e.jsx(N,{appear:!0,show:d,as:t.Fragment,children:e.jsxs(C,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>n(!1),children:[e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(C.Panel,{className:"w-full max-w-3xl transform overflow-hidden rounded-md bg-brown-main-bg bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",as:"form",onSubmit:o(v),children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(C.Title,{as:"h3",className:"text-xl font-semibold leading-6 text-gray-900",children:"Ask AI"}),e.jsx("button",{onClick:()=>n(!1),type:"button",children:e.jsx(P,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"bold mt-4 flex items-center gap-4 font-iowan text-lg",children:[e.jsx("input",{type:"radio",...i("expandOrShorten"),value:"expand"}),e.jsx("label",{children:"Expand"}),e.jsx("input",{type:"radio",...i("expandOrShorten"),value:"shorten"}),e.jsx("label",{children:"Shorten"})]}),e.jsxs("div",{className:"bold flex items-center gap-4 font-iowan text-lg",children:[e.jsx("input",{type:"checkbox",...i("rephrase")}),e.jsx("label",{children:"Rephrase"})]}),e.jsxs("div",{className:"bold flex items-center gap-4 font-iowan text-lg",children:[e.jsx("input",{type:"checkbox",...i("correctGrammar")}),e.jsx("label",{children:"Correct Grammar"})]}),e.jsx(z,{type:"submit",loading:m,disabled:m,className:"mt-6 w-full rounded bg-primary-black px-4 py-2 font-bold text-white",children:"Send request"})]})})})})]})})]})}function Me({note:s,afterEdit:p}){const[d,n]=t.useState(!1),{dispatch:a}=t.useContext(E),{dispatch:r}=t.useContext(D),[l,i]=t.useState(!1),[o,m]=t.useState(null),[c,h]=t.useState(!1),{startRecording:v,stopRecording:u,recordingBlob:b,isRecording:j}=ve();t.useEffect(()=>{b&&(console.log("setting blob"),m(b))},[b]),t.useEffect(()=>{!o||!l||k()},[o,l]);async function k(){n(!0);try{let f=new FormData;const x=new File([o],"audio.wav",{type:"audio/wav"});f.append("file",x),console.log("f",o);const _=await new F().callTranscribe("/v3/api/custom/goodbadugly/ai/transcribe-audio",f,"POST");console.log(_);const $=V(s.content,{blocks:[]});g(JSON.stringify({time:Date.now(),blocks:[...$.blocks,{id:Math.floor(Math.random()*999)+1,type:"list",data:{style:"unordered",items:[_.data]}}]}))}catch(f){S(a,f.message),f.message!=="TOKEN_EXPIRED"&&w(r,f.message,5e3,"error")}n(!1),i(!1),m(null)}async function g(f){h(!0);try{await new F().callRawAPI(`/v4/api/records/notes/${s.id}`,{content:f},"PUT"),p()}catch(x){S(a,x.message),x.message!=="TOKEN_EXPIRED"&&w(r,x.message,5e3,"error")}h(!1)}return e.jsx("button",{onClick:async()=>{j?(u(),i(!0)):v()},disabled:d||c,className:`focus:shadow-outline ${j?"animate-pulse":""}`,title:"Transcribe more",children:j?e.jsx("div",{className:"rounded-[50%] bg-red-500 p-2",children:e.jsx(ye,{className:"h-6 text-white"})}):e.jsx(we,{className:"h-6 w-6 text-primary-black",strokeWidth:2})})}function ze({note:s,afterEdit:p}){const{dispatch:d}=t.useContext(E),{dispatch:n}=t.useContext(D),[a,r]=t.useState(!1),[l,i]=t.useState(""),[o,m]=t.useState(null);t.useEffect(()=>{i(s.type)},[s]);async function c(h){r(!0);try{await new F().callRawAPI(`/v4/api/records/notes/${s.id}`,{type:h},"PUT"),p(),w(n,"Saved")}catch(v){S(d,v.message),v.message!=="TOKEN_EXPIRED"&&w(n,v.message,5e3,"error")}r(!1)}return e.jsx(e.Fragment,{children:e.jsx("input",{className:"no-box-shadow w-full rounded border-none bg-transparent p-0 text-xl font-bold ring-transparent",value:l,onChange:h=>{i(h.target.value),o&&clearTimeout(o);const v=setTimeout(()=>c(h.target.value),2e3);m(v)},readOnly:a})})}const J=t.memo(({note:s,refetch:p,profilePic:d})=>{var o;const{dispatch:n,state:a}=t.useContext(E),{note:r,refetch:l}=Ne(s.id,s),i=V(r.content,{blocks:[{id:"zbGZFPM-iI",type:"paragraph",data:{text:""}}]});return console.log(a),e.jsx("div",{className:"flex flex-col gap-4 justify-between",children:e.jsxs("div",{className:"flex flex-col justify-between",children:[e.jsxs("div",{className:"flex flex-row justify-between items-center w-full",children:[e.jsx("div",{className:"flex gap-3 items-center group",children:e.jsx(ze,{note:r,afterEdit:l})}),d&&e.jsx("img",{className:"h-7 w-7 rounded-[50%] object-cover",src:((o=a==null?void 0:a.profile)==null?void 0:o.photo)||"/default.png"})]}),e.jsx("div",{className:"self-end mt-4 space-y-4 w-full",children:e.jsxs("div",{className:"",children:[" ",e.jsx(Oe,{data:i,note_id:r.id,editorID:`editorjs-container-${r.id}`,afterEdit:l}),e.jsxs("div",{className:"flex justify-between items-center mt-4",children:[e.jsxs("div",{className:"flex gap-4 items-center",children:[e.jsx(Le,{note:r,afterAsking:l}),e.jsx($e,{note_id:r.id,afterDelete:p}),e.jsx(Me,{note:r,afterEdit:l})]}),e.jsxs("p",{className:"font-medium",children:["Last saved:"," ",Math.abs(O(r.update_at).diff(O(),"hours"))<24?O(r.update_at).format("hh:mm a z"):Math.abs(O(r.update_at).diff(O(),"years"))>0?O(r.update_at).format("MM/DD/YYYY"):O(r.update_at).format("DD MMM hh:mm a z")]})]})]})})]})})});function qe({afterAdd:s}){const[p,d]=t.useState(!1),{dispatch:n}=t.useContext(E),{dispatch:a}=t.useContext(D),[r,l]=t.useState(!1),[i,o]=t.useState(""),{id:m}=M();async function c(){l(!0);try{await new F().callRawAPI("/v4/api/records/notes",{type:i,update_id:m,status:0},"POST"),d(!1),o(""),s(),w(a,"New section added")}catch(h){S(n,h.message),h.message!=="TOKEN_EXPIRED"&&w(a,h.message,5e3,"error")}l(!1)}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"mt-6 block w-full rounded-[2px] bg-primary-black/90 py-2 font-semibold text-white",onClick:()=>d(!0),children:"Add section +"}),e.jsx(N,{appear:!0,show:p,as:t.Fragment,children:e.jsxs(C,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>d(!1),children:[e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(C.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-sm shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(C.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Add section name"}),e.jsx("button",{onClick:()=>d(!1),type:"button",children:e.jsx(P,{className:"h-6 w-6"})})]}),e.jsx("div",{className:"mt-6",children:e.jsx("input",{type:"text",value:i,onChange:h=>o(h.target.value),className:"focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none "})}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a]/30 py-2 text-center font-iowan font-medium",type:"button",onClick:()=>d(!1),children:"Cancel"}),e.jsx(Y,{loading:r,disabled:r,onClick:c,className:"disabled:bg-disabledblack rounded-lg bg-primary-black py-2 text-center font-semibold text-white transition-colors duration-100",children:"Save"})]})]})})})})]})})]})}function Ue({update:s,afterEdit:p}){const{dispatch:d}=t.useContext(E),{dispatch:n}=t.useContext(D),[a,r]=t.useState(!1),[l,i]=t.useState(""),{id:o}=M(),[m,c]=t.useState(null);t.useEffect(()=>{i(s.name)},[s]);async function h(v){r(!0);try{await new F().callRawAPI(`/v4/api/records/updates/${o}`,{name:v},"PUT"),p(),w(n,"Saved")}catch(u){S(d,u.message),u.message!=="TOKEN_EXPIRED"&&w(n,u.message,5e3,"error")}r(!1)}return e.jsx(e.Fragment,{children:e.jsx("input",{className:"no-box-shadow focus:shadow-outline appearance-none border-none bg-brown-main-bg p-0 text-3xl font-bold focus:outline-none",value:l,onChange:v=>{i(v.target.value),m&&clearTimeout(m);const u=setTimeout(()=>h(v.target.value),2e3);c(u)},readOnly:a,style:{width:`${l.length+3}ch`}})})}function Ge({update:s,refetch:p}){const[d,n]=t.useState(!1),{dispatch:a}=t.useContext(E),{dispatch:r}=t.useContext(D),[l,i]=t.useState(!1),[o,m]=t.useState(s.private_link_open==K.YES),{id:c}=M(),[h,v]=t.useState(s.public_link_id),[u,b]=t.useState(s.private_link_access),[j,k]=t.useState(s.public_link_enabled==1);async function g(){i(!0);try{await new F().callRawAPI(`/v4/api/records/updates/${c}`,{private_link_access:u,private_link_open:o?K.YES:K.NO,public_link_id:h,public_link_enabled:j?1:0},"PUT"),p(),w(r,"Saved"),n(!1)}catch(x){S(a,x.message),x.message!=="TOKEN_EXPIRED"&&w(r,x.message,5e3,"error")}i(!1)}function f(){v(ne(15)),w(r,"New link generated")}return e.jsxs(e.Fragment,{children:[e.jsxs("button",{className:"flex items-center gap-2 font-medium",onClick:()=>n(!0),children:[e.jsx(_e,{className:"h-4",strokeWidth:2}),Z[s.private_link_access]]}),e.jsx(N,{appear:!0,show:d,as:t.Fragment,children:e.jsxs(C,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>n(!1),children:[e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(C.Panel,{className:"relative mb-12 w-full max-w-4xl transform overflow-visible rounded-md bg-brown-main-bg p-8 text-left align-middle text-base shadow-xl transition-all",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsx(C.Title,{as:"h3",className:"text-xl font-bold text-gray-900",children:"Share this update"})}),e.jsxs("div",{className:"mt-6 flex items-center gap-6 rounded-md bg-brown-main-bg p-4 font-medium text-gray-800",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-lg font-semibold text-[#1f1d1a]",children:"Restricted link"}),e.jsx("p",{children:"Logged in users that have access to your update will be able to view it on update sent with the following link."})]}),e.jsx("select",{className:"focus:shadow-outline appearance-none rounded border py-2 pl-3 pr-8 leading-tight text-[#1f1d1a] shadow focus:outline-none ",value:u,onChange:x=>b(x.target.value),children:Object.entries(Z).map(([x,y])=>e.jsx("option",{value:x,children:y},y))})]}),e.jsxs("div",{className:"mt-6 flex items-center gap-12 rounded-md bg-blue-100 px-4 py-3",children:[e.jsx("input",{id:"private-link-input",onFocus:x=>x.target.select(),value:`${window.location.origin}/reports/view/${c}`,className:"no-box-shadow flex-grow border-none bg-transparent px-2 py-0 text-lg font-medium text-primary-black ring-transparent selection:bg-green-200"}),e.jsx("button",{type:"button",className:"font-semibold text-primary-black",onClick:async()=>{var x;await navigator.clipboard.writeText(`${window.location.origin}/reports/view/${c}`),(x=document.getElementById("private-link-input"))==null||x.select(),w(r,"Copied")},children:"Copy link"})]}),e.jsxs("div",{className:"mt-4 flex items-center gap-4",children:[e.jsx(q,{enabled:o,setEnabled:m}),e.jsx("p",{children:"If allowed access via update privacy, enable logged out users to view by providing their email"})]}),e.jsxs("div",{className:"mt-6 rounded-md border border-black/60 p-4",children:[e.jsxs("p",{className:"font-semibold",children:["Shareable link ",j?"":"(disabled)"]}),e.jsx("p",{className:"mt-2",children:"Anyone with the shareable link can view this update."}),e.jsxs("div",{className:"mt-6 flex items-center gap-12 rounded-md bg-red-100 px-4 py-3",children:[e.jsx("input",{value:h?`${window.location.origin}/reports/public/view/${c}/${h}`:"",onFocus:x=>x.target.select(),className:`no-box-shadow flex-grow border-none bg-transparent px-2 py-0 text-lg font-medium text-red-500 selection:bg-orange-200 focus:outline-transparent ${j?"":"opacity-50"}`,disabled:!j,id:"public-link-input"}),e.jsx("button",{type:"button",className:"font-semibold text-red-500",disabled:!h||!j,onClick:async()=>{var x;await navigator.clipboard.writeText(`${window.location.origin}/reports/public/view/${c}/${h}`),(x=document.getElementById("public-link-input"))==null||x.select(),w(r,"Copied")},children:"Copy link"})]}),e.jsxs("div",{className:"mt-6 flex items-center gap-3",children:[e.jsx("button",{className:"rounded-md bg-primary-black px-4 py-2 font-iowan font-medium text-white",onClick:f,children:"Generate New Shareable Link"}),s.public_link_id&&j?e.jsx("button",{className:"rounded-md border border-[#1f1d1a] px-4 py-2 font-iowan text-[14px] font-medium  xl:text-[16px]",onClick:()=>k(!1),children:"Disable Shareable Link"}):null,s.public_link_id&&!j?e.jsx("button",{className:"rounded-md border border-[#1f1d1a] px-4 py-2 font-iowan text-[14px] font-medium  xl:text-[16px]",onClick:()=>k(!0),children:"Enable Shareable Link"}):null]})]}),e.jsx("div",{className:"mt-12 flex justify-end",children:e.jsx(Y,{loading:l,disabled:l,onClick:g,className:"disabled:bg-disabledblack rounded-md bg-primary-black px-6 py-2 text-center font-semibold text-white transition-colors duration-100",children:"Save"})}),e.jsx("button",{onClick:()=>n(!1),type:"button",className:"absolute right-0 top-0 -translate-y-1/2 translate-x-1/2 rounded-[50%] bg-brown-main-bg p-1 shadow-md",children:e.jsx(P,{className:"h-6 w-6 text-gray-900",strokeWidth:5})})]})})})})]})})]})}const It=()=>{const{dispatch:s}=t.useContext(D),{dispatch:p,state:d}=t.useContext(E),{id:n}=M(),{update:a,loading:r,refetch:l}=le(n),{notes:i,refetch:o}=oe(n),{updateGroups:m,refetch:c}=Ee(n),[h,v]=t.useState(""),[u,b]=t.useState("standard"),[j,k]=t.useState(!1),[g,f]=t.useState("7"),[x,y]=t.useState(!1),{updateCollaborators:_,refetch:$}=ce(n);t.useEffect(()=>{v(a.date),f(a.recipient_access||"7")},[a]),t.useEffect(()=>{s({type:"SETPATH",payload:{path:"updates"}})},[]);async function R(T){y(!0);try{await new F().callRawAPI(`/v4/api/records/updates/${n}`,{[T]:a[T]?0:1},"PUT"),l(),w(s,"Updated")}catch(L){S(p,L.message),L.message!=="TOKEN_EXPIRED"&&w(s,L.message,5e3,"error")}y(!1)}return r?e.jsx(Ce,{}):(console.log(i),e.jsxs("div",{className:"mx-auto rounded px-5 py-5 shadow-md xl:px-12",children:[e.jsxs("h2",{className:"text-xl font-normal",children:[d.company.name," ",a.status==ie.DRAFT?e.jsx("span",{className:"ml-4 bg-brown-main-bg text-xl text-gray-400/90",children:"DRAFT IN PROGRESS"}):null]}),e.jsxs("div",{className:"mt-8 flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(Ue,{update:a,afterEdit:l}),e.jsx("div",{children:e.jsx("input",{className:"border pr-5 sm:pr-3",type:"date",value:h,onChange:T=>v(T.target.value)})})]}),e.jsx(Fe,{afterRestore:o})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("p",{className:"font-medium",children:"Privacy:"}),e.jsx(Ge,{update:a,refetch:l}),e.jsxs("button",{className:"flex items-center gap-2 rounded-md bg-brown-main-bg px-3 py-1",children:[e.jsx(de,{className:"h-4",strokeWidth:2}),"Share"]})]})]}),e.jsxs("div",{className:"",children:[e.jsxs("div",{className:"mt-12 flex items-start justify-between",children:[e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(q,{enabled:a.show_financial_metrics==1,setEnabled:()=>R("show_financial_metrics")}),e.jsx(Ae,{update:a,refetchUpdate:l})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(q,{enabled:a.show_company_metrics==1,setEnabled:()=>R("show_company_metrics")}),e.jsx("p",{className:"text-xl font-semibold text-gray-700",children:"Show company/team metrics"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(q,{enabled:a.show_marketing_metrics==1,setEnabled:()=>R("show_marketing_metrics")}),e.jsx("p",{className:"text-xl font-semibold text-gray-700",children:"Show marketing metrics"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(q,{enabled:a.show_investment_metrics==1,setEnabled:()=>R("show_investment_metrics")}),e.jsx(Pe,{update:a,refetchUpdate:l})]})]}),e.jsxs("div",{className:"",children:[e.jsx("div",{className:"flex w-full max-w-xl justify-end",children:e.jsx(Te,{updateGroups:m,refetch:c})}),e.jsxs("div",{className:"mt-6 flex items-center justify-end gap-6",children:[e.jsx("p",{className:"font-semibold",children:"Select update template"}),e.jsx("div",{children:e.jsxs("select",{value:u,onChange:T=>b(T.target.value),className:"focus:shadow-outline w-full appearance-none rounded border bg-[#FFF4EC] py-2 pl-6 pr-8 leading-tight text-[#1f1d1a] shadow focus:outline-none ",children:[e.jsx("option",{value:"",children:"Select Template"}),e.jsx("option",{value:"standard",children:"Standard template"})]})})]})]})]}),e.jsx("div",{className:"mt-10 space-y-12",children:i.map(T=>_.some(L=>L.note_id===T.id)?e.jsx(J,{note:T,refetch:o,profilePic:!0},T.id):e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute z-[100] h-full w-full cursor-not-allowed"}),e.jsx(J,{note:T,refetch:o},T.id)]}))}),e.jsx(qe,{afterAdd:o}),e.jsx(Se,{investors:m.flatMap(T=>T.members).filter((T,L,Q)=>Q.findIndex(ee=>ee.id===T.id)===L)}),e.jsxs("div",{className:"mt-8 flex flex-col items-start justify-between md:flex-row md:items-center",children:[e.jsxs("label",{className:"flex items-center gap-3 text-lg font-medium capitalize",children:[e.jsx("input",{type:"checkbox",checked:j,onChange:()=>k(T=>!T)})," ","CC myself"]}),e.jsxs("div",{className:"flex w-fit flex-wrap items-center gap-4",children:[e.jsx("p",{className:"whitespace-nowrap",children:"Give recipients access for "}),e.jsxs("select",{value:g,onChange:T=>f(T.target.value),className:"focus:shadow-outline w-full appearance-none rounded border py-2 pl-6 pr-8 leading-tight text-[#1f1d1a] shadow focus:outline-none ",children:[e.jsx("option",{value:"7",children:"7 days"}),e.jsx("option",{value:"30",children:"One month"})]}),e.jsx("a",{href:`/collaborator/updates/${n}/preview`,target:"_blank",className:"focus:shadow-outline w-fit whitespace-nowrap rounded bg-primary-black px-4 py-2 font-bold text-white focus:outline-none",children:"Preview Update"})]})]})]})]}))};export{It as default};
