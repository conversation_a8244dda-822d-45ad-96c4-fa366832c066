import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{b as x,r as a}from"./vendor-4cdf2bd1.js";import{A as u,G as h}from"./index-46de5032.js";import{_ as f}from"./MoonLoader-6f2b5db4.js";import{t as s,S as o}from"./@headlessui/react-cdd9213e.js";const N=({open:r,setOpen:n,accounts:i,loading:l,login:c,data:d})=>{x(),a.useContext(u),a.useContext(h);const[m,p]=a.useState();return e.jsx(s,{appear:!0,show:r,as:a.Fragment,children:e.jsxs(o,{as:"div",className:"relative z-[7000]",onClose:()=>n(!0),children:[e.jsx(s.Child,{as:a.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(s.Child,{as:a.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(o.Panel,{className:"w-full max-w-[500px] transform space-y-10 overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-sm shadow-xl transition-all",children:[e.jsx("h5",{className:"mt-6 text-center",children:"Select an account to login"}),e.jsx("div",{className:"space-y-2",children:i.map(t=>e.jsxs("div",{className:"flex h-10 cursor-pointer flex-row items-center rounded-md border-[1px] border-black/60 p-1 px-4",onClick:()=>{p(t.company_id),c("",{company_id:t.company_id,data:d})},children:[e.jsx(f,{color:"black",loading:l&&m===t.company_id,size:20}),e.jsx("span",{className:"text-[15px] ",children:t.company_name})]},t.company_id))})]})})})})]})})};export{N as P};
