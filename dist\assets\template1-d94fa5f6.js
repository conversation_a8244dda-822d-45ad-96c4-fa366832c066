import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{M as x}from"./index-46de5032.js";import{R as h,i as p,r as i}from"./vendor-4cdf2bd1.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const I=({data:n})=>{const{badNews:l,goodNews:m,uglyNews:c,weeklyNotes:a}=n==null?void 0:n.notes;h.useState("");let r=n==null?void 0:n.updates[0];new x;const d=new Date(r==null?void 0:r.create_at).toLocaleString("default",{month:"long"});return p(),i.useState(""),i.useEffect(()=>{const s=t=>{let o=String(t.key).toLowerCase();o==="insert"&&t.preventDefault(),o==="s"&&t.shiftKey&&t.metaKey&&t.preventDefault()};return window.addEventListener("keydown",s),()=>{window.removeEventListener("keydown",s)}},[]),i.useEffect(()=>{const s=t=>{t.preventDefault()};return window.addEventListener("contextmenu",s),()=>{window.removeEventListener("contextmenu",s)}},[]),i.useEffect(()=>{const s=t=>{(t.key==="F12"||t.ctrlKey&&t.shiftKey&&t.key==="I"||t.ctrlKey&&t.shiftKey&&t.key==="J")&&t.preventDefault()};return window.addEventListener("keydown",s),()=>{window.removeEventListener("keydown",s)}},[]),e.jsxs("div",{className:"mx-auto w-[90%] py-10 md:w-[50%]",children:[e.jsxs("h1",{className:"heading text-[1.3rem]",children:["Hey all - Hope all is well! Here is a quick recap of ",d,"."]}),e.jsxs("section",{className:"sect-1",children:[e.jsx("h1",{className:"mt-10 text-[1.2rem] font-[500]",children:"KPI"}),e.jsxs("ul",{className:"ml-10 list-disc text-[1.15rem] font-[400] ",children:[e.jsxs("li",{children:[" MRR: $",r.mrr]}),e.jsxs("li",{children:["ARR: $",r.arr]}),e.jsxs("li",{children:["CASH: $",r.cash]}),e.jsxs("li",{children:["BURN RATE: $",r.burnrate]}),e.jsx("li",{children:"Customers: X (+Y%)"}),e.jsx("li",{children:"Net churn: Y% or -Y% (when upsell > churn)"}),e.jsx("li",{children:"Visit to signup: X% (+Y%)"}),e.jsx("li",{children:"Signup to paying: X% (+Y%)"}),e.jsx("li",{children:"NPS: X (-Y%)"})]})]}),e.jsx("section",{className:"mt-8 text-[1.2rem] font-[400]"}),e.jsxs("section",{className:"mt-8 text-[1.2rem] font-[400]",children:[e.jsx("h1",{className:"mt-8 text-[1.2rem] font-[500]",children:"Bad & Ugly News"}),e.jsx("div",{dangerouslySetInnerHTML:{__html:l},className:"ml-10  text-[1.15rem] font-[400] "}),e.jsx("div",{dangerouslySetInnerHTML:{__html:c},className:"ml-10  text-[1.15rem] font-[400] "})]}),e.jsxs("section",{className:"mt-8 text-[1.2rem] font-[400]",children:[e.jsx("h1",{className:"mt-8 text-[1.2rem] font-[500]",children:"Good & Great News"}),e.jsx("div",{dangerouslySetInnerHTML:{__html:m},className:"ml-10  text-[1.15rem] font-[400] "})]}),e.jsxs("section",{className:"mt-8 text-[1.2rem] font-[400]",children:[e.jsx("h1",{className:"mt-8 text-[1.2rem] font-[500]",children:"Additional"}),e.jsx("div",{dangerouslySetInnerHTML:{__html:a},className:"ml-10  text-[1.15rem] font-[400] "})]}),e.jsx("section",{children:e.jsx("h5",{className:"text-right text-[1.2rem]",children:"Powered by updatestack"})})]})};export{I as default};
