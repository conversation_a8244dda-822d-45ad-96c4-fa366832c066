import{b as i,a as b}from"./index-46de5032.js";import{r as l}from"./vendor-4cdf2bd1.js";function v(){i();const{getMany:d,custom:c}=b(),[f,m]=l.useState({list:[],replies:[],single:null,delete:null}),[s,u]=l.useState({list:!1,single:!1,update:!1,delete:!1,create:!1,replies:!1}),y=l.useCallback((e={filter:[],exposure:"private",update_id:null,note_id:null})=>(async()=>{u(t=>({...t,list:!0}));const r={private:async()=>await d("update_comments",{filter:[...e==null?void 0:e.filter],join:["update_comment_replies|update_comment_id","user"]}),public:async()=>await c({endpoint:`/v3/api/custom/goodbadugly/updates/comments/${e==null?void 0:e.update_id}/${e==null?void 0:e.note_id}`,method:"GET"})};try{const t=r==null?void 0:r[e==null?void 0:e.exposure];if(!t)return;const a=await t();return a!=null&&a.error?[]:(m(n=>({...n,list:a==null?void 0:a.data})),a==null?void 0:a.data)}catch{return[]}finally{u(t=>({...t,list:!1}))}})(),[d]),_=l.useCallback((e={updateId:null,noteId:null,commentId:null})=>(async()=>{u(r=>({...r,delete:!0}));try{const r=await c({endpoint:`/v3/api/custom/goodbadugly/updates/comments/${e==null?void 0:e.updateId}/${e==null?void 0:e.noteId}/${e==null?void 0:e.commentId}`,method:"DELETE"});return r!=null&&r.error||m(t=>({...t,delete:r})),r}catch(r){return{error:!0,message:r==null?void 0:r.message}}finally{u(r=>({...r,delete:!1}))}})(),[c]),g=l.useCallback((e={filter:[],exposure:"private",update_id:null,note_id:null,comment_id:null})=>(async()=>{u(t=>({...t,replies:!0}));const r={private:async()=>await d("update_comment_replies",{filter:[...e==null?void 0:e.filter],join:["user"]}),public:async()=>await c({endpoint:`/v3/api/custom/goodbadugly/updates/replies/${e==null?void 0:e.update_id}/${e==null?void 0:e.note_id}/${e==null?void 0:e.comment_id}`,method:"GET"})};try{const t=r==null?void 0:r[e==null?void 0:e.exposure];if(!t)return;const a=await t();return a!=null&&a.error?[]:(m(n=>({...n,replies:a==null?void 0:a.data})),a==null?void 0:a.data)}catch{return[]}finally{u(t=>({...t,replies:!1}))}})(),[d]);return{loading:s,comments:f,getComments:y,getCommentReplies:g,customDeleteNoteComment:_}}export{v as u};
