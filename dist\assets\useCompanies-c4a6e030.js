import{A as _,G as x,M as C,t as E,s as b}from"./index-46de5032.js";import{r as e,h as k}from"./vendor-4cdf2bd1.js";function w(p){const[l,n]=e.useState(!1),[d,u]=e.useState([]),[t]=k(),{dispatch:f}=e.useContext(_),{dispatch:g}=e.useContext(x),r=t.get("limit")||30,i=t.get("page"),o=t.get("name");async function c(){n(!0);try{const h=await new C().callRawAPI(`/v4/api/records/company_member?join=companies|company_id&filter=member_id,eq,${p}&filter=member_role,cs,stakeholder${o?`&filter=name,cs,${o}`:""}&page=${i??1},${r??10}&order=id,asc`);u(h.list.map(a=>{var m;return{id:a.company_id,logo:(m=a.companies)==null?void 0:m.logo,name:a.companies.name,user_id:a.companies.user_id}}))}catch(s){E(f,s.message),s.message!=="TOKEN_EXPIRED"&&b(g,s.message,5e3,"error")}n(!1)}return e.useEffect(()=>{c()},[r,i,o]),{loading:l,companies:d,refetch:c}}export{w as u};
