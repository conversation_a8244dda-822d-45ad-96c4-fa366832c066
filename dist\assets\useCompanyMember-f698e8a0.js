import{R as m}from"./vendor-4cdf2bd1.js";import{A as d,u as w,q as c,r as o,s as i,v as _,w as S,x as u,y as k}from"./index-46de5032.js";const D=(x={filter:[]})=>{const{state:P,dispatch:s}=m.useContext(d),{state:j,dispatch:a}=m.useContext(d),[p,f]=m.useState({list:[],single:null,myMembers:[]}),[g,n]=m.useState({list:!1,single:!1,update:!1,delete:!1,create:!1,myMembers:!1}),{profile:y}=w({isPublic:!1}),M=m.useCallback((r={filter:[]})=>(async()=>{n(e=>({...e,list:!0}));try{const e=await c(a,s,"company_member",{filter:[...r==null?void 0:r.filter],join:["user|member_id"]});if(!(e!=null&&e.error))return f(t=>({...t,list:e==null?void 0:e.data})),e==null?void 0:e.data}catch{return null}finally{n(e=>({...e,list:!1}))}})(),[y,s,a,c]),C=m.useCallback((r={filter:[]})=>(async()=>{try{n(t=>({...t,myMembers:!0}));let e=null;return e=await c(a,s,"company_member",{filter:[...(r==null?void 0:r.filter)||[]],join:["user|member_id"]}),e!=null&&e.error||(console.log("Setting company members data:",e==null?void 0:e.data),f(t=>{const b={...t,myMembers:e==null?void 0:e.data};return console.log("Updated company member state:",b),b})),e}catch(e){return console.error("Error fetching company members:",e),{error:!0,message:(e==null?void 0:e.message)||"Failed to fetch members"}}finally{n(e=>({...e,myMembers:!1}))}})(),[y,s,a,c]),l=m.useCallback(r=>{if(o(r))return i(a,"Company id is Required!");(async()=>{n(e=>({...e,single:!0}));try{const e=await _(a,s,"company_member",r,{method:"GET",isPublic:!1,join:["user|member_id"],state:"companyMemberModel"});e!=null&&e.error||f(t=>({...t,single:e==null?void 0:e.data}))}catch{}finally{n(e=>({...e,single:!1}))}})()},[y,s,a,c]),h=m.useCallback(r=>{if(o(r))return i(a,"Payload is Required!");(async()=>{n(e=>({...e,create:!0}));try{const e=await S(a,s,"company_member",{...r},!1);e!=null&&e.error||l(e==null?void 0:e.data)}catch{}finally{n(e=>({...e,create:!1}))}})()},[l,s,a,u]),R=m.useCallback((r,e)=>{if(o(r))return i(a,"Member id is Required!");if(o(e))return i(a,"Payload is Required!");(async()=>{n(t=>({...t,update:!0}));try{const t=await u(a,s,"company_member",r,e,!1);t!=null&&t.error||l(r)}catch{}finally{n(t=>({...t,update:!1}))}})()},[l,s,a,u]),q=m.useCallback(r=>{if(o(r))return i(a,"Member id is Required!");(async()=>{n(e=>({...e,delete:!0}));try{const e=await k(a,s,"company_member",r,!0);e!=null&&e.error||l(r)}catch{}finally{n(e=>({...e,delete:!1}))}})()},[l,s,a,u]);return{loading:g,companyMember:p,getCompanyMember:l,getCompanyMembers:M,getMyCompanyMembers:C,updateCompanyMember:R,createCompanyMember:h,deleteCompanyMember:q}};export{D as u};
