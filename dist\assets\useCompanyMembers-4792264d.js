import{A as C,G as E,T as d,t as S,s as T}from"./index-46de5032.js";import{r as s,h as D}from"./vendor-4cdf2bd1.js";function w(o){const[h,m]=s.useState(!1),[p,l]=s.useState([]),[e]=D(),{dispatch:b}=s.useContext(C),{dispatch:_}=s.useContext(E),[$,x]=s.useState(0),f=e.get("limit")||30,g=e.get("page")??1,r=e.get("email"),n=e.get("first_name"),c=e.get("last_name"),i=e.get("role");async function u(){m(!0);try{const t=await new d().getPaginate("company_member",{join:["user|member_id"],filter:[`company_id,eq,${o}`,`${r?`&filter=email,cs,${r}`:[]}`,`${n?`&filter=first_name,cs,${n}`:[]}`,`${c?`&filter=last_name,cs,${c}`:[]}`,`${i?`&filter=member_role,cs,${i}`:[]}`],limit:f,page:g});l(()=>t==null?void 0:t.list),x(t==null?void 0:t.total)}catch(a){S(b,a.message),a.message!=="TOKEN_EXPIRED"&&T(_,a.message,5e3,"error")}m(!1)}return s.useEffect(()=>{o&&u()},[f,g,r,n,c,i,o]),{loading:h,members:p,refetch:u,setMembers:l,totalCount:$}}export{w as u};
