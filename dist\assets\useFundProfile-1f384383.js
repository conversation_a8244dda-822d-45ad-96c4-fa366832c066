import{A as i,G as l,n as d,t as f,s as p}from"./index-46de5032.js";import{r as e}from"./vendor-4cdf2bd1.js";function g(h){const[n,a]=e.useState(!1),[u,c]=e.useState([]),{dispatch:s}=e.useContext(i),{dispatch:o}=e.useContext(l),r=e.useCallback(async()=>{a(!0);try{const t=await d(o,s,{endpoint:"/v3/api/goodbadugly/customer/fund-profile/",method:"GET"});s({type:"SET_COMPANY_PROFILE",payload:t.data}),c(t.data)}catch(t){f(s,t.message),t.message!=="TOKEN_EXPIRED"&&p(o,t.message,5e3,"error")}a(!1)},[]);return e.useEffect(()=>{r()},[]),{loading:n,profileData:u,refetch:r}}export{g as u};
