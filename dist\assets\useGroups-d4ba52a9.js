import{A as d,G as m,M as g,t as f,s as h}from"./index-46de5032.js";import{r as e}from"./vendor-4cdf2bd1.js";function y(){const[r,o]=e.useState(!1),[n,c]=e.useState([]),{dispatch:u,state:s}=e.useContext(d),{dispatch:i}=e.useContext(m);async function p(){var a;console.log(s),o(!0);try{const t=new g,l=(a=s==null?void 0:s.company)!=null&&a.user_id?await t.callRawAPI(`/v3/api/custom/goodbadugly/member/get-recipient-groups?user_id=${s.company.user_id}&limit=1000`,void 0,"GET"):[];c(l.list)}catch(t){f(u,t.message),t.message!=="TOKEN_EXPIRED"&&h(i,t.message,5e3,"error")}o(!1)}return e.useEffect(()=>{p()},[s.company.user_id]),{loading:r,groups:n}}export{y as u};
