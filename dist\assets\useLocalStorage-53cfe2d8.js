import{R as c}from"./vendor-4cdf2bd1.js";import{aN as S}from"./index-46de5032.js";const m=(n=null)=>{const[s,o]=c.useState(null),e=(a=null)=>{const t=a?Array.isArray(a)?a:a==null?void 0:a.split("|"):[],l=S(t);return o(()=>l),l},g=(a=null,t=null,l=!1)=>{l?localStorage.setItem(a,JSON.stringify(t)):localStorage.setItem(a,t),o(r=>({...r,[a]:t}))},u=(a=null)=>{localStorage.removeItem(a),o(t=>({...t,[a]:void 0}))};return c.useEffect(()=>{e(n)},[]),{localStorageData:s,setLocalStorage:g,getLocalStorage:e,removeFromLocalStorage:u}},D=m;export{D as u};
