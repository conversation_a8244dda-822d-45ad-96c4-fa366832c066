import{A as d,G as f,M as p,t as h,s as m}from"./index-46de5032.js";import{r as t}from"./vendor-4cdf2bd1.js";function g(o){const[r,e]=t.useState(!1),[c,n]=t.useState([]),{dispatch:i}=t.useContext(d),{dispatch:u}=t.useContext(f),a=t.useCallback(async()=>{e(!0);try{const l=await new p().callRawAPI(`/v4/api/records/notes?filter=update_id,eq,${o}&order=\`order\`,asc`);n(l.list)}catch(s){h(i,s.message),s.message!=="TOKEN_EXPIRED"&&m(u,s.message,5e3,"error")}e(!1)},[]);return t.useEffect(()=>{a()},[]),{loading:r,notes:c,refetch:a}}export{g as u};
