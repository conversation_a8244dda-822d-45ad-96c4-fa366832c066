import{b as y,a as _}from"./index-46de5032.js";import{r as c}from"./vendor-4cdf2bd1.js";function h(){y();const{getMany:n,custom:u}=_(),[f,d]=c.useState({list:[],react:null,single:null}),[i,l]=c.useState({list:!1,single:!1,update:!1,delete:!1,create:!1,reacting:!1}),g=c.useCallback((e={filter:[],target:null,note_id:null,update_id:null,comment_id:null,reply_id:null})=>(async()=>{l(r=>({...r,list:!0}));const a={private:async()=>await n("update_reaction",{filter:[...e==null?void 0:e.filter],join:["user"]}),public:async()=>await u({endpoint:`/v3/api/custom/goodbadugly/updates/reactions/${e==null?void 0:e.target}/${e==null?void 0:e.update_id}/${e==null?void 0:e.note_id}`,method:"POST",payload:{comment_id:e==null?void 0:e.comment_id,reply_id:e==null?void 0:e.reply_id}})};try{const r=a==null?void 0:a[e==null?void 0:e.exposure];if(!r)return;const t=await r();return t!=null&&t.error?[]:(d(s=>({...s,list:t==null?void 0:t.data})),t==null?void 0:t.data)}catch{return[]}finally{l(r=>({...r,list:!1}))}})(),[n]),m=c.useCallback(e=>(async()=>{l(a=>({...a,reacting:!0}));try{const a=await u({endpoint:"/v3/api/custom/goodbadugly/updates/reactions",method:"POST",payload:e},null,!1);return a!=null&&a.error||d(r=>({...r,react:a==null?void 0:a.data})),a}catch(a){return{error:!0,message:a==null?void 0:a.message}}finally{l(a=>({...a,reacting:!1}))}})(),[u]);return{loading:i,reaction:f,getReactions:g,react:m}}export{h as u};
