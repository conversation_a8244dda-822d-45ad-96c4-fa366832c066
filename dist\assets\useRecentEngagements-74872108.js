import{a as C}from"./index-46de5032.js";import{r as i}from"./vendor-4cdf2bd1.js";const b=()=>{const[m,a]=i.useState(!1),[d,u]=i.useState({showAllCompany:!1,showAllTeam:!1}),[p,f]=i.useState({myUpdates:[],teamUpdates:[]}),[g,h]=i.useState({totalMyUpdates:0,totalTeamUpdates:0}),{custom:r,globalState:s,setGlobalState:w}=C(),y=e=>{const n="/v3/api/custom/goodbadugly/member/get-engagements-side",t=[];return e!=null&&e.showAllCompany&&t.push("showAllCompany=true"),e!=null&&e.showAllTeam&&t.push("showAllTeam=true"),t.length>0?`${n}?${t.join("&")}`:n};i.useEffect(()=>{const e=location.pathname.includes("/public/view/");w("isPublicView",e),!e&&!location.pathname.includes("sign-up")&&l()},[location.pathname,s==null?void 0:s.isPublicView]);const l=async(e={})=>{if(console.log("fetchRecentEngagements",s==null?void 0:s.isPublicView),s!=null&&s.isPublicView){console.log("Skipping engagements fetch - public view");return}a(!0);try{u(U=>({showAllCompany:(e==null?void 0:e.showAllCompany)||!1,showAllTeam:(e==null?void 0:e.showAllTeam)||!1}));const n=y(e),t=await r({method:"GET",endpoint:n},{},!1);if(!(t!=null&&t.error)){const A=((t==null?void 0:t.data)||[]).filter(Boolean).reduce((c,o)=>(o.engagements&&o.engagements.length>0&&(o.engagements.some(T=>T.update_source==="company_update")?c.myUpdates.push(o):c.teamUpdates.push(o)),c),{myUpdates:[],teamUpdates:[]});f(A),h({totalMyUpdates:t.totalCompanyUpdates||0,totalTeamUpdates:t.totalTeamUpdates||0})}}catch(n){console.error("Error fetching recent engagements:",n)}finally{a(!1)}};return{loading:m,engagements:p,totals:g,activeParams:d,refetch:l,resetView:()=>{s!=null&&s.isPublicView||(u({showAllCompany:!1,showAllTeam:!1}),l())}}};export{b as u};
