import{A as l,G as p,M as d,t as f,s as h}from"./index-46de5032.js";import{r as t}from"./vendor-4cdf2bd1.js";function b(){const[c,e]=t.useState(!1),[r,n]=t.useState([]),{dispatch:i}=t.useContext(l),{dispatch:u}=t.useContext(p),a=t.useCallback(async()=>{e(!0);try{const o=await new d().callRawAPI("/v3/api/goodbadugly/customer/recent-subscription/");console.log(o),n(o.model)}catch(s){f(i,s.message),s.message!=="TOKEN_EXPIRED"&&h(u,s.message,5e3,"error")}e(!1)},[]);return t.useEffect(()=>{a()},[]),{loading:c,data:r,refetch:a}}export{b as u};
