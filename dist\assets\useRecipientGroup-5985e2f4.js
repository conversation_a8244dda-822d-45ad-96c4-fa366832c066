import{R as i}from"./vendor-4cdf2bd1.js";import{A as p,q as l,r as c,s as o,v as G,x as g}from"./index-46de5032.js";const _=(b={filter:[],join:[]})=>{const{state:C,dispatch:s}=i.useContext(p),{state:q,dispatch:a}=i.useContext(p),[d,f]=i.useState({list:[],single:null}),[h,n]=i.useState({list:!1,single:!1,update:!1,delete:!1,create:!1}),R=i.useCallback((r={filter:[]})=>(async()=>{var t;n(e=>({...e,list:!0}));try{let e=null;return((t=r==null?void 0:r.filter)==null?void 0:t.length)>0?e=await l(a,s,"recipient_group",{filter:[...r==null?void 0:r.filter],join:["group","user","recipient_member"]}):e={data:[],error:!1},e!=null&&e.error||f(y=>({...y,list:e==null?void 0:e.data})),e}catch(e){return{error:!0,message:e==null?void 0:e.message}}finally{n(e=>({...e,list:!1}))}})(),[s,a,l]),u=i.useCallback(r=>c(r)?o(a,"Group id is Required!"):(async()=>{n(t=>({...t,single:!0}));try{const t=await G(a,s,"recipient_group",r,{join:["group","user","recipient_member"]});if(!(t!=null&&t.error))return f(e=>({...e,single:t==null?void 0:t.data})),t==null?void 0:t.data}catch{}finally{n(t=>({...t,single:!1}))}})(),[s,a,l]),m=i.useCallback((r,t)=>{if(c(r))return o(a,"Recipient Group id is Required!");if(c(t))return o(a,"Payload is Required!");(async()=>{n(!0);try{const e=await g(a,s,"recipient_group",r,t,!1);e!=null&&e.error||u(r)}catch{}finally{n(!1)}})()},[u,s,a,g]);return{recipientGroup:d,loading:h,getRecipientGroups:R,getRecipientGroup:u,updateRecipientGroup:m}};export{_ as u};
