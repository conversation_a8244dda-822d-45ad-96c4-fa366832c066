import{A as E,G as M,M as C,t as N,s as G}from"./index-46de5032.js";import{r as s,h as _}from"./vendor-4cdf2bd1.js";function $(k){const[p,i]=s.useState(!1),[l,h]=s.useState([]),[o,P]=s.useState(0),[r,n]=_(),{dispatch:x}=s.useContext(E),{dispatch:b}=s.useContext(M),e=r.get("limit")||30,t=r.get("page")||1,c=r.get("group_name"),u=r.get("members")??"";async function g(){i(!0);try{const m=await new C().callRawAPI(`/v3/api/custom/goodbadugly/member/get-recipient-groups?group_name=${c||""}&members=${u||""}&limit=${e}&page=${t}`);h(m.list),P(m.total)}catch(a){N(x,a.message),a.message!=="TOKEN_EXPIRED"&&G(b,a.message,5e3,"error")}i(!1)}const d=a=>{n({limit:a,page:1})},f=()=>{n({page:Math.max(1,Number(t)-1),limit:e})},S=()=>{console.log(o,e);const a=Math.ceil(o/e);console.log(Math.min(a,t+1)),n({page:Math.max(1,Number(t)+1),limit:e})};return s.useEffect(()=>{g()},[e,t,c,u]),{loading:p,groups:l,refetch:g,totalCount:o,currentPage:Number(t),pageSize:Number(e),updatePageSize:d,previousPage:f,nextPage:S,canPreviousPage:t>1,canNextPage:t<Math.ceil(o/e)}}export{$ as u};
