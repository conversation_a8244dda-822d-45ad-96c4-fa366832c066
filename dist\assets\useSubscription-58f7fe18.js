import{r as x}from"./vendor-4cdf2bd1.js";import{a as M,b as U}from"./index-46de5032.js";const I=()=>{const _=new Date().getMonth(),m=new Date().getFullYear(),g=new Date(m,_,1),C=new Date(m,_+1,0),E=new Date(g.setHours(24,59,59,999)).toISOString(),D=new Date(C.setHours(24,59,59,999)).toISOString(),S=E.split("T")[0],w=D.split("T")[0];return{start:S,end:w}},Q=()=>{const{getMany:_,tokenExpireError:m,showToast:g,getSingle:C,setGlobalState:E}=M(),{operations:D,sdk:S}=U(),[w,f]=x.useState({card:null,plan:null,object:null,sentUpdates:0,days_left:null,cardExpired:!1,subscription:null,trial_expired:!1,accessedFundManagers:0,subscriptionStatus:null,customerSubscription:null});x.useEffect(()=>{var s;((s=w==null?void 0:w.subscription)==null?void 0:s.status)==="active"&&E("subscriptionData",w)},[w]);const[v,u]=x.useState({card:!1,plan:!1,subscribe:!1,subscription:!1,customerSubscription:!1,processRegisteredDate:!1}),O=s=>{const i=new Date,c=new Date(s==null?void 0:s.exp_month,s==null?void 0:s.exp_year,0,0,0).getTime(),a=i.getTime();return c<a},$=async(s={filter:[]})=>{var i,d,c,a,e,l,o,r,n,p;u(t=>({...t,subscription:!0}));try{const t=await _("stripe_subscription",{filter:[...s==null?void 0:s.filter],...(s==null?void 0:s.order)&&{order:s.order},...(s==null?void 0:s.limit)&&{limit:s.limit}});if(!(t!=null&&t.error)){console.log("getSubscription result:",{totalResults:(i=t==null?void 0:t.data)==null?void 0:i.length,allSubscriptions:(d=t==null?void 0:t.data)==null?void 0:d.map(y=>({id:y.id,price_id:y.price_id,status:y.status,cancelled:y.cancelled,canceled_at:y.canceled_at,cancel_at_period_end:y.cancel_at_period_end})),selectedSubscription:(c=t==null?void 0:t.data)==null?void 0:c[0],queryOptions:s});const b=(e=(a=t==null?void 0:t.data)==null?void 0:a[0])!=null&&e.object&&JSON.parse((o=(l=t==null?void 0:t.data)==null?void 0:l[0])==null?void 0:o.object)?JSON.parse((n=(r=t==null?void 0:t.data)==null?void 0:r[0])==null?void 0:n.object):null,h=(p=t==null?void 0:t.data)==null?void 0:p[0];f(y=>({...y,subscription:h,object:b}))}}catch(t){console.log(t)}finally{u(t=>({...t,subscription:!1}))}},j=async()=>{var s,i,d,c,a,e,l;try{u(r=>({...r,customerSubscription:!0}));const o=await S.getCustomerStripeSubscription();console.log("getCustomerSubscription result >>",o),o!=null&&o.error||(f(r=>({...r,customerSubscription:o==null?void 0:o.customer})),(s=o==null?void 0:o.customer)!=null&&s.planId&&(console.log("Calling getPlan with planId:",(i=o==null?void 0:o.customer)==null?void 0:i.planId),P((d=o==null?void 0:o.customer)==null?void 0:d.planId)))}catch(o){console.error(o);const r=(a=(c=o==null?void 0:o.response)==null?void 0:c.data)!=null&&a.message?(l=(e=o==null?void 0:o.response)==null?void 0:e.data)==null?void 0:l.message:o==null?void 0:o.message;g(r,5e3,"error"),m(r)}finally{u(o=>({...o,customerSubscription:!1}))}},P=async s=>{var i,d,c,a;try{u(l=>({...l,plan:!0}));const e=await C("stripe_price",s,{join:["stripe_product|product_id"],allowToast:!1,method:"GET",state:"currentPlan"});e!=null&&e.error||f(l=>({...l,plan:e==null?void 0:e.data}))}catch(e){console.error(e);const l=(d=(i=e==null?void 0:e.response)==null?void 0:i.data)!=null&&d.message?(a=(c=e==null?void 0:e.response)==null?void 0:c.data)==null?void 0:a.message:e==null?void 0:e.message;g(l,5e3,"error"),m(l)}finally{u(e=>({...e,plan:!1}))}};return{loading:v,updateData:async(s=null)=>{s&&f(i=>({...i,...s}))},getCardData:async()=>{var s,i,d,c,a;try{u(p=>({...p,card:!0}));const{data:e,limit:l,error:o,message:r}=await S.getCustomerStripeCards();if(console.log(e),o&&g(r,5e3),!e)return;const n=(s=e==null?void 0:e.data)==null?void 0:s.find(p=>{var t;return!!((t=p==null?void 0:p.customer)!=null&&t.default_source)});f(p=>({...p,card:n,cardExpired:n?O(n):!1}))}catch(e){console.error(e);const l=(d=(i=e==null?void 0:e.response)==null?void 0:i.data)!=null&&d.message?(a=(c=e==null?void 0:e.response)==null?void 0:c.data)==null?void 0:a.message:e==null?void 0:e.message;g(l,5e3,"error"),m(l)}finally{u(e=>({...e,card:!1}))}},getSentUpdates:async s=>{console.log("start","limitt");const{start:i,end:d}=I();try{const c=await _("updates",{filter:[`sent_at,${D.IS_NOT_NULL}`,`user_id,${D.EQUAL},${s==null?void 0:s.id}`,`sent_at,${D.BETWEEN},'${i}','${d}'`]});console.log("start","end",c,"limitt"),f(a=>{var e;return{...a,sentUpdates:(e=c==null?void 0:c.data)==null?void 0:e.length}})}catch(c){console.log(c)}finally{}},getSubscription:$,handleSubscription:async(s,i=null)=>{var d,c,a,e,l,o;try{u(p=>({...p,subscribe:!0}));const r={planId:s};i&&(r.coupon=i);const n=await S.createStripeSubscription(r);if(!(n!=null&&n.error)){E("refreshSubscription",!0),f(p=>({...p,subscriptionStatus:"success"})),g("Subscription created successfully!",5e3,"success");return}if(n!=null&&n.validation){const p=Object.keys(n.validation);for(let t=0;t<p.length;t++){const b=p[t],h=n.validation[b];g(h==null?void 0:h.message,5e3,"error")}throw new Error(((c=(d=n==null?void 0:n.validation)==null?void 0:d[Object.keys(n.validation)[0]])==null?void 0:c.message)||"Validation error")}else throw g(n==null?void 0:n.message,5e3,"error"),new Error((n==null?void 0:n.message)||"Subscription failed")}catch(r){console.log("error >>",r);const n=(e=(a=r==null?void 0:r.response)==null?void 0:a.data)!=null&&e.message?(o=(l=r==null?void 0:r.response)==null?void 0:l.data)==null?void 0:o.message:r==null?void 0:r.message;throw g(n,5e3,"error"),m(n),r}finally{u(r=>({...r,subscribe:!1}))}},data:w,processRegisteredDate:async s=>{u(i=>({...i,processRegisteredDate:!0}));try{const i=new Date().getTime(),d=new Date(s).getTime(),c=Math.abs(i-d),l=30-Math.ceil(c/(1e3*60*60*24));f(o=>({...o,days_left:l,trial_expired:l<=0}))}catch(i){console.log(i)}finally{u(i=>({...i,processRegisteredDate:!1}))}},getAccessedFundManager:async s=>{const{start:i,end:d}=I();try{const c=await _("fund_managers",{filter:[`user_id,${D.EQUAL},${s==null?void 0:s.id}`,`create_at,${D.BETWEEN},'${i}','${d}'`]});f(a=>{var e;return{...a,accessedFundManagers:(e=c==null?void 0:c.data)==null?void 0:e.length}})}catch(c){console.log(c)}finally{}},getCustomerSubscription:j,handleUpgradeSubscription:async(s,i=null)=>{var d,c,a,e,l,o,r;try{u(b=>({...b,upgrade:!0}));const n=await S.getCustomerStripeSubscription();if(!((d=n==null?void 0:n.customer)!=null&&d.subId))throw new Error("No active subscription found");const p={activeSubscriptionId:n.customer.subId,newPlanId:s};i&&(p.coupon=i);const t=await S.changeStripeSubscription(p);if(!(t!=null&&t.error)){E("refreshSubscription",!0),f(b=>({...b,subscriptionStatus:"success"})),g("Subscription updated successfully!",5e3,"success");return}if(t!=null&&t.validation){const b=Object.keys(t.validation);for(let h=0;h<b.length;h++){const y=b[h],T=t.validation[y];g(T==null?void 0:T.message,5e3,"error")}throw new Error(((a=(c=t==null?void 0:t.validation)==null?void 0:c[Object.keys(t.validation)[0]])==null?void 0:a.message)||"Validation error")}else throw g(t==null?void 0:t.message,5e3,"error"),new Error((t==null?void 0:t.message)||"Subscription update failed")}catch(n){console.log("error >>",n);const p=(l=(e=n==null?void 0:n.response)==null?void 0:e.data)!=null&&l.message?(r=(o=n==null?void 0:n.response)==null?void 0:o.data)==null?void 0:r.message:n==null?void 0:n.message;throw g(p,5e3,"error"),m(p),n}finally{u(n=>({...n,upgrade:!1}))}},createVIPCoupon:async()=>{var s,i,d,c;try{return await S.createStripeCoupon({couponId:"vip-access-100",name:"VIP Access - 100% Off",percent_off:100,duration:"forever"})}catch(a){console.error("Error creating VIP coupon:",a);const e=(i=(s=a==null?void 0:a.response)==null?void 0:s.data)!=null&&i.message?(c=(d=a==null?void 0:a.response)==null?void 0:d.data)==null?void 0:c.message:a==null?void 0:a.message;throw g(e,5e3,"error"),m(e),a}},getAllCoupons:async()=>{var s,i,d,c;try{return await S.getStripeCoupons()}catch(a){console.error("Error fetching coupons:",a);const e=(i=(s=a==null?void 0:a.response)==null?void 0:s.data)!=null&&i.message?(c=(d=a==null?void 0:a.response)==null?void 0:d.data)==null?void 0:c.message:a==null?void 0:a.message;throw g(e,5e3,"error"),m(e),a}}}};export{Q as u};
