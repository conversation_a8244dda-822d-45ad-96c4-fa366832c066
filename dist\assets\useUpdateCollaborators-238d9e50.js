import{A as p,G as d,M as h,t as b,s as C}from"./index-46de5032.js";import{r as t}from"./vendor-4cdf2bd1.js";function E(a,r){const[l,o]=t.useState(!1),[c,n]=t.useState([]),{dispatch:i}=t.useContext(p),{dispatch:u}=t.useContext(d);async function e(){o(!0);try{const f=await new h().callRawAPI(`/v4/api/records/update_collaborators?filter=update_id,eq,${a}&join=user|collaborator_id&filter=note_id,eq,${r}`);n(f.list)}catch(s){b(i,s.message),s.message!=="TOKEN_EXPIRED"&&C(u,s.message,5e3,"error")}o(!1)}return t.useEffect(()=>{a&&e()},[a]),{loading:l,updateCollaborators:c,refetch:e}}export{E as u};
