import{r as t}from"./vendor-4cdf2bd1.js";import{A as d,G as f,M as p,t as h,s as g}from"./index-46de5032.js";function m({title:s,titleId:n,...a},r){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":n},a),s?t.createElement("title",{id:n},s):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.19 8.688a4.5 4.5 0 0 1 1.242 7.244l-4.5 4.5a4.5 4.5 0 0 1-6.364-6.364l1.757-1.757m13.35-.622 1.757-1.757a4.5 4.5 0 0 0-6.364-6.364l-4.5 4.5a4.5 4.5 0 0 0 1.242 7.244"}))}const E=t.forwardRef(m),b=E;function C(s){const[n,a]=t.useState(!0),[r,c]=t.useState({}),{dispatch:i}=t.useContext(d),{dispatch:l}=t.useContext(f);async function o(){try{const u=await new p().callRawAPI(`/v4/api/records/updates/${s}?join=companies|company_id`,void 0,"GET");c(u.model)}catch(e){h(i,e.message),e.message!=="TOKEN_EXPIRED"&&g(l,e.message,5e3,"error")}a(!1)}return t.useEffect(()=>{o()},[s]),{loading:n,update:r,refetch:o}}function x(s){const[n,a]=t.useState(!1),[r,c]=t.useState([]),{dispatch:i}=t.useContext(d),{dispatch:l}=t.useContext(f),o=t.useCallback(async()=>{a(!0);try{const u=await new p().callRawAPI(`/v4/api/records/notes?filter=update_id,eq,${s}&order=id,asc`);c(u.list)}catch(e){h(i,e.message),e.message!=="TOKEN_EXPIRED"&&g(l,e.message,5e3,"error")}a(!1)},[]);return t.useEffect(()=>{o()},[]),{loading:n,notes:r,refetch:o}}function D(s){const[n,a]=t.useState(!1),[r,c]=t.useState([]),{dispatch:i}=t.useContext(d),{dispatch:l}=t.useContext(f);async function o(){a(!0);try{const u=await new p().callRawAPI(`/v4/api/records/update_questions?filter=update_id,eq,${s}&join=user|investor_id`);c(u.list)}catch(e){h(i,e.message),e.message!=="TOKEN_EXPIRED"&&g(l,e.message,5e3,"error")}a(!1)}return t.useEffect(()=>{s&&o()},[s]),{loading:n,questions:r,refetch:o}}function y(s){const[n,a]=t.useState(!1),[r,c]=t.useState([]),{dispatch:i}=t.useContext(d),{dispatch:l}=t.useContext(f);async function o(){a(!0);try{const u=await new p().callRawAPI(`/v4/api/records/update_collaborators?filter=update_id,eq,${s}&join=user|collaborator_id`);c(u.list)}catch(e){h(i,e.message),e.message!=="TOKEN_EXPIRED"&&g(l,e.message,5e3,"error")}a(!1)}return t.useEffect(()=>{s&&o()},[s]),{loading:n,updateCollaborators:r,refetch:o}}export{b as L,x as a,D as b,y as c,C as u};
