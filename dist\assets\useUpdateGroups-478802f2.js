import{A as l,G as f,M as d,t as g,s as h}from"./index-46de5032.js";import{r as s}from"./vendor-4cdf2bd1.js";function E(o){const[c,a]=s.useState(!1),[n,u]=s.useState([]),{dispatch:p}=s.useContext(l),{dispatch:i}=s.useContext(f);async function r(){a(!0);try{const e=await new d().callRawAPI(`/v3/api/custom/goodbadugly/member/updates/${o}/groups`);console.log("LIST >>",e==null?void 0:e.list),u(e.list)}catch(t){g(p,t.message),t.message!=="TOKEN_EXPIRED"&&h(i,t.message,5e3,"error")}a(!1)}return s.useEffect(()=>{o&&r()},[o]),{loading:c,updateGroups:n,refetch:r}}export{E as u};
