import{A as l,G as p,M as h,t as d,s as m}from"./index-46de5032.js";import{r as s}from"./vendor-4cdf2bd1.js";function g(e){const[r,o]=s.useState(!1),[n,i]=s.useState([]),{dispatch:c}=s.useContext(l),{dispatch:u}=s.useContext(p);async function a(){o(!0);try{const f=await new h().callRawAPI(`/v4/api/records/update_questions?filter=update_id,eq,${e}&join=user|investor_id`);i(f.list)}catch(t){d(c,t.message),t.message!=="TOKEN_EXPIRED"&&m(u,t.message,5e3,"error")}o(!1)}return s.useEffect(()=>{e&&a()},[e]),{loading:r,questions:n,refetch:a}}export{g as u};
