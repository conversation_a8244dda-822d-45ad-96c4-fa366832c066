import{A as p,G as d,M as f,t as g,s as h}from"./index-46de5032.js";import{r as t}from"./vendor-4cdf2bd1.js";function E(e){const[c,a]=t.useState(!1),[n,u]=t.useState([]),{dispatch:i}=t.useContext(p),{dispatch:l}=t.useContext(d);async function o(){a(!0);try{const r=await new f().callRawAPI(`/v3/api/goodbadugly/customer/view-insights?page=1&limit=1000&update_id=${e}`);u(r.model),console.log(r)}catch(s){g(i,s.message),s.message!=="TOKEN_EXPIRED"&&h(l,s.message,5e3,"error")}a(!1)}return t.useEffect(()=>{e&&o()},[e]),{loading:c,updateRequests:n,refetch:o}}export{E as u};
