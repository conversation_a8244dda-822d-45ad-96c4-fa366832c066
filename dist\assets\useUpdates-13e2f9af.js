import{A as z,G as A,M,t as R,s as T}from"./index-46de5032.js";import{r as s,h as G}from"./vendor-4cdf2bd1.js";function q(p,u){const[x,m]=s.useState(!1),[y,C]=s.useState([]),[l,E]=s.useState(0),[t,g]=G(),{dispatch:w,state:K}=s.useContext(z),{dispatch:b}=s.useContext(A),o=parseInt(t.get("limit")||"30"),e=parseInt(t.get("page")||"1"),P=t.get("company_name")||"",d=t.get("status")||"";async function f(){var c,h;m(!0);try{const n=new M,D=U=>Object.entries(U).reduce((S,[k,i])=>(i!=null&&i!==""&&(S[k]=i),S),{}),I=new URLSearchParams(D({page:e,limit:o,request:p,update_id:u,company_name:P,status:d})),a=await n.callRawAPI(`/v3/api/goodbadugly/customer/fund-update-requests?${I.toString()}`);C(a==null?void 0:a.list),console.log(a==null?void 0:a.total),E((h=(c=a==null?void 0:a.total)==null?void 0:c[0])==null?void 0:h.total)}catch(n){R(w,n.message),n.message!=="TOKEN_EXPIRED"&&T(b,n.message,5e3,"error")}m(!1)}s.useEffect(()=>{f()},[o,e,P,d,p,u]);const r=Math.ceil(l/o);return console.log(l,o),console.log(r),{loading:x,updates:y,refetch:f,currentPage:e,pageCount:r,pageSize:o,updatePageSize:c=>{t.set("limit",c.toString()),t.set("page","1"),g(t)},previousPage:()=>{e>1&&(t.set("page",(e-1).toString()),g(t))},nextPage:()=>{e<r&&(t.set("page",(e+1).toString()),g(t))},canPreviousPage:e>1,canNextPage:e<r}}export{q as u};
