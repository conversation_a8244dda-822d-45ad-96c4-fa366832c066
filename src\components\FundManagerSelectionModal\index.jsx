import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { LazyLoad } from "Components/LazyLoad";
import { InteractiveButton } from "Components/InteractiveButton";
import { MkdInput } from "Components/MkdInput";
import useRecipientGroups from "Pages/member/List/RecipientGroups/useRecipientGroups";
import { useContexts } from "Hooks/useContexts";

const FundManagerSelectionModal = ({
  isOpen,
  onClose,
  fundManagers = [],
  onSubmit,
  loading = false,
  onRemoveFundManager, // New prop for removing fund managers
}) => {
  const [createNewGroup, setCreateNewGroup] = useState(false);
  const { authState } = useContexts();
  const { groups } = useRecipientGroups(authState?.user);

  const schema = yup.object({
    group_id: yup
      .string()
      .required("Please select a group or create a new one"),
    new_group_name: createNewGroup
      ? yup.string().required("Group name is required")
      : yup.string(),
  });

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    watch,
    setValue,
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: { group_id: "", new_group_name: "" },
  });

  const groupId = watch("group_id");

  useEffect(() => {
    if (createNewGroup) {
      setValue("group_id", "new");
    } else {
      setValue("group_id", "");
      setValue("new_group_name", "");
    }
  }, [createNewGroup, setValue]);

  const handleFormSubmit = (data) => {
    onSubmit({
      ...data,
      fundManagers,
      isNewGroup: createNewGroup,
    });
  };

  const handleClose = () => {
    reset();
    setCreateNewGroup(false);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="mx-4 max-h-[90vh] w-full max-w-2xl overflow-y-auto rounded-lg bg-brown-main-bg shadow-xl">
        {/* Header */}
        <div className="flex items-center justify-between border-b p-6">
          <h2 className="font-iowan text-xl font-semibold">
            Add Investors to Group
          </h2>
          <button
            onClick={handleClose}
            className="text-gray-400 transition-colors hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Selected Fund Managers Summary */}
          <div className="mb-6 rounded-lg bg-brown-main-bg/70 p-4">
            <div className="mb-3 flex items-center gap-4">
              <span className="text-3xl font-bold text-primary-black">
                {fundManagers.length}
              </span>
              <span className="text-lg font-medium">
                Fund Manager{fundManagers.length !== 1 ? "s" : ""} Selected
              </span>
            </div>

            {/* Fund Manager Names */}
            <div className="space-y-2">
              <p className="mb-2 text-sm font-medium text-gray-700">
                Selected:
              </p>
              <div className="max-h-32 overflow-y-auto">
                {fundManagers.map((manager, index) => (
                  <div
                    key={manager.id}
                    className="group flex items-center justify-between gap-2 rounded px-2 py-1 text-sm text-gray-600 hover:bg-gray-100"
                    title={`${manager.first_name} ${manager.last_name} from ${manager.fund_name}`}
                  >
                    <div className="flex items-center gap-2">
                      <span className="w-4 text-center">{index + 1}.</span>
                      <span className="font-medium">
                        {manager.first_name} {manager.last_name}
                      </span>
                      <span className="text-gray-400">-</span>
                      <span>{manager.fund_name}</span>
                    </div>
                    {onRemoveFundManager && (
                      <button
                        onClick={() => onRemoveFundManager(manager.id)}
                        className="opacity-0 transition-opacity hover:text-red-600 group-hover:opacity-100"
                        title="Remove from selection"
                      >
                        <XMarkIcon className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
            {/* Group Selection Toggle */}
            <div className="space-y-3">
              <label className="block text-sm font-medium text-gray-700">
                Choose an option:
              </label>

              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="radio"
                    checked={!createNewGroup}
                    onChange={() => setCreateNewGroup(false)}
                    className="mr-2"
                  />
                  <span>Add to existing group</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="radio"
                    checked={createNewGroup}
                    onChange={() => setCreateNewGroup(true)}
                    className="mr-2"
                  />
                  <span>Create new group</span>
                </label>
              </div>
            </div>

            {/* Existing Group Selection */}
            {!createNewGroup && (
              <div>
                <LazyLoad>
                  <MkdInput
                    type="select"
                    register={register}
                    name="group_id"
                    label="Select Existing Group"
                    errors={errors}
                    options={groups?.map((group) => group.group_name) || []}
                    noneText="Choose a group..."
                    className="!h-[2.75rem] !w-full !rounded-md !border"
                  />
                </LazyLoad>
              </div>
            )}

            {/* New Group Name */}
            {createNewGroup && (
              <div>
                <LazyLoad>
                  <MkdInput
                    type="text"
                    register={register}
                    name="new_group_name"
                    label="New Group Name"
                    errors={errors}
                    placeholder="Enter group name..."
                    className="!h-[2.75rem] !w-full !rounded-md !border"
                  />
                </LazyLoad>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-3 pt-4">
              <LazyLoad>
                <InteractiveButton
                  type="submit"
                  loading={isSubmitting || loading}
                  disabled={isSubmitting || loading}
                  className="flex-1 rounded-md bg-primary-black px-4 py-2 font-medium text-white transition-colors hover:bg-gray-800"
                >
                  Add to Group
                </InteractiveButton>
              </LazyLoad>

              <button
                type="button"
                onClick={handleClose}
                disabled={isSubmitting || loading}
                className="flex-1 rounded-md border border-black bg-transparent px-4 py-2 font-medium text-gray-800 transition-colors disabled:opacity-50"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default FundManagerSelectionModal;
