import React, { useEffect, useState } from "react";
import { TrialNotice, UpdateStatus } from "./index";
import { AuthContext } from "Context/Auth";
import { LazyLoad } from "Components/LazyLoad";
import { GlobalContext } from "Context/Global";
import { Link, useLocation, useNavigate } from "react-router-dom";
import EditStartupCompanyName from "Pages/Common/EditStartupCompanyName";
import { BellIcon } from "@heroicons/react/24/outline";
import logo5 from "Assets/images/logo5.svg";
import { useSDK } from "Hooks/useSDK";
import { useProfile } from "Hooks/useProfile";
import { useContexts } from "Hooks/useContexts";
import moment from "moment";
import { ActionConfirmationModal } from "Components/ActionConfirmationModal";
import { useTeamInvitations } from "Context/TeamInvitationsContext";
import { AdminBar } from "Components/AdminBar/AdminBar";
import TreeSDK from "Utils/TreeSDK";
import MkdSDK from "Utils/MkdSDK";
import { useMentions } from "Hooks/useMentions";

const TopHeaderMember = () => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { globalState: globalState, showToast, setGlobalState } = useContexts();

  const { state, dispatch } = React.useContext(AuthContext);
  const [currentPath, setCurrentPath] = useState("");
  const { isOpen, showBackButton } = globalState;
  const location = useLocation();
  const { tdk, sdk } = useSDK();

  // Team invitations state
  const [showNotifications, setShowNotifications] = useState(false);
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [currentInvitation, setCurrentInvitation] = useState(null);
  const [actionType, setActionType] = useState(""); // "accept" or "reject"
  const [collaborationRequests, setCollaborationRequests] = useState([]);
  const [loadingCollabs, setLoadingCollabs] = useState(false);
  const [currentCollaboration, setCurrentCollaboration] = useState(null);
  const [currentInvitationType, setCurrentInvitationType] = useState("member");

  // Update requests state
  const [updateRequests, setUpdateRequests] = useState([]);
  const [loadingUpdateRequests, setLoadingUpdateRequests] = useState(false);
  const [currentUpdateId, setCurrentUpdateId] = useState(null);

  // Unanswered questions state
  const [unansweredQuestions, setUnansweredQuestions] = useState([]);
  const [loadingUnansweredQuestions, setLoadingUnansweredQuestions] =
    useState(false);

  // Mentions state
  const {
    loading: loadingMentions,
    mentions,
    unreadCount: unreadMentionsCount,
    fetchMentions,
    fetchUnreadCount,
    markMentionAsSeen,
    markAllMentionsAsSeen,
  } = useMentions();

  // Update shared notifications state
  const [updateSharedNotifications, setUpdateSharedNotifications] = useState(
    []
  );
  const [
    loadingUpdateSharedNotifications,
    setLoadingUpdateSharedNotifications,
  ] = useState(false);

  // Cadence notifications state
  const [cadenceNotifications, setCadenceNotifications] = useState([]);
  const [loadingCadenceNotifications, setLoadingCadenceNotifications] =
    useState(false);

  console.log(
    currentCollaboration,
    "currentCollaboration",
    collaborationRequests,
    updateRequests
  );

  const { invitations, loading, handleInvitation, refetchInvitations } =
    useTeamInvitations();

  console.log(invitations, "invite");

  // // Add onboarding check effect
  // useEffect(() => {
  //   const checkOnboarding = async () => {
  //     if (profile?.id && location.pathname === "/member/dashboard") {
  //       try {
  //         const sdk = new MkdSDK();
  //         sdk.setTable("user");
  //         const user = await sdk.callRawAPI(
  //           `/v3/api/custom/goodbadugly/get-profile/${profile.id}`,
  //           {},
  //           "GET"
  //         );

  //         if (!user?.data?.is_onboarded) {
  //           navigate("/member/get-started");
  //         }
  //       } catch (error) {
  //         console.error("Error checking onboarding status:", error);
  //         showToast(
  //           error?.response?.data?.message ?? error.message,
  //           4000,
  //           "error"
  //         );
  //       }
  //     }
  //   };

  //   checkOnboarding();
  // }, [profile?.id]);

  // Fetch collaboration requests
  const fetchCollaborationRequests = async () => {
    console.log("Fetching collaboration requests");
    setLoadingCollabs(true);
    const tdk = new TreeSDK();
    try {
      const result = await sdk.callRawAPI(
        "/v3/api/custom/goodbadugly/team-updates",
        {},
        "GET"
      );

      if (!result.error && result.data) {
        console.log("Team updates data received:", result.data);
        // Filter for collaboration_status === 2 and convert to array before mapping
        const uniqueUpdates = Array.from(
          result.data.filter((update) => update.collaborator_status == 2)
        );
        console.log("Filtered collaboration updates:", uniqueUpdates);

        // Use Promise.all to wait for all async operations
        const collaboratorData = await Promise.all(
          uniqueUpdates.map(async (update) => {
            const res = await tdk.getList("update_collaborators", {
              filter: [`id,eq,${update?.update_collaborator_id}`],
            });

            // Only take the first matching collaborator since we're dealing with specific IDs
            const collaborator = res?.list?.[0];
            if (collaborator) {
              return {
                ...update, // Keep the original update data with user info
                collaborator_details: collaborator, // Add collaborator details as a nested object
              };
            }
            return null;
          })
        );

        // Filter out any null values and set to state
        const validData = collaboratorData.filter(Boolean);
        console.log("Processed collaborator data:", validData);
        setCollaborationRequests(validData);
      }
    } catch (error) {
      console.error("Error fetching collaboration requests:", error);
      const message = error?.response?.data?.message ?? error.message;
      showToast(message, 5000);
      tokenExpireError(message);
    } finally {
      setLoadingCollabs(false);
    }
  };

  // Handle collaboration action
  const handleCollaborationAction = async (updateId, action) => {
    try {
      const endpoint = "/v3/api/custom/goodbadugly/collaborator/response";
      const status = action === "accept" ? 1 : 2; // 1 for accept, 2 for reject

      const collaborator = collaborationRequests.find(
        (request) => request.id === updateId
      );

      console.log("Handling collaboration action", {
        updateId,
        action,
        collaborator_id: collaborator?.collaborator_details?.collaborator_id,
      });

      const result = await sdk.callRawAPI(
        endpoint,
        {
          collaborator_id: collaborator?.collaborator_details?.collaborator_id,
          update_id: updateId,
          status: status,
        },
        "POST"
      );

      console.log("Collaboration action result:", result);

      // Update global state to trigger refetch
      setGlobalState("collaborationChange", !globalState?.collaborationChange);

      if (!result.error) {
        showToast(`Collaboration request ${action}ed successfully`);

        // Refresh the lists immediately
        await fetchCollaborationRequests();
        await fetchUpdateRequests();

        if (action === "accept") {
          // Add a small delay before navigation
          setTimeout(() => {
            navigate(`/member/edit-updates/${currentCollaboration}`);
          }, 100);
        }
      } else {
        showToast(`Failed to ${action} collaboration request`, 5000);
      }
    } catch (error) {
      console.error("Error handling collaboration:", error);
      const message = error?.response?.data?.message ?? error.message;
      showToast(message, 5000);
      tokenExpireError(message);
    }
  };

  // Fetch update requests
  const fetchUpdateRequests = async () => {
    console.log("Fetching update requests");
    try {
      setLoadingUpdateRequests(true);
      const sdk = new MkdSDK();
      const tdk = new TreeSDK();

      // First get the update requests
      const result = await sdk.callRawAPI(
        `/v3/api/custom/goodbadugly/users/update-requests`,
        undefined,
        "GET"
      );

      console.log("Update requests data received:", result);
      if (!result.error && result.data?.length > 0) {
        // Get unique requesting user IDs
        const requestingUserIds = [
          ...new Set(result.data.map((req) => req.requesting_user_id)),
        ];
        console.log("Requesting user IDs:", requestingUserIds);

        // Fetch user details for all requesting users in one call
        const userDetails = await tdk.getPaginate("user", {
          filter: [`id,in,${requestingUserIds.join(",")}`],
          page: 1,
          limit: requestingUserIds.length,
        });
        console.log("User details:", userDetails);

        // Create a map of user details for quick lookup
        const userMap = userDetails.list.reduce((acc, user) => {
          acc[user.id] = user;
          return acc;
        }, {});

        // Fetch company details for requesting user
        const userCompanyMap = {};
        for (const userId of requestingUserIds) {
          sdk.setTable("companies");
          const companyResult = await sdk.callRawAPI(
            `/v4/api/records/companies?filter=user_id,eq,${userId}`,
            undefined,
            "GET"
          );
          if (companyResult?.list?.[0]) {
            userCompanyMap[userId] = companyResult.list[0];
          }
        }
        console.log("User company map:", userCompanyMap);

        // Combine update requests with user details and company info
        const updatesWithUserDetails = result.data.map((request) => ({
          ...request,
          requesting_user: userMap[request.requesting_user_id] || null,
          requesting_user_company:
            userCompanyMap[request.requesting_user_id] || null,
        }));

        console.log("Updates with user details:", updatesWithUserDetails);
        setUpdateRequests(updatesWithUserDetails);
        setGlobalState("requestedUpdates", updatesWithUserDetails?.length);
      } else {
        console.log("No update requests found or error in response");
        setUpdateRequests([]);
        setGlobalState("requestedUpdates", 0);
      }
    } catch (error) {
      console.error("Error fetching update requests:", error);
      showToast(
        error.message || "Error fetching update requests",
        5000,
        "error"
      );
    } finally {
      setLoadingUpdateRequests(false);
    }
  };

  // Fetch unanswered questions
  const fetchUnansweredQuestions = async () => {
    try {
      setLoadingUnansweredQuestions(true);
      const sdk = new MkdSDK();

      // Get unanswered questions
      const result = await sdk.callRawAPI(
        `/v3/api/custom/goodbadugly/user/unanswered-questions`,
        [],
        "GET"
      );

      if (!result.error && result.data?.length > 0) {
        setUnansweredQuestions(result.data);
        setGlobalState("unansweredQuestions", result.data.length);
      } else {
        setUnansweredQuestions([]);
        setGlobalState("unansweredQuestions", 0);
      }
    } catch (error) {
      console.error("Error fetching unanswered questions:", error);
      showToast(
        error.message || "Error fetching unanswered questions",
        5000,
        "error"
      );
    } finally {
      setLoadingUnansweredQuestions(false);
    }
  };

  // Handle update request action
  const handleUpdateRequestAction = async (requestId, updateId, action) => {
    try {
      console.log("Handling update request action", {
        requestId,
        updateId,
        action,
      });

      const sdk = new MkdSDK();
      const result = await sdk.callRawAPI(
        `/v3/api/custom/goodbadugly/update-requests/acceptance`,
        {
          request: action === "accept" ? 1 : 2,
          update_id: updateId,
        },
        "POST"
      );

      console.log("Update request action result:", result);

      // Update global state to trigger refetch
      setGlobalState("updateRequestChange", !globalState?.updateRequestChange);

      if (!result.error) {
        showToast(`Update request ${action}ed successfully`);

        // Refresh the lists immediately
        await fetchUpdateRequests();
        await fetchCollaborationRequests();
        await refetchInvitations();

        if (action === "accept") {
          // Add a small delay before navigation
          setTimeout(() => {
            navigate(`/member/edit-updates/${updateId}?autofocus=true`);
          }, 100);
        }
      } else {
        showToast(`Failed to ${action} update request`, 5000, "error");
      }
    } catch (error) {
      console.error("Error handling update request:", error);
      showToast(
        error.message || `Error ${action}ing update request`,
        5000,
        "error"
      );
    }
  };

  // Fetch cadence notifications
  const fetchCadenceNotifications = async () => {
    try {
      setLoadingCadenceNotifications(true);
      const sdk = new MkdSDK();

      const result = await sdk.callRawAPI(
        `/v3/api/custom/goodbadugly/updates/cadence-notifications`,
        [],
        "GET"
      );

      if (!result.error && result.data?.notifications) {
        setCadenceNotifications(result.data.notifications);
      } else {
        setCadenceNotifications([]);
      }
    } catch (error) {
      console.error("Error fetching cadence notifications:", error);
      showToast(
        error.message || "Error fetching cadence notifications",
        5000,
        "error"
      );
    } finally {
      setLoadingCadenceNotifications(false);
    }
  };

  // Fetch update shared notifications
  const fetchUpdateSharedNotifications = async () => {
    try {
      setLoadingUpdateSharedNotifications(true);
      const sdk = new MkdSDK();

      const result = await sdk.callRawAPI(
        `/v3/api/custom/goodbadugly/notifications/reports`,
        [],
        "GET"
      );

      if (!result.error && result?.notifications) {
        setUpdateSharedNotifications(result?.notifications);
      } else {
        setUpdateSharedNotifications([]);
      }
    } catch (error) {
      console.error("Error fetching update shared notifications:", error);
      showToast(
        error.message || "Error fetching update shared notifications",
        5000,
        "error"
      );
    } finally {
      setLoadingUpdateSharedNotifications(false);
    }
  };

  useEffect(() => {
    fetchCollaborationRequests();
    fetchUpdateRequests();
    fetchUnansweredQuestions();
    fetchMentions();
    fetchUnreadCount();
    fetchCadenceNotifications();
    fetchUpdateSharedNotifications();

    const interval = setInterval(() => {
      fetchCollaborationRequests();
      fetchUpdateRequests();
      fetchUnansweredQuestions();
      fetchMentions();
      fetchUnreadCount();
      fetchCadenceNotifications();
      fetchUpdateSharedNotifications();
    }, 180000); // 3 minutes

    return () => clearInterval(interval);
  }, []);

  const getRelativeTime = (date) => {
    const now = moment();
    const then = moment(date);
    const diff = now.diff(then, "seconds");

    if (diff < 60) return "just now";
    if (diff < 3600) {
      const mins = Math.floor(diff / 60);
      return `${mins} ${mins === 1 ? "min" : "mins"} ago`;
    }
    if (diff < 86400) {
      const hours = Math.floor(diff / 3600);
      return `${hours} ${hours === 1 ? "hour" : "hours"} ago`;
    }
    if (diff < 604800) {
      const days = Math.floor(diff / 86400);
      return `${days} ${days === 1 ? "day" : "days"} ago`;
    }
    if (diff < 2592000) {
      const weeks = Math.floor(diff / 604800);
      return `${weeks} ${weeks === 1 ? "week" : "weeks"} ago`;
    }
    if (diff < 31536000) {
      const months = Math.floor(diff / 2592000);
      return `${months} ${months === 1 ? "month" : "months"} ago`;
    }
    const years = Math.floor(diff / 31536000);
    return `${years} ${years === 1 ? "year" : "years"} ago`;
  };

  const handleInvitationAction = (inviteId, action) => {
    setCurrentInvitation(inviteId);
    setActionType(action);
    setGlobalState("memberChange", !globalState?.memberChange);
    setShowConfirmationModal(true);
  };

  // Add useEffect to watch for memberChange
  useEffect(() => {
    refetchInvitations();
  }, [globalState?.memberChange]);

  // Add useEffect to watch for collaborationChange
  useEffect(() => {
    console.log(
      "collaborationChange triggered",
      globalState?.collaborationChange
    );
    // Use async function to ensure proper execution
    const fetchData = async () => {
      await fetchCollaborationRequests();
      console.log("Collaboration requests fetched");
    };
    fetchData();
  }, [globalState?.collaborationChange]);

  // Add useEffect to watch for updateRequestChange
  useEffect(() => {
    console.log(
      "updateRequestChange triggered",
      globalState?.updateRequestChange
    );
    // Use async function to ensure proper execution
    const fetchData = async () => {
      await fetchUpdateRequests();
      console.log("Update requests fetched");
    };
    fetchData();
  }, [globalState?.updateRequestChange]);

  // Add useEffect to watch for mentionsChange
  useEffect(() => {
    console.log("mentionsChange triggered", globalState?.mentionsChange);
    // Use async function to ensure proper execution
    const fetchData = async () => {
      await fetchMentions();
      await fetchUnreadCount();
      console.log("Mentions refetched");
    };
    fetchData();
  }, [globalState?.mentionsChange]);

  // Add useEffect to watch for updateSharedNotificationChange
  useEffect(() => {
    console.log(
      "updateSharedNotificationChange triggered",
      globalState?.updateSharedNotificationChange
    );
    // Use async function to ensure proper execution
    const fetchData = async () => {
      await fetchUpdateSharedNotifications();
      console.log("Update shared notifications refetched");
    };
    fetchData();
  }, [globalState?.updateSharedNotificationChange]);

  // Modify the showNotifications effect to only fetch if data is stale
  // useEffect(() => {
  //   if (showNotifications) {
  //     fetchUpdateRequests();
  //     fetchCollaborationRequests();
  //     refetchInvitations();
  //   }
  // }, [showNotifications]);

  // Close notifications when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (!event.target.closest(".notifications-container")) {
        setShowNotifications(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  let toggleOpen = (open) => {
    console.log(open, isOpen);
    globalDispatch({
      type: "OPEN_SIDEBAR",
      payload: { isOpen: open },
    });
  };

  // console.log(state)
  const navigate = useNavigate();
  useEffect(() => {
    const pathArr = location.pathname.split("/");
    if (pathArr[1] !== "user" && pathArr[1] !== "admin") {
      setCurrentPath(pathArr[1]);
    } else {
      setCurrentPath(pathArr[2]);
    }
  }, [location]);

  // Add a function to combine and sort all notifications by timestamp
  const getSortedNotifications = () => {
    const allNotifications = [];

    // Add cadence notifications
    cadenceNotifications.forEach((notification) => {
      allNotifications.push({
        ...notification,
        type: "cadence",
        timestamp: new Date(notification.created_at || Date.now()),
        displayTime:
          notification.urgency_status === "overdue"
            ? `${Math.abs(notification.days_until_due)} days overdue`
            : notification.urgency_status === "due_today"
            ? "Today"
            : "Tomorrow",
      });
    });

    // Add team invitations
    invitations.forEach((invitation) => {
      allNotifications.push({
        ...invitation,
        type: "invitation",
        timestamp: new Date(invitation?.invited_at || Date.now()),
        displayTime: invitation.timeAgo,
      });
    });

    // Add collaboration requests
    collaborationRequests.forEach((collab) => {
      allNotifications.push({
        ...collab,
        type: "collaboration",
        timestamp: new Date(
          collab.collaborator_details?.update_at || Date.now()
        ),
        displayTime: getRelativeTime(collab.collaborator_details?.update_at),
      });
    });

    // Add update requests
    updateRequests.forEach((request) => {
      allNotifications.push({
        ...request,
        type: "update_request",
        timestamp: new Date(request.update_at || Date.now()),
        displayTime: getRelativeTime(request.update_at),
      });
    });

    // Add unanswered questions
    unansweredQuestions.forEach((question) => {
      allNotifications.push({
        ...question,
        type: "question",
        timestamp: new Date(question.sent_at || Date.now()),
        displayTime: getRelativeTime(question.sent_at),
      });
    });

    // Add mentions
    mentions.forEach((mention) => {
      allNotifications.push({
        ...mention,
        type: "mention",
        timestamp: new Date(mention.created_at || Date.now()),
        displayTime: getRelativeTime(mention.created_at),
      });
    });

    // Add update shared notifications
    updateSharedNotifications.forEach((notification) => {
      allNotifications.push({
        ...notification,
        type: "shared_update",
        timestamp: new Date(notification.created_at || Date.now()),
        displayTime: getRelativeTime(notification.created_at),
      });
    });

    // Sort by timestamp (most recent first)
    return allNotifications.sort((a, b) => b.timestamp - a.timestamp);
  };

  // Get sorted notifications
  const sortedNotifications = getSortedNotifications();

  console.log(sortedNotifications, "notifications", "invitations", invitations);

  // Helper function to render notification based on type
  const renderNotification = (notification) => {
    switch (notification.type) {
      case "cadence":
        return (
          <div key={notification.id} className="mb-4 last:mb-0">
            {/* Header with orange background */}
            <div className="mb-2 rounded-t-lg bg-orange-100 px-3 py-2">
              <p className="text-sm text-orange-800">
                <span className="font-medium">
                  Your {notification.cadence_text?.toLowerCase()} scheduled
                  update called {notification.name}
                </span>{" "}
                is ready to view, edit, & send
              </p>
            </div>

            {/* Content area */}
            <div className="rounded-b-lg border border-t-0 border-gray-200 bg-white px-3 py-3">
              <div className="mb-3 flex items-center gap-2">
                <div className="flex max-h-8 min-h-8 min-w-8 max-w-8 items-center justify-center overflow-hidden rounded-full bg-gray-200">
                  <div className="flex h-full w-full items-center justify-center bg-[#1f1d1a]/5 text-sm font-medium">
                    {notification.name?.[0] || "U"}
                  </div>
                </div>
                <div className="flex-grow">
                  <p className="font-medium text-gray-900">
                    {notification.name}
                  </p>
                  <p className="text-sm text-gray-600">
                    is due{" "}
                    {notification.urgency_status === "due_today"
                      ? "today"
                      : "tomorrow"}{" "}
                    ({notification.cadence_text} update)
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-xs font-medium text-gray-500">
                    {notification.action_text ||
                      (notification.urgency_status === "due_today"
                        ? "Due Today"
                        : "Due Tomorrow")}
                  </p>
                </div>
              </div>

              {/* Action buttons */}
              <div className="flex gap-2">
                <button
                  onClick={() => {
                    navigate(notification.edit_link);
                    setShowNotifications(false);
                  }}
                  className="rounded bg-[#1f1d1a] px-4 py-1.5 text-sm font-medium text-white hover:opacity-90"
                >
                  Update Now
                </button>
                <button
                  onClick={() => {
                    // Navigate to view with notification param for auto-dismiss
                    navigate(
                      `${notification.edit_link}?notification_id=${notification.id}`
                    );
                    setShowNotifications(false);
                  }}
                  className="rounded border border-red-500 px-4 py-1.5 text-sm font-medium text-red-600 hover:bg-red-50"
                >
                  View update
                </button>
              </div>
            </div>
          </div>
        );

      case "invitation":
        return (
          <div key={notification.id} className="mb-4 last:mb-0">
            <div className="mb-2 flex items-center gap-2">
              <div className="h-8 max-h-8 min-h-8 w-8 min-w-8 max-w-8 overflow-hidden rounded-full bg-gray-200">
                {notification.photo ? (
                  <img
                    src={notification.photo}
                    alt={notification.inviter}
                    className="h-full w-full object-cover"
                  />
                ) : (
                  <div className="flex h-full w-full items-center justify-center bg-[#1f1d1a]/5 text-sm font-medium">
                    {notification.inviter.charAt(0)}
                  </div>
                )}
              </div>
              <div className="font-iowan-regular flex flex-grow items-center justify-between gap-6">
                <p className="font-iowan-regular whitespace-nowrap  text-[11px] sm:text-sm">
                  <span className="font-iowan font-semibold">
                    {notification.inviter}
                  </span>{" "}
                  <span className="font-iowan-regular text-base">
                    has invited you to{" "}
                  </span>
                  <span className="font-iowan font-semibold">
                    {notification.team}
                  </span>
                  <span className="ml-[5px] font-iowan font-semibold">
                    team
                  </span>
                </p>
                <p className="whitespace-nowrap font-inter text-xs font-medium text-black">
                  {notification.displayTime}
                </p>
              </div>
            </div>
            <div className="flex w-[50%] gap-2">
              <button
                onClick={() =>
                  handleInvitationAction(notification.id, "reject")
                }
                disabled={loading}
                className="= w-[85px] flex-1 rounded-sm border border-[#1f1d1a] px-4 py-1.5 font-iowan text-sm font-medium disabled:opacity-50"
              >
                Reject
              </button>
              <button
                onClick={() =>
                  handleInvitationAction(notification.id, "accept")
                }
                disabled={loading}
                className="w-[85px] flex-1 rounded-sm bg-[#1f1d1a] px-4 py-1.5 font-iowan text-sm font-medium text-white hover:bg-[#2a2724] disabled:opacity-50"
              >
                Accept
              </button>
            </div>
          </div>
        );

      case "collaboration":
        return (
          <div key={notification.id} className="mb-4 last:mb-0">
            <div className="mb-2 flex items-center gap-2">
              <div className="flex max-h-8 min-h-8 min-w-8 max-w-8 items-center justify-center overflow-hidden rounded-full bg-gray-200">
                {notification.user?.photo ? (
                  <img
                    src={notification.user.photo}
                    alt={`${notification.user.first_name} ${notification.user.last_name}`}
                    className="h-full w-full object-cover"
                  />
                ) : (
                  <div className="flex h-full w-full items-center justify-center bg-[#1f1d1a]/5 text-sm font-medium">
                    {notification.user?.first_name?.charAt(0)}
                  </div>
                )}
              </div>
              <div className="font-iowan-regular flex flex-grow items-center justify-between gap-6">
                <p className="font-iowan-regular whitespace-nowrap text-[11px] sm:text-sm">
                  <span className="font-iowan font-semibold">
                    {`${notification.user?.first_name} ${notification.user?.last_name}`}
                  </span>{" "}
                  <span className="font-iowan-regular text-base">
                    requested collaboration on
                  </span>{" "}
                  <span className="font-iowan font-semibold">
                    {notification.name}
                  </span>
                  {notification.company_name && (
                    <>
                      <span className="font-iowan-regular text-base">
                        {" "}
                        for{" "}
                      </span>
                      <span className="font-iowan font-semibold">
                        {notification.company_name}
                      </span>
                    </>
                  )}
                </p>
                <p className="whitespace-nowrap font-inter text-xs font-medium text-black">
                  {notification.displayTime}
                </p>
              </div>
            </div>
            <div className="flex w-[50%] gap-2">
              <button
                onClick={() => {
                  setCurrentCollaboration(notification.id);
                  setActionType("reject");
                  setShowConfirmationModal(true);
                }}
                disabled={loadingCollabs}
                className="w-[85px] flex-1 rounded-sm border border-[#1f1d1a] px-4 py-1.5 font-iowan text-sm font-medium disabled:opacity-50"
              >
                Reject
              </button>
              <button
                onClick={() => {
                  setCurrentCollaboration(notification.id);
                  setActionType("accept");
                  setShowConfirmationModal(true);
                }}
                disabled={loadingCollabs}
                className="w-[85px] flex-1 rounded-sm bg-[#1f1d1a] px-4 py-1.5 font-iowan text-sm font-medium text-white hover:bg-[#2a2724] disabled:opacity-50"
              >
                Accept
              </button>
            </div>
          </div>
        );

      case "update_request":
        return (
          <div key={notification.id} className="mb-4 last:mb-0">
            <div className="mb-2 flex items-center gap-2">
              <div className="flex max-h-8 min-h-8 min-w-8 max-w-8 items-center justify-center overflow-hidden rounded-full bg-gray-200">
                {notification.requesting_user?.photo ? (
                  <img
                    src={notification.requesting_user.photo}
                    alt={`${notification.requesting_user.first_name} ${notification.requesting_user.last_name}`}
                    className="h-full w-full object-cover"
                  />
                ) : (
                  <div className="flex h-full w-full items-center justify-center bg-[#1f1d1a]/5 text-sm font-medium">
                    {notification.requesting_user?.first_name?.[0] || ""}
                  </div>
                )}
              </div>
              <div className="font-iowan-regular flex flex-grow items-center justify-between gap-6">
                <p className="font-iowan-regular whitespace-normal text-sm">
                  <span className="font-iowan font-semibold">
                    {`${notification.requesting_user?.first_name || ""} ${
                      notification.requesting_user?.last_name || ""
                    }`}
                  </span>{" "}
                  {notification.requesting_user_company && (
                    <>
                      <span className="font-iowan-regular text-base">
                        from{" "}
                      </span>
                      <span className="font-iowan font-semibold">
                        {notification.requesting_user_company.name}
                      </span>{" "}
                    </>
                  )}
                  <span className="font-iowan-regular text-base">
                    requested an update
                  </span>{" "}
                </p>
                <p className="whitespace-nowrap font-inter text-xs font-medium text-black">
                  {notification.displayTime}
                </p>
              </div>
            </div>
            <div className="flex w-[50%] gap-2">
              <button
                onClick={() => {
                  setCurrentInvitation(notification.id);
                  setCurrentInvitationType("update_request");
                  setActionType("reject");
                  setShowConfirmationModal(true);
                  setCurrentUpdateId(notification.update_id);
                }}
                disabled={loadingUpdateRequests}
                className="w-[85px] flex-1 rounded-sm border border-[#1f1d1a] px-4 py-1.5 font-iowan text-sm font-medium disabled:opacity-50"
              >
                Reject
              </button>
              <button
                onClick={() => {
                  setCurrentInvitation(notification.id);
                  setCurrentInvitationType("update_request");
                  setActionType("accept");
                  setShowConfirmationModal(true);
                  setCurrentUpdateId(notification.update_id);
                }}
                disabled={loadingUpdateRequests}
                className="w-[85px] flex-1 rounded-sm bg-[#1f1d1a] px-4 py-1.5 font-iowan text-sm font-medium text-white hover:bg-[#2a2724] disabled:opacity-50"
              >
                Accept
              </button>
            </div>
          </div>
        );

      case "question":
        return (
          <div key={notification.id} className="mb-4 last:mb-0">
            <div className="mb-2 flex items-center gap-2">
              <div className="flex max-h-8 min-h-8 min-w-8 max-w-8 items-center justify-center overflow-hidden rounded-full bg-gray-200">
                {notification.creator?.photo ? (
                  <img
                    src={notification.creator.photo}
                    alt={`${notification.creator.first_name} ${notification.creator.last_name}`}
                    className="h-full w-full object-cover"
                  />
                ) : (
                  <div className="flex h-full w-full items-center justify-center bg-[#1f1d1a]/5 text-sm font-medium">
                    {notification.creator?.first_name?.[0] || ""}
                  </div>
                )}
              </div>
              <div className="font-iowan-regular flex flex-grow items-center justify-between gap-6">
                <p className="font-iowan-regular whitespace-normal text-sm">
                  <span className="font-iowan font-semibold">
                    {`${notification.creator?.first_name || ""} ${
                      notification.creator?.last_name || ""
                    }`}
                  </span>{" "}
                  <span className="font-iowan-regular text-base">
                    asked a question in{" "}
                  </span>
                  <span className="font-iowan font-semibold">
                    {notification.update_name}
                  </span>
                </p>
                <p className="whitespace-nowrap font-inter text-xs font-medium text-black">
                  {notification.displayTime}
                </p>
              </div>
            </div>
            <div className="">
              <button
                onClick={() => {
                  navigate(
                    `/member/update/private/view/${notification.update_id}?question=${notification.id}`
                  );
                  setShowNotifications(false);
                }}
                className="w-[92px] flex-1 rounded-sm bg-[#1f1d1a] px-4 py-1.5 font-iowan text-sm font-medium text-white hover:bg-[#2a2724]"
              >
                Respond
              </button>
            </div>
          </div>
        );

      case "mention":
        return (
          <div key={notification.id} className="mb-4 last:mb-0">
            <div className="mb-2 flex items-center gap-2">
              <div className="flex max-h-8 min-h-8 min-w-8 max-w-8 items-center justify-center overflow-hidden rounded-full bg-gray-200">
                {notification.mentioner_photo ? (
                  <img
                    src={notification.mentioner_photo}
                    alt={`${notification.mentioner_first_name} ${notification.mentioner_last_name}`}
                    className="h-full w-full object-cover"
                  />
                ) : (
                  <div className="flex h-full w-full items-center justify-center bg-[#1f1d1a]/5 text-sm font-medium">
                    {notification.mentioner_first_name?.[0] || ""}
                  </div>
                )}
              </div>
              <div className="font-iowan-regular flex flex-grow items-center justify-between gap-6">
                <p className="font-iowan-regular whitespace-normal text-sm">
                  <span className="font-iowan font-semibold">
                    {`${notification.mentioner_first_name || ""} ${
                      notification.mentioner_last_name || ""
                    }`}
                  </span>{" "}
                  <span className="font-iowan-regular text-base">
                    {notification.mention_type === "reply"
                      ? "replied to you in"
                      : "mentioned you in"}{" "}
                  </span>
                  <span className="font-iowan font-semibold">
                    {notification.update_name}
                  </span>
                </p>
                <p className="whitespace-nowrap font-inter text-xs font-medium text-black">
                  {notification.displayTime}
                </p>
              </div>
            </div>
            <div className="">
              <button
                onClick={() => {
                  // Mark this mention as seen

                  // Navigate to the update with the comment
                  let url = `/member/update/private/view/${notification.update_id}`;

                  // If we have a comment_id, add it to navigate directly to the comment
                  if (notification.comment_id) {
                    url += `#${btoa(`comment:${notification.comment_id}`)}`;
                  }

                  navigate(url);
                  setShowNotifications(false);
                }}
                className={`w-[92px] flex-1 rounded-sm ${
                  notification.seen === 0
                    ? "bg-[#1f1d1a] text-white hover:bg-[#2a2724]"
                    : "border border-[#1f1d1a] bg-transparent text-[#1f1d1a]"
                } px-4 py-1.5 font-iowan text-sm font-medium`}
              >
                {notification.seen === 0 ? "View" : "Viewed"}
              </button>
            </div>
          </div>
        );

      case "shared_update":
        return (
          <div key={notification.id} className="mb-4 last:mb-0">
            <div className="mb-2 flex items-center gap-2">
              <div className="flex max-h-8 min-h-8 min-w-8 max-w-8 items-center justify-center overflow-hidden rounded-full bg-gray-200">
                {notification?.sender_photo ? (
                  <img
                    src={notification?.sender_photo}
                    alt={`${notification.sender_first_name} ${notification.sender_last_name}`}
                    className="h-full w-full object-cover"
                  />
                ) : (
                  <div className="flex h-full w-full items-center justify-center bg-[#1f1d1a]/5 text-sm font-medium">
                    {notification.sender_first_name?.[0] || "U"}
                  </div>
                )}
              </div>
              <div className="font-iowan-regular flex flex-grow items-center justify-between gap-6">
                <p className="font-iowan-regular whitespace-normal text-sm">
                  <span className="font-iowan font-semibold">
                    {`${notification.sender_first_name || ""} ${
                      notification.sender_last_name || ""
                    }`}
                  </span>{" "}
                  <span className="font-iowan-regular text-base">
                    shared an update with you
                  </span>{" "}
                  {notification.company_name && (
                    <>
                      <span className="font-iowan-regular text-base">
                        from{" "}
                      </span>
                      <span className="font-iowan font-semibold">
                        {notification.company_name}
                      </span>
                    </>
                  )}
                </p>
                <p className="whitespace-nowrap font-inter text-xs font-medium text-black">
                  {notification.displayTime}
                </p>
              </div>
            </div>
            <div className="">
              <button
                onClick={() => {
                  // Navigate to the update
                  navigate(
                    `/member/update/private/view/${notification.update_id}`
                  );
                  setShowNotifications(false);
                }}
                className={`w-[92px] flex-1 rounded-sm ${
                  notification.seen === 0
                    ? "bg-[#1f1d1a] text-white hover:bg-[#2a2724]"
                    : "border border-[#1f1d1a] bg-transparent text-[#1f1d1a]"
                } px-4 py-1.5 font-iowan text-sm font-medium`}
              >
                {notification.seen === 0 ? "View" : "Viewed"}
              </button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <>
      <div className="sticky right-0 top-0 z-[9] flex h-[4.5rem] w-full flex-row items-center justify-between bg-[#1f1d1a] px-4 md:hidden md:px-8">
        <img
          onClick={() => navigate("/member/dashboard")}
          src={logo5}
          alt="logo"
          className="h-10 w-[180px] cursor-pointer sm:invisible"
        />

        <div className="flex items-center gap-3">
          {/* Notifications Component */}
          <div className="notifications-container relative md:hidden">
            <button
              className="relative rounded-[50%] border border-[#E9DBD2] p-2"
              onClick={() => setShowNotifications(!showNotifications)}
              disabled={loading || loadingCollabs}
            >
              <BellIcon className="h-4 w-4 fill-brown-main-bg text-brown-main-bg" />
              {invitations.length +
                collaborationRequests.length +
                updateRequests.length +
                unansweredQuestions.length +
                unreadMentionsCount +
                cadenceNotifications.length +
                updateSharedNotifications.length >
                0 && (
                <div className="absolute -right-2 -top-[0.25rem] flex h-4 w-4 items-center justify-center rounded-full bg-[#F6A03C] text-xs text-[black]">
                  <span className="text-xs">
                    {invitations.length +
                      collaborationRequests.length +
                      updateRequests.length +
                      unansweredQuestions.length +
                      unreadMentionsCount +
                      cadenceNotifications.length +
                      updateSharedNotifications.length}
                  </span>
                </div>
              )}
            </button>

            {/* Notifications Dropdown */}
            {showNotifications && (
              <div className="custom-overscroll absolute right-[-42px] top-12 z-[1000] max-h-[252px] min-w-[300px] overflow-y-auto rounded-sm border border-[#1f1d1a]/10 bg-brown-main-bg p-4 shadow-lg sm:right-0 md:w-[445px]">
                {/* Arrow pointing to notification icon */}
                <div className="absolute -top-2 right-4 h-4 rotate-45 transform border-l border-t border-[#1f1d1a]/10 bg-brown-main-bg"></div>

                {sortedNotifications.length > 0 ? (
                  <>
                    {/* Render sorted notifications */}
                    {sortedNotifications.map((notification) =>
                      renderNotification(notification)
                    )}
                  </>
                ) : (
                  <div className="flex flex-col items-center justify-center py-8">
                    <div className="mb-4 rounded-full bg-[#1f1d1a]/5 p-4">
                      <BellIcon className="h-8 w-8 text-[#1f1d1a]/40" />
                    </div>
                    <h3 className="mb-2 text-lg font-semibold text-[#1f1d1a]">
                      You have no notifications
                    </h3>
                    <p className="text-center text-sm text-[#1f1d1a]/60">
                      Recent activity within your account that requires you to
                      take action
                      <br />
                      will be shown here
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
          {!isOpen ? (
            <svg
              className="cursor-pointer"
              width="24"
              onClick={() => toggleOpen(!isOpen)}
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M21 12C21 12.1989 20.921 12.3897 20.7803 12.5303C20.6397 12.671 20.4489 12.75 20.25 12.75H3.75C3.55109 12.75 3.36032 12.671 3.21967 12.5303C3.07902 12.3897 3 12.1989 3 12C3 11.8011 3.07902 11.6103 3.21967 11.4697C3.36032 11.329 3.55109 11.25 3.75 11.25H20.25C20.4489 11.25 20.6397 11.329 20.7803 11.4697C20.921 11.6103 21 11.8011 21 12ZM3.75 6.75H20.25C20.4489 6.75 20.6397 6.67098 20.7803 6.53033C20.921 6.38968 21 6.19891 21 6C21 5.80109 20.921 5.61032 20.7803 5.46967C20.6397 5.32902 20.4489 5.25 20.25 5.25H3.75C3.55109 5.25 3.36032 5.32902 3.21967 5.46967C3.07902 5.61032 3 5.80109 3 6C3 6.19891 3.07902 6.38968 3.21967 6.53033C3.36032 6.67098 3.55109 6.75 3.75 6.75ZM20.25 17.25H3.75C3.55109 17.25 3.36032 17.329 3.21967 17.4697C3.07902 17.6103 3 17.8011 3 18C3 18.1989 3.07902 18.3897 3.21967 18.5303C3.36032 18.671 3.55109 18.75 3.75 18.75H20.25C20.4489 18.75 20.6397 18.671 20.7803 18.5303C20.921 18.3897 21 18.1989 21 18C21 17.8011 20.921 17.6103 20.7803 17.4697C20.6397 17.329 20.4489 17.25 20.25 17.25Z"
                fill="#FEF1E5"
              />
            </svg>
          ) : (
            <svg
              onClick={() => toggleOpen(!isOpen)}
              className="cursor-pointer"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M7 7L12 12M12 12L17 17M12 12L17 7M12 12L7 17"
                stroke="#FCF1E6"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          )}
        </div>
      </div>

      <div className="sticky right-0 top-0 z-[9] hidden h-[4.5rem] w-full flex-row items-center justify-between border-b-[2px] border-b-[#1F1D1A] bg-brown-main-bg px-8  shadow-sm sm:h-[4.5rem] sm:max-h-[4.5rem]  sm:flex-row sm:px-6 sm:pl-[20px] md:flex md:pl-[100px] lg:pl-[20px] xl:pl-[20px]">
        {/* <div className="flex gap-3 items-center">
        {showBackButton && <BackButton />}
        <h1 className="text-xl capitalize">
          {profileData?.display_name}
        </h1>
      </div> */}

        {/* <span className="text-[20px] font-[700] ">SpaceX</span> */}
        <div className="flex items-center gap-2">
          <EditStartupCompanyName />
          <LazyLoad>
            <UpdateStatus />
          </LazyLoad>
          <AdminBar />
        </div>

        <div className="flex items-center gap-4 md:self-auto">
          <div className="hidden md:block">
            <LazyLoad>
              <TrialNotice />
            </LazyLoad>
          </div>

          {/* Notifications Component */}
          <div className="notifications-container relative">
            <button
              className="relative rounded-[50%] border border-[#E9DBD2] p-2"
              onClick={() => setShowNotifications(!showNotifications)}
              disabled={loading || loadingCollabs}
            >
              <BellIcon className="h-6 w-6 fill-black text-[#1f1d1a]" />
              {invitations.length +
                collaborationRequests.length +
                updateRequests.length +
                unansweredQuestions.length +
                unreadMentionsCount +
                cadenceNotifications.length +
                updateSharedNotifications.length >
                0 && (
                <div className="absolute -right-2 -top-[0.25rem] flex h-5 w-5 items-center justify-center rounded-full bg-[#F6A03C] text-xs text-black">
                  <span className="text-xs">
                    {invitations.length +
                      collaborationRequests.length +
                      updateRequests.length +
                      unansweredQuestions.length +
                      unreadMentionsCount +
                      cadenceNotifications.length +
                      updateSharedNotifications.length}
                  </span>
                </div>
              )}
            </button>

            {/* Notifications Dropdown */}
            {showNotifications && (
              <div className="custom-overscroll absolute right-0 top-12 z-[1000] max-h-[252px] w-[420px] overflow-y-auto rounded-sm border border-[#1f1d1a]/10 bg-brown-main-bg p-4 shadow-lg sm:w-[460px]">
                {/* Arrow pointing to notification icon */}
                <div className="absolute -top-2 right-4 h-4 rotate-45 transform border-l border-t border-[#1f1d1a]/10 bg-brown-main-bg"></div>

                {sortedNotifications.length > 0 ? (
                  <>
                    {/* Render sorted notifications */}
                    {sortedNotifications.map((notification) =>
                      renderNotification(notification)
                    )}
                  </>
                ) : (
                  <div className="flex flex-col items-center justify-center py-8">
                    <div className="mb-4 rounded-full bg-[#1f1d1a]/5 p-4">
                      <BellIcon className="h-8 w-8 text-[#1f1d1a]/40" />
                    </div>
                    <h3 className="mb-2 text-lg font-semibold text-[#1f1d1a]">
                      You have no notifications
                    </h3>
                    <p className="text-center text-sm text-[#1f1d1a]/60">
                      Recent activity within your account that requires you to
                      take action
                      <br />
                      will be shown here
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
      <LazyLoad>
        <ActionConfirmationModal
          mode="manual"
          action={actionType}
          multiple={false}
          inputConfirmation={false}
          className="action-confirmation-modal"
          isOpen={showConfirmationModal}
          onClose={() => {
            setShowConfirmationModal(false);
            setCurrentInvitation(null);
            setCurrentCollaboration(null);
            setCurrentUpdateId(null);
          }}
          onSuccess={async () => {
            if (currentInvitationType === "update_request") {
              await handleUpdateRequestAction(
                currentInvitation,
                currentUpdateId,
                actionType
              );
            } else if (currentInvitation) {
              await handleInvitation(currentInvitation, actionType);
            } else if (currentCollaboration) {
              await handleCollaborationAction(currentCollaboration, actionType);
            }
            setShowConfirmationModal(false);
            setCurrentInvitation(null);
            setCurrentCollaboration(null);
            setCurrentUpdateId(null);
          }}
          title={
            currentInvitationType === "update_request"
              ? "Update Request"
              : currentInvitation
              ? "Team Invitation"
              : "Collaboration Request"
          }
          customMessage={
            currentInvitationType === "update_request"
              ? `Are you sure you want to ${actionType} this update request?${
                  actionType === "accept"
                    ? " You will be redirected to the update page after accepting."
                    : ""
                }`
              : currentInvitation
              ? `This action will ${actionType} the team invitation.`
              : `This action will ${actionType} the collaboration request. ${
                  actionType === "accept"
                    ? "You will be redirected to the update page after accepting."
                    : ""
                }`
          }
        />
      </LazyLoad>
    </>
  );
};

export default TopHeaderMember;
// <div className="flex w-full max-w-[370px] flex-row items-center  gap-2 ">
//   {/* <div className="flex flex-row flex-nowrap items-center gap-2 font-Inter text-[13px] sm:text-[16px]">
//     <span className="font-iowan whitespace-nowrap font-[400]">
//       Your free trial expires in{" "}
//     </span>

//     <span className="whitespace-nowrap  font-[600]"> 6 Days</span>
//   </div>{" "} */}
//   {/* <button
//     onClick={() => {
//       navigate("/member/billing");
//     }}
//     className="h-[28px] w-full max-w-[90px] rounded-[2px] border-[1px] border-[#1f1d1a] text-[12px] font-[700] sm:hidden sm:h-[36px] sm:max-w-[139px] sm:text-[16px]"
//   >
//     Upgrade
//   </button>
//   <button
//     onClick={() => {
//       navigate("/member/billing");
//     }}
//     className="hidden h-[28px] w-full max-w-[90px] rounded-[2px] border-[1px] border-[#1f1d1a] text-[12px] font-[700] sm:block sm:h-[36px] sm:max-w-[139px] sm:text-[16px]"
//   >
//     Upgrade Now
//   </button> */}
// </div>
