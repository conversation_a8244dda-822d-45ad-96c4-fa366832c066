import { convertDataToHtml, parseJsonSafely } from "Utils/utils";
import { UpdateTextToSpeech } from "./index";
import { useEffect, useState, useCallback, useRef, useContext } from "react";
import { useLocation, useNavigate, useParams } from "react-router";
import { useContexts } from "Hooks/useContexts";
import { Loader } from "Components/Loader";
import { useSDK } from "Hooks/useSDK";
import moment from "moment";
import { empty } from "Utils/utils";
import {
  ReturnToUpdates,
  CompanyDetails,
  UpdateChange,
  UpdateName,
  UpdateContributors,
  UpdateAvailability,
  UpdateMetrics,
  UpdateSectionList,
  UpdateSections,
} from "./index";
import { LazyLoad } from "Components/LazyLoad";
import { OverlayLoader } from "Components/OverlayLoader";
import { useRecentEngagements } from "Hooks/useRecentEngagements";
import MkdSDK from "Utils/MkdSDK";
import { AuthContext } from "Context/Auth";
import FirstStep from "Pages/Investor/Custom/Billing/Plans/CancelSubscriptionButton/FirstStep";
import { useSearchParams } from "react-router-dom";

const UpdatePreview = ({ isPublic = false }) => {
  // SDK
  const { sdk, tdk } = useSDK();
  const { engagements, refetch } = useRecentEngagements();
  const { state: authState } = useContext(AuthContext);
  const projectId = sdk.getProjectId();

  // Get location for hash changes
  const location = useLocation();
  const navigate = useNavigate();
  const [showControls, setShowControls] = useState(true);
  const [searchParams] = useSearchParams();
  const mode = searchParams.get("new");

  // Add this useEffect at the top to set public view state early
  useEffect(() => {
    const isPublicView = location.pathname.includes("/public/view/");
    setGlobalState("isPublicView", isPublicView);

    console.log("Setting public view state:", isPublicView, location.pathname);
  }, [location.pathname]);

  const [Owner, setOwner] = useState("");

  const checkOwner = async (owner) => {
    if (owner) {
      await sdk.setTable("updates");
      const result = await tdk.getOne("user", Number(owner), {
        join: ["profile|user_id"],
      });
      // const result = await sdk.callRawAPI(
      //   `/v4/api/records/user/${owner}`,
      //   {},
      //   "GET"
      // );

      setOwner(result?.model?.first_name + " " + result?.model?.last_name);
      console.log(result, "bop");

      return {
        name: `${result?.model?.first_name} ${result?.model?.last_name}`,
        position: result?.model?.profile?.[0]?.title,
      };
    }
  };

  console.log(Owner, "bop");

  const changestatus = async () => {
    try {
      console.log("changestatus called with update:", update);

      if (!update?.id) {
        console.log("Update ID not available yet, skipping changestatus");
        return;
      }

      // Call the view endpoint before navigation
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        `/v3/api/custom/goodbadugly/activities/${mode}/view`,
        {},
        "PUT"
      );

      console.log("changestatus response:", response);
    } catch (error) {
      console.error("Error calling view endpoint:", error);
    }
  };

  // Add cleanup effect to refetch engagements
  useEffect(() => {
    return () => {
      // Refetch engagements when component unmounts
      refetch();
    };
  }, []);

  // Parse URL parameters correctly
  const getUrlParams = () => {
    const hash = window.location.hash;
    // Find the last occurrence of ? to get the query parameters
    const lastQuestionMarkIndex = hash.lastIndexOf("?");
    if (lastQuestionMarkIndex === -1) return null;

    const queryString = hash.substring(lastQuestionMarkIndex + 1);
    return new URLSearchParams(queryString);
  };

  // Contexts
  const {
    globalDispatch,
    authState: state,
    authDispatch,
    custom,
    getSingle,
    setGlobalState,
    getMany,
    globalState,
  } = useContexts();

  const token = localStorage.getItem("token");

  // States

  const [updateData, setUpdateData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [update, setUpdate] = useState(null);
  const [summary, setSummary] = useState();
  const [error, setError] = useState("");
  const [companyDetails, setCompanyDetails] = useState(null);
  const [questionAnswer, setQuestionAnswer] = useState("");

  // Hooks
  const { update_id, public_link_id } = useParams();

  const path = window.location.href;

  // Add state for active section
  const [activeSection, setActiveSection] = useState(null);

  const [localData, setLocalData] = useState({
    newUpdates: [],
    currentUpdate: null,
    updateNames: [],
    index: 0,
    previousUpdate: null,
    nextUpdate: null,
  });

  // useEffect(() => {
  //   checkOwner(update?.user_id);
  // }, [update]);

  // Add ref for the scrollable container
  const scrollContainerRef = useRef(null);

  // Functions below

  async function fetchCompanyDetails(exposure) {
    const exposures = {
      private: async () => {
        return await getSingle("companies", update?.companies?.id, {
          method: "GET",
        });
      },

      public: async () => {
        return await custom({
          endpoint: `/v3/api/custom/goodbadugly/user/company/${update?.companies?.id}`,
          method: "GET",
        });
      },
    };

    try {
      // const result = await getSingle("companies", update?.companies?.id, {
      //   method: "GET",
      // });b

      const companyRequest = exposures?.[exposure];
      if (!companyRequest) return;

      const result = await companyRequest();

      const company = result?.data ?? {};

      setCompanyDetails(() => {
        return {
          ...company,
          socials: company?.socials
            ? Object.entries(JSON.parse(company?.socials))
                .map(([key, value]) => {
                  if (!empty(value)) {
                    return {
                      key,
                      value,
                    };
                  }
                })
                .filter(Boolean)
            : [],
        };
      });
    } catch (err) {
      showToast(err.message, 5000, "error");
      tokenExpireError(err.message);
    } finally {
    }
  }

  async function fetchUpdate(exposure) {
    setLoading(true);
    const exposures = {
      private: async () => {
        const result = await getSingle("updates", Number(update_id), {
          join: [
            "companies|company_id",
            "notes",
            "update_questions",
            "update_comments",
            "update_reaction",
            "update_comment_replies",
            "update_question_answers",
          ],
          method: "GET",
        });

        const payload = { update_id: update_id };

        // Mark all mentions in this update as seen
        await sdk.callRawAPI(
          "/v3/api/custom/goodbadugly/mentions/mark-all-seen",
          payload,
          "PUT"
        );

        // Update global state to trigger mentions refetch in TopHeaderMember
        setGlobalState("mentionsChange", !globalState?.mentionsChange);

        // Sort notes by order field if they exist
        if (result?.data?.notes && Array.isArray(result.data.notes)) {
          result.data.notes.sort((a, b) => {
            // Handle undefined or null order values
            const orderA =
              a.order !== undefined && a.order !== null
                ? a.order
                : Number.MAX_SAFE_INTEGER;
            const orderB =
              b.order !== undefined && b.order !== null
                ? b.order
                : Number.MAX_SAFE_INTEGER;
            return orderA - orderB;
          });
        }

        return result;
      },
      public: async () => {
        return await custom({
          endpoint: `/v3/api/custom/goodbadugly/public/updates/${update_id}/${public_link_id}`,
          method: "GET",
        });
      },
    };
    // localhost:3000/update/public/view/1572/TXYzQE9WSVVwaGF6THdwX1ByaXZhdGVfMTczNjQ1NTUwNDE2Ng==
    // /v3/api/custom/goodbadugly/ai/ask
    // get company data in public
    // get notes data in public
    // get comments data in public
    // get replies data in public
    // get reactions data in public

    try {
      const updateRequest = exposures?.[exposure];
      if (!updateRequest) return;

      const result = await updateRequest();
      // const result = await tdk.getOne("updates", Number(update_id), {
      //   join: ["companies|company_id", "notes", "update_questions"],
      // });
      // const result = await sdk.callRestAPI(
      //   { id: Number(update_id), join: "companies,notes" },
      //   "GET"
      // );

      setUpdate(result?.data);
      console.log("Update data loaded:", result?.data);
      console.log("Update ID:", result?.data?.id);
      setQuestionAnswer(result?.data?.update_question_answers);

      // Notes are already sorted in the fetchUpdate function
      setUpdateData(result?.data?.notes);

      // GENERATE SUUMMARY
      // loop through notes
      const notes = result?.data?.notes?.map((item) => {
        const { blocks } = parseJsonSafely(item?.content, {
          blocks: [],
        });
        if (blocks.length == 0) return null;
        const html = convertDataToHtml(blocks);
        return { title: item?.type, item: html };
      });

      //Function to remove tags from content
      function stripHtmlTags(html) {
        return html?.replace(/<[^>]*>/g, "");
      }

      // Remove notes and return cleaned summary texts
      const cleanedSummary = notes
        ?.map((note) => stripHtmlTags(note?.item))
        .join("\n");

      const owner2 = await checkOwner(result?.data?.user_id);
      console.log(owner2, "owner2");

      // Create AI prompt
      const prompt = [
        {
          role: "user",
          content: `You are a professional financial analyst creating a summary for investors and stakeholders. Write a comprehensive summary of the following update report.

IMPORTANT: Your summary MUST start with exactly this format:
"This update was sent to you on [month/day/year] by [owner name], [position] at [company name], and is titled [update title]. This update..."

Update Details:
- Update Title: ${result?.data?.name || "Untitled Update"}
- Date Sent (sent_at field): ${
            result?.data?.sent_at
              ? moment(result?.data?.sent_at).format("MMMM D, YYYY")
              : "Not specified"
          }
- Owner Name: ${owner2?.name}
- Company Name: ${result?.data?.companies?.name || "Unknown Company"}
- position :${owner2?.position}

Available Financial Metrics:
${result?.data?.mrr ? `- MRR: ${result?.data?.mrr}` : ""}
${result?.data?.arr ? `- ARR: ${result?.data?.arr}` : ""}
${result?.data?.cash ? `- Cash: ${result?.data?.cash}` : ""}
${result?.data?.burnrate ? `- Burn Rate: ${result?.data?.burnrate}` : ""}
${
  result?.data?.burnrate && result?.data?.cash
    ? `- Runway (months): ${Math.round(
        result?.data?.cash / result?.data?.burnrate
      )}`
    : ""
}

Available Investment Information:
${
  result?.data?.investment_stage
    ? `- Investment Stage: ${result?.data?.investment_stage}`
    : ""
}
${
  result?.data?.invested_to_date
    ? `- Invested to Date: ${result?.data?.invested_to_date}`
    : ""
}
${
  result?.data?.investors_on_cap_table
    ? `- Fund Managers on Cap Table: ${result?.data?.investors_on_cap_table}`
    : ""
}
${
  result?.data?.valuation_at_last_round
    ? `- Valuation of Last Round: ${result?.data?.valuation_at_last_round}`
    : ""
}
${
  result?.data?.date_of_last_round
    ? `- Date of Last Round: ${moment(result?.data?.date_of_last_round).format(
        "MM/DD/YYYY"
      )}`
    : ""
}

Update Content:
${cleanedSummary}

Instructions:
1. Start with the exact format specified above using the "Date Sent" field
2. Write a professional summary in less than 450 words
3. ONLY mention financial metrics and investment information that are actually provided (not empty/null/undefined)
4. Focus on the update content and key developments
5. Maintain a positive, professional tone suitable for investor communications
6. If no metrics are provided and no substantial content exists, focus on the introduction and any available context
7. Do NOT mention missing data or limitations - focus on what is available
8. Use professional, investor-friendly language that builds confidence
just because format is [company name ] doesnt mean you have to use the dummy thats why we have the data.If position is not set just make sure we construct a good grammer with company name`,
        },
      ];

      // Ask AI post request to generate summary from prompt
      const AIsummary = await custom({
        endpoint: `/v3/api/custom/goodbadugly/ai/ask`,
        payload: { prompt: prompt },
        method: "POST",
      });

      setSummary(AIsummary?.data);
      setLoading(false);
    } catch (err) {
      setError(err.message);
    }
    setLoading(false);
  }
  async function fetchUpdate2() {
    try {
      sdk.setTable("updates");
      const result = await tdk.getOne("updates", Number(update_id), {
        join: ["companies|company_id", "notes", "update_questions"],
      });

      // Sort notes by order field if they exist
      if (result?.update?.notes && Array.isArray(result.update.notes)) {
        result.update.notes.sort((a, b) => {
          // Handle undefined or null order values
          const orderA =
            a.order !== undefined && a.order !== null
              ? a.order
              : Number.MAX_SAFE_INTEGER;
          const orderB =
            b.order !== undefined && b.order !== null
              ? b.order
              : Number.MAX_SAFE_INTEGER;
          return orderA - orderB;
        });
      }

      setUpdate(result.update);
      setUpdateData(result?.update?.notes);
      // GENERATE SUUMMARY
      // loop through notes
      const notes = result?.update?.notes?.map((item) => {
        const { blocks } = parseJsonSafely(item?.content, {
          blocks: [],
        });
        if (blocks.length == 0) return null;
        const html = convertDataToHtml(blocks);
        return { title: item?.type, item: html };
      });
      //Function to remove tags from content
      function stripHtmlTags(html) {
        return html?.replace(/<[^>]*>/g, "");
      }
      // Remove notes and return cleaned summary texts
      const cleanedSummary = notes
        ?.map((note) => stripHtmlTags(note?.item))
        .join("\n");
      // Create AI prompt
      const prompt = [
        {
          role: "user",
          content: `You are a professional financial analyst creating a summary for investors and stakeholders. Write a comprehensive summary of the following update report.

IMPORTANT: Your summary MUST start with exactly this format:
"This update was sent to you on [month/day/year] by [owner name] from [company name], and is titled [update title]. This update..."

Update Details:
- Update Title: ${result?.update?.name || "Untitled Update"}
- Date Sent (sent_at field): ${
            result?.update?.sent_at
              ? moment(result?.update?.sent_at).format("MMMM D, YYYY")
              : "Not specified"
          }
- Owner Name: ${(() => {
            const owner = result?.update?.contributors?.find(
              (contributor) => contributor.id === result?.update?.user_id
            );
            return owner ? `${owner.first_name} ${owner.last_name}` : "Unknown";
          })()}
- Company Name: ${result?.update?.companies?.name || "Unknown Company"}

Available Financial Metrics:
${result?.update?.mrr ? `- MRR: ${result?.update?.mrr}` : ""}
${result?.update?.arr ? `- ARR: ${result?.update?.arr}` : ""}
${result?.update?.cash ? `- Cash: ${result?.update?.cash}` : ""}
${result?.update?.burnrate ? `- Burn Rate: ${result?.update?.burnrate}` : ""}
${
  result?.update?.burnrate && result?.update?.cash
    ? `- Runway (months): ${Math.round(
        result?.update?.cash / result?.update?.burnrate
      )}`
    : ""
}

Available Investment Information:
${
  result?.update?.investment_stage
    ? `- Investment Stage: ${result?.update?.investment_stage}`
    : ""
}
${
  result?.update?.invested_to_date
    ? `- Invested to Date: ${result?.update?.invested_to_date}`
    : ""
}
${
  result?.update?.investors_on_cap_table
    ? `- Fund Managers on Cap Table: ${result?.update?.investors_on_cap_table}`
    : ""
}
${
  result?.update?.valuation_at_last_round
    ? `- Valuation of Last Round: ${result?.update?.valuation_at_last_round}`
    : ""
}
${
  result?.update?.date_of_last_round
    ? `- Date of Last Round: ${moment(
        result?.update?.date_of_last_round
      ).format("MM/DD/YYYY")}`
    : ""
}

Update Content:
${cleanedSummary}

Instructions:
1. Start with the exact format specified above using the "Date Sent" field
2. Write a professional summary in less than 450 words
3. ONLY mention financial metrics and investment information that are actually provided (not empty/null/undefined)
4. Focus on the update content and key developments
5. Maintain a positive, professional tone suitable for investor communications
6. If no metrics are provided and no substantial content exists, focus on the introduction and any available context
7. Do NOT mention missing data or limitations - focus on what is available
8. Use professional, investor-friendly language that builds confidence`,
        },
      ];
      // Ask AI post request to generate summary from prompt
      const AIsummary = await sdk.callRawAPI(
        `/v3/api/custom/goodbadugly/ai/ask`,
        {
          prompt: prompt,
        },
        "POST"
      );
      setSummary(AIsummary?.data);
      setLoading(false);
    } catch (err) {
      setError(err.message);
    }
  }
  // Functions above

  const copyPath = () => {
    navigator.clipboard.writeText(path);
    showToast(globalDispatch, "Link Copied");
  };

  // console.log(update?.notes);
  // useEffect(() => {
  //   const preventRightClick = (e) => {
  //     e.preventDefault();
  //   };

  //   window.addEventListener("contextmenu", preventRightClick);

  //   return () => {
  //     window.removeEventListener("contextmenu", preventRightClick);
  //   };
  // }, []);

  useEffect(() => {
    console.log("mode", mode);
    if (mode && update?.id) {
      console.log(
        "Running changestatus with mode:",
        mode,
        "and update ID:",
        update.id
      );
      changestatus();
    }
  }, [mode, update?.id]);

  useEffect(() => {
    const preventDevToolsShortcut = (e) => {
      if (
        e.key === "F12" ||
        (e.ctrlKey && e.shiftKey && e.key === "I") ||
        (e.ctrlKey && e.shiftKey && e.key === "J")
      ) {
        e.preventDefault();
      }
    };

    window.addEventListener("keydown", preventDevToolsShortcut);

    return () => {
      window.removeEventListener("keydown", preventDevToolsShortcut);
    };
  }, []);

  useEffect(() => {
    fetchUpdate(isPublic ? "public" : "private");
  }, [isPublic, update_id]);

  // Mark engagement as viewed when component loads
  useEffect(() => {
    if (update_id && !isPublic) {
      markEngagementViewed(null, null);
    }
  }, [update_id, isPublic]);

  useEffect(() => {
    if (localData.newUpdates.length > 0) {
      const currentIndex = localData.newUpdates.findIndex(
        (id) => id == Number(update_id)
      );

      if (currentIndex !== -1) {
        const previousUpdateId =
          currentIndex < localData.newUpdates.length - 1
            ? localData.newUpdates[currentIndex + 1]
            : null;

        const nextUpdateId =
          currentIndex > 0 ? localData.newUpdates[currentIndex - 1] : null;

        const previousUpdate = previousUpdateId
          ? localData.updateNames.find(
              (item) => item.update_id === previousUpdateId
            )
          : null;
        const nextUpdate = nextUpdateId
          ? localData.updateNames.find(
              (item) => item.update_id === nextUpdateId
            )
          : null;

        setLocalData((prev) => ({
          ...prev,
          currentUpdate: Number(update_id),
          index: currentIndex,
          previousUpdate,
          nextUpdate,
        }));
      }
    }
  }, [localData.newUpdates, localData.updateNames, update?.id]);

  // Add this at the start of the component
  useEffect(() => {
    // Set initial active section
    setActiveSection({ id: "overview", index: 0 });
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "overview",
        sectionIndex: 0,
        hasDuplicates: false,
      },
    });
  }, []); // Run once on mount

  // Modify the handleScroll function
  const handleScroll = useCallback(
    (e) => {
      const container = e.target;
      const sections = container.querySelectorAll("[id]");
      const scrollPosition = container.scrollTop;
      const containerHeight = container.clientHeight;
      const scrollHeight = container.scrollHeight;
      const scrollBottom = scrollPosition + containerHeight;
      const isAtBottom = scrollHeight - scrollBottom < 10; // Near bottom threshold
      const isAtTop = scrollPosition < 10; // Near top threshold

      console.log("--- Scroll Event Debug ---");
      console.log("Scroll Position:", scrollPosition);
      console.log("Container Height:", containerHeight);
      console.log("Scroll Bottom:", scrollBottom);
      console.log("Is at bottom:", isAtBottom);
      console.log("Is at top:", isAtTop);

      // Handle special cases first
      if (isAtTop) {
        // At the very top, Overview is active
        const overviewSection = Array.from(sections).find(
          (section) => section.id === "overview"
        );
        if (overviewSection) {
          updateActiveSection(overviewSection);
          return;
        }
      }

      if (isAtBottom) {
        // At the very bottom, Summary is active
        const summarySection = Array.from(sections).find(
          (section) => section.id === "summary"
        );
        if (summarySection) {
          updateActiveSection(summarySection);
          return;
        }
      }

      // For all other cases, find the first section that's in view
      let activeSection = null;

      for (const section of sections) {
        const rect = section.getBoundingClientRect();
        const sectionTop = section.offsetTop - container.offsetTop;
        const isInView = sectionTop <= scrollPosition + 50; // 50px threshold

        if (isInView) {
          activeSection = section;
        }

        console.log("Section Debug:", {
          id: section.id,
          noteId: section.getAttribute("data-note-id"),
          top: sectionTop,
          isInView,
        });
      }

      if (activeSection) {
        updateActiveSection(activeSection);
      }
    },
    [activeSection, globalDispatch, updateData]
  );

  // Helper function to update the active section
  const updateActiveSection = useCallback(
    (section) => {
      const currentNoteId = section.getAttribute("data-note-id");
      const currentSectionId = section.id;

      console.log("Most Visible Section:", {
        id: currentSectionId,
        noteId: currentNoteId,
      });

      setActiveSection(currentSectionId);
      setGlobalState("noteId", currentNoteId);

      globalDispatch({
        type: "SETPATH",
        payload: {
          path: currentSectionId,
          noteId: currentNoteId,
        },
      });
    },
    [globalDispatch, setGlobalState]
  );

  // Modify the useEffect to attach listener to the container
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      console.log("Attaching scroll listener to container");
      container.addEventListener("scroll", handleScroll, { passive: true });
      // Initial check
      handleScroll({ target: container });
    }

    return () => {
      if (container) {
        container.removeEventListener("scroll", handleScroll);
      }
    };
  }, [handleScroll]);

  useEffect(() => {
    if (isPublic) return;

    const notesWithCounts = update?.notes?.map((note) => {
      const comments =
        update?.update_comments?.filter((c) => c.note_id === note.id) || [];
      const replies =
        update?.update_comment_replies?.filter((r) => r.note_id === note.id) ||
        [];
      const reactions =
        update?.update_reaction?.filter((r) => r.note_id === note.id) || [];

      return {
        ...note,
        counts: {
          comments: comments.length,
          replies: replies.length,
          reactions: reactions.length,
        },
      };
    });

    setGlobalState("updateSideNotes", notesWithCounts);
    setGlobalState("currentUpdate", update?.name);
    setGlobalState("updateQuestions", update?.update_questions);
  }, [update?.notes]);

  useEffect(() => {
    if (location?.hash && !loading) {
      // Only try to scroll when not loading
      // Split hash from parameters
      const [hashPart, paramsPart] = location.hash.split("?");
      const hash = hashPart.substring(1); // Remove the # symbol

      // Decode the hash part
      const decodedHash = decodeURIComponent(hash);

      // Get parameters if they exist
      const params = new URLSearchParams(paramsPart || "");
      const from = params.get("from");

      // Update path for sidebar highlighting
      const path = from === "engagement" ? "engagements" : decodedHash;
      globalDispatch({
        type: "SETPATH",
        payload: {
          path,
        },
      });

      // Handle engagement marking
      const engagement_id = params.get("engagement_id");
      const engagement_type = params.get("engagement_type");
      if (from === "engagement" && engagement_id && engagement_type) {
        markEngagementViewed(engagement_id, engagement_type);
      }

      // Add retry mechanism for scrolling
      const maxAttempts = 10;
      let attempts = 0;

      const tryScroll = () => {
        if (decodedHash.includes("comment:")) {
          const [_, commentId] = decodedHash.split(":");
          const targetElement = document.getElementById(commentId);
          if (targetElement) {
            targetElement.scrollIntoView({
              behavior: "smooth",
              block: "center",
            });
            return true;
          }
        } else {
          const targetElement = document.getElementById(decodedHash);
          if (targetElement) {
            targetElement.scrollIntoView({
              behavior: "smooth",
              block: "start",
            });
            return true;
          }
        }
        return false;
      };

      const scrollInterval = setInterval(() => {
        attempts++;
        if (tryScroll() || attempts >= maxAttempts) {
          clearInterval(scrollInterval);
        }
      }, 200); // Try every 200ms

      return () => clearInterval(scrollInterval); // Cleanup
    } else {
      // Default path when no hash
      globalDispatch({
        type: "SETPATH",
        payload: {
          path: "updates",
        },
      });
    }
  }, [location?.hash, loading]); // Add loading as dependency

  useEffect(() => {
    if (update?.companies?.id) {
      fetchCompanyDetails(public_link_id ? "public" : "private");
    }
  }, [update?.companies?.id]);

  // Handle engagement view marking
  const markEngagementViewed = async (engagement_id, engagement_type) => {
    try {
      const response = await custom(
        {
          endpoint: `/v3/api/custom/goodbadugly/member/mark-engagement-update-viewed`,
          method: "POST",
          payload: {
            update_id: Number(update_id),
          },
        },
        undefined,
        false,
        null
      );

      if (response?.error) {
        console.error("Error marking engagement as viewed:", response.message);
      } else {
        // Trigger notification refresh in TopHeaderMember
        setGlobalState(
          "updateSharedNotificationChange",
          !globalState?.updateSharedNotificationChange
        );
      }
    } catch (error) {
      console.error("Error marking engagement as viewed:", error);
    }
  };

  const fetchNewUpdates = async () => {
    const updateData = [
      { update_id: 1501, name: "Update Dec 4, 2024" },
      { update_id: 1511, name: "Update 1511" },
      { update_id: 1512, name: "Update Title" },
      { update_id: 1539, name: "Update Title" },
      { update_id: 1541, name: "Update Title" },
      { update_id: 1547, name: "Update Title 1547" },
      { update_id: 1549, name: "Update Title" },
      { update_id: 1555, name: "Update Title" },
      { update_id: 1557, name: "Update Title" },
      { update_id: 1569, name: "Update Title optimistic layer" },
      { update_id: 1571, name: "Update Title optimistic layer" },
      { update_id: 1572, name: "Update Title optimistic layer" },
      { update_id: 1590, name: "Update Title" },
      { update_id: 1601, name: "Update Title" },
      { update_id: 1602, name: "Update Title" },
      { update_id: 1607, name: "Update Title" },
    ];
    try {
      const result = await getMany("update_member", {
        filter: [
          `goodbadugly_update_member.user_id,eq,${localStorage.getItem(
            "user"
          )}`,
        ],

        join: ["goodbadugly_updates|update_id,id"],
        fields: ["goodbadugly_updates.name"],
        order: "goodbadugly_update_member.id,desc",
      });

      if (!result?.error) {
        const updateAndNames = result?.data
          ?.map((item) => ({
            update_id: item?.update_id,
            name: item?.name,
            date_received: item?.date_receieve,
            first_name: item?.first_name,
            last_name: item?.last_name,
            company_name: item?.company_name,
          }))
          .filter(Boolean);

        const updateIds = updateAndNames
          .map((update) => update.update_id)
          .filter((id, index, self) => self.indexOf(id) === index);

        setLocalData((prev) => ({
          ...prev,
          newUpdates: updateIds,
          updateNames: updateAndNames,
        }));
      }
    } catch (err) {
      console.log(err);
    }
  };

  let activeAudioRef = useRef(null);

  // Add dedicated function to stop all audio
  const stopAllAudio = async () => {
    return new Promise((resolve) => {
      if (activeAudioRef?.current) {
        try {
          // Try both stop and pause methods
          if (typeof activeAudioRef.current.stop === "function") {
            activeAudioRef.current.stop();
          }
          if (typeof activeAudioRef.current.pause === "function") {
            activeAudioRef.current.pause();
          }
          // Reset time to beginning and clear src
          activeAudioRef.current.currentTime = 0;
          activeAudioRef.current.src = "";

          // Remove event listeners
          activeAudioRef.current.onended = null;
          activeAudioRef.current.oncanplaythrough = null;
          activeAudioRef.current.onerror = null;

          // Clear the reference
          activeAudioRef.current = null;
        } catch (error) {
          console.error("Error stopping audio:", error);
        }
      }
      // Add a delay to ensure audio is fully stopped
      setTimeout(resolve, 200);
    });
  };

  const previousUpdate = async () => {
    if (localData?.index > 0) {
      // Stop any playing audio before proceeding
      await stopAllAudio();

      const index = localData?.index - 1;
      const currentUpdate = localData?.newUpdates?.[index];
      const previousUpdateId = localData?.newUpdates?.[index - 1];
      const previousUpdate = localData?.updateNames?.find(
        (item) => item?.update_id === previousUpdateId
      );
      const nextUpdateId = localData?.newUpdates?.[index + 1];
      const nextUpdate = localData?.updateNames?.find(
        (item) => item?.update_id === nextUpdateId
      );

      setLocalData((prev) => {
        return {
          ...prev,
          index,
          currentUpdate,
          previousUpdate,
          nextUpdate,
        };
      });
      navigate(`/member/update/private/view/${currentUpdate}`);
    }
  };

  const nextUpdate = async (autoplay = false) => {
    if (localData?.index < localData?.newUpdates?.length - 1) {
      // Stop any playing audio before proceeding
      await stopAllAudio();

      const index = localData?.index + 1;
      const currentUpdate = localData?.newUpdates?.[index];

      const previousUpdateId = localData?.newUpdates?.[index + 1];
      const previousUpdate = localData?.updateNames?.find(
        (item) => item?.update_id === previousUpdateId
      );
      const nextUpdateId = localData?.newUpdates?.[index - 1];
      const nextUpdate = localData?.updateNames?.find(
        (item) => item?.update_id === nextUpdateId
      );

      setLocalData((prev) => {
        return {
          ...prev,
          index,
          currentUpdate,
          previousUpdate,
          nextUpdate,
        };
      });

      // Add a small delay before starting new audio
      if (autoplay && nextUpdate) {
        await new Promise((resolve) => setTimeout(resolve, 300));
        const { first_name, last_name, company_name, name, date_received } =
          nextUpdate;
        console.log(nextUpdate, "nextUpdate", company_name);

        // Format the date in a more natural way for speech
        const formattedDate = moment(date_received).format(
          "MMMM Do, YYYY [at] h:mm A"
        );

        const message = `Your next update is from ${first_name} ${last_name} from ${
          company_name || `${FirstStep}'s Company`
        } titled ${name} sent to you on ${formattedDate}`;

        // Use a text-to-speech library to speak the message
        await speakMessage(message);
      }

      // Preserve current URL parameters
      const currentParams = new URLSearchParams(window.location.search);
      const listenOption = currentParams.get("listen_option");

      // Build new URL with preserved parameters
      const newParams = new URLSearchParams();
      if (autoplay) newParams.set("autoplay", "true");
      if (listenOption) newParams.set("listen_option", listenOption);

      const queryString = newParams.toString();
      const navigationPath = `/member/update/private/view/${currentUpdate}${
        queryString ? `?${queryString}` : ""
      }`;

      navigate(navigationPath);
    }
  };

  // Function to speak a message
  const speakMessage = async (message) => {
    try {
      // Stop any currently playing audio before starting new one
      await stopAllAudio();

      // Call AWS Polly endpoint
      const sdk = new MkdSDK();
      if (globalState?.isPublicView) {
        console.log("Skipping Polly synthesis - public view");
        return;
      }

      // Create a cleanup function for the audio
      let cleanup = () => {};

      try {
        const result = await sdk.callRawAPI(
          "/v3/api/custom/goodbadugly/integrations/polly/synthesize",
          {
            text: message,
          },
          "POST"
        );

        if (result && !result.error && result.data?.audioUrl) {
          await new Promise((resolve) => setTimeout(resolve, 100)); // Small delay before creating new audio

          // Create new audio element
          const audio = new Audio(result.data.audioUrl);

          // Set up cleanup function
          cleanup = () => {
            if (audio) {
              audio.pause();
              audio.currentTime = 0;
              audio.src = "";
              audio.onended = null;
              audio.oncanplaythrough = null;
              audio.onerror = null;
              audio.remove();
            }
          };

          // Create a new ref for this specific audio instance
          const messageAudioRef = { current: audio };
          activeAudioRef.current = audio;

          // Set up error handling
          audio.onerror = () => {
            cleanup();
            messageAudioRef.current = null;
            activeAudioRef.current = null;
          };

          // Wait for audio to be loaded
          await new Promise((resolve, reject) => {
            audio.oncanplaythrough = resolve;
            audio.onerror = reject;
            audio.load();
          });

          // Play the audio
          await audio.play();

          // Clear the ref when done
          audio.onended = () => {
            cleanup();
            messageAudioRef.current = null;
            activeAudioRef.current = null;
          };
        } else {
          throw new Error(result?.error || "Failed to generate audio");
        }
      } catch (error) {
        cleanup();
        throw error;
      }
    } catch (error) {
      console.error("Failed to speak message:", error);
    }
  };

  useEffect(() => {
    if (authState.user) {
      fetchNewUpdates();
    }
  }, [authState.user]);

  if (!isPublic && !state?.isAuthenticated && !loading) {
    // https://goodbadugly.manaknightdigital.com
    const origin = window.location.origin;
    const baseUrl = `${origin}/member/login`;
    const redirectUri = encodeURIComponent(
      `/member/updates?availability=available`
    );

    // Construct the full URL with the redirect_uri parameter
    const fullUrl = `${baseUrl}?redirect_uri=${redirectUri}`;

    window.location.href = fullUrl;
    return (
      <div className="flex h-screen -scroll-mt-6 items-center justify-center overflow-y-auto scroll-smooth bg-brown-main-bg">
        <h2>Redirecting to Login ....</h2>
      </div>
    );
  }

  if (error)
    return (
      <div className="min-full max-full flex h-full w-full items-center justify-center text-7xl text-gray-700">
        {error}
      </div>
    );

  return (
    <div
      ref={scrollContainerRef}
      className={`min-full max-full relative h-full w-full -scroll-mt-6 space-y-[2rem] scroll-smooth bg-brown-main-bg px-4 py-[1.0375rem] pb-[270px] md:px-[3.125rem] md:pb-[1.9375rem] md:pt-[2.252rem] lg:pb-[100px] ${
        loading ? "overflow-y-hidden" : "overflow-y-auto"
      }`}
    >
      {loading ? <OverlayLoader loading /> : null}

      {isPublic ? (
        <LazyLoad>
          <ReturnToUpdates />
        </LazyLoad>
      ) : null}

      {update && companyDetails && (
        <div className="mx-auto w-full">
          <LazyLoad>
            <CompanyDetails companyDetails={companyDetails} update={update} />
          </LazyLoad>

          <hr className="my-6 hidden border-[1px] border-[#1f1d1a] md:block" />

          <div className="flex flex-col items-start justify-between gap-5 border-t-2 border-black pt-5 md:flex-row md:items-center md:border-t-0 md:pt-0">
            <div className="block w-full md:hidden">
              {!isPublic && (
                <LazyLoad>
                  <UpdateChange
                    update={update}
                    localData={localData}
                    previousUpdate={previousUpdate}
                    nextUpdate={nextUpdate}
                  />
                </LazyLoad>
              )}
            </div>
            <LazyLoad>
              <UpdateName update={update} />
            </LazyLoad>

            <div className="hidden w-full md:block md:w-auto">
              {!isPublic && (
                <LazyLoad>
                  <UpdateChange
                    update={update}
                    localData={localData}
                    previousUpdate={previousUpdate}
                    nextUpdate={nextUpdate}
                  />
                </LazyLoad>
              )}
            </div>
          </div>

          <div className="mt-[1.5rem] flex w-full  flex-row items-start gap-5 md:mt-8 md:items-center md:justify-between">
            <LazyLoad>
              <UpdateContributors update={update} />
            </LazyLoad>

            <div className="flex w-full flex-col items-center gap-[1.5625rem]  md:w-[75%] md:flex-row md:justify-between">
              <LazyLoad>
                <UpdateTextToSpeech
                  data={update}
                  showControls={showControls}
                  setShowControls={setShowControls}
                  summary={summary}
                  nextUpdate={nextUpdate}
                  localData={localData}
                  activeAudioRef={activeAudioRef}
                  onBeforePlay={() => {
                    // Stop any message audio that might be playing
                    if (activeAudioRef?.current) {
                      if (typeof activeAudioRef.current.stop === "function") {
                        activeAudioRef.current.stop();
                      }
                      if (typeof activeAudioRef.current.pause === "function") {
                        activeAudioRef.current.pause();
                      }
                      activeAudioRef.current = null;
                    }
                  }}
                />
              </LazyLoad>

              <LazyLoad>
                <UpdateAvailability update={update} />
              </LazyLoad>
            </div>
          </div>
          <hr className="mt-5 border-[.0625rem] border-black/40 md:block" />
          <section className="mb-10 mt-[0.5rem] flex flex-col items-center gap-[2.5rem] md:mb-0 md:mt-[0] md:flex-row md:items-start md:gap-4">
            {isPublic ? (
              <LazyLoad>
                <UpdateSectionList update={update} />
              </LazyLoad>
            ) : null}

            <div
              className={`min-h-[40rem] max-w-[100%] flex-grow border-0 ${
                isPublic
                  ? "border-l-[#1f1d1a] pl-[3.25rem] md:border-l-[.125rem]"
                  : ""
              }`}
            >
              {/*  */}

              <LazyLoad>
                <UpdateMetrics update={update} />
              </LazyLoad>
              <div className="flex flex-col gap-5">
                <LazyLoad>
                  <UpdateSections
                    update={update}
                    showControls={showControls}
                    refetch={refetch}
                    updateData={updateData.map((note) => ({
                      ...note,
                      "data-note-id": note.id, // Add data-note-id to each note
                    }))}
                    questionAnswerState={questionAnswer}
                    setQuestionAnswerState={setQuestionAnswer}
                  />
                </LazyLoad>

                {/* Summary section */}
                <div id="summary" className="mt-[16px] bg-[#F2DFCE] p-3 lg:p-5">
                  <span className="text-[18px] font-bold">Summary</span>
                  <p>
                    <p
                      className="font-regular mt-2 font-iowan text-[16px] font-medium leading-7"
                      dangerouslySetInnerHTML={{
                        __html: summary?.content,
                      }}
                    ></p>
                  </p>
                </div>
              </div>
            </div>
          </section>
        </div>
      )}
    </div>
  );
};

export default UpdatePreview;
