import { createRequest, GlobalContext, showToast } from "Context/Global";
import { InteractiveButton } from "Components/InteractiveButton";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { MkdCustomInput } from "Components/MkdCustomInput";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";
import { useContext } from "react";
import * as yup from "yup";

export default function CreateGroup({
  onSuccess,
  onClose,
  buttonText = "Next",
}) {
  const { dispatch: authDispatch, state: authState } = useContext(AuthContext);
  const {
    dispatch: globalDispatch,
    state: { createModel },
  } = useContext(GlobalContext);

  const creating = createModel?.loading || false;

  const schema = yup.object({
    group_name: yup.string().required("This field is required"),
    // group_type: yup.string().required("This field is required"),
  });

  const {
    register,
    handleSubmit,
    setError,
    setValue,
    watch,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  async function createGroup(_data) {
    try {
      const result = await createRequest(
        globalDispatch,
        authDispatch,
        `group`,
        {
          user_id: authState.user,
          group_name: _data?.group_name,
          role: "member",
        }
      );
      if (onSuccess) {
        onSuccess(result.data);
      }
    } catch (err) {
      tokenExpireError(authDispatch, err.message);
      if (err.message !== "TOKEN_EXPIRED") {
        showToast(globalDispatch, err.message, 5000, "error");
      }
    }
  }

  return (
    <div className="space-y-[1.375rem]">
      <p className="mt-2 font-inter text-lg">
        Use groups to organize your update recipients
      </p>

      <div>
        <label className="mb-2 block  text-[1rem] font-semibold capitalize leading-[1.5rem] text-[#1f1d1a]">
          Group name
        </label>
        <MkdCustomInput
          type={"text"}
          errors={errors}
          name="group_name"
          placeholder="New group name"
          onChange={(e) => {
            setValue("group_name", e.target.value);
          }}
        />
      </div>

      <div className="mt-6 flex justify-end gap-4">
        <button
          className="border border-[#0003] px-3 py-2 text-center font-iowan font-medium "
          type="button"
          onClick={() => onClose()}
        >
          Cancel
        </button>
        <InteractiveButton
          loading={creating}
          disabled={creating}
          onClick={handleSubmit(createGroup, (error) =>
            console.log("error >>", error)
          )}
          className="bg-primary-black px-3 py-2 text-center font-semibold text-white transition-colors duration-100 disabled:bg-disabled-black"
        >
          {buttonText}
        </InteractiveButton>
      </div>
    </div>
  );
}
