import { Modal } from "Components/Modal";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { createRequest, GlobalContext, showToast } from "Context/Global";
import { Fragment, useContext, useState } from "react";
import CreateGroup from "./CreateGroup";
import CreateRecipientGroup from "./CreateRecipientGroup";
import { Loader } from "Components/Loader";

const Steps = {
  1: "Create New Group",
  2: "Add Members To New Group",
};

export default function CreateGroupModal({
  afterCreate,
  buttonRef,
  type = "update",
}) {
  const { dispatch: authDispatch, state: authState } = useContext(AuthContext);
  const {
    dispatch: globalDispatch,
    state: { update, createModel },
  } = useContext(GlobalContext);
  const [groupId, setGroupId] = useState(0);
  const [open, setOpen] = useState(false);
  const [step, setStep] = useState(1);

  // Debug logging
  console.log("CreateGroupModal render - open state:", open);

  const handleOpenModal = () => {
    console.log(
      "CreateGroupModal: handleOpenModal called, setting open to true"
    );
    setOpen(true);
  };

  const handleCloseModal = () => {
    console.log(
      "CreateGroupModal: handleCloseModal called, setting open to false"
    );
    setOpen(false);
  };

  return (
    <Modal
      isOpen={open}
      modalCloseClick={handleCloseModal}
      modalHeader
      title={Steps[step]}
      classes={{
        modal: "w-full",
        modalDialog: "!w-[100%] md:!w-[40%]",
        modalContent: "",
      }}
    >
      <div className="relative">
        <button
          type="button"
          hidden
          ref={buttonRef}
          onClick={handleOpenModal}
        ></button>

        {step == 1 ? (
          <CreateGroup
            buttonText={type === "update" ? "Next" : "Add New Group"}
            onSuccess={(groupId) => {
              console.log(
                "CreateGroupModal: CreateGroup onSuccess, groupId:",
                groupId
              );
              setGroupId(groupId);
              if (type === "update") {
                setStep(2);
              } else {
                setOpen(false);
                setStep(1); // Reset step for next time
                if (afterCreate) {
                  afterCreate(groupId); // Pass the groupId to afterCreate
                }
              }
            }}
            onClose={() => {
              console.log("CreateGroupModal: CreateGroup onClose");
              setOpen(false);
              setStep(1); // Reset step when closing
            }}
          />
        ) : null}

        {step == 2 ? (
          <CreateRecipientGroup
            groupId={groupId}
            onSuccess={(step) => {
              console.log("CreateGroupModal: CreateRecipientGroup onSuccess");
              setOpen(false);
              setStep(1);
              afterCreate();
            }}
            onClose={() => {
              console.log("CreateGroupModal: CreateRecipientGroup onClose");
              setOpen(false);
              setStep(1);
            }}
          />
        ) : null}
      </div>
    </Modal>
  );
}
