import { Fragment, useContext, useEffect, useState } from "react";
import { Combobox, Transition } from "@headlessui/react";
import { ChevronUpDownIcon } from "@heroicons/react/20/solid";
import { useController } from "react-hook-form";
import { getList, GlobalContext, showToast } from "Context/Global";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { XMarkIcon } from "@heroicons/react/24/solid";
import { COMPANY_MEMBER_STATUSES } from "Utils/utils";
import AddUserModal from "Pages/Member/Add/AddUserModal";

export default function SelectGroupMembers({
  control,
  name,
  setValue,
  watch,
  defaultSelectedMembers = [],
}) {
  const [recipients, setRecipients] = useState([]);
  const [query, setQuery] = useState("");

  const [selected, setSelected] = useState([]);
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const { dispatch: authDispatch, state: authState } = useContext(AuthContext);

  const [isModalOpen, setIsModalOpen] = useState(false);

  // console.log(recipients, "recipients");

  const membersSelected =
    defaultSelectedMembers.length > 0 ? defaultSelectedMembers : selected;

  // console.log(recipients);

  async function fetchRecipients() {
    try {
      const result = await getList(
        globalDispatch,
        authDispatch,
        "company_member",
        {
          filter: [`company_id,eq,${authState.company.id}`],
          join: ["user|member_id"],
        }
      );
      // await sdk.callRawAPI(
      //   `/v4/api/records/startup_member?join=user|member_id&filter=company_id,eq,${authState.company.id}&filter=member_status,eq,${COMPANY_MEMBER_STATUSES.ACTIVE}`,
      //   undefined,
      //   "GET"
      // );

      if (!result?.error) {
        setRecipients(() => result?.data);
      }
    } catch (err) {
      tokenExpireError(authDispatch, err.message);
      if (err.message !== "TOKEN_EXPIRED") {
        showToast(globalDispatch, err.message, 5000, "error");
      }
    }
  }

  useEffect(() => {
    authState.company.id && fetchRecipients();
  }, [authState.company.id]);

  const { field, formState, fieldState } = useController({
    control,
    name,
  });

  useEffect(() => {
    console.log("SelectGroupMembers - field.value changed:", field.value);
    if (field.value) {
      setSelected(field.value);
    } else {
      setSelected([]);
    }
  }, [field.value]); // Watch field.value directly instead of formState

  console.log("recipients >>", recipients);
  console.log("membersSelected >>", membersSelected);

  const filteredPeople =
    query === ""
      ? recipients?.filter((u) => !selected?.includes(u.id.toString()))
      : recipients
          ?.filter((u) => !selected?.includes(u.id))
          .filter(
            (person) =>
              person?.user?.email
                ?.toLowerCase()
                ?.replace(/\s+/g, "")
                ?.includes(query?.toLowerCase()?.replace(/\s+/g, "")) ||
              person?.user?.first_name
                ?.toLowerCase()
                ?.replace(/\s+/g, "")
                ?.includes(query?.toLowerCase()?.replace(/\s+/g, "")) ||
              person?.user?.last_name
                ?.toLowerCase()
                ?.replace(/\s+/g, "")
                ?.includes(query?.toLowerCase()?.replace(/\s+/g, ""))
          );
  // console.log(recipients.filter((u) => !selected.includes(u.id.toString())));
  // console.log(filteredPeople, recipients, selected);

  return (
    <>
      <Combobox
        value={field.value}
        onChange={(user_id) =>
          setValue([...(field.value ? field.value : []), user_id])
        }
      >
        <div className="relative z-30 mt-3 w-full">
          <label className="mb-1 block  font-iowan text-[16px] font-semibold capitalize text-[#1f1d1a]">
            Members
          </label>
          <div className="w-full">
            <Combobox.Button className="relative w-full cursor-default rounded-md text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 sm:text-sm">
              <Combobox.Input
                className={`focus:shadow-outline h-[2.6rem] w-full appearance-none rounded-[2px] border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal leading-tight text-[#1f1d1a]   placeholder:text-[14px] placeholder:text-[#1f1d1a] focus:outline-none md:h-[44px] ${
                  fieldState.error?.message ? "border-red-500" : ""
                }`}
                placeholder="Type to search"
                onChange={(event) => setQuery(event.target.value)}
                onBlur={field.onBlur}
                ref={field.ref}
                name={field.name}
                autoComplete="off"
              />

              <div className="absolute inset-y-0 right-0 flex items-center pr-2">
                <ChevronUpDownIcon
                  className="h-5 w-5 text-[#1f1d1a]"
                  aria-hidden="true"
                />
              </div>
            </Combobox.Button>
            <p className="text-field-error italic text-red-500">
              {fieldState.error?.message}
            </p>
          </div>

          <Transition
            as={Fragment}
            leave="transition ease-in duration-100"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
            afterLeave={() => setQuery("")}
          >
            <Combobox.Options className="custom-overflow absolute mt-1 max-h-96 w-full overflow-auto rounded-md border bg-brown-main-bg py-1 text-base shadow-lg ring-1 ring-[#1f1d1a]/5 focus:outline-none">
              {filteredPeople?.length === 0 && query !== "" ? (
                <div className="relative cursor-default select-none px-4 py-2 text-gray-700">
                  Nothing found.
                </div>
              ) : (
                <>
                  {filteredPeople?.map((person) => (
                    <Combobox.Option
                      key={person.id}
                      className={({ active }) =>
                        `relative cursor-default select-none py-2 pl-10 pr-4 ${
                          active
                            ? "bg-primary-black text-white"
                            : "text-gray-900"
                        }`
                      }
                      value={person?.user?.id}
                    >
                      {({ selected, active }) => (
                        <>
                          <span
                            className={`block truncate ${
                              selected ? "font-medium" : "font-medium"
                            }`}
                          >
                            {person?.user?.email}
                          </span>
                        </>
                      )}
                    </Combobox.Option>
                  ))}
                  <li
                    value="add_new"
                    className={({ active }) =>
                      `relative cursor-pointer select-none py-2 pl-10 pr-4 ${
                        active ? "bg-primary-black text-white" : "text-gray-900"
                      }`
                    }
                    onClick={() => setIsModalOpen(true)}
                  >
                    {({ active }) => (
                      <span
                        className={`block truncate font-medium underline ${
                          active ? "text-white" : "text-primary-black"
                        }`}
                      >
                        + Add New User
                      </span>
                    )}
                  </li>
                </>
              )}
            </Combobox.Options>
          </Transition>
        </div>
        <div className="custom-overflow mt-2 flex w-full flex-wrap gap-4 overflow-y-auto text-sm">
          {/* {console.log(recipients)} */}
          {recipients &&
            recipients?.map((member, memberKey) => {
              if (membersSelected?.includes(member?.user?.id)) {
                return (
                  <div
                    key={memberKey}
                    className="flex items-center gap-3 rounded-sm border border-[#1f1d1a] bg-brown-main-bg px-3 py-2"
                  >
                    <span>{member?.user?.email}</span>
                    <button
                      type="button"
                      onClick={() => {
                        const copy = [...field.value];
                        const index = copy.findIndex(
                          (e) => e === member?.user?.id
                        );

                        if (index !== -1) {
                          copy.splice(index, 1); // Remove the item from the array
                        }
                        // console.log(copy);
                        setValue(copy);
                      }}
                    >
                      <XMarkIcon className="h-4" strokeWidth={2} />
                    </button>
                  </div>
                );
              }
            })}
        </div>
      </Combobox>

      <AddUserModal
        isOpen={isModalOpen}
        closeModal={() => setIsModalOpen(false)}
      />
    </>
  );
}
