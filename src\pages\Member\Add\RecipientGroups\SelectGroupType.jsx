import { Fragment, useContext, useEffect, useRef, useState } from "react";
import { Combobox, Transition } from "@headlessui/react";
import { ChevronUpDownIcon } from "@heroicons/react/20/solid";
import { useController, useWatch } from "react-hook-form";
import { GlobalContext, showToast } from "Context/Global";
import { AuthContext, tokenExpireError } from "Context/Auth";
import MkdSDK from "Utils/MkdSDK";
import CreateGroupModal from "./CreateGroupModal";

export default function SelectGroupType({
  control,
  name,
  setValue,
  allowedRoles,
  label = "Group name",
  setGroupName = null,
  errors,
  onReady,
  isEditMode = false, // New prop to distinguish between Add and Edit modes
  onGroupChange = null, // Callback for when group changes (for Edit mode)
}) {
  const createGroupModalRef = useRef(null);

  const [groups, setGroups] = useState([]);
  const [query, setQuery] = useState("");

  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const { dispatch: authDispatch, state: authState } = useContext(AuthContext);
  const [createGroupModal, setCreateGroupModal] = useState(false);
  const [fetching, setFetching] = useState(true);

  const fieldValue = useWatch({ control, name });

  // console.log("fieldValue >>", fieldValue);

  const filteredGroups =
    query === ""
      ? groups
      : groups.filter((g) =>
          g.group_name
            .toLowerCase()
            .replace(/\s+/g, "")
            .includes(query.toLowerCase().replace(/\s+/g, ""))
        );

  async function fetchGroups() {
    setFetching(true);
    try {
      const sdk = new MkdSDK();

      // Fetch both APIs to get complete picture
      const [groupsRes, recipientGroupsRes] = await Promise.all([
        // Main groups API - contains ALL groups
        sdk.callRawAPI(
          `/v4/api/records/group?filter=user_id,in,'NULL',${authState.user}`
        ),
        // Recipient groups API - contains groups WITH members
        sdk.callRawAPI(
          `/v3/api/custom/goodbadugly/member/get-recipient-groups?user_id=${authState.user}&limit=1000`,
          undefined,
          "GET"
        ),
      ]);

      const mainGroups = groupsRes.list || [];
      const recipientGroups = recipientGroupsRes.list || [];

      // Create a map of group names to recipient group data
      const recipientGroupMap = {};
      recipientGroups.forEach((rg) => {
        recipientGroupMap[rg.group_name.trim()] = rg;
      });

      // Merge: Use main groups as base, add member info from recipient groups
      const mergedGroups = mainGroups.map((group) => {
        const recipientGroup = recipientGroupMap[group.group_name.trim()];
        return {
          id: group.id, // Always use main group.id for form consistency
          group_name: group.group_name,
          members: recipientGroup ? recipientGroup.members : [], // Add members if available
          hasMembers: !!recipientGroup, // Flag to indicate if group has members
          recipient_group_id: recipientGroup ? recipientGroup.id : null, // Keep recipient group ID for reference
        };
      });

      setGroups(mergedGroups);
    } catch (err) {
      tokenExpireError(authDispatch, err.message);
      if (err.message !== "TOKEN_EXPIRED") {
        showToast(globalDispatch, err.message, 5000, "error");
      }
    }
    setFetching(false);
  }

  useEffect(() => {
    fetchGroups();
  }, []);

  useEffect(() => {
    if (onReady) onReady();
  }, [groups?.length]);

  useEffect(() => {
    setGroupName &&
      setGroupName(groups.find((g) => g.id == fieldValue)?.group_name ?? "");
  }, [fieldValue]);

  // <Combobox value={fieldValue} onChange={setValue}>
  return (
    <>
      <Combobox
        value={query || fieldValue}
        onChange={(value) => {
          setValue(value);
          // Call the onGroupChange callback if provided
          if (onGroupChange) {
            onGroupChange(value);
          }
        }}
      >
        <div className="relative z-50 w-full">
          <div className="flex items-center justify-between">
            <label className="mb-1 block font-iowan  text-[16px] font-semibold capitalize text-[#1f1d1a]">
              {label}
            </label>
            <div className="">
              <button
                className="font-medium"
                type="button"
                onClick={() => {
                  createGroupModalRef?.current?.click();
                }}
              >
                + New Group
              </button>
            </div>
          </div>
          <div className="w-full">
            <Combobox.Button className="relative w-full cursor-default rounded-[2px] text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 ">
              <div className="w-full">
                <Combobox.Input
                  className={`focus:shadow-outline  h-[2.6rem] w-full appearance-none rounded-[2px] border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal leading-tight text-[#1f1d1a]    placeholder:text-[14px] placeholder:text-[#1f1d1a] focus:outline-none md:h-[44px] ${
                    errors?.[name]?.message ? "border-red-500" : ""
                  }`}
                  defaultValue={fieldValue}
                  placeholder="Type to search"
                  displayValue={(group_id) => {
                    // console.log("group_id >>", group_id);
                    const value = groups.find((g) => g.id == group_id);
                    // console.log("?.group_name ??  >>", value);
                    return value?.group_name ?? "";
                  }}
                  onChange={(event) => {
                    setQuery(event.target.value);
                    // setValue(event.target.value);
                    // console.log("osdododoodo");

                    if (event.target.value === "") {
                      // setValue("");
                      setQuery("");
                    }
                  }}
                  onBlur={() => setQuery("")}
                  // ref={field.ref}
                  name={name}
                  autoComplete="off"
                />
              </div>

              <div className="absolute inset-y-0 right-3 flex items-center">
                <ChevronUpDownIcon
                  className="h-5 w-5 text-[#1f1d1a]"
                  aria-hidden="true"
                />
              </div>
            </Combobox.Button>
            <p className="text-field-error italic text-red-500">
              {errors?.[name]?.message}
            </p>
          </div>
          <Transition
            as={Fragment}
            leave="transition ease-in duration-100"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <Combobox.Options className="custom-overflow absolute z-30 mt-1 max-h-[18.75rem]  w-full overflow-y-auto rounded-md border bg-brown-main-bg py-1 pt-6 text-base shadow-lg ring-1 ring-[#1f1d1a]/5 focus:outline-none">
              {fetching || (filteredGroups.length === 0 && query !== "") ? (
                <div className="relative cursor-default select-none bg-brown-main-bg px-2 py-2 pl-10 text-gray-700">
                  Nothing found.
                </div>
              ) : (
                filteredGroups.map((group) => (
                  <Combobox.Option
                    key={group.id}
                    className={({ active }) =>
                      `relative cursor-default select-none py-2  pl-10 pr-4 ${
                        active ? "bg-primary-black text-white" : "text-gray-900"
                      }`
                    }
                    value={group.id}
                  >
                    {({ selected, active }) => (
                      <>
                        <span
                          className={`block truncate ${
                            selected ? "font-medium" : "font-medium"
                          }`}
                        >
                          {group.group_name}
                        </span>
                      </>
                    )}
                  </Combobox.Option>
                ))
              )}
            </Combobox.Options>
          </Transition>
        </div>
      </Combobox>
      <CreateGroupModal
        // isOpen={createGroupModal}
        // closeModal={() => setCreateGroupModal(false)}
        type="base"
        buttonRef={createGroupModalRef}
        afterCreate={(newId) => {
          fetchGroups();
          setValue(newId);
        }}
      />
    </>
  );
}
