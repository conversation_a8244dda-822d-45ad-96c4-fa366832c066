import { Fragment, useContext, useEffect, useRef, useState } from "react";
import { Combobox, Transition } from "@headlessui/react";
import { ChevronUpDownIcon } from "@heroicons/react/20/solid";
import {
  PencilIcon,
  CheckIcon,
  XMarkIcon,
  TrashIcon,
} from "@heroicons/react/24/outline";
import { useController, useWatch } from "react-hook-form";
import { GlobalContext, showToast } from "Context/Global";
import { AuthContext, tokenExpireError } from "Context/Auth";
import MkdSDK from "Utils/MkdSDK";
import TreeSDK from "Utils/TreeSDK";
import CreateGroupModal from "./CreateGroupModal";

export default function SelectGroupType({
  control,
  name,
  setValue,
  allowedRoles,
  label = "Group name",
  setGroupName = null,
  errors,
  onReady,
  isEditMode = false, // New prop to distinguish between Add and Edit modes
  onGroupChange = null, // Callback for when group changes (for Edit mode)
}) {
  const createGroupModalRef = useRef(null);

  const [groups, setGroups] = useState([]);
  const [query, setQuery] = useState("");

  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const { dispatch: authDispatch, state: authState } = useContext(AuthContext);
  const [createGroupModal, setCreateGroupModal] = useState(false);
  const [fetching, setFetching] = useState(true);
  const [editingGroupId, setEditingGroupId] = useState(null);
  const [editingGroupName, setEditingGroupName] = useState("");
  const [isUpdating, setIsUpdating] = useState(false);

  const fieldValue = useWatch({ control, name });

  // console.log("fieldValue >>", fieldValue);

  const filteredGroups =
    query === ""
      ? groups
      : groups.filter((g) =>
          g.group_name
            .toLowerCase()
            .replace(/\s+/g, "")
            .includes(query.toLowerCase().replace(/\s+/g, ""))
        );

  // Function to update group name
  async function updateGroupName(groupId, newName) {
    if (!newName.trim()) {
      showToast(globalDispatch, "Group name cannot be empty", 5000, "error");
      return false;
    }

    setIsUpdating(true);
    try {
      const sdk = new MkdSDK();
      await sdk.callRawAPI(
        `/v4/api/records/group/${groupId}`,
        { group_name: newName.trim() },
        "PUT"
      );

      // Update the local groups state
      setGroups((prevGroups) =>
        prevGroups.map((group) =>
          group.id === groupId
            ? { ...group, group_name: newName.trim() }
            : group
        )
      );

      showToast(
        globalDispatch,
        "Group name updated successfully",
        5000,
        "success"
      );
      return true;
    } catch (err) {
      tokenExpireError(authDispatch, err.message);
      if (err.message !== "TOKEN_EXPIRED") {
        showToast(globalDispatch, err.message, 5000, "error");
      }
      return false;
    } finally {
      setIsUpdating(false);
    }
  }

  // Handle edit mode
  function startEditing(group, event) {
    event.stopPropagation(); // Prevent dropdown from closing
    setEditingGroupId(group.id);
    setEditingGroupName(group.group_name);
  }

  // Handle save edit
  async function saveEdit(event) {
    event.stopPropagation();
    const success = await updateGroupName(editingGroupId, editingGroupName);
    if (success) {
      setEditingGroupId(null);
      setEditingGroupName("");
    }
  }

  // Handle cancel edit
  function cancelEdit(event) {
    event.stopPropagation();
    setEditingGroupId(null);
    setEditingGroupName("");
  }

  async function fetchGroups() {
    setFetching(true);
    try {
      const sdk = new MkdSDK();

      // Use the same approach as MemberListRecipientGroupTablePage
      // Get recipient groups with joined user and group data using TreeSDK
      const [allGroupsRes, recipientGroupsRes] = await Promise.all([
        // All groups API - for groups that might not have recipient groups yet
        sdk.callRawAPI(
          `/v4/api/records/group?filter=user_id,in,'NULL',${authState.user}`
        ),
        // Use TreeSDK directly like the list page does
        (async () => {
          const tdk = new TreeSDK();
          return await tdk.getPaginate("recipient_group", {
            join: ["user", "group"],
            filter: [
              `goodbadugly_recipient_group.user_id,eq,${authState.user}`,
            ],
            size: 1000,
            page: 1,
            order: "id",
            direction: "desc",
          });
        })(),
      ]);

      const allGroups = allGroupsRes.list || [];
      const recipientGroups = recipientGroupsRes.list || [];

      console.log(
        "All Groups:",
        allGroups.map((g) => ({ id: g.id, name: g.group_name }))
      );
      console.log(
        "Recipient Groups (TreeSDK):",
        recipientGroups.map((rg) => ({
          id: rg.id,
          group_id: rg.group_id,
          group_name: rg.group?.group_name,
          members: rg.members,
        }))
      );

      // Create a map of group_id to recipient group data
      const recipientGroupMap = {};
      recipientGroups.forEach((rg) => {
        if (rg.group && rg.group.id) {
          const memberIds = rg.members
            ? rg.members.split(",").map((id) => id.trim())
            : [];
          recipientGroupMap[rg.group.id] = {
            recipient_group_id: rg.id,
            members: memberIds, // Convert comma-separated string to array and trim
            group_name: rg.group.group_name,
          };
          console.log(
            `Recipient group mapping: ${rg.group.group_name} (ID: ${
              rg.group.id
            }) -> members: [${memberIds.join(", ")}]`
          );
        }
      });

      // Start with all groups and enhance with recipient group data
      const mergedGroups = allGroups.map((group) => {
        const recipientData = recipientGroupMap[group.id];
        return {
          id: group.id, // Use group.id for form consistency
          group_name: group.group_name,
          members: recipientData ? recipientData.members : [], // Member IDs as array
          hasMembers: !!recipientData,
          recipient_group_id: recipientData
            ? recipientData.recipient_group_id
            : null,
        };
      });

      // Add recipient groups that don't exist in allGroups (shouldn't happen but just in case)
      const allGroupIds = new Set(allGroups.map((g) => g.id));
      recipientGroups.forEach((rg) => {
        if (rg.group && !allGroupIds.has(rg.group.id)) {
          const memberIds = rg.members
            ? rg.members.split(",").map((id) => id.trim())
            : [];
          mergedGroups.push({
            id: rg.group.id,
            group_name: rg.group.group_name,
            members: memberIds,
            hasMembers: true,
            recipient_group_id: rg.id,
            isFromRecipientAPI: true,
          });
          console.log(
            `Added missing group from recipient API: ${rg.group.group_name} (ID: ${rg.group.id})`
          );
        }
      });

      console.log(
        "Final Merged Groups:",
        mergedGroups.map((g) => ({
          id: g.id,
          name: g.group_name,
          hasMembers: g.hasMembers,
          memberCount: g.members?.length || 0,
          members: g.members,
        }))
      );

      setGroups(mergedGroups);
    } catch (err) {
      tokenExpireError(authDispatch, err.message);
      if (err.message !== "TOKEN_EXPIRED") {
        showToast(globalDispatch, err.message, 5000, "error");
      }
    }
    setFetching(false);
  }

  useEffect(() => {
    fetchGroups();
  }, []);

  useEffect(() => {
    if (onReady) onReady();
  }, [groups?.length]);

  useEffect(() => {
    setGroupName &&
      setGroupName(groups.find((g) => g.id == fieldValue)?.group_name ?? "");
  }, [fieldValue]);

  // <Combobox value={fieldValue} onChange={setValue}>
  return (
    <>
      <Combobox
        value={query || fieldValue}
        onChange={(value) => {
          console.log("SelectGroupType onChange called with value:", value);
          setValue(value);

          // Find the selected group and its members
          const selectedGroup = groups.find((g) => g.id == value);
          console.log("Selected group data:", selectedGroup);

          // Call the onGroupChange callback if provided, pass both group ID and member data
          if (onGroupChange) {
            console.log(
              "Calling onGroupChange with value:",
              value,
              "and members:",
              selectedGroup?.members
            );
            onGroupChange(value, selectedGroup?.members || []);
          } else {
            console.log("No onGroupChange callback provided");
          }
        }}
      >
        <div className="relative z-50 w-full">
          <div className="flex items-center justify-between">
            <label className="mb-1 block font-iowan  text-[16px] font-semibold capitalize text-[#1f1d1a]">
              {label}
            </label>
            <div className="">
              <button
                className="font-medium"
                type="button"
                onClick={() => {
                  createGroupModalRef?.current?.click();
                }}
              >
                + New Group
              </button>
            </div>
          </div>
          <div className="w-full">
            <Combobox.Button className="relative w-full cursor-default rounded-[2px] text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 ">
              <div className="w-full">
                <Combobox.Input
                  className={`focus:shadow-outline  h-[2.6rem] w-full appearance-none rounded-[2px] border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal leading-tight text-[#1f1d1a]    placeholder:text-[14px] placeholder:text-[#1f1d1a] focus:outline-none md:h-[44px] ${
                    errors?.[name]?.message ? "border-red-500" : ""
                  }`}
                  defaultValue={fieldValue}
                  placeholder="Type to search"
                  displayValue={(group_id) => {
                    // console.log("group_id >>", group_id);
                    const value = groups.find((g) => g.id == group_id);
                    // console.log("?.group_name ??  >>", value);
                    return value?.group_name ?? "";
                  }}
                  onChange={(event) => {
                    setQuery(event.target.value);
                    // setValue(event.target.value);
                    // console.log("osdododoodo");

                    if (event.target.value === "") {
                      // setValue("");
                      setQuery("");
                    }
                  }}
                  onBlur={() => setQuery("")}
                  // ref={field.ref}
                  name={name}
                  autoComplete="off"
                />
              </div>

              <div className="absolute inset-y-0 right-3 flex items-center">
                <ChevronUpDownIcon
                  className="h-5 w-5 text-[#1f1d1a]"
                  aria-hidden="true"
                />
              </div>
            </Combobox.Button>
            <p className="text-field-error italic text-red-500">
              {errors?.[name]?.message}
            </p>
          </div>
          <Transition
            as={Fragment}
            leave="transition ease-in duration-100"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <Combobox.Options className="custom-overflow absolute z-30 mt-1 max-h-[18.75rem]  w-full overflow-y-auto rounded-md border bg-brown-main-bg py-1 pt-6 text-base shadow-lg ring-1 ring-[#1f1d1a]/5 focus:outline-none">
              {fetching || (filteredGroups.length === 0 && query !== "") ? (
                <div className="relative cursor-default select-none bg-brown-main-bg px-2 py-2 pl-10 text-gray-700">
                  Nothing found.
                </div>
              ) : (
                filteredGroups.map((group) => (
                  <Combobox.Option
                    key={group.id}
                    className={({ active }) =>
                      `relative cursor-default select-none py-2 pl-10 pr-12 ${
                        active ? "bg-primary-black text-white" : "text-gray-900"
                      }`
                    }
                    value={group.id}
                  >
                    {({ selected, active }) => (
                      <>
                        {editingGroupId === group.id ? (
                          // Edit mode: Show input field with save/cancel buttons
                          <div
                            className="flex items-center gap-2"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <input
                              type="text"
                              value={editingGroupName}
                              onChange={(e) =>
                                setEditingGroupName(e.target.value)
                              }
                              className="flex-1 rounded border border-gray-300 px-2 py-1 text-sm text-black"
                              autoFocus
                              onKeyDown={(e) => {
                                if (e.key === "Enter") {
                                  saveEdit(e);
                                } else if (e.key === "Escape") {
                                  cancelEdit(e);
                                }
                              }}
                            />
                            <button
                              onClick={saveEdit}
                              disabled={isUpdating}
                              className="p-1 text-green-600 hover:text-green-800 disabled:opacity-50"
                              title="Save"
                            >
                              <CheckIcon className="h-4 w-4" />
                            </button>
                            <button
                              onClick={cancelEdit}
                              disabled={isUpdating}
                              className="p-1 text-red-600 hover:text-red-800 disabled:opacity-50"
                              title="Cancel"
                            >
                              <XMarkIcon className="h-4 w-4" />
                            </button>
                          </div>
                        ) : (
                          // Normal mode: Show group name with edit and delete icons
                          <div className="flex items-center justify-between">
                            <span
                              className={`block truncate ${
                                selected ? "font-medium" : "font-medium"
                              }`}
                            >
                              {group.group_name}
                            </span>
                            <div className="flex items-center gap-1">
                              <button
                                onClick={(e) => startEditing(group, e)}
                                className={`p-1 transition-all ${
                                  active
                                    ? "text-white opacity-100 hover:text-gray-200"
                                    : "text-gray-500 opacity-0 hover:text-gray-700 hover:opacity-100"
                                }`}
                                title="Edit group name"
                              >
                                <PencilIcon className="h-4 w-4" />
                              </button>
                              <button
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  // Add delete confirmation and logic here
                                  if (
                                    window.confirm(
                                      `Are you sure you want to delete the group "${group.group_name}"?`
                                    )
                                  ) {
                                    // TODO: Implement delete functionality
                                    console.log("Delete group:", group);
                                  }
                                }}
                                className={`p-1 transition-all ${
                                  active
                                    ? "text-white opacity-100 hover:text-red-200"
                                    : "text-gray-500 opacity-0 hover:text-red-600 hover:opacity-100"
                                }`}
                                title="Delete group"
                              >
                                <TrashIcon className="h-4 w-4" />
                              </button>
                            </div>
                          </div>
                        )}
                      </>
                    )}
                  </Combobox.Option>
                ))
              )}
            </Combobox.Options>
          </Transition>
        </div>
      </Combobox>
      <CreateGroupModal
        // isOpen={createGroupModal}
        // closeModal={() => setCreateGroupModal(false)}
        type="base"
        buttonRef={createGroupModalRef}
        afterCreate={(newId) => {
          fetchGroups();
          setValue(newId);
        }}
      />
    </>
  );
}
