import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";

import { useNavigate, useSearchParams } from "react-router-dom";
import SelectGroupMembers from "./SelectGroupMembers";
import SelectGroupType from "./SelectGroupType";
import useRecipientGroups from "Pages/member/List/RecipientGroups/useRecipientGroups";
import { ArrowLeftIcon } from "@heroicons/react/24/outline";
import { useContexts } from "Hooks/useContexts";
import { LazyLoad } from "Components/LazyLoad";
import { InteractiveButton } from "Components/InteractiveButton";
import { useSDK } from "Hooks/useSDK";
import { StringCaser } from "Utils/utils";
import FundManagerSelectionModal from "Components/FundManagerSelectionModal";
import MkdSDK from "Utils/MkdSDK";

const AddRecipientGroupPage = () => {
  const { sdk } = useSDK();

  const {
    globalDispatch,
    authDispatch,
    authState,
    globalState: { fundManagersToAdd, subscriptionData },
    setGlobalState,
    tokenExpireError,
    showToast,
    create,
    custom,
  } = useContexts();

  // const fundManagersToAdd = globalState?.fundManagersToAdd || [];
  const [groups2, setGroups2] = useState([]);
  const [groupName, setGroupName] = useState("");
  const { groups, refetch, loading } = useRecipientGroups(authState?.user);
  const [defaultValuesP, setDefaultValuesP] = useState({ members: [] });
  const [showFundManagerModal, setShowFundManagerModal] = useState(false);
  const [modalLoading, setModalLoading] = useState(false);

  const schema = yup.object({
    group_id: yup.string().required("This field is required"),
    members: yup
      .array()
      .min(1, "You must add at least one member")
      .of(yup.string()),
  });

  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const updateId = searchParams.get("updateId");

  let exist = groups.find((elem) => elem?.group_name == groupName) || null;

  const {
    register,
    handleSubmit,
    setError,
    setValue,
    formState: { errors, isSubmitting, defaultValues },
    control,
    clearErrors,
    watch,
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: { group_id: "", members: [] },
  });

  const [members, group_id] = watch(["members", "group_id"]);

  // Function to load members when a group is selected (for auto-fill)
  async function loadMembersForSelectedGroup(selectedGroupId) {
    console.log("loadMembersForSelectedGroup called with ID:", selectedGroupId);

    if (!selectedGroupId) {
      console.log("No selectedGroupId, clearing members");
      setValue("members", []);
      return;
    }

    try {
      const sdk = new MkdSDK();

      // Get the group name from the main groups API
      console.log("Fetching group details for ID:", selectedGroupId);
      const groupResult = await sdk.callRawAPI(
        `/v4/api/records/group/${selectedGroupId}`,
        undefined,
        "GET"
      );

      const groupName = groupResult.model?.group_name;
      console.log("Found group name:", groupName);

      if (!groupName) {
        console.log("No group name found, clearing members");
        setValue("members", []);
        return;
      }

      // Get recipient groups to find if this group has existing members
      console.log("Fetching recipient groups...");
      const recipientGroupsResult = await sdk.callRawAPI(
        `/v3/api/custom/goodbadugly/member/get-recipient-groups?user_id=${authState.user}&limit=1000`,
        undefined,
        "GET"
      );

      console.log(
        "Recipient groups:",
        recipientGroupsResult.list?.map((g) => ({
          id: g.id,
          name: g.group_name,
          memberCount: g.members?.length || 0,
        }))
      );

      // Find the recipient group with matching group name
      const selectedRecipientGroup = recipientGroupsResult.list?.find(
        (group) => group.group_name.trim() === groupName.trim()
      );

      console.log("Found matching recipient group:", selectedRecipientGroup);

      if (selectedRecipientGroup && selectedRecipientGroup.members) {
        // Auto-fill members from the selected recipient group
        const memberIds = selectedRecipientGroup.members.map(
          (member) => member.id
        );
        console.log("Auto-filling members:", memberIds);
        setValue("members", memberIds);
      } else {
        // No existing members for this group, clearing selection
        console.log("No existing members for this group, clearing selection");
        setValue("members", []);
      }
    } catch (error) {
      console.error("Error loading members for group:", error);
      setValue("members", []);
    }
  }

  async function getDefaultValues() {
    try {
      sdk.setTable("recipient_group");
      const result = await sdk.callRawAPI(
        `/v4/api/records/recipient_group/${exist.id}`,
        undefined,
        "GET"
      );

      // get members
      sdk.setTable("recipient_member");
      const { list: members } = await sdk.callRawAPI(
        `/v4/api/records/recipient_member?filter=recipient_group_id,eq,${exist.id}`
      );
      return {
        group_id: result.model.group_id,
        members: members.map((m) => String(m.user_id)),
      };
    } catch (error) {
      tokenExpireError(error.message);
      return { group_id: "", members: [] };
    }
  }

  const addUpdateGroup = async (updateId, groupId) => {
    try {
      const result = await create(
        "update_group",
        {
          update_id: updateId,
          group_id: groupId,
        },
        false
      );

      if (!result?.error) {
        navigate(`/member/edit-updates/${updateId}?next=3`);
      }
    } catch (error) {
      tokenExpireError(error.message);
      setError("group_id", {
        type: "manual",
        message: error.message,
      });
    }
  };
  // console.log(watch("members"));

  console.log(groups, groups2, "selected", group_id);

  const addFundManager = async (data) => {
    console.log("hii");
    // Find existing group
    const selectedGroup = groups.find((g) => g.group_name === data.group_id);

    console.log(selectedGroup, "selected");
    // groupId = selectedGroup?.id;
    try {
      const result = await custom({
        endpoint: "/v3/api/custom/goodbadugly/recipients/add-fund-manager",
        method: "POST",
        payload: {
          group_id: Number(selectedGroup?.id),
          fund_managers: data?.members?.map((member) => Number(member)),
        },
      });
      if (!result?.error) {
        showToast(result?.message);
        navigate("/member/recipient_group");
      }
    } catch (error) {
      showToast(error?.message);
    } finally {
    }
  };

  const handleModalSubmit = async (modalData) => {
    setModalLoading(true);
    try {
      let groupId;

      if (modalData.isNewGroup) {
        // Create new group first
        sdk.setTable("recipient_group");
        const newGroupResult = await sdk.callRestAPI(
          {
            group_id: modalData.new_group_name,
            members: "",
            user_id: authState.user,
          },
          "POST"
        );
        groupId = newGroupResult.data;
      } else {
        // Find existing group
        const selectedGroup = groups2.find(
          (g) => g.group_name == modalData.group_id
        );
        console.log(modalData.group_id, selectedGroup, "selected", groups2);
        groupId = selectedGroup?.id;
      }

      if (!groupId) {
        throw new Error("Failed to get group ID");
      }

      // Add fund managers to the group
      const result = await custom({
        endpoint: "/v3/api/custom/goodbadugly/recipients/add-fund-manager",
        method: "POST",
        payload: {
          group_id: Number(groupId),
          fund_managers: modalData.fundManagers.map((fm) => Number(fm.id)),
        },
      });

      if (!result?.error) {
        showToast(
          `Successfully added ${modalData.fundManagers.length} fund manager${
            modalData.fundManagers.length !== 1 ? "s" : ""
          } to ${
            modalData.isNewGroup ? modalData.new_group_name : modalData.group_id
          }`
        );
        setGlobalState("fundManagersToAdd", null);
        setShowFundManagerModal(false);
        navigate("/member/recipient_group");
      } else {
        throw new Error(result.message || "Failed to add fund managers");
      }
    } catch (error) {
      showToast(error?.message || "An error occurred");
    } finally {
      setModalLoading(false);
    }
  };

  const onSubmit = async (data) => {
    console.log(data);

    if (fundManagersToAdd && fundManagersToAdd?.length) {
      await addFundManager(data);
      return;
    }

    try {
      if (!exist) {
        sdk.setTable("recipient_group");
        const result = await sdk.callRestAPI(
          {
            group_id: data.group_id,
            members: data.members.join(","),
            user_id: authState.user,
          },
          "POST"
        );

        // sdk.setTable("recipient_group");
        // const result3 = await sdk.callRawAPI(
        //   `/v4/api/records/recipient_group/${result.recipient_group_id}`,
        //   undefined,
        //   "GET"
        // );
        // console.log(data, result);

        // add the members
        sdk.setTable("recipient_member");
        await Promise.all(
          data.members.map((user_id) =>
            sdk.callRestAPI(
              {
                recipient_group_id: result.data,
                user_id,
              },
              "POST"
            )
          )
        );
        showToast("Added");
        if (updateId) {
          addUpdateGroup(updateId, result.data);
        } else {
          navigate("/member/recipient_group");
        }
      } else {
        sdk.setTable("recipient_group");
        await sdk.callRestAPI(
          {
            id: exist.id,
            group_id: data.group_id,
            members: data.members.join(","),
            user_id: authState.user,
          },
          "PUT"
        );
        // members to add

        const membersToAdd = data.members.filter(
          (m) => !defaultValuesP.members.includes(m)
        );
        const membersToDelete = defaultValuesP.members.filter(
          (m) => !data.members.includes(m)
        );

        console.log(membersToAdd, membersToDelete);
        sdk.setTable("recipient_member");
        await Promise.all(
          membersToAdd.map((user_id) =>
            sdk.callRestAPI(
              {
                user_id: user_id.id,
                recipient_group_id: exist.id,
              },
              "POST"
            )
          )
        );

        await Promise.all(
          membersToDelete.map((user_id) =>
            sdk.callRawAPI(
              `/v4/api/records/recipient_member`,
              { user_id: user_id.id, recipient_group_id: exist.id },
              "DELETE"
            )
          )
        );
        showToast("Added");
        navigate("/member/recipient_group");
      }
    } catch (error) {
      tokenExpireError(error.message);
      setError("group_id", {
        type: "manual",
        message: error.message,
      });
    }
  };

  const loadGroupswithId = async () => {
    const sdk = new MkdSDK();
    const res = await sdk.callRawAPI(
      `/v4/api/records/group?filter=user_id,in,'NULL',${authState.user}`
    );
    console.log(res, "selected");
    setGroups2(res.list);
  };

  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "recipient_group",
      },
    });
  }, []);

  useEffect(() => {
    clearErrors(["members", "group_id"]);
  }, [members, group_id]);

  useEffect(() => {
    (async function () {
      if (exist) {
        await loadGroupswithId();
        const defaultValues = await getDefaultValues();
        setDefaultValuesP(defaultValues);
        setValue("members", defaultValues.members);
      } else {
        setValue("members", []);
      }
    })();
  }, [exist, groupName]);

  useEffect(() => {
    if (fundManagersToAdd && fundManagersToAdd?.length) {
      setShowFundManagerModal(true);
      setValue("members", [
        ...fundManagersToAdd?.map((fundManager) => fundManager?.id),
      ]);
      loadGroupswithId();
    }
  }, [fundManagersToAdd?.length]);
  // console.log("fundManagersToAdd >>", fundManagersToAdd);

  return (
    <div className="min-h-screen p-5 pt-8 sm:px-8">
      <button
        className="mb-7 flex h-[2.25rem] w-[5.1875rem] items-center justify-center gap-2 "
        onClick={() => navigate(-1)}
      >
        <div className="flex min-h-[32px] min-w-[32px] items-center justify-center  gap-2 rounded border border-[#1f1d1a] ">
          <svg
            className="min-h-5 min-w-5"
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M17.5 10.0001L2.5 10.0001"
              stroke="black"
              stroke-width="1.66667"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M8.33203 15.8335L2.4987 10.0002L8.33203 4.16683"
              stroke="black"
              stroke-width="1.66667"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>
        <span className="font-inter font-[400]"> Back</span>
      </button>
      <div className="flex h-fit flex-col items-start gap-5">
        <div>
          <h4 className="mb-2 font-iowan text-[16px] font-[600] sm:text-[20px]">
            Create Recipient Group <br />
            <span className="font-inter text-[12px] font-normal md:text-[14px]">
              Add existing users to existing or new recipient group
            </span>
          </h4>
          <form
            className="mt-5 w-full max-w-[500px]"
            onSubmit={handleSubmit(onSubmit, (error) => {
              console.error(error);
            })}
          >
            <SelectGroupType
              control={control}
              name="group_id"
              errors={errors}
              setGroupName={setGroupName}
              setValue={(v) => setValue("group_id", v)}
              allowedRoles={["investor", "stakeholder"]}
              onGroupChange={(selectedGroupId, memberIds) => {
                // Auto-fill members when group changes - now we get member IDs directly
                console.log(
                  "ADD PAGE - onGroupChange called with:",
                  selectedGroupId,
                  memberIds
                );
                setValue("members", memberIds || []);
                console.log(
                  "ADD PAGE - Form members value after setValue:",
                  watch("members")
                );
              }}
            />
            {errors && errors?.group_id && (
              <p className="text-field-error absolute inset-x-0 top-[90%] m-auto mt-2 text-[.8rem] italic text-red-500">
                Group is Required
              </p>
            )}
            {fundManagersToAdd && fundManagersToAdd?.length ? (
              <div className="font-iowan-regular flex grow items-center justify-start gap-5">
                <b className="text-[3.125rem]">{fundManagersToAdd?.length}</b>{" "}
                <span>Fund Managers Selected</span>
              </div>
            ) : (
              <SelectGroupMembers
                control={control}
                name="members"
                setValue={(v) => setValue("members", v)}
              />
            )}

            {errors && errors?.members && (
              <p className="text-field-error absolute inset-x-0 top-[90%] m-auto mt-2 text-[.8rem] italic text-red-500">
                Members is Required
              </p>
            )}
            <div className="flex items-center justify-start gap-5">
              <LazyLoad>
                <InteractiveButton
                  type="submit"
                  loading={isSubmitting}
                  disabled={isSubmitting}
                  className="focus:shadow-outline mt-4 w-[88px] rounded bg-primary-black px-4 py-2 font-iowan font-bold text-white focus:outline-none"
                >
                  Submit
                </InteractiveButton>
              </LazyLoad>
              {fundManagersToAdd && fundManagersToAdd?.length ? (
                <LazyLoad>
                  <InteractiveButton
                    type="button"
                    // loading={isSubmitting}
                    disabled={isSubmitting}
                    onClick={() => {
                      setGlobalState("fundManagersToAdd", null);
                    }}
                    className="focus:shadow-outline mt-4 rounded bg-primary-black px-4 py-2 font-iowan font-bold text-white focus:outline-none"
                  >
                    Remove Fund Managers
                  </InteractiveButton>
                </LazyLoad>
              ) : null}
            </div>
          </form>
        </div>

        <div className="my-6 flex h-[.125rem] max-h-[.125rem] min-h-[.125rem]  w-full grow flex-row items-center gap-4  border  border-[#1f1d1a]/10 "></div>

        <div className="mb-[30px] flex h-fit w-full flex-col space-y-5 font-iowan">
          <h4 className="mb-2 font-iowan text-[16px] font-[600] sm:text-[20px]">
            Add Investors to a Recipient Group <br />
            {!subscriptionData?.subscription && (
              <span className="font-inter text-[12px] font-normal md:text-[14px]">
                <span
                  className="tex-sm cursor-pointer font-semibold underline underline-offset-2"
                  onClick={() => {
                    navigate("/member/billing");
                  }}
                >
                  Upgrade
                </span>
                &nbsp;to a Pro, Business, or Enterprise plan to access more
                features.
              </span>
            )}
          </h4>

          <div>
            <LazyLoad>
              <InteractiveButton
                onClick={() => navigate("/member/search-fund-managers")}
                className="!rounded-[.125rem] !px-4 !py-2 font-iowan text-white"
              >
                Search fund managers
              </InteractiveButton>
            </LazyLoad>
          </div>

          {/* {fundManagersToAdd && fundManagersToAdd?.length ? (
            <div className="flex flex-col justify-center items-center grow">
              <b className="text-[3.125rem]">{fundManagersToAdd?.length}</b>{" "}
              <span>Fund Managers Selected</span>
            </div>
          ) : null} */}
        </div>
      </div>

      {/* Fund Manager Selection Modal */}
      <FundManagerSelectionModal
        isOpen={showFundManagerModal}
        onClose={() => {
          setShowFundManagerModal(false);
          setGlobalState("fundManagersToAdd", null);
        }}
        fundManagers={fundManagersToAdd || []}
        onSubmit={handleModalSubmit}
        loading={modalLoading}
        onRemoveFundManager={(managerId) => {
          // Remove fund manager from the selection
          const updatedFundManagers = fundManagersToAdd?.filter(
            (manager) => manager.id !== managerId
          );
          setGlobalState("fundManagersToAdd", updatedFundManagers);
        }}
      />
    </div>
  );
};

export default AddRecipientGroupPage;
