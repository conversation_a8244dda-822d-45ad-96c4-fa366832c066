import { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "Utils/MkdSDK";
import { useNavigate, useParams } from "react-router-dom";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { getList, GlobalContext, showToast } from "Context/Global";
import { InteractiveButton } from "Components/InteractiveButton";
import { Spinner } from "Assets/svgs";
import SelectGroupType from "Pages/Member/Add/RecipientGroups/SelectGroupType";
import SelectGroupMembers from "Pages/Member/Add/RecipientGroups/SelectGroupMembers";

import { Loader } from "Components/Loader";
import { InteractiveButton2 } from "Components/InteractiveButton/InteractiveButton";

const EditRecipientGroupPage = () => {
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const { dispatch: authDispatch, state: authState } = useContext(AuthContext);
  const navigate = useNavigate();
  const { id: recipient_group_id } = useParams();
  const [defaultSelectedMembers, setDefaultSelectedMembers] = useState([]);
  const [originalGroupId, setOriginalGroupId] = useState(null);

  const schema = yup.object({
    group_id: yup.string().required("This field is required"),
    members: yup
      .array()
      .min(1, "You must add at least one member")
      .of(yup.string()),
  });

  const {
    register,
    handleSubmit,
    setError,
    setValue,
    formState: { errors, isSubmitting, isLoading, defaultValues },
    control,
    clearErrors,
    watch,
  } = useForm({
    resolver: yupResolver(schema),
  });

  async function getDefaultValues() {
    try {
      let sdk = new MkdSDK();

      // Get the recipient group record to get the actual group_id
      const recipientGroupResult = await sdk.callRawAPI(
        `/v4/api/records/recipient_group/${recipient_group_id}`,
        undefined,
        "GET"
      );

      if (!recipientGroupResult.model) {
        throw new Error("Recipient group not found");
      }

      // get members
      const recipientMembers = await getList(
        globalDispatch,
        authDispatch,
        "recipient_member",
        {
          filter: [`recipient_group_id,eq,${recipient_group_id}`],
          join: ["user"],
        }
      );
      if (!recipientMembers.error) {
        setDefaultSelectedMembers(() =>
          recipientMembers?.data?.map((member) => member?.user?.id)
        );
      }

      // Set the form values
      setValue("group_id", recipientGroupResult.model.group_id);
      setValue(
        "members",
        recipientMembers?.data?.map((member) => member?.user?.id)
      );
      setValue(
        "old_members",
        recipientMembers?.data?.map((member) => member?.user?.id)
      );

      // Set state values
      setOriginalGroupId(recipientGroupResult.model.group_id);
    } catch (error) {
      tokenExpireError(authDispatch, error.message);
      return { group_id: "", members: [] };
    }
  }

  const onSubmit = async (data) => {
    let sdk = new MkdSDK();
    try {
      sdk.setTable("recipient_group");
      await sdk.callRestAPI(
        {
          id: recipient_group_id,
          group_id: data.group_id,
          members: data.members.join(","),
          user_id: authState.user,
        },
        "PUT"
      );
      // members to add
      const membersToAdd = data.members.filter(
        (m) => !data.old_members.includes(m)
      );
      const membersToDelete = data.old_members.filter(
        (m) => !data.members.includes(m)
      );

      sdk.setTable("recipient_member");
      await Promise.all(
        membersToAdd.map((user_id) =>
          sdk.callRestAPI(
            {
              user_id,
              recipient_group_id,
            },
            "POST"
          )
        )
      );

      await Promise.all(
        membersToDelete.map((user_id) =>
          sdk.callRawAPI(
            `/v4/api/records/recipient_member`,
            { user_id, recipient_group_id },
            "DELETE"
          )
        )
      );

      showToast(globalDispatch, "Edit successful");
      navigate("/member/recipient_group");
    } catch (error) {
      tokenExpireError(authDispatch, error.message);
      setError("group_id", {
        type: "manual",
        message: error.message,
      });
    }
  };

  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "recipient_group",
      },
    });
  }, []);

  const [members, group_id] = watch(["members", "group_id"]);

  // Function to load members for a specific group using group.id from main groups API
  async function loadMembersForGroup(selectedGroupId) {
    console.log(
      "EDIT PAGE - loadMembersForGroup called with ID:",
      selectedGroupId
    );

    if (!selectedGroupId) {
      console.log("EDIT PAGE - No selectedGroupId, clearing members");
      setValue("members", []);
      return;
    }

    try {
      const sdk = new MkdSDK();

      // Step 1: Get the group name from the main groups API
      console.log(
        "EDIT PAGE - Fetching group details for ID:",
        selectedGroupId
      );
      const groupResult = await sdk.callRawAPI(
        `/v4/api/records/group/${selectedGroupId}`,
        undefined,
        "GET"
      );

      const groupName = groupResult.model?.group_name;
      console.log("EDIT PAGE - Found group name:", groupName);

      if (!groupName) {
        console.log("EDIT PAGE - No group name found, clearing members");
        setValue("members", []);
        return;
      }

      // Step 2: Get recipient groups to find members by group name
      console.log("EDIT PAGE - Fetching recipient groups...");
      const recipientGroupsResult = await sdk.callRawAPI(
        `/v3/api/custom/goodbadugly/member/get-recipient-groups?user_id=${authState.user}&limit=1000`,
        undefined,
        "GET"
      );

      console.log(
        "EDIT PAGE - Recipient groups:",
        recipientGroupsResult.list?.map((g) => ({
          id: g.id,
          name: g.group_name,
          memberCount: g.members?.length || 0,
        }))
      );

      // Step 3: Find the recipient group with matching group name
      const selectedRecipientGroup = recipientGroupsResult.list?.find(
        (group) => group.group_name.trim() === groupName.trim()
      );

      console.log(
        "EDIT PAGE - Found matching recipient group:",
        selectedRecipientGroup
      );

      if (selectedRecipientGroup && selectedRecipientGroup.members) {
        // Set members from the selected recipient group
        const memberIds = selectedRecipientGroup.members.map(
          (member) => member.id
        );
        console.log("EDIT PAGE - Setting members:", memberIds);
        setValue("members", memberIds);
      } else {
        // No members for this group (it's an empty group)
        console.log("EDIT PAGE - No members for this group, clearing");
        setValue("members", []);
      }
    } catch (error) {
      console.error("EDIT PAGE - Error loading members for group:", error);
      setValue("members", []);
    }
  }

  // Note: Member loading is now handled by onGroupChange callback in SelectGroupType

  useEffect(() => {
    clearErrors(["members", "group_id"]);
  }, [members, group_id]);

  if (isLoading) return <Loader />;
  return (
    <div className="mx-auto rounded p-5 pt-8 md:px-8">
      <h4 className="text-xl font-semibold text-[#1f1d1a]">
        Edit Recipient Group
      </h4>
      <form
        className="mt-5 w-full max-w-[500px] pb-80"
        onSubmit={handleSubmit(onSubmit)}
      >
        <SelectGroupType
          control={control}
          name="group_id"
          errors={errors}
          onReady={() => getDefaultValues()}
          setValue={(v) => setValue("group_id", v)}
          allowedRoles={["investor", "stakeholder", "NULL"]}
          onGroupChange={(selectedGroupId, memberIds) => {
            // Load members when group changes - now we get member IDs directly
            console.log(
              "EDIT PAGE - onGroupChange called with:",
              selectedGroupId,
              memberIds
            );
            setValue("members", memberIds || []);
            console.log(
              "EDIT PAGE - Form members value after setValue:",
              watch("members")
            );
          }}
        />

        <SelectGroupMembers
          defaultSelectedMembers={members}
          control={control}
          name="members"
          setValue={(v) => setValue("members", v)}
        />

        <InteractiveButton2
          type="submit"
          loading={isSubmitting}
          disabled={isSubmitting}
          className="focus:shadow-outline mt-6 h-[40px] w-[90px] rounded bg-primary-black px-4 py-2 text-sm font-bold text-white focus:outline-none"
        >
          {isSubmitting ? "" : "Submit"}
        </InteractiveButton2>
      </form>
    </div>
  );
};

export default EditRecipientGroupPage;
