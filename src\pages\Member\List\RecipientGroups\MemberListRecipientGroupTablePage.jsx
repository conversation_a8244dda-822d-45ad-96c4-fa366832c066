import React, {
  Fragment,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useForm } from "react-hook-form";
import { useNavigate, useSearchParams } from "react-router-dom";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { AddButton } from "Components/AddButton";
import { useProfile } from "Hooks/useProfile";
import { InteractiveButton } from "Components/InteractiveButton";
import { MkdListTableV2 } from "Components/MkdListTable";
import { NoRecipients } from "Components/NoRecipients";
import { LazyLoad } from "Components/LazyLoad";
import { EditIcon2, ExportIcon, SendIcon, TrashIcon } from "Assets/svgs";
import { useContexts } from "Hooks/useContexts";
import {
  getCorrectOperator,
  getCorrectValueTypeFormat,
} from "Components/MkdListTable/MkdListTableV2";
import {
  GroupDropDownSearch,
  MembersDropDownSearch,
  RecipientGroupMembers,
} from "./index";
import { useSDK } from "Hooks/useSDK";
import { ActionConfirmationModal } from "Components/ActionConfirmationModal";
import moment from "moment";
import MkdSDK from "Utils/MkdSDK";
import RecipientGroupMembers2 from "Components/RecipientGroupMembers2";
import useGroups from "Pages/Member/Edit/Updates/api/useGroups";

const columns = [
  // {
  //   header: "Id",
  //   accessor: "id",
  // },

  {
    header: "ID",
    accessor: "id",
    isSorted: true,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
  },
  {
    header: "Group Name",
    accessor: "group_name",
    join: "group",
    isSorted: true,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
  },

  {
    header: "Members",
    accessor: "members",
    isSorted: true,
    thumbnail: true,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
  },
  {
    header: "Action",
    accessor: "",
  },
];

const MemberListRecipientGroupTablePage = () => {
  const recipientGroupTableRefreshRef = useRef(null);
  const recipientGroupTableUpdateRef = useRef(null);
  const groupDropDownRefreshRef = useRef(null);
  const groupDropDownClearRef = useRef(null);
  const membersDropDownRefreshRef = useRef(null);
  const membersDropDownClearRef = useRef(null);

  const groupIdUid = btoa("group:group_id");
  const membersUid = btoa("group:members");

  const { groups } = useGroups();
  console.log(groups);

  const { sdk } = useSDK();
  const {
    globalDispatch,
    authDispatch,
    authState,
    getManyById,
    showToast,
    tokenExpireError,
  } = useContexts();

  const [selectedOptions, setSelectedOptions] = React.useState([
    {
      accessor: "group_id",
      operator: "cs",
      value: "",
      uid: groupIdUid,
    },
    {
      accessor: "members",
      operator: "cs",
      value: "",
      uid: membersUid,
    },
  ]);
  const [localData, setLocalData] = useState({
    filter: [],
    refresh: false,
    modal: null,
    showModal: false,
    selectedItems: [],
    loading: false,
  });

  const [deleteLoading, setDeleteLoading] = useState(false);

  const selectedOptionsMemo = useMemo(
    () => JSON.stringify(selectedOptions),
    [selectedOptions]
  );

  const navigate = useNavigate();

  const [searchParams, setSearchParams] = useSearchParams();
  const { profile } = useProfile();

  const schema = yup.object({
    group_id: yup.string(),
    members: yup.string(),
  });

  const {
    register,
    reset,
    setValue,
    watch,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
    // defaultValues: async () => {
    //   const group_name = searchParams.get("group_name") ?? "";
    //   const members = searchParams.get("members") ?? "";
    //   return { group_name, members };
    // },
  });

  const { group_id, members } = watch();

  const setOptionValue = useCallback(
    (field, value, uid) => {
      setSelectedOptions((prev) =>
        prev.map((item) =>
          item?.uid === uid ? { ...item, [field]: value } : item
        )
      );
    },
    [selectedOptions, selectedOptionsMemo]
  );

  // const exportTableAPI = async (id) => {
  //   try {
  //     const uri = "/v3/api/custom/goodbadugly/export-recipients?company_id=406";
  //     const res = await sdk.callRawAPI(uri, [], "GET");
  //     return res;
  //   } catch (error) {
  //     return error;
  //   }
  // };
  const processFilters = useCallback(
    (selectedOptions) => {
      let filters = [];
      const uniqueSet = new Set(selectedOptions.map((item) => item?.accessor));

      uniqueSet.forEach((uniqueSetItem) => {
        const filterSet = selectedOptions.filter(
          (item) => item.accessor === uniqueSetItem
        );

        if (filterSet?.length > 0) {
          const valueSet = filterSet.filter((item) => item?.value);

          if (valueSet.length > 1) {
            valueSet.forEach((valueSetItem) => {
              const { accessor, operator, value } = valueSetItem;
              const filter = `goodbadugly_recipient_group.${accessor},${operator},${value}`;
              filters.push(filter);
            });
          } else if (valueSet.length === 1) {
            const { accessor, operator, value } = valueSet[0];
            filters.push(
              `goodbadugly_recipient_group.${accessor},${operator},${getCorrectValueTypeFormat(
                value,
                operator
              )}`
            );
          }
        }
      });
      return filters;

      //     operator === "cs" || operator === "eq"
      //     ? getCorrectOperator("o" + operator, value)
      //     : getCorrectOperator(operator, value)
      // }
    },
    [selectedOptions]
  );

  async function exportTable() {
    const sdk = new MkdSDK();
    try {
      await sdk.exportCSVGroup();
    } catch (error) {
      console.error("Export error:", error);
      showToast("Failed to export data", 5000, "error");
    }
  }

  const gotoAdd = () => {
    navigate("/member/add-recipient_group");
  };

  function onSubmit(data) {
    searchParams.set("group_name", data.group_name);
    searchParams.set("members", data.members);
    searchParams.set("page", 1);
    setSearchParams(searchParams);
  }

  const onToggleModal = useCallback((modal, toggle, ids = []) => {
    setLocalData((prev) => ({
      ...prev,
      selectedItems: ids,
      showModal: toggle,
      modal: toggle ? modal : null,
    }));
  }, []);

  async function deleteItem(id) {
    if (!id) return;

    setDeleteLoading(true);
    try {
      sdk.setTable("recipient_group");
      await sdk.callRestAPI({ id }, "DELETE");
      showToast(globalDispatch, "Delete successful");
      // Refresh the table after successful deletion
      if (recipientGroupTableRefreshRef?.current) {
        recipientGroupTableRefreshRef.current.click();
      }
    } catch (err) {
      console.error("Delete error:", err);
      tokenExpireError(authDispatch, err.message);
      if (err.message !== "TOKEN_EXPIRED") {
        showToast(globalDispatch, err.message, 5000, "error");
      }
    } finally {
      setDeleteLoading(false);
      onToggleModal("delete", false, []);
    }
  }

  const refreshTable = useCallback(
    (options = { clear: false }) => {
      if (options?.clear) {
        setOptionValue("value", "", groupIdUid);
        setOptionValue("value", "", membersUid);

        if (groupDropDownClearRef?.current) {
          groupDropDownClearRef?.current?.click();
        }
        if (membersDropDownClearRef?.current) {
          membersDropDownClearRef?.current?.click();
        }
      }

      const filters = options?.clear ? [] : processFilters(selectedOptions);

      setLocalData((prev) => {
        return {
          ...prev,
          filter: [
            `goodbadugly_recipient_group.user_id,eq,${profile?.id}`,
            ...filters,
          ],
          refresh: true,
        };
      });
    },
    [selectedOptions]
  );

  const preprocessRecipientGroups = async (data, columns, filter) => {
    const promise = data?.map(async (item) => {
      const users = await getManyById("user", item?.members?.split(","));
      return {
        ...item,
        members: <RecipientGroupMembers2 members={users?.data} />,
      };
    });

    const recipientGroups = await Promise.all(promise);
    return recipientGroups;
  };

  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "recipient_group",
      },
    });

    if (profile?.id) {
      setLocalData((prev) => ({
        ...prev,
        refresh: false,
        filter: [`goodbadugly_recipient_group.user_id,eq,${profile?.id}`],
      }));
    }
  }, [profile?.id]);

  useEffect(() => {
    if (localData?.refresh && recipientGroupTableRefreshRef?.current) {
      recipientGroupTableRefreshRef?.current?.click();
    }
  }, [localData?.refresh]);

  return (
    <Fragment>
      <div className=" space-y-[1rem] rounded  bg-brown-main-bg p-5 px-5 md:px-8">
        <div className="my-[16px] flex w-full items-center justify-between gap-5">
          <h4 className="font-iowan text-[20px] font-bold md:text-[2rem] md:leading-[2.486rem]">
            Recipient Groups
          </h4>

          <div className="flex w-fit items-center justify-start gap-5">
            <LazyLoad>
              <AddButton
                showPlus={false}
                className={"!rounded-[.125rem] !p-[10px] !px-[10px]"}
                onClick={
                  () => gotoAdd()
                  // onToggleModal("add", true, [])
                }
              >
                New Group
              </AddButton>
            </LazyLoad>
          </div>
        </div>

        <div className="flex w-full items-end justify-between gap-5">
          <div className="flex w-full flex-col gap-5 sm:flex-row sm:items-end md:w-[55%]">
            <div className="sm:grow=[0.5] flex grid-cols-[repeat(auto-fill,minmax(14rem,1fr))] flex-wrap items-end gap-2 sm:grid">
              <div className={`!grow`}>
                <GroupDropDownSearch
                  onSelect={(value, clear = false) => {
                    if (clear) {
                      setOptionValue("value", "", groupIdUid);
                      setValue("group_id", "");
                    } else {
                      setOptionValue("value", value?.group?.id, groupIdUid);
                      setValue("group_id", value?.group?.id);
                    }
                  }}
                  value={group_id}
                  refreshRef={groupDropDownRefreshRef}
                  clearRef={groupDropDownClearRef}
                />

                {/* <MkdInput
                  type="text"
                  // disabled={true}
                  errors={errors}
                  register={register}
                  name={"group_name"}
                  label={"Group Name"}
                  placeholder={"Search..."}
                  className={`!h-[2.25rem] !rounded-[0.125rem] !border !py-[0.5rem]`}
                /> */}
              </div>
              <div className={`!grow`}>
                <LazyLoad>
                  <MembersDropDownSearch
                    onSelect={(value, clear = false) => {
                      if (clear) {
                        setOptionValue("value", "", membersUid);
                        setValue("member_id", "");
                      } else {
                        setOptionValue("value", value?.member_id, membersUid);
                        setValue("member_id", value?.member_id);
                      }
                    }}
                    value={members}
                    refreshRef={membersDropDownRefreshRef}
                    clearRef={membersDropDownClearRef}
                  />
                </LazyLoad>
                {/* <MkdInput
                  type="text"
                  errors={errors}
                  register={register}
                  name={"members"}
                  label={"Members"}
                  placeholder={"Search..."}
                  className={`!h-[2.25rem] !rounded-[0.125rem] !border !py-[0.5rem]`}
                /> */}
              </div>
            </div>
            <div className="flex items-end gap-3">
              <InteractiveButton
                type="submit"
                className={`mb-[-5px] flex !h-[2.25rem] !w-fit !min-w-fit !max-w-fit items-center justify-center whitespace-nowrap !rounded-[0.125rem] !border  bg-[#1f1d1a] !py-[0.5rem] px-2 !text-[1rem] tracking-wide text-white outline-none focus:outline-none md:px-5`}
                color="black"
                disabled={!profile?.id}
                onClick={() => {
                  refreshTable();
                }}
              >
                Search
              </InteractiveButton>
              <AddButton
                onClick={() => {
                  reset();
                  refreshTable({ clear: true });
                }}
                showPlus={false}
                className="!w-fit !min-w-fit !max-w-fit !border-0 !bg-transparent !p-0 !font-inter !text-[1rem] !font-[600] !leading-[1.21rem] !tracking-wide !text-black !underline !shadow-none md:mb-[-10px]"
              >
                Clear
              </AddButton>
            </div>
          </div>

          <InteractiveButton
            type="button"
            className={`flex !h-[2.25rem]  !w-fit !min-w-fit items-center justify-center whitespace-nowrap !rounded-[0.125rem] !border border-black bg-transparent !py-[0.5rem] px-2 !text-[1rem] tracking-wide text-black outline-none focus:outline-none md:px-5`}
            color="black"
            onClick={() => {
              exportTable();
            }}
          >
            <ExportIcon /> Export
          </InteractiveButton>
        </div>

        <div className="h-[.125rem] w-full border-[.125rem] border-black bg-black " />

        {profile?.id && localData?.filter?.length > 0 ? (
          <MkdListTableV2
            showSearch={false}
            useDefaultColumns={true}
            defaultColumns={[...columns]}
            noDataComponent={{
              use: true,
              component: (
                <LazyLoad>
                  <NoRecipients />
                </LazyLoad>
              ),
            }}
            // onUpdateCurrentTableData={(cb) => {
            //   console.log("externalData", externalData);
            //   cb(externalData);
            // }}
            // externalData={{
            //   ...externalData,
            //   fetch: () => {},
            // }}
            onReady={() => {
              // console.log("onReady");
              setLocalData((prev) => {
                return { ...prev, refresh: false };
              });
            }}
            processes={[preprocessRecipientGroups]}
            hasFilter={false}
            tableRole={"admin"}
            actionId={"id"}
            table={"recipient_group"}
            // tableTitle={`Team Members`}
            join={["user", "group"]}
            defaultFilter={localData?.filter}
            actions={{
              view: { show: false, action: null, multiple: false },
              select: { show: false, action: null, multiple: false },
              update: {
                show: true,
                action: (ids) => {
                  if (ids?.[0]) {
                    navigate(`/member/edit-recipient_group/${ids[0]}`, {
                      state: ids[0],
                    });
                  }
                },
                multiple: false,
                children: "Edit",
                showChildren: true,
                icon: <EditIcon2 />,
                locations: ["dropdown"],
                onClick: (e) => {
                  e.preventDefault();
                  e.stopPropagation();
                },
              },
              remove: {
                show: true,
                action: (ids) => {
                  if (ids?.[0]) {
                    onToggleModal("delete", true, ids);
                  }
                },
                multiple: false,
                children: "Delete",
                icon: <TrashIcon fill="#292D32" />,
                locations: ["dropdown"],
                onClick: (e) => {
                  e.preventDefault();
                  e.stopPropagation();
                },
              },
              view_all: {
                show: false,
                type: "static",
                action: () => {},
                children: <>View All</>,
                className:
                  "!gap-2 !bg-transparent !text-black !underline !shadow-none !border-0",
              },
              add: {
                show: false,
                // action: () => navigate("/admin/add-receipts"),
                multiple: true,
                children: "+ Add",
              },
              export: { show: false, action: null, multiple: true },
            }}
            defaultPageSize={20}
            showPagination={true}
            maxHeight={`md:grid-rows-[inherit] grid-rows-[inherit]`}
            actionPostion={["dropdown"]}
            refreshRef={recipientGroupTableRefreshRef}
            updateRef={recipientGroupTableUpdateRef}
          />
        ) : null}
      </div>

      <LazyLoad>
        <ActionConfirmationModal
          mode="delete"
          action="delete"
          table="recipient_group"
          inputConfirmation={false}
          title="Delete Recipient Group"
          data={{ id: localData?.selectedItems[0] }}
          onSuccess={() => {
            deleteItem(localData?.selectedItems[0]);
          }}
          onClose={() => onToggleModal("delete", false, [])}
          isOpen={localData?.showModal && ["delete"].includes(localData?.modal)}
          loading={deleteLoading}
        />
      </LazyLoad>
    </Fragment>
  );
};

export default MemberListRecipientGroupTablePage;

// {!loading ? (
//   <>
//     <div className="p-5 pt-8 rounded shadow bg-brown-main-bg md:px-8">
//       <div className="flex justify-between mb-6 w-full text-center">
//         <h4 className="text-left text-[16px] font-[600] sm:text-[20px]">
//           Search
//         </h4>
//       </div>
//       <form
//         onSubmit={handleSubmit(onSubmit)}
//         className="flex flex-col gap-4 max-w-3xl"
//       >
//         <div className="grid grid-cols-1 gap-6 items-end sm:flex">
//           <div>
//             <label className="mb-2 block  text-sm font-semibold capitalize text-[#1f1d1a]">
//               Group name
//             </label>
//             <input
//               type="text"
//               {...register("group_name")}
//               className={`focus:shadow-outline w-full appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm  font-normal capitalize leading-tight text-[#1d1f1a] shadow focus:outline-none   sm:w-[180px] ${
//                 errors.group_name?.message ? "border-red-500" : ""
//               }`}
//             />
//             <p className="italic text-red-500 text-field-error">
//               {errors.group_name?.message}
//             </p>
//           </div>
//           <div>
//             <label className="mb-2 block  text-sm font-semibold capitalize text-[#1f1d1a]">
//               Members{" "}
//               <span className="font-iowan-regulartext-xs">
//                 (email separated by comma)
//               </span>
//             </label>
//             <input
//               type="text"
//               {...register("members")}
//               className={`focus:shadow-outline w-full appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm text-sm text-sm font-normal font-normal font-normal capitalize leading-tight text-[#1d1f1a] shadow focus:outline-none   sm:w-[150px] sm:w-[180px] ${
//                 errors.members?.message ? "border-red-500" : ""
//               }`}
//             />
//             <p className="italic text-red-500 text-field-error">
//               {errors.members?.message}
//             </p>
//           </div>
//         </div>
//         <div className="flex gap-4 items-center">
//           <button
//             type="submit"
//             disabled={loading}
//             className="px-4 py-1 font-semibold text-white font-iowan-regularrounded-md bg-primary-black/80 hover:bg-primary-black"
//           >
//             Search
//           </button>
//           <button
//             type="button"
//             onClick={() => {
//               reset({ group_name: "", members: "" });
//               searchParams.set("group_name", "");
//               searchParams.set("members", "");
//               searchParams.set("page", 1);
//               setSearchParams(searchParams);
//             }}
//             disabled={loading}
//             className="rounded-md px-4 py-1 font-semibold text-[#1f1d1a]"
//           >
//             Clear
//           </button>
//         </div>
//       </form>
//       <div className="overflow-x-auto p-5 px-0 mt-10 rounded bg-brown-main-bg md:mt-8">
//         <div className="flex justify-between mb-6 w-full text-center">
//           <h4 className="text-left text-[16px] font-[600] sm:text-[20px]">
//             Recipient Groups
//           </h4>
//           <div className="flex gap-3 items-center">
//             <ExportButton
//               onClick={() => exportTable()}
//               className="sm:px-1"
//             />
//             <AddButton
//               onClick={gotoAdd}
//               className={"py-2 font-medium sm:px-2"}
//             />
//           </div>
//         </div>
//         <ResponsiveTableWrapperRecipientGroups>
//           <table className="min-w-full divide-y divide-[#1f1d1a]/10">
//             <thead>
//               <tr>
//                 {columns.map((column, i) => {
//                   return (
//                     <th
//                       key={i}
//                       scope="col"
//                       className={`font whitespace-normal  border-b-[#1f1d1a]/10 px-3 text-left capitalize tracking-wider  text-[#1f1d1a] md:whitespace-nowrap md:border-0 md:border-b-[3px]  md:border-dashed md:px-6 md:py-3`}
//                     >
//                       {column.header}
//                     </th>
//                   );
//                 })}
//               </tr>
//             </thead>
//             <tbody className="font-iowan-regular divide-y divide-[#1f1d1a]/10">
//               {groups.map((row, i) => {
//                 return (
//                   <tr
//                     key={row.id}
//                     className="border-b-[#1f1d1a]/20 md:h-[60px]"
//                   >
//                     {/* <td className="px-3 py-2 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-4">
//                       {row.id}
//                     </td> */}
//                     <td className="px-3 py-2 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-4">
//                       {row.group_name}
//                     </td>
//                     <td className="px-3 py-2 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-4">
//                       {row.members.map((m) => m.email).join(", ")}
//                     </td>
//                     <td className="flex gap-2 justify-start items-center px-6 py-4 whitespace-nowrap">
//                       <div className="flex h-auto items-center gap-2 sm:h-[60px] sm:justify-center">
//                         <button
//                           className="cursor-pointer text-sm font-medium text-[#292829fd] underline hover:underline"
//                           onClick={() => {
//                             navigate(
//                               `/member/edit-recipient_group/` + row.id,
//                               {
//                                 state: row,
//                               }
//                             );
//                           }}
//                         >
//                           <span>Edit</span>
//                         </button>

//                         <DeleteRecipientGroupButton
//                           group={row}
//                           afterDelete={refetch}
//                         />
//                       </div>
//                     </td>
//                   </tr>
//                 );
//               })}
//             </tbody>
//           </table>
//         </ResponsiveTableWrapperRecipientGroups>
//         {groups?.length == 0 ? (
//           <div className="mb-[20px] mt-24 flex flex-col items-center">
//             <ClipboardDocumentIcon
//               className="w-8 h-8 text-gray-700"
//               strokeWidth={2}
//             />
//             <p className="mt-4 text-base font-medium text-center">
//               No Recipient Group Added yet
//             </p>
//           </div>
//         ) : null}
//         <PaginationBar
//           currentPage={currentPage}
//           pageCount={Math.ceil(totalCount / pageSize)}
//           pageSize={pageSize}
//           canPreviousPage={canPreviousPage}
//           canNextPage={canNextPage}
//           updatePageSize={updatePageSize}
//           previousPage={previousPage}
//           nextPage={nextPage}
//           dataLoading={loading}
//           totalCount={totalCount}
//         />
//       </div>
//     </div>
//   </>
// ) : (
//   <Loader />
// )}
